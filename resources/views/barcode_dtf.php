<html>

<head>
    <style>
        .page-break {
            page-break-after: always;
        }

        html {
            padding: 0;
            margin: 0;
        }

        body {
            font-family: sans-serif;
            font-size: 8pt;
            font-weight: bold;
        }

        .barcode {
            padding: 0;
            margin: 0;
            position: absolute;
            left: 22.167px;
            top: 81.279px;
        }

        .barcode2 {
            padding: 0;
            margin: 0;
            position: absolute;
            left: 325.116px;
            top: 59.112px;
        }

        .qr {
            position: absolute;
            left: 502.452px;
            bottom: 14.778px;

        }

        .xqc {
            position: absolute;
            top: 56.156px;
            right: 14.778px;
            left: 502.452px;
            width: 147.78px;
            text-align: center;
            font-size: 10pt;
        }

        .ink_color {
            position: absolute;
            top: 110.735px;
            left: 502.452px;
            width: 147.78px;
            text-align: center;
            background: black;
            color: #ffffff;
        }


        .item_num {
            position: absolute;
            top: 155.169px;
            left: 502.452px;
            text-align: center;
            font-size: 10pt;
            width: 147.78px;
            z-index: 9999;
        }

        .order_date {
            position: absolute;
            top: 8.867px;
            left: 517.23px;
            text-align: center;
            width: 147.78px;
            font-size: 12pt;
        }

        .print_side_front {
            position: absolute;
            bottom: 59.112px;
            left: 399.006px;
            background: #ffffff;
            color: #000000;
            border: 1px solid #000000;
            padding: 1px;
            width: 66.501px;
            text-align: center;
            font-size: 14pt;
            z-index: 9999;
            border-radius: 50%;
        }

        .s_print_side_front {
            position: absolute;
            bottom: 125.613px;
            left: 399.006px;
            background: #ffffff;
            color: #000000;
            border: 1px solid #000000;
            padding: 1px;
            width: 66.501px;
            text-align: center;
            font-size: 14pt;
        }

        .s_print_side_back {
            width: 66.501px;
            text-align: center;
            font-size: 15pt;
            position: absolute;
            bottom: 125.613px;
            left: 399.006px;
            background: #000000;
            border: 1px solid #000000;
            color: #ffffff;
            padding: 1px;
        }

        .print_side_back {
            width: 59.112px;
            text-align: center;
            font-size: 15pt;
            position: absolute;
            bottom: 66.501px;
            left: 399.006px;
            background: #000000;
            border: 1px solid #000000;
            color: #ffffff;
            padding: 1px;
            z-index: 9999;
            border-radius: 50%;
        }

        .back {
            background: #000000;
            color: #ffffff;
            font-size: 14pt;
            position: absolute;
            bottom: 14.778px;
            /*left: 280px;*/
            left: 399.006px;
            width: 66.501px;
            text-align: center;
            padding: 1px;
            z-index: 9999;
            border-radius: 50%;
            /* border: 1px solid #000000;*/
        }

        .store {
            position: absolute;
            font-size: 13pt;
            bottom: 14.778px;
            left: 192.114px;
            width: 295.56px;
            height: 280.782px;
            /* display: block; */
            /* word-wrap: break-word */
        }

        .store_short {
            position: absolute;
            font-size: 18pt;
            bottom: 14.778px;
            left: 192.114px;
            /* width: 200px; */
            height: 273.393px;
        }

        .s_back {
            background: #000000;
            color: #ffffff;
            font-size: 14pt;
            position: absolute;
            bottom: 29.556px;
            /*left: 280px;*/
            left: 399.006px;
            width: 66.501px;
            text-align: center;
            padding: 1px;
            /* border: 1px solid #000000;*/
        }

        .s_front {
            text-align: center;
            bottom: 118.224px;
            /*left: 230px;*/
            left: 399.006px;
            width: 66.501px;
            position: absolute;
            border: 1px solid #000000;
            font-size: 14pt;
        }

        .front {
            text-align: center;
            bottom: 96.507px;
            /*left: 230px;*/
            left: 399.006px;
            width: 66.501px;
            position: absolute;
            border: 1px solid #000000;
            font-size: 14pt;
            z-index: 9999;
            border-radius: 50%;
        }

        .s_info {
            position: absolute;
            bottom: 32.512px;
            /*left: 15px;*/
            left: 29.556px;
        }

        .info {
            position: absolute;
            bottom: 14.778px;
            /*left: 15px;*/
            left: 22.167px;
            line-height: 0.8rem;

        }

        .rotate {
            position: absolute;
            bottom: 51.723px;
            /*left: 220px;*/
            left: 29.556px;
        }

        .rotate2 {
            position: absolute;
            top: 11.822px;
            /*left: 220px;*/
            left: 22.167px;
            font-size: 10pt;
        }

        .rotate3 {
            position: absolute;
            bottom: 2.956px;
            /*left: 220px;*/
            left: 133.002px;
            font-size: 6pt;
        }

        .s_rotate3 {
            position: absolute;
            bottom: 2.956px;
            /*left: 220px;*/
            left: 133.002px;
            font-size: 6pt;
        }
    </style>
</head>

<body>
    <?php
    $total = count($items);
    foreach ($items as $key => $item) {
        ?>

        <div class="item_num"><?php echo $item->barcode_number; ?>/<?php echo $item->order_quantity; ?></div>

        <?php if ($item->ink_color == 1) { ?>
            <div class="ink_color">BK</div>
        <?php } ?>
        <?php if ($item->is_xqc == true && $item->is_eps == true) { ?>
            <div class="xqc">XQC/EPS</div>
        <?php } elseif ($item->is_xqc == true) { ?>
            <div class="xqc">XQC</div>
        <?php } elseif ($item->is_eps == true) { ?>
            <div class="xqc">EPS</div>
        <?php } ?>
        <div class="order_date"><?php echo $item->order_date_formatted ?? '-'; ?></div>
        <?php if (strlen($item->store_name ?? '') <= 8) { ?>
            <div class="store_short"><?php echo $item->store_name ?? ''; ?></div>
        <?php } else { ?>
            <div class="store"><?php echo substr($item->store_name ?? '', 0, 16); ?></div>
        <?php } ?>
        <div class="info">
            <div style="font-size: 11pt"><?php echo $item->style_formatted; ?></div>
            <div style="line-height: 0.7rem"><?php echo $item->size_formatted; ?></div>
            <div><?php echo $item->color_formatted; ?></div>
        </div>


        <div class="rotate2">
            <?php echo $item->label_id ?>
        </div>

        <div class="rotate3">
        </div>

        <div class="barcode">
            <?php echo $item->label_1; ?>
        </div>

        <!--<div class="barcode2">
        <?php /*if ($item->label_2) echo $item->label_2; */ ?>
    </div>-->

        <?php if ($item->print_side == '2') { ?>
            <div class="front">F</div>
            <div class="back">B</div>
        <?php } elseif ($item->print_side == '0') { ?>
            <div class="print_side_front">F</div>
        <?php } elseif ($item->print_side == '1') { ?>
            <div class="print_side_back">B</div>
        <?php } ?>

        <div class="qr">
            <?php echo $item->qrcode; ?>
        </div>
        <?php if ($key + 1 < $total) { ?>

            <div class="page-break"></div>
        <?php } ?>


    <?php } ?>
</body>

</html>
