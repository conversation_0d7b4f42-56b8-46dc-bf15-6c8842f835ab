<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .page-break {
            page-break-after: always;
        }

        html {
            padding: 0;
            margin: 0;
        }

        body {
            font-family: sans-serif;
            font-size: 8pt;
            font-weight: bold;
            margin-left: 8px;
        }

        .info-order {
            top: 10px;
            font-size: 11pt;
            position: absolute;
            width: 97%;
        }

        .order-date {
            position: absolute;
            top: 0;
            font-size: 19px;
            right: 0px;
        }

        .counter {
            font-size: 12pt;
        }

        .store {
            position: absolute;
            font-size: 14pt;
            top: 38px;
        }

        .code-store {
            position: absolute;
            font-size: 8.5pt;
            top: 45px;
        }

        .order-type {
            top: 80px;
            position: absolute;
        }

        .order-type span {
            background: black;
            color: #ffffff;
            margin-right: 5px;
            padding: 5px;
            text-align: center;
            border: 1px solid #000000;
            font-size: 11pt;
        }

        .info-product {}

        .product {
            max-width: 200px;
            position: absolute;
            bottom: 15px;
        }

        .style {
            font-size: 10pt;
        }

        .area-top span {
            border: 1px solid #000000;
            padding: 2px;
            width: 40px;
            height: 40px;
            text-align: center;
            font-size: 17pt;
            font-weight: bolder;
            display: inline-block;
            line-height: 45px;
            z-index: 9999;
        }

        .area-bottom span {
            border: 1px solid #000000;
            padding: 2px;
            width: 40px;
            height: 40px;
            text-align: center;
            font-size: 17pt;
            font-weight: bolder;
            display: inline-block;
            line-height: 45px;
            z-index: 9999;
        }

        .info-qr {
            position: absolute;
            left: 320px;
            top: 80px;
        }

        .item-num {
            text-align: center;
            z-index: 9999;
            font-size: 12pt;
        }

        .qr {
            margin-top: 0px;
        }

        .bk span {
            background: black;
            color: #ffffff;
            margin-right: 5px;
            padding: 5px;
            text-align: center;
            border: 1px solid #000000;
            font-size: 10pt;
        }
    </style>
</head>

<body>
    <div>
        <?php
        $total = count($items);
        ?>
        @foreach ($items as $key => $item)
            <?php
                $subLabel = substr($item->label_id, 7);
        ?>
            <div class="info-order">
                <div class="label-id">{{ $subLabel }}</div>
                <div class="order-date">
                    <span style="font-size: 11pt">{{ $item->order_date_formatted ?? '' }}</span>
                    <span>{{ $item->ink_color_detected_at }}</span>
                </div>
            </div>
            <div class="code-store">{{ $item->store_code ?? 'N/A' }}</div>
            <div class="order-type" style="top: {{ strlen($item->store_name ?? '') <= 18 ? 82 : 80 }}px">
                <span class="blank-type">BLANK</span>
            </div>
            <div class="info-product">
                <div class="product">
                    <div class="style">{{ $item->style_formatted }}</div>
                    <div class="size">{{ $item->size_formatted }}</div>
                    <div class="color">{{ $item->color_formatted }}</div>
                </div>
                <div class="info-qr">
                    <div class="item-num">{{ $item->barcode_number . '/' . $item->order_quantity }}</div>
                    <div class="qr">{!! $item->barcode_qr !!}</div>
                </div>
            </div>
            @if ($key + 1 < $total)
                <div class="page-break"></div>
            @endif
        @endforeach
    </div>
</body>

</html>
