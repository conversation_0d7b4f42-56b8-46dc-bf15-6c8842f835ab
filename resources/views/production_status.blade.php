<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@9.0.1/public/assets/styles/choices.min.css"/>

    <title>Design Check</title>
    <style>
        * {
            font-size: 0.9rem;
        }
    </style>
</head>
<style>
    nav {
        display: flex;
    }
    .pagination {
        margin-top: 20px;
        margin-right: auto;
        margin-left: auto;
    }
    .form-control {
        height: 44.42px
    }
    .logout {
        float: right;
        padding: 5px;
        color: blue;
        font-weight: 700;
    }
</style>
<body>

<header>
</header>
<div class="container">
        @if(session('error'))
            <div class="alert alert-danger">
                <ul>
                {{ session('error') }}
                </ul>
            </div>
        @endif

        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

    <div class="row" style="justify-content: space-between;">
        <h1 style="width: fit-content;">Update Production Status</h1>
        <a href="{{ route('logout') }}" style="width: fit-content; margin-top: auto; margin-bottom: auto; text-decoration: none;">Logout</a>
    </div>
    <div class="my-3">
        <!-- Add the form element -->
        <form action="{{ url('/tool/update/production-status') }}" method="POST">
            @csrf <!-- Add CSRF token for security -->

            <ul>
                <textarea type="number" class="form-control" required name="order_ids" placeholder="Enter sales order id"></textarea>
            </ul>

            <!-- Add a submit button -->
            <button type="submit" class="btn btn-primary">Submit</button>
        </form>
    </div>
</div>
<!-- Optional JavaScript; choose one of the two! -->

<!-- Option 1: Bootstrap Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        crossorigin="anonymous"></script>

<!-- Option 2: Separate Popper and Bootstrap JS -->
<!--
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js" integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js" integrity="sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF" crossorigin="anonymous"></script>
-->
<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

</script>
</body>
</html>
