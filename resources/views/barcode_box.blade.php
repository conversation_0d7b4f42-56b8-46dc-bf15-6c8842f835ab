<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .page-break {
            page-break-after: always;
        }

        html {
            padding: 0;
            margin: 0;
        }

        body {
            font-family: sans-serif;
            font-size: 8pt;
            font-weight: bold;
            margin-left: 8px;
        }

        .barcode {
            font-size: 16pt;
            display: flex;
            position: absolute;
            left: 30px;
            top: 15px;
        }
        .qr {
            position: absolute;
            left: 70px;
            top: 75px;
        }
        .bottom-barcode {
            font-size: 6pt;
            display: flex;
            position: absolute;
            left: 39%;
            top: 200px;
            font-weight: normal !important;
        }
    </style>
</head>

<body>
    <div>
        <?php
        $total = count($items);
        ?>
        @foreach ($items as $key => $item)
            <div class="info-order">
            </div>
            <div class="barcode">
                <span>BOX ID:</span>
                <span>{{ $item->box_barcode }}</span>
            </div>
            <div class="qr">
                {!! $item->barcode_qr !!}
            </div>

            <div class="bottom-barcode">
                <span>{{ $item->box_barcode }}</span>
            </div>

            @if ($key + 1 < $total)
                <div class="page-break"></div>
            @endif
        @endforeach
    </div>
</body>

</html>
