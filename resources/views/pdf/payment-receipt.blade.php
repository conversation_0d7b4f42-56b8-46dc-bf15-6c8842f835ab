@extends('pdf.receipt')
@section('content')
<table>
    <thead>
        <tr>
            <th>Item</th>
            <th class="align-right">Qty</th>
            <th class="align-right">Line total</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($data->items as $item)
            <tr>
                <td>
                    <span class="bold-text fw-600">Product: {{ $item['name'] ?? '' }}</span>
                    <span style="display: block; color: #667085">SKU: {{ $item['sku'] ?? '' }} • Print area: {{$item['print_areas'] ??
                        ''}}</span>
                </td>
                <td class="align-right amount">{{ $item['quantity'] }}</td>
                <td class="align-right amount">${{ number_format($item['item_price'], 2) }}</td>
            </tr>
        @endforeach
    </tbody>
</table>
<div class="clearfix">
    <div class="totals">
        <table>
            <tr class="total-money">
                <td class="text-left bold-text"><strong>Blank price</strong></td>
                <td class="amount fw-500">${{ number_format($data->blank_price, 2)  }}</td>
            </tr>
            <tr class="total-money">
                <td class="text-left bold-text"><strong>Print price</strong></td>
                <td class="amount fw-500">${{ number_format($data->print_price, 2) }}</td>
            </tr>
            @if ($data->surcharge_fee)
            <tr>
                <td class="text-left bold-text"><strong>Surcharge</strong></td>
                <td></td>
            </tr>
            @foreach ($data->surcharge_fee as $surchargeName => $surchargeFee)
            <tr>
                <td class="text-left" style="padding-left: 13px">{{ $surchargeName }}</td>
                <td class="amount">${{ number_format($surchargeFee, 2) }}</td>
            </tr>
            @endforeach
            <tr class="total-money">
                <td></td>
                <td></td>
            </tr>
            <tr class="total-money bold-text">
                <td class="text-left"><strong>Shipping ({{ ucfirst($data->shipping_method) }})</strong></td>
                <td class="amount">${{ number_format($data->shipping_calculate, 2) }}</td>
            </tr>
            @endif
            <tr class="total-refund bold-text fw-700">
                <td class="text-left"><strong>Total order</strong></td>
                <td class="amount">${{ number_format($data->total_price, 2) }}</td>
            </tr>
            
        </table>
    </div>
</div>

@endsection