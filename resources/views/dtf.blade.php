<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <style>
        html {
            padding: 0;
            margin: 0;
            font-family: 'Roboto', sans-serif;
        }
    </style>
</head>

<body style="width: 4500px;">
<div style="text-align: center; font-weight: bold; font-size: 100pt;">{{ $batch_number ?? 'Batch Number' }}</div>
<div class="file" style="position: relative; margin-top: 30px;">
    @foreach ($images as $image)
        @if(!isset($image['is_neck']) || !$image['is_neck'])
            @if(isset($image['qrCode']))
                <div
                    style="position: absolute; height: 90px; top: {{ $image['top'] - 200 }}px; left: {{ $image['left']}}px; text-align: center;">
                    <div style="display: inline-block; text-align: center; vertical-align: middle;">
                        {!! $image['qrCode'] !!}
                    </div>
                    <div style="font-size: 30px; margin-top: 10px; display: inline-block;">
                        {{ $image['label'] }}
                    </div>
                </div>
            @endif

            @if(isset($image['print_side']))
                <div
                    style="font-size: 30px; top: {{ $image['top'] - 200}}px; left: {{ $image['left'] + ($image['width'] - 520)  }}px; position: absolute;">{{ $image['print_side']}}</div>
            @endif
            <div style="top: {{ $image['top'] -100}}px; left: {{ $image['left'] }}px;
                width: {{ $image['width_platen']}}px; height: {{ $image['height_platen']}}px; border-left: solid 1px black;
                 border-top: solid 1px black; position: absolute">
                <img src="{{ $image['image'] }}"
                     style=" width: {{ $image['width'] }}px; height: {{ $image['height'] }}px;position: absolute">
            </div>
            <img src="{{ $image['image'] }}"
                 style="top: {{ $image['top'] -100}}px; left: {{ $image['left'] }}px; position: absolute;
                width: {{ $image['width'] }}px; height: {{ $image['height'] }}px;">
            <!-- Đường kẻ đen -->
            @if(isset($image['line']))
                <div
                    style="width: 100%; height: 1px; background-color: black; position: absolute; top: {{ $image['top']- 100}}px;"></div>
            @endif
        @else
            @if(isset($image['qrCode']))
                <div
                    style="position: absolute; height: 90px; top: {{ $image['top'] - 200 }}px; left: {{ $image['left']}}px; text-align: center;">
                    <div style="display: inline-block; text-align: center; vertical-align: middle;">
                        {!! $image['qrCode'] !!}
                    </div>
                    <div style="font-size: 30px; margin-top: 10px; margin-left: 20px; display: inline-block;">
                        {{ $image['label'] }}
                    </div>
                </div>
            @endif
            <div style="font-size: 30px; top: {{ $image['top'] - 200 }}px; left: {{ $image['left'] + $image['width']}}px; text-align: center; position: absolute">
                {{ $image['print_side']}}
            </div>
            <!-- Đường kẻ đen -->
            @if(isset($image['line']))
                <div
                    style="width: 100%; height: 1px; background-color: black; position: absolute; top: {{ $image['top']- 100}}px;"></div>
            @endif
            @if($image['is_printify'])
                <div style="width: 825px; height: 1050px;
            top: {{ $image['top'] - 100}}px;
            left: {{ $image['left'] }}px;
            position: absolute; border-left: solid 1px black; border-top: solid 1px black;">
                    <div style="width: 825px; height: 825px; position: relative;">
                        <img style="position: absolute;"
                             src="{{ $image['image'] }}">
                    </div>
                </div>

            @else
                <div style="top: {{ $image['top'] -100}}px; left: {{ $image['left'] }}px; position: absolute;
                width: {{ $image['width_platen'] }}px;
                 height: {{ $image['height_platen'] }}px;
                 border-left: solid 1px black; border-top: solid 1px black;">
                    <div style="width: 750px; height: 600px; position: relative;">
                        <img
                            style="position: absolute; bottom: 0; left: {{ (750 - $image['width']) / 2 }}px; display: block; margin-right: auto; margin-left: auto"
                            src="{{ $image['image'] }}">
                    </div>
                    <div
                        style="display: block; text-align: center; height: 90px; line-height: 100px; padding-top:20px; padding-bottom:20px; font-size: 50pt; color: {{ $image['text_color'] }}; border-bottom: 5px solid {{ $image['text_color'] }}">
                        {{ $image['size'] }}
                    </div>
                    <div
                        style="display: block; text-align: center; color: {{ $image['text_color'] }}; margin-top: 25px">
                        @foreach ($image['icons'] as $key => $icon)
                            @if ($key <= 5)
                                <img src="{{ $icon }}" style="width: 100px; height: 100px;">
                            @endif
                        @endforeach
                    </div>
                    <div style="text-align: center;">
                        <div style="display: block; text-align: center; color: {{ $image['text_color'] }}">
                            RN#{{ $image['rn'] }}
                        </div>
                        <div style="display: block; text-align: center; color: {{ $image['text_color'] }}">
                            {{ $image['fabric'] }}
                        </div>
                        <div
                            style="display: block; text-align: center; color: {{ $image['text_color'] }}; text-transform: uppercase;">
                            MADE IN {{ $image['country'] }}
                        </div>
                    </div>
                </div>
            @endif
        @endif

    @endforeach
</div>
</body>

</html>
