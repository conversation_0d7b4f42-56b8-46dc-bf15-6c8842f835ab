<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <style>
            html {
                padding: 0;
                margin: 0;
                font-family: 'Roboto', sans-serif;
            }
        </style>
    </head>

    <body style="width: {{ $widthFrame }}px;">
        <div class="file" style="position: relative;">
            @foreach ($images as $key => $image)
                <div style="position: absolute; width: {{ $image['width_item'] }}px; height: {{ $image['height_item'] }}px; top: {{ $image['top']}}px; left: {{ $image['left'] }}px;">
                    <div style="width: {{ $image['width_safe_zone'] }}px; height: {{ $image['height_safe_zone'] }}px; position: absolute; top: 50%; left: 50%; -webkit-transform: translate(-50%, -50%);">
                        <img src="{{ $image['image'] }}" alt="" class="image" style="position: absolute; top: 50%; left: 50%; -webkit-transform: translate(-50%, -50%); width: {{ $image['width_image'] }}px; height: {{ $image['height_image'] }}px;">
                    </div>
                </div>

                <div style="position: absolute; top: {{ $image['top_qr'] }}px; left: {{ $image['left'] + ( $image['width_item'] - 400)/2}}px; text-align: center;">
                    <div style="display: inline-block; text-align: center; vertical-align: middle;">
                        {!! $image['qrCode'] !!}
                    </div>
                    <div style="font-size: 30px; margin-top: 10px; display: inline-block;">
                        {{ $image['label'] }}
                    </div>
                </div>
            @endforeach
        </div>
    </body>

    <script>
        const count = document.getElementById("count").value;
        for (let i = 0; i < count; i++) {
            let el = document.getElementById(`border-${i}`);
            let props = el.getBoundingClientRect();
            document.getElementById(`input-${i}`).value = `${props.x}x${props.y}`;
        }
    </script>

</html>
