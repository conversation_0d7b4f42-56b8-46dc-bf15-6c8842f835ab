<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <title>Ink Supply Management</title>
    <link rel="icon" type="image" href="{{asset('logo.png')}}">
    <link href="//maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
    <style>
        body {
            font-family: "Lato", sans-serif;
        }

        .sidenav {
            display: flex;
            height: 100%;
            background-color: #000;
        }


        .main {
            padding: 0px 10px;
        }

        @media screen and (max-height: 450px) {
            .sidenav {
                padding-top: 15px;
            }
        }

        @media screen and (max-width: 450px) {
            .login-form {
                margin-top: 10%;
            }
        }

        @media screen and (min-width: 768px) {
            .main {
                margin-left: 40%;
            }

            .sidenav {
                width: 40%;
                position: fixed;
                z-index: 1;
                top: 0;
                left: 0;
            }

            .login-form {
                margin-top: 80%;
            }
        }

        .login-main-text {
            text-align: center;
            margin: auto;
            padding: 60px;
            color: #fff;
            font-weight: 400;
        }

        .login-main-text .title {
            font-size: 28px;
        }

        .login-main-text .description {
            margin-top: 40px;
        }

        .btn-black {
            background-color: #000 !important;
            color: #fff;
        }
    </style>
</head>

<body>
<div class="sidenav">
    <div class="login-main-text">
        <p class="title">SwiftPOD<br/>Login Page</p>
        <p class="description">Ink Supply Management</p>
    </div>
</div>
<div class="main">
    <div class="col-md-6 col-sm-12">
        <div class="login-form">
            <form method="POST" action="{{ route('supply.login') }}">
                @csrf
                <div class="form-group">
                    <label>Username</label>
                    <input type="text" class="form-control" name="email">
                    @error('email')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <div class="form-group">
                    <label>Password</label>
                    <input type="password" class="form-control" name="password">
                    @error('password')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <button type="submit" class="btn btn-black">Login</button>
            </form>
        </div>
    </div>
</div>

<script src="//maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
<script src="//code.jquery.com/jquery-1.11.1.min.js"></script>
</body>
</html>
