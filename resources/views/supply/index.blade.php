@extends('supply._partials.master')

@section('css-head')
    <style>
        .apexcharts-xaxis-label tspan:first-child {
            font-style: italic;
        }

        .apexcharts-xaxis-label tspan:not(:first-child) {
            font-weight: bold;
        }
    </style>
@endsection

@section('content')
    <div class="d-flex justify-content-between mb-5">
        <h2 class="px-3">Ink Consumption</h2>
    </div>

    <form class="form-inline px-3 mb-5">
        <div class="form-group mb-2">
            <input type="text" name="year" readonly class="form-control" id="datetime"
                   placeholder="Report year"
                   value="{{request()->query('year')}}">
        </div>
        <div class="form-group mx-sm-3 mb-2">
            <select class="form-control" name="category" id="category">
                @foreach($categories as $key => $name)
                    <option
                        value="{{$key}}" {{request()->query('category') == $key ? 'selected' : ''}}>
                        {{$name}}
                    </option>
                @endforeach
            </select>
        </div>
        <a href="{{route('supply.report')}}" class="btn btn-outline-danger mb-2">Reset</a>
    </form>

    <div id="chart">
    </div>
@endsection

@section('js-bottom')
    <script>
        let idleTimeout;

        function logout() {
            alert('You have been logged out due to inactivity.');
            window.location.href = "{{ route('supply.logout') }}";
        }

        function resetTimeout() {
            clearTimeout(idleTimeout);
            idleTimeout = setTimeout(logout, 20 * 60 * 1000);
        }

        ['mousemove', 'keydown', 'scroll', 'click'].forEach(event => {
            window.addEventListener(event, resetTimeout);
        });

        resetTimeout();
        $("#datetime").datepicker({
            format: "yyyy",
            viewMode: "years",
            minViewMode: "years"
        });

        $("#datetime,#category").on('change', function () {
            $("form").first().trigger("submit");
        })

        const INK_WHITE = 2;
        let options = {
            series: [],
            chart: {
                toolbar: {
                    show: false
                },
                type: 'bar',
                height: $(document).height() - 300
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            yaxis: {
                title: {
                    text: ''
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                custom: function ({series, seriesIndex, dataPointIndex, w}) {
                    const {
                        warehouse,
                        month,
                        unitName
                    } = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
                    const val = series[seriesIndex][dataPointIndex];

                    return `
                <div style="background-color: #DFE6ED; padding: 10px 20px; font-size: 11px;">
                    <div style="font-weight: bold; text-align: center; font-size: 14px;">${$('#category option:selected').text()}</div>
                    <div style="text-align: center;">${month} - ${warehouse}</div>
                    <hr style="margin: 10px 0; padding: 0; border-color: #000;" />
                    <div style="font-weight: bold; margin-left: 10px;">${val} ${unitName} Consumed</div>
                </div>
                `;
                }
            }
        };

        let year = $('#datetime') ? $('#datetime') : moment().format('YYYY');
        const data = JSON.parse('{!! $dataReport !!}')
        options.yaxis.title.text = data.unit;
        let isShowYear = moment().format('YYYY') == {{request()->query('year') ?? now()->format('Y')}} && moment().format('MM') != 12;

        if (data?.data_report.length > 0) {
            data?.data_report.forEach(item => {
                const {month, warehouses} = item;
                let _month = moment(month, 'YYYY-MM');
                let total = warehouses.reduce((accumulator, w) => {
                        let qty = '{{request()->query('category')}}' == INK_WHITE ? w.qty_ink_white : w.qty_ink_color;
                        return accumulator + qty;
                    }, 0
                );
                total = Math.round(total * 1000) / 1000;

                warehouses.map(w => {
                    let index = options.series.findIndex(x => x.name == w.name);
                    let _data = {
                        y: '{{request()->query('category')}}' == INK_WHITE ? w.qty_ink_white : w.qty_ink_color,
                        x: isShowYear ? [`${total} ${data.unit}`, _month.format('MMM'), _month.format('YYYY')] : [`${total} ${data.unit}`, _month.format('MMM')],
                        warehouse: w.name,
                        month: _month.format('MMMM YYYY'),
                        unitName: data.unit,
                    }

                    if (index == -1) {
                        options.series.push({
                            name: w.name,
                            data: [_data],
                            color: w.color,
                        });
                    } else {
                        options.series[index].data.push(_data);
                    }
                })
            });
        }

        let chart = new ApexCharts(document.querySelector("#chart"), options);
        chart.render();
    </script>
@endsection
