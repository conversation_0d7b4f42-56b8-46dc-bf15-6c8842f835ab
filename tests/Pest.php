<?php

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/

use App\Models\User;
use App\Models\Warehouse;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(Tests\TestCase::class)->in('Feature');

function createAccessToken() {
    $warehouse = Warehouse::factory()->create();
    $user = User::factory()->create();
    return [
        'accessToken' => JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user),
        'warehouse' => $warehouse,
        'user' => $user,
    ];
}
