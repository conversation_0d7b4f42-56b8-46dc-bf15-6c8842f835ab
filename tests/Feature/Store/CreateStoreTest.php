<?php

use App\Models\Setting;
use App\Models\Store;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;

    $this->endpoint = '/api/store/store-info';
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->role = Role::create(['name' => 'Sales Team']);

    $this->user = User::factory()->create([
        'username' => 'test',
        'email' => '<EMAIL>'
    ]);

    $this->userRoleSalesTeam = User::factory()->create([
        'username' => 'sale',
        'email' => '<EMAIL>',
    ]);
    $this->userRoleSalesTeam->assignRole($this->role);

    $this->params = [
        'name' => 'test',
        'company' => 'company',
        'country' => 'US',
        'street1' => 'street1',
        'street2' => 'street2',
        'city' => 'city',
        'state' => 'state',
        'zip' => 10000,
        'phone' => *********,
        'email' => 'email',
        'contact_name' => 'contact_name',
        'contact_email' => 'contact_email',
        'contact_phone' => 'contact_phone',
        'account_id' => 12,
        'code' => 'code',
        'is_active' => true,
        'template_neck' => 1,
        'limit_order' => 0,
        'is_on_hold' => 1,
        'client_id' => 1,
        'sale_rep' => $this->userRoleSalesTeam->id,
        'payment_terms' => 1,
        'billing_email' => null,
    ];

    Setting::factory()->create([
        'name' => 'alert_store_change_status_create_label',
        'label' => 'alert_store_change_status_create_label',
        'value' => ''
    ]);
});

function createStoreSuccess($self)
{
    $store = Store::where('name', $self->params['name'])->count();
    $self->assertEquals(1, $store);
}

// miss name param
test('create store failed - miss name param', function () {
    unset($this->params['name']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
});

// name already exist
test('create store failed - name already exist', function () {
    Store::factory(['name' => $this->params['name']])->create();
    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(422);
});

// error code param
test('create store failed - error code param', function () {
    $this->params['code'] = '123456789123';

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
});

// miss sale rep param
test('create store failed - miss sale rep param', function () {
    unset($this->params['sale_rep']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
});

// error code param
test('create store failed - error sale rep param', function () {
    $this->params['sale_rep'] = $this->user->id;

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
});

// miss code param
test('create store failed - miss code param', function () {
    unset($this->params['code']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss account_id param
test('create store failed - miss account_id param', function () {
    unset($this->params['account_id']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss company param
test('create store failed - miss company param', function () {
    unset($this->params['company']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss phone param
test('create store failed - miss phone param', function () {
    unset($this->params['phone']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss street1 param
test('create store failed - miss street1 param', function () {
    unset($this->params['street1']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss street2 param
test('create store failed - miss street2 param', function () {
    unset($this->params['street2']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss city param
test('create store failed - miss city param', function () {
    unset($this->params['city']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss state param
test('create store failed - miss state param', function () {
    unset($this->params['state']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss zip param
test('create store failed - miss zip param', function () {
    unset($this->params['zip']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss country param
test('create store failed - miss country param', function () {
    unset($this->params['country']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});

// miss template_neck param
test('create store failed - miss template_neck param', function () {
    unset($this->params['template_neck']);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    $store = Store::where('name', $this->params['name'])
        ->where('template_neck', 0)
        ->first();
    $this->assertNotEmpty($store);
});
//miss email param with store pay now
test('create store failed - miss email param with store pay now', function () {
    $this->params['payment_terms'] = 0;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
});
//invalid email param with store pay now
test('create store failed - invalid email param with store pay now', function () {
    $this->params['payment_terms'] = 0;
    $this->params['billing_email'] = 'invalid_email';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
});
//invalid email format param with store pay now
test('create store failed - invalid email format param with store pay now', function () {
    $this->params['payment_terms'] = 0;
    $this->params['billing_email'] = '<EMAIL>/<EMAIL>';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
});

// create store success
test('create store success', function () {
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});
//create pay now store success
test('create pay now store success', function () {
    $this->params['payment_terms'] = 1;
    $this->params['billing_email'] = '<EMAIL>';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);

    createStoreSuccess($this);
});
