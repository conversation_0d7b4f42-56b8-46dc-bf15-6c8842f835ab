<?php

use App\Models\Employee;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Store;
use App\Models\Warehouse;
use Carbon\Carbon;
use Faker\Factory as faker;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 1011]);
    $this->store = Store::factory()->create();
    setTimezone();
    $this->today = $today = now()->setTimezone(getTimezone());
    $this->productStyles = ProductStyle::factory()->count(6)->state(new Sequence(
        ['sku' => 'UNGH', 'type' => 'Fleece'],
        ['sku' => 'UNPT', 'type' => 'Tee'],
        ['sku' => 'YOGH', 'type' => 'Fleece'],
        ['sku' => 'UNGT', 'type' => 'Tee'],
        ['sku' => 'ORMT', 'type' => 'Ornament'],
        ['sku' => 'MUGS', 'type' => 'Mugs'],
    ))->create();
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'in_production',
        'shipping_method' => 'standard',
        'order_number' => '071722-SJ-M-000368',
        'external_number' => '071722-SJ-M',
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'is_shipment_create_error' => 0,
        'order_quantity' => 1,
        'barcode_printed_status' => 1,
        'shipment_id' => null,
        'order_date' => Carbon::now(),
        'order_pulled_status' => 1,
        'order_staged_status' => 1,
        'order_printed_status' => 1,
    ])->has(SaleOrderItem::factory()->count(2)->state(new Sequence(
        [
            'product_style_sku' => ProductStyle::query()->inRandomOrder()->first()->sku,
            'quantity' => 1
        ],
        [
            'product_style_sku' => ProductStyle::query()->inRandomOrder()->first()->sku,
            'quantity' => 1
        ],
    ))->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'pulled_at' => $today,
        'printed_at' => $today,
        'qc_at' => $today,
        'shipped_at' => $today,
        'folded_at' => $today,
        'is_deleted' => 0
    ])->count(faker::create()->randomDigitNotNull), 'barcodes'), 'items')
        ->create();

    $this->params = [
        'warehouse' => $this->warehouse->id,
        'date' => $today->toDateString()
    ];
    $this->endpoint = 'api/production-report?' . http_build_query($this->params);
});

test('production report : validate required input', function () {
    foreach ($this->params as $key => $field) {
        $input = $this->params;
        unset($input[$key]);
        $this->endpoint = 'api/production-report?' . http_build_query($input);
        $response = $this->get($this->endpoint);
        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey($key);
    }
});

test('production report : warehouse not data report', function () {
    $warehouse = Warehouse::factory()->create();
    $this->params['warehouse'] = $warehouse->id;
    $this->endpoint = 'api/production-report?' . http_build_query($this->params);
    $start = $this->today->clone()->format('Y-m-d');
    $end = $this->today->clone()->format('Y-m-d');
    $this->artisan("calculate-production-report --start=$start --end=$end");
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);

    foreach ($dataResponse as $item) {
        expect($item['quantity_pulled'])->toEqual(0)
            ->and($item['quantity_printed'])->toEqual(0)
            ->and($item['quantity_qc'])->toEqual(0)
            ->and($item['quantity_shipped'])->toEqual(0)
            ->and($item['quantity_folded'])->toEqual(0);
    }
});

test('production report success', function () {
    $apparelStyle = ProductStyle::query()->whereIn('type', ['Fleece', 'Tee'])->pluck('sku', 'id');
    $accessoriesStyle = ProductStyle::query()->whereIn('type', ['Ornament'])->pluck('sku', 'id');
    $mugStyle = ProductStyle::query()->whereIn('type', ['Mugs'])->pluck('sku', 'id');
    $start = $this->today->clone()->format('Y-m-d');
    $end = $this->today->clone()->format('Y-m-d');
    $this->artisan("calculate-production-report --start=$start --end=$end");
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);

    foreach ($dataResponse as $item) {
        switch ($item['product_type']) {
            case 'accessories' :
                $arrStyles = $accessoriesStyle;
                break;
            case 'apparel' :
                $arrStyles = $apparelStyle;
                break;
            default:
                $arrStyles = $mugStyle;
                break;
        }
        expect($item['quantity_pulled'])->toEqual(SaleOrderItemBarcode::whereHas('orderItem', function ($query) use ($arrStyles) {
            $query->whereIn('product_style_sku', $arrStyles);
        })->whereNotNull('pulled_at')->count())
            ->and($item['quantity_printed'])->toEqual(SaleOrderItemBarcode::whereHas('orderItem', function ($query) use ($arrStyles) {
                $query->whereIn('product_style_sku', $arrStyles);
            })->whereNotNull('printed_at')->count())
            ->and($item['quantity_qc'])->toEqual(SaleOrderItemBarcode::whereHas('orderItem', function ($query) use ($arrStyles) {
                $query->whereIn('product_style_sku', $arrStyles);
            })->whereNotNull('qc_at')->count())
            ->and($item['quantity_shipped'])->toEqual(SaleOrderItemBarcode::whereHas('orderItem', function ($query) use ($arrStyles) {
                $query->whereIn('product_style_sku', $arrStyles);
            })->whereNotNull('shipped_at')->count())
            ->and($item['quantity_folded'])->toEqual(SaleOrderItemBarcode::whereHas('orderItem', function ($query) use ($arrStyles) {
                $query->whereIn('product_style_sku', $arrStyles);
            })->whereNotNull('folded_at')->count());
    }
});
