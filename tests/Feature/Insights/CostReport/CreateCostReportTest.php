<?php

use App\Models\CostReport;
use App\Models\CostReportHistory;
use App\Models\Employee;
use App\Models\ProductType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user,] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;
    $employee = Employee::factory()->create();
    ProductType::factory()->count(2)->sequence([
        'name' => 'Fleece'
    ], [
        'name' => 'Tee'
    ])->create();
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->body = [
        "product_type" => "Fleece",
        "bag_cost" => 4.234,
        "white_ink_cost" => 4.567,
        "color_ink_cost" => 14.00,
        "utility_cost" => 13.00,
        "pretreat_cost" => 15.24,
        "affected_at" => "2021-04",
        "employee_id" => $employee->id
    ];
    $this->endpoint = '/api/cost-report';
});


test('Create cost report validate failed', function () {
    $body = [
        'product_type' => 'test',
        'bag_cost' => 'test',
        'white_ink_cost' => 'test',
        'color_ink_cost' => 'test',
        'utility_cost' => 'test',
        'pretreat_cost' => 'test',
        'affected_at' => 'test',
        'employee_id' => 'test'
    ];
    $response = $this->post($this->endpoint, $body);

    $responseParse = json_decode($response->getContent(), true);

    $response->assertStatus(422);
    expect($responseParse['errors'])->toHaveKeys([
        'employee_id',
        'product_type',
        'white_ink_cost',
        'color_ink_cost',
        'pretreat_cost',
        'bag_cost',
        'utility_cost',
        'affected_at',
    ]);
    $this->assertDatabaseCount('cost_report', 0);
    $this->assertDatabaseCount('cost_report_history', 0);
});

test('Create Cost report success', function () {
    $productType = ProductType::where('name', $this->body['product_type'])->first();
    $response = $this->post($this->endpoint, $this->body);

    $responseParse = json_decode($response->getContent())->data;

    $response->assertStatus(200);
    $this->assertDatabaseCount('cost_report', 1);
    $this->assertDatabaseCount('cost_report_history', 5);
    $this->assertDatabaseHas('cost_report', [
        'id' => $responseParse->id,
        'product_type_id' => $productType->id,
        'bag_cost' => round($this->body['bag_cost'], 2),
        'white_ink_cost' => round($this->body['white_ink_cost'], 2),
        'color_ink_cost' => round($this->body['color_ink_cost'], 2),
        'utility_cost' => round($this->body['utility_cost'], 2),
        'pretreat_cost' => round($this->body['pretreat_cost'], 2),
        'employee_id' => $this->body['employee_id'],
        'affected_year_at' => Carbon::parse($this->body['affected_at'])->format('Y'),
        'affected_month_at' => Carbon::parse($this->body['affected_at'])->format('m')
    ]);
    $this->assertDatabaseHas('cost_report_history', [
        'type' => 'bag_cost',
        'value' => round($this->body['bag_cost'], 2),
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productType->id
    ]);
    $this->assertDatabaseHas('cost_report_history', [
        'type' => 'white_ink_cost',
        'value' => round($this->body['white_ink_cost'], 2),
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productType->id
    ]);
    $this->assertDatabaseHas('cost_report_history', [
        'type' => 'color_ink_cost',
        'value' => round($this->body['color_ink_cost'], 2),
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productType->id
    ]);
    $this->assertDatabaseHas('cost_report_history', [
        'type' => 'utility_cost',
        'value' => round($this->body['utility_cost'], 2),
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productType->id
    ]);
    $this->assertDatabaseHas('cost_report_history', [
        'type' => 'pretreat_cost',
        'value' => round($this->body['pretreat_cost'], 2),
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productType->id
    ]);
});

test('Update cost report', function () {
    CostReport::factory([
        'product_type_id' => ProductType::where('name', $this->body['product_type'])->first()->id,
        'warehouse_id' => $this->warehouse->id,
        'employee_id' => $this->body['employee_id'],
        'bag_cost' => $this->body['bag_cost'],
        'white_ink_cost' => $this->body['white_ink_cost'],
        'color_ink_cost' => $this->body['color_ink_cost'],
        'utility_cost' => $this->body['utility_cost'],
        'pretreat_cost' => $this->body['pretreat_cost'],
        'affected_year_at' => Carbon::parse($this->body['affected_at'])->format('Y'),
        'affected_month_at' => Carbon::parse($this->body['affected_at'])->format('m'),
    ])->create();

    $productTypeId = ProductType::where('name', $this->body['product_type'])->first()->id;
    $affectedYear = Carbon::parse($this->body['affected_at'])->year;
    $affectedMonth = Carbon::parse($this->body['affected_at'])->month;
    CostReportHistory::factory()->count(5)->sequence([
        'type' => 'bag_cost',
        'value' => $this->body['bag_cost'],
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productTypeId,
        'employee_id' => $this->body['employee_id'],
        'affected_year_at' => $affectedYear,
        'affected_month_at' => $affectedMonth,
    ], [
        'type' => 'white_ink_cost',
        'value' => $this->body['white_ink_cost'],
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productTypeId,
        'employee_id' => $this->body['employee_id'],
        'affected_year_at' => $affectedYear,
        'affected_month_at' => $affectedMonth,
    ], [
        'type' => 'color_ink_cost',
        'value' => $this->body['color_ink_cost'],
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productTypeId,
        'employee_id' => $this->body['employee_id'],
        'affected_year_at' => $affectedYear,
        'affected_month_at' => $affectedMonth,
    ], [
        'type' => 'utility_cost',
        'value' => $this->body['utility_cost'],
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productTypeId,
        'employee_id' => $this->body['employee_id'],
        'affected_year_at' => $affectedYear,
        'affected_month_at' => $affectedMonth,
    ], [
        'type' => 'pretreat_cost',
        'value' => $this->body['pretreat_cost'],
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productTypeId,
        'employee_id' => $this->body['employee_id'],
        'affected_year_at' => $affectedYear,
        'affected_month_at' => $affectedMonth,
    ])->create();
    unset($this->body['bag_cost'], $this->body['pretreat_cost'], $this->body['utility_cost'], $this->body['color_ink_cost']);
    $this->body['white_ink_cost'] = $this->body['white_ink_cost'] + 1;
    $response = $this->post($this->endpoint, $this->body);

    $response->assertStatus(200);
    $this->assertDatabaseCount('cost_report', 1);
    $this->assertDatabaseCount('cost_report_history', 6);
    $this->assertDatabaseHas('cost_report_history', [
        'type' => 'white_ink_cost',
        'value' => round($this->body['white_ink_cost'], 2),
        'warehouse_id' => $this->warehouse->id,
        'product_type_id' => $productTypeId
    ]);
});
