<?php
use App\Models\Location;
use App\Models\Machine;
use App\Models\PartNumber;

use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse,] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/machine';
    $this->location = \App\Models\LocationMachine::factory()->createMany([
        ['warehouse_id' => $this->warehouse->id, 'name' => 'Test 1'],
        ['warehouse_id' => 2, 'name' => 'Test 2'],
    ]);
    $this->params = [
        'name' => 'Machine 1',
        'series' => 'Series 1',
        'manufacturer' => 'SAKURA',
        'bought_in' => '2023-10-10',
        'model' => '1533',
        'location_id' => $this->location[0]->id,
    ];
});


test('Create fail - Missing name, series, location_id, model, bought_in', function () {
    unset($this->params['name'], $this->params['series'], $this->params['location_id'], $this->params['model'], $this->params['bought_in']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'series' => [
                'The serial number field is required.'
            ],
            'name' => [
                'The name field is required.'
            ],
            'location_id' => [
                'The location field is required.'
            ],
            'bought_in' => [
                'The purchase date field is required.'
            ],
            'model' => [
                'The model field is required.'
            ]
        ]
    ]);
});

test('Create fail - location id not exists in location table', function () {
    $this->params['location_id'] = $this->location[1]->id + 1;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'location_id' => [
                'The selected location is invalid.'
            ]
        ]
    ]);
});

test('Create fail - series, name already exist.', function () {
    Machine::factory()->create([
        'series' => $this->params['series'],
        'name' => $this->params['name'],
        'location_id' => $this->location[0]->id,
    ]);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'name' => [
                'The name has already been taken.'
            ],
            'series' => [
                'The serial number has already been taken.'
            ],
        ]
    ]);
});

test('Create success - series, name already exist in another warehouse.', function () {
    Machine::factory()->create([
        'name' => 'Machine 1',
        'series' => 'Series 2',
        'manufacturer' => 'SAKURA',
        'bought_in' => '2023-10-10',
        'model' => '1533',
        'location_id' => $this->location[1]->id,
    ]);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('machine', $this->params);
});

test('Create success.', function () {
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('machine', $this->params);
});

