<?php

use App\Events\UpdateStatusPrintNotification;
use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedTime;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductQuantity;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->style = ProductStyle::factory([
        'name' => 'style_name',
        'sku' => '1533',
        'type' => 'tee'
    ])->create();
    $this->color = ProductColor::factory([
        'name' => 'color_name',
        'sku' => '1B'
    ])->create();
    $this->employee = Employee::factory(['department' => Department::DEPARTMENT_PULLING])->create();
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'is_test' => 0,
        'is_manual' => 0,
        'is_fba_order' => 0,
        'is_xqc' => 0,
        'is_eps' => 0,
        'warehouse_id' => 1
    ])->has(SaleOrderItem::factory([
        'product_style_sku' => $this->style->sku,
        'product_color_sku' => $this->color->sku,
        'ink_color_status' => 1
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => 1,
        'is_deleted' => 0,
        'barcode_printed_id' => 0,
        'label_root_id' => null,
        'employee_reroute_id' => null,
        'print_method' => 'DTG',
        'label_id' => 'xxx'
    ]), 'barcodes'), 'items')
    ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $this->barcodePrinted = BarcodePrinted::factory([
        'style_sku' => $this->style->sku,
        'color_sku' => $this->color->sku
    ])->create();
    $this->withHeaders([
        'Accept' => 'application/json',
    ]);
    $this->endpoint = "/api/mobile/barcode/{$this->barcodePrinted->id}/status-print";
});

test('status print validation', function () {
    $params = [
        'required' => [],
        'in' => [
            'status_print' => 'xxx'
        ]
    ];

    // required
    $response = $this->post($this->endpoint, $params['required']);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'status_print' => [
                'The status print field is required.'
            ]
        ]
    ]);

    // in
    $response = $this->post($this->endpoint, $params['in']);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'status_print' => [
                'The selected status print is invalid.'
            ]
        ]
    ]);
});

test('success', function () {
    Event::fake();

    $param = [
        'status_print' => BarcodePrinted::STATUS_PRINTED
    ];

    $this->post($this->endpoint, $param);

    Event::assertDispatched(UpdateStatusPrintNotification::class);

    $this->assertEquals(BarcodePrinted::find($this->barcodePrinted->id)->status_print, BarcodePrinted::STATUS_PRINTED);
});
