<?php

use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedTime;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductQuantity;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->style = ProductStyle::factory([
        'name' => 'style_name',
        'sku' => '1533',
        'type' => 'tee'
    ])->create();
    $this->color = ProductColor::factory([
        'name' => 'color_name',
        'sku' => '1B'
    ])->create();
    $this->employee = Employee::factory(['department' => Department::DEPARTMENT_PULLING])->create();
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'is_test' => 0,
        'is_manual' => 0,
        'is_fba_order' => 0,
        'is_xqc' => 0,
        'is_eps' => 0,
        'warehouse_id' => 1
    ])->has(SaleOrderItem::factory([
        'product_style_sku' => $this->style->sku,
        'product_color_sku' => $this->color->sku,
        'ink_color_status' => 1
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => 1,
        'is_deleted' => 0,
        'barcode_printed_id' => 0,
        'label_root_id' => null,
        'employee_reroute_id' => null,
        'print_method' => 'DTG',
        'label_id' => 'xxx'
    ]), 'barcodes'), 'items')
    ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $this->withHeaders([
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/mobile/barcode/reprint';
});

test('limit validation', function () {
    $param = [
        'limit' => 'xxx',
        'warehouse_id' => 1
    ];

    $response = $this->get("{$this->endpoint}?limit={$param['limit']}&warehouse_id={$param['warehouse_id']}");

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'limit' => [
            'The limit must be a number.'
        ]
    ]);
});

test('page validation', function () {
    $param = [
        'page' => 'xxx',
        'warehouse_id' => 1
    ];

    $response = $this->get("{$this->endpoint}?page={$param['page']}&warehouse_id={$param['warehouse_id']}");

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'page' => [
            'The page must be a number.'
        ]
    ]);
});

test('warehouse id validation', function () {
    $params = [
        'required' => [
            'limit' => 25,
            'page' => 1,
        ],
        'numeric' => [
            'limit' => 25,
            'page' => 1,
            'warehouse_id' => 'xxx'
        ]
    ];

    $response = $this->get("{$this->endpoint}?page={$params['required']['page']}&limit={$params['required']['limit']}");

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'warehouse_id' => [
            'The warehouse id field is required.'
        ]
    ]);

    $response = $this->get("{$this->endpoint}?page={$params['numeric']['page']}&limit={$params['numeric']['limit']}&warehouse_id={$params['numeric']['warehouse_id']}");

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'warehouse_id' => [
            'The warehouse id must be a number.'
        ]
    ]);
});

test('success', function () {
    BarcodePrinted::factory([
        'style_sku' => $this->style->sku,
        'color_sku' => $this->color->sku,
        'convert_status' => BarcodePrinted::ACTIVE,
        'print_status' => BarcodePrinted::INACTIVE,
        'warehouse_id' => 1,
        'is_error_print' => BarcodePrinted::ACTIVE,
        'employee_id' => $this->employee->id
    ])->create();

    $response = $this->get("$this->endpoint?warehouse_id=1");

    $this->assertEquals(count(json_decode($response->getContent(), true)['data']['data']), 1);
});
