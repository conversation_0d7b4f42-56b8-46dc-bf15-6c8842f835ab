<?php

use App\Events\CountPendingNotification;
use App\Events\UpdateStatusPrintNotification;
use App\Jobs\CheckWipPendingScanBatchJob;
use App\Models\BarcodePrinted;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Printer;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductQuantity;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Services\FirebaseService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->style = ProductStyle::factory([
        'name' => 'style_name',
        'sku' => '1533',
        'type' => 'tee'
    ])->create();
    $this->color = ProductColor::factory([
        'name' => 'color_name',
        'sku' => '1B'
    ])->create();
    $this->product = Product::factory([
        'parent_id' => 0,
        'style' => $this->style->name,
        'color' => $this->color->name,
        'sku' => 'PRO1'
    ])->create();

    $this->productChild = Product::factory([
        'parent_id' => $this->product->id,
        'style' => $this->style->name,
        'color' => $this->color->name,
        'sku' => 'XXX'
    ])->create();
    $this->employee = Employee::factory(['department' => Department::DEPARTMENT_PULLING])->create();
    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'is_test' => 0,
        'is_manual' => 0,
        'is_fba_order' => 0,
        'is_xqc' => 0,
        'is_eps' => 0,
        'warehouse_id' => 1
    ])->has(SaleOrderItem::factory([
        'product_style_sku' => $this->style->sku,
        'product_color_sku' => $this->color->sku,
        'ink_color_status' => 1
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => 1,
        'is_deleted' => 0,
        'barcode_printed_id' => 0,
        'label_root_id' => null,
        'employee_reroute_id' => null,
        'print_method' => 'DTG',
        'label_id' => 'xxx'
    ]), 'barcodes'), 'items')
    ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });
    $this->withHeaders([
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/mobile/barcode/print';
});

test('quantity validation', function () {
    $params = [
        'required' => [
            'style' => 'style',
            'color' => 'color',
            'employee_code' => 9999,
        ],
        'numeric' => [
            'style' => 'style',
            'color' => 'color',
            'employee_code' => 9999,
            'quantity' => 'aa'
        ]
    ];

    // required
    $response = $this->post($this->endpoint, $params['required']);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'quantity' => [
                'The quantity field is required.'
            ]
        ]
    ]);

    // numeric
    $response = $this->post($this->endpoint, $params['numeric']);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'quantity' => [
                'The quantity must be a number.'
            ]
        ]
    ]);
});

test('style validation', function () {
    $param = [
        'color' => 'color',
        'employee_code' => 9999,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'style' => [
                'The style field is required.'
            ]
        ]
    ]);
});

test('color validation', function () {
    $param = [
        'style' => 'style',
        'employee_code' => 9999,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'color' => [
                'The color field is required.'
            ]
        ]
    ]);
});

test('employee code validation', function () {
    $params = [
        'require' => [
            'style' => 'style',
            'color' => 'color',
            'quantity' => 1
        ],
        'numeric' => [
            'style' => 'style',
            'color' => 'color',
            'employee_code' => 'xxx',
            'quantity' => 1
        ]
    ];

    $response = $this->post($this->endpoint, $params['require']);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_code' => [
                'The employee code field is required.'
            ]
        ]
    ]);

    $response = $this->post($this->endpoint, $params['numeric']);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_code' => [
                'The employee code must be a number.'
            ]
        ]
    ]);
});

test('style not found', function () {
    $param = [
        'style' => 'style',
        'color' => $this->color->name,
        'employee_code' => $this->employee->code,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(404);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        "code" => 404,
        "success" => false,
        "message" => "Style not found"
    ]);
});

test('product not found', function () {
    Product::where('id', $this->product->id)->delete();
    $param = [
        'style' => $this->style->name,
        'color' => $this->color->name,
        'employee_code' => $this->employee->code,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(404);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        "code" => 404,
        "success" => false,
        "message" => "Product not found"
    ]);
});

test('color not found', function () {
    $param = [
        'style' => $this->style->name,
        'employee_code' => $this->employee->code,
        'color' => 'color',
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(404);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        "code" => 404,
        "success" => false,
        "message" => "Color not found"
    ]);
});

test('employee not found', function () {
    $param = [
        'style' => $this->style->name,
        'employee_code' => 9999,
        'color' => $this->color->name,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(404);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        "code" => 404,
        "success" => false,
        "message" => "Employee not found"
    ]);
});

test('printer is not config', function () {
    $param = [
        'style' => $this->style->name,
        'employee_code' => $this->employee->code,
        'color' => $this->color->name,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(400);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        "code" => 400,
        "success" => false,
        "message" => "Unable to print because the style and color have not been configured for any printer. \nKhông thể in do chưa có máy in nào được cài đặt style và color này."
    ]);
});

test('printer not turn on', function () {
    $printer = Printer::factory([
        'name' => 'DURIAN TEST',
        'warehouse_id' => 1,
        'device_id' => 'DS423GD',
    ])->create();

    $printer->styles()->attach($this->style->sku, ['color_sku' => $this->color->sku]);

    $param = [
        'style' => $this->style->name,
        'employee_code' => $this->employee->code,
        'color' => $this->color->name,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(400);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        "code" => 400,
        "success" => false,
        "message" => "Unable to print because the Durian app is not turned on. \nKhông thể in do ứng dụng Durian chưa được bật."
    ]);
});

test('check another wip of current employee is pending', function () {
    $printer = Printer::factory([
        'name' => 'DURIAN TEST',
        'warehouse_id' => 1,
        'device_id' => 'DS423GD',
        'last_printer_active_at' => Carbon::now()
    ])->create();

    $printer->styles()->attach($this->style->sku, ['color_sku' => $this->color->sku]);

    $param = [
        'style' => $this->style->name,
        'employee_code' => $this->employee->code,
        'color' => $this->color->name,
        'quantity' => 1
    ];

    $style = ProductStyle::factory([
        'name' => 'style2',
        'sku' => '3001',
        'type' => 'tee'
    ])->create();
    $color = ProductColor::factory([
        'name' => 'color2',
        'sku' => '1X'
    ])->create();

    BarcodePrinted::factory([
        'style_sku' => $style->sku,
        'color_sku' => $color->sku,
        'convert_status' => BarcodePrinted::ACTIVE,
        'print_status' => BarcodePrinted::INACTIVE,
        'warehouse_id' => 1,
        'is_error_print' => BarcodePrinted::INACTIVE,
        'employee_id' => $this->employee->id
    ])->create();

    Product::factory([
        'parent_id' => 0,
        'style' => $style->name,
        'color' => $color->name
    ])->create();

    $param = [
        'style' => $this->style->name,
        'color' => $this->color->name,
        'employee_code' => $this->employee->code,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $this->assertEquals(json_decode($response->getContent(), true)['data']['is_wip_pending'], true);
});

test('check wip converted and not print yet', function () {
    $printer = Printer::factory([
        'name' => 'DURIAN TEST',
        'warehouse_id' => 1,
        'device_id' => 'DS423GD',
        'last_printer_active_at' => Carbon::now()
    ])->create();

    $printer->styles()->attach($this->style->sku, ['color_sku' => $this->color->sku]);

    BarcodePrinted::factory([
        'style_sku' => $this->style->sku,
        'color_sku' => $this->color->sku,
        'convert_status' => BarcodePrinted::ACTIVE,
        'print_status' => BarcodePrinted::INACTIVE,
        'warehouse_id' => 1,
        'is_error_print' => BarcodePrinted::INACTIVE,
        'employee_id' => $this->employee->id
    ])->create();

    Product::factory([
        'parent_id' => 0,
        'style' => $this->style->name,
        'color' => $this->color->name
    ])->create();

    $param = [
        'style' => $this->style->name,
        'color' => $this->color->name,
        'employee_code' => $this->employee->code,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $this->assertEquals(json_decode($response->getContent(), true)['data']['is_wip_pending'], true);
});

test('check wip of other employee', function () {
    $printer = Printer::factory([
        'name' => 'DURIAN TEST',
        'warehouse_id' => 1,
        'device_id' => 'DS423GD',
        'last_printer_active_at' => Carbon::now()
    ])->create();

    $printer->styles()->attach($this->style->sku, ['color_sku' => $this->color->sku]);

    $employee = Employee::factory(['name' => 'xxx'])->create();

    BarcodePrinted::factory([
        'style_sku' => $this->style->sku,
        'color_sku' => $this->color->sku,
        'convert_status' => BarcodePrinted::ACTIVE,
        'print_status' => BarcodePrinted::INACTIVE,
        'warehouse_id' => 1,
        'is_error_print' => BarcodePrinted::INACTIVE,
        'employee_id' => $employee->id
    ])->create();

    Product::factory([
        'parent_id' => 0,
        'style' => $this->style->name,
        'color' => $this->color->name
    ])->create();

    $param = [
        'style' => $this->style->name,
        'color' => $this->color->name,
        'employee_code' => $this->employee->code,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $this->assertEquals(json_decode($response->getContent(), true)['data']['is_another_employee'], true);
});

test('check wip convert error or print error', function () {
    $printer = Printer::factory([
        'name' => 'DURIAN TEST',
        'warehouse_id' => 1,
        'device_id' => 'DS423GD',
        'last_printer_active_at' => Carbon::now()
    ])->create();

    $printer->styles()->attach($this->style->sku, ['color_sku' => $this->color->sku]);

    BarcodePrinted::factory([
        'style_sku' => $this->style->sku,
        'color_sku' => $this->color->sku,
        'warehouse_id' => 1,
        'is_error_print' => BarcodePrinted::ACTIVE,
        'convert_status' => BarcodePrinted::ACTIVE,
        'print_status' => BarcodePrinted::INACTIVE,
        'employee_id' => $this->employee->id
    ])->create();

    Product::factory([
        'parent_id' => 0,
        'style' => $this->style->name,
        'color' => $this->color->name
    ])->create();

    $param = [
        'style' => $this->style->name,
        'color' => $this->color->name,
        'employee_code' => $this->employee->code,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $this->assertEquals(json_decode($response->getContent(), true)['data']['is_scan_leader'], true);
});

test('check another wip of current employee is pending deduct', function () {
    $printer = Printer::factory([
        'name' => 'DURIAN TEST',
        'warehouse_id' => 1,
        'device_id' => 'DS423GD',
        'last_printer_active_at' => Carbon::now()
    ])->create();

    $printer->styles()->attach($this->style->sku, ['color_sku' => $this->color->sku]);

    $style = ProductStyle::factory([
        'name' => 'style2',
        'sku' => '3001',
        'type' => 'tee'
    ])->create();
    $color = ProductColor::factory([
        'name' => 'color2',
        'sku' => '1X'
    ])->create();

    BarcodePrinted::factory([
        'style_sku' => $style->sku,
        'color_sku' => $color->sku,
        'warehouse_id' => 1,
        'print_status' => BarcodePrinted::ACTIVE,
        'employee_id' => $this->employee->id
    ])->create();

    Product::factory([
        'parent_id' => 0,
        'style' => $this->style->name,
        'color' => $this->color->name
    ])->create();

    $param = [
        'style' => $this->style->name,
        'color' => $this->color->name,
        'employee_code' => $this->employee->code,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $this->assertEquals(json_decode($response->getContent(), true)['data']['is_deduct_pending'], true);
});

test('check wip pending deduct', function () {
    $printer = Printer::factory([
        'name' => 'DURIAN TEST',
        'warehouse_id' => 1,
        'device_id' => 'DS423GD',
        'last_printer_active_at' => Carbon::now()
    ])->create();

    $printer->styles()->attach($this->style->sku, ['color_sku' => $this->color->sku]);

    BarcodePrinted::factory([
        'style_sku' => $this->style->sku,
        'color_sku' => $this->color->sku,
        'warehouse_id' => 1,
        'print_status' => BarcodePrinted::ACTIVE,
        'employee_id' => $this->employee->id
    ])->create();

    Product::factory([
        'parent_id' => 0,
        'style' => $this->style->name,
        'color' => $this->color->name
    ])->create();

    $param = [
        'style' => $this->style->name,
        'color' => $this->color->name,
        'employee_code' => $this->employee->code,
        'quantity' => 1
    ];

    $response = $this->post($this->endpoint, $param);

    $this->assertEquals(json_decode($response->getContent(), true)['data']['is_deduct_pending'], true);
});

test('success', function () {
    Event::fake();
    Bus::fake();
    $printer = Printer::factory([
        'name' => 'DURIAN TEST',
        'warehouse_id' => 1,
        'device_id' => 'DS423GD',
        'last_printer_active_at' => Carbon::now()
    ])->create();

    $printer->styles()->attach($this->style->sku, ['color_sku' => $this->color->sku]);

    $product = Product::factory([
        'parent_id' => 0,
        'style' => $this->style->name,
        'color' => $this->color->name,
    ])->create();

    $productChild = Product::factory([
        'parent_id' => $product->id,
        'style' => $this->style->name,
        'color' => $this->color->name
    ])->create();

    ProductQuantity::factory([
        'product_id' => $productChild->id,
        'quantity' => 11,
        'warehouse_id' => 1
    ])->create();

    $param = [
        'style' => $this->style->name,
        'color' => $this->color->name,
        'quantity' => 1,
        'employee_code' => $this->employee->code,
    ];

    $response = $this->post($this->endpoint, $param);

    Event::assertDispatched(CountPendingNotification::class);
    Event::assertDispatched(UpdateStatusPrintNotification::class);
    Bus::assertDispatched(CheckWipPendingScanBatchJob::class);

    $this->assertEquals(json_decode($response->getContent(), true)['data']['style_sku'], $this->style->sku);
    $this->assertEquals(json_decode($response->getContent(), true)['data']['color_sku'], $this->color->sku);
    $this->assertEquals(json_decode($response->getContent(), true)['data']['id'], SaleOrderItemBarcode::where('order_id', $this->saleOrder->id)->first()->barcode_printed_id);
});
