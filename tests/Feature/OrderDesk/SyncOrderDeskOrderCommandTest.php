<?php

use App\Jobs\SyncOrderDeskNewOrderJob;
use App\Models\FolderOrderDesk;
use App\Models\SaleOrderAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = SaleOrderAccount::factory([
        'source' => SaleOrderAccount::SOURCE_ORDER_DESK,
        'sync_orderdesk' => SaleOrderAccount::ACTIVE,
    ])->create();
    foreach (FolderOrderDesk::folders() as $folder) {
        FolderOrderDesk::create([
            'sale_order_account_id' => $this->user->id,
            'folder_id' => rand(200, 500),
            'name' => $folder,
        ]);
    }
});

test('Pushed Job success', function () {
    $folderNew = FolderOrderDesk::where('name', FolderOrderDesk::FOLDER_NEW)->first();
    $query = Arr::query([
        'folder_id' => $folderNew->folder_id,
        'limit' => 50,
        'offset' => 0,
    ]);
    $path = sprintf(config('orderdesk.path'), 'orders?' . $query);
    $response = '{"status":"success","execution_time":"0.0200 seconds","total_records":1,"records_returned":1,"offset":0,"limit":50,"orders":[{"id":"**********"},{"id":"**********"},{"id":"**********"},{"id":"**********"}]}';

    Http::fake([
        $path => Http::response($response),
    ]);
    Queue::fake();

    $this->artisan('sync:order-desk-order')
        ->expectsOutput('Synced Success.')
        ->assertExitCode(0);
    foreach (json_decode($response, true)['orders'] as $order) {
        Queue::assertPushed(SyncOrderDeskNewOrderJob::class, function ($job) use ($order) {
            return $job->params['external_key'] == $order['id'] && $this->user->id == $job->params['account_id'];
        });
    }
});
