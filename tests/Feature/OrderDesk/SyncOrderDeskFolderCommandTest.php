<?php

use App\Models\SaleOrderAccount;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = SaleOrderAccount::factory([
        'source' => SaleOrderAccount::SOURCE_ORDER_DESK,
        'sync_orderdesk' => SaleOrderAccount::ACTIVE,
    ])->create();
});

test('Success sync folder', function () {
    $path = sprintf(config('orderdesk.path'), 'store');
    $response = '{"status":"success","execution_time":"0.0488 seconds","store":{"id":25027,"name":"SWIFTPOD","settings":{"locale_code":"en_US","timezone":"America/Los_Angeles","store_country":"US","store_email":"<EMAIL>","admin_email":"<EMAIL>","normalize_capitalization":1,"normalize_phone_numbers":0,"date_format":"Y-m-d H:i:s","short_date_format":"Y-m-d","orders_per_page":50,"inventory_items_per_page":50,"report_frequency":"","dashboard_report_type":"daily","store_url":"","custom_css":"","security_po_box_warn":"","security_different_address_warn":"","security_different_country_warn":"","security_quantity_warn":0,"limit_address_field_char_length":0,"country_name_format":0,"all_orders_name":"All Orders","api_key":"5uQvMteqxtDbrQnqLWYqH35QFEGtmNBSiPr5u2mx7KdtLJJ6UT","split_method":"clone","prepend_old_store_id":"","all_orders_column_view":[],"shipping_methods":[],"default_checkout_data_fields":[],"default_variation_data_fields":[],"custom_buttons":[],"smtp_port":"","smtp_host":"","smtp_username":"","smtp_password":"","smtp_encryption":"","smtp_headers":"","auto_update_stock_count":"","auto_create_inventory_items":"","auto_sync_inventory_items":"on","multi_location_inventory":"","auto_appointment_process":1,"special":[],"admin_store_tags":"api_logging","collapsed_menu":"","dashboard_filter":[],"inventory_sync_store_id_list":[],"store_type_options":["In House","Other"],"ioss":""},"folders":{"144183":"New","144184":"Prepared","144185":"Closed","144186":"Canceled","194784":"Moteefe New Orders","195361":"DLS Inc New Orders"}}}';

    Http::fake([
        $path => Http::response($response),
    ]);
    $this->artisan('sync:order-desk-folder')
        ->expectsOutput('Synced Success.')
        ->assertExitCode(0);
    $dataFolders = json_decode($response, true)['store']['folders'];
    foreach ($dataFolders as $folderId => $folder) {
        $this->assertDatabaseHas('folder_order_desks', [
            'folder_id' => $folderId,
            'name' => $folder,
            'sale_order_account_id' => $this->user->id,
        ]);
    }
});

test('Not Authorized Order desk', function () {
    // write unit test artisan command: sync:order-desk-base-info
    $path = sprintf(config('orderdesk.path'), 'store');
    $response = '{"status":"error","execution_time":"0.0488 seconds","message":"Not Authorized"}';

    Http::fake([
        $path => Http::response($response, 401),
    ]);

    $this->artisan('sync:order-desk-folder')
    ->expectsOutput('Error sync folder for account: ' . $this->user->name)
    ->expectsOutput('Synced Success.')
    ->assertExitCode(0);
    $this->assertDatabaseCount('folder_order_desks', 0);
});
