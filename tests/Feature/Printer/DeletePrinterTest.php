<?php
use App\Models\ProductColor;
use App\Models\ProductStyle;
use App\Models\Printer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse,] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->productStyle = ProductStyle::factory()->create(['sku' => 'UNPT']);
    $this->productColor = ProductColor::factory()->createMany([
        ['sku' => '7S'],
        ['sku' => '6S'],
    ]);
    $this->printer = Printer::create([
        'name' => 'WIP test',
        'warehouse_id' => $this->warehouse->id,
        'device_id' => 124123,
    ]);
    $this->printer->colors()->attach($this->productColor[0]['sku'], ['style_sku' => $this->productStyle->sku]);

    $this->endpoint = '/api/printer/' . $this->printer->id;
});

// - Delete fail
test('Delete fail - Printer not found.', function () {
    Printer::find($this->printer->id)->delete();
    $response = $this->delete($this->endpoint);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'code' => 404,
        'success' => false,
        'message' => 'Printer not found.',
    ]);
});

// - Delete success
test('Delete success', function () {
    $response = $this->delete($this->endpoint);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'code' => 200,
        'success' => true,
        'message' => 'success',
        'data' => [],
    ]);
    $printer = Printer::find($this->printer->id);
    $this->assertNull($printer);
    $printProduct = DB::table('printer_product')->where('printer_id', $this->printer->id)->exists();
    $this->assertFalse($printProduct);
});

