<?php

use App\Models\SaleOrderItemImage;
use App\Models\Warehouse;
use App\Repositories\SupplyReportRepository;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->wareHouses = Warehouse::factory()->createMany(
        [
            ['name' => 'San Jose, CA'],
            ['name' => 'Dallas, Texas'],
            ['name' => 'Las Vegas, Nevada'],
            ['name' => 'Ensenada, Mexico'],
            ['name' => 'Springfield, Virginia'],
        ],
    );
    $this->endpoint = 'api/supply-report/ink-consumption';
    $this->rateConversion = 1000; // 1l = 1000cc
});

test('Case return 12 month with old year: 2023', function () {
    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2023');
    $dataTarget = [
        '2023-01',
        '2023-02',
        '2023-03',
        '2023-04',
        '2023-05',
        '2023-06',
        '2023-07',
        '2023-08',
        '2023-09',
        '2023-10',
        '2023-11',
        '2023-12',
    ];

    expect($result['data_report'])->toBeTruthy()
        ->and(count($result['data_report']))->toBe(12); // 12 months

    $dataReport = $result['data_report'];

    foreach ($dataReport as $dataMonth) {
        expect(in_array($dataMonth['month'], $dataTarget))->toBeTruthy();
    }
});

test('Case return 12 month with now year with year 2023-04', function () {
    Carbon::setTestNow(Carbon::parse('2023-04-03'));
    $dataTarget = [
        '2023-04',
        '2023-03',
        '2023-02',
        '2023-01',
        '2022-12',
        '2022-11',
        '2022-10',
        '2022-09',
        '2022-08',
        '2022-07',
        '2022-06',
        '2022-05',
    ];
    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2023');

    expect($result['data_report'])->toBeTruthy()
        ->and(count($result['data_report']))->toBe(12); // 12 months

    $dataReport = $result['data_report'];

    foreach ($dataReport as $dataMonth) {
        expect(in_array($dataMonth['month'], $dataTarget))->toBeTruthy();
    }
});

test('Case return 12 month with now year with year 2023-12', function () {
    Carbon::setTestNow(Carbon::parse('2023-12-03'));
    $dataTarget = [
        '2023-01',
        '2023-02',
        '2023-03',
        '2023-04',
        '2023-05',
        '2023-06',
        '2023-07',
        '2023-08',
        '2023-09',
        '2023-10',
        '2023-11',
        '2023-12',
    ];
    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2023');

    expect($result['data_report'])->toBeTruthy()
        ->and(count($result['data_report']))->toBe(12); // 12 months

    $dataReport = $result['data_report'];

    foreach ($dataReport as $dataMonth) {
        expect(in_array($dataMonth['month'], $dataTarget))->toBeTruthy();
    }
});

test('Case sum data each warehouse group by order_date is 2024-03, 2024-04', function () {
    SaleOrderItemImage::factory()->createMany(
        [
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 100.5,
                'ink_white_cc' => 10.1,
                'order_date' => '2024-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 200.5,
                'ink_white_cc' => 20.1,
                'order_date' => '2024-04-03'
            ],

            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 110.5,
                'ink_white_cc' => 11.1,
                'order_date' => '2024-03-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 210.5,
                'ink_white_cc' => 21.1,
                'order_date' => '2024-03-03'
            ],
        ],
    );
    $this->artisan('calculate-supply-ink-report --start=2023-01-01');

    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2024');

    expect($result['data_report'])->toBeTruthy();

    $dataReport = $result['data_report'];

    foreach ($dataReport as $dataMonth) {
        if ($dataMonth['month'] == '2024-04') {
            foreach ($dataMonth['warehouses'] as $dataWarehouse) {
                if ($dataWarehouse['name'] == 'San Jose, CA') {
                    expect($dataWarehouse['qty_ink_color'])->toBe(round((100.5 + 200.5) / $this->rateConversion, 3))
                        ->and($dataWarehouse['qty_ink_white'])->toBe(round((10.1 + 20.1) / $this->rateConversion, 3));
                } else {
                    expect($dataWarehouse['qty_ink_color'])->toBe(0)
                        ->and($dataWarehouse['qty_ink_white'])->toBe(0);
                }
            }
        } elseif ($dataMonth['month'] == '2024-03') {
            foreach ($dataMonth['warehouses'] as $dataWarehouse) {
                if ($dataWarehouse['name'] == 'San Jose, CA') {
                    expect($dataWarehouse['qty_ink_color'])->toBe(round((110.5 + 210.5) / $this->rateConversion, 3))
                        ->and($dataWarehouse['qty_ink_white'])->toBe(round((11.1 + 21.1) / $this->rateConversion, 3));
                } else {
                    expect($dataWarehouse['qty_ink_color'])->toBe(0)
                        ->and($dataWarehouse['qty_ink_white'])->toBe(0);
                }
            }
        } else {
            foreach ($dataMonth['warehouses'] as $dataWarehouse) {
                expect($dataWarehouse['qty_ink_color'])->toBe(0)
                    ->and($dataWarehouse['qty_ink_white'])->toBe(0);
            }
        }
    }
});

test('Case sum data each warehouse with order_date is 2024-04-04', function () {
    SaleOrderItemImage::factory()->createMany(
        [
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 100.5,
                'ink_white_cc' => 10.1,
                'order_date' => '2024-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 200.5,
                'ink_white_cc' => 20.1,
                'order_date' => '2024-04-04'
            ],

            [
                'warehouse_id' => $this->wareHouses[1]->id,
                'ink_color_cc' => 110.5,
                'ink_white_cc' => 11.1,
                'order_date' => '2024-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[1]->id,
                'ink_color_cc' => 210.5,
                'ink_white_cc' => 21.1,
                'order_date' => '2024-04-04'
            ],

            [
                'warehouse_id' => $this->wareHouses[2]->id,
                'ink_color_cc' => 120.5,
                'ink_white_cc' => 12.1,
                'order_date' => '2024-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[2]->id,
                'ink_color_cc' => 220.5,
                'ink_white_cc' => 22.1,
                'order_date' => '2024-04-04'
            ],

            [
                'warehouse_id' => $this->wareHouses[3]->id,
                'ink_color_cc' => 130.5,
                'ink_white_cc' => 13.1,
                'order_date' => '2024-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[3]->id,
                'ink_color_cc' => 230.5,
                'ink_white_cc' => 23.1,
                'order_date' => '2024-04-04'
            ],

            [
                'warehouse_id' => $this->wareHouses[4]->id,
                'ink_color_cc' => 140.5,
                'ink_white_cc' => 14.1,
                'order_date' => '2024-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[4]->id,
                'ink_color_cc' => 240.5,
                'ink_white_cc' => 24.1,
                'order_date' => '2024-04-04'
            ],
        ],
    );
    $this->artisan('calculate-supply-ink-report --start=2023-01-01');

    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2024');

    expect($result['data_report'])->toBeTruthy();

    $dataReport = $result['data_report'];

    foreach ($dataReport as $dataMonth) {
        if ($dataMonth['month'] == '2024-04') {
            foreach ($dataMonth['warehouses'] as $dataWarehouse) {
                switch ($dataWarehouse['name']) {
                    case 'San Jose, CA':

                        expect($dataWarehouse['qty_ink_color'])->toBe(round((100.5 + 200.5) / $this->rateConversion, 3))
                            ->and($dataWarehouse['qty_ink_white'])->toBe(round((10.1 + 20.1) / $this->rateConversion, 3));
                        break;

                    case 'Dallas, Texas':

                        expect($dataWarehouse['qty_ink_color'])->toBe(round((110.5 + 210.5) / $this->rateConversion, 3))
                            ->and($dataWarehouse['qty_ink_white'])->toBe(round((11.1 + 21.1) / $this->rateConversion, 3));
                        break;

                    case 'Las Vegas, Nevada':

                        expect($dataWarehouse['qty_ink_color'])->toBe(round((120.5 + 220.5) / $this->rateConversion, 3))
                            ->and($dataWarehouse['qty_ink_white'])->toBe(round((12.1 + 22.1) / $this->rateConversion, 3));
                        break;

                    case 'Ensenada, Mexico':

                        expect($dataWarehouse['qty_ink_color'])->toBe(round((130.5 + 230.5) / $this->rateConversion, 3));
                        expect($dataWarehouse['qty_ink_white'])->toBe(round((13.1 + 23.1) / $this->rateConversion, 3));
                        break;

                    case 'Springfield, Virginia':

                        expect($dataWarehouse['qty_ink_color'])->toBe(round((140.5 + 240.5) / $this->rateConversion, 3))
                            ->and($dataWarehouse['qty_ink_white'])->toBe(round((14.1 + 24.1) / $this->rateConversion, 3));
                        break;
                }
            }
        } else {
            foreach ($dataMonth['warehouses'] as $dataWarehouse) {
                expect($dataWarehouse['qty_ink_color'])->toBe(0)
                    ->and($dataWarehouse['qty_ink_white'])->toBe(0);
            }
        }
    }
});

test('Case sum data San Jose, CA warehouse with order_date is 2024-04-04', function () {
    SaleOrderItemImage::factory()->createMany(
        [
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 100.5,
                'ink_white_cc' => 10.1,
                'order_date' => '2024-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 200.5,
                'ink_white_cc' => 20.1,
                'order_date' => '2024-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 0,
                'ink_white_cc' => 0,
                'order_date' => '2024-04-04'
            ]
        ],
    );
    $this->artisan('calculate-supply-ink-report --start=2023-01-01');

    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2024');

    expect($result['data_report'])->toBeTruthy();

    $dataReport = $result['data_report'];

    foreach ($dataReport as $dataMonth) {
        if ($dataMonth['month'] == '2024-04') {
            foreach ($dataMonth['warehouses'] as $dataWarehouse) {
                if ($dataWarehouse['name'] == 'San Jose, CA') {
                    expect($dataWarehouse['qty_ink_color'])->toBe(round((100.5 + 200.5) / $this->rateConversion, 3))
                        ->and($dataWarehouse['qty_ink_white'])->toBe(round((10.1 + 20.1) / $this->rateConversion, 3));
                } else {
                    expect($dataWarehouse['qty_ink_color'])->toBe(0)
                        ->and($dataWarehouse['qty_ink_white'])->toBe(0);
                }
            }
        } else {
            foreach ($dataMonth['warehouses'] as $dataWarehouse) {
                expect($dataWarehouse['qty_ink_color'])->toBe(0)
                    ->and($dataWarehouse['qty_ink_white'])->toBe(0);
            }
        }
    }
});

test('Case sum data each warehouse of old year: 2023', function () {
    SaleOrderItemImage::factory()->createMany(
        [
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 101.5,
                'ink_white_cc' => 11.1,
                'order_date' => '2023-01-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 202.5,
                'ink_white_cc' => 12.1,
                'order_date' => '2023-02-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 203.5,
                'ink_white_cc' => 13.1,
                'order_date' => '2023-03-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 204.5,
                'ink_white_cc' => 14.1,
                'order_date' => '2023-04-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 205.5,
                'ink_white_cc' => 15.1,
                'order_date' => '2023-05-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 206.5,
                'ink_white_cc' => 16.1,
                'order_date' => '2023-06-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 207.5,
                'ink_white_cc' => 17.1,
                'order_date' => '2023-07-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 208.5,
                'ink_white_cc' => 18.1,
                'order_date' => '2023-08-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 209.5,
                'ink_white_cc' => 19.1,
                'order_date' => '2023-09-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 2010.5,
                'ink_white_cc' => 110.1,
                'order_date' => '2023-10-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 2011.5,
                'ink_white_cc' => 111.1,
                'order_date' => '2023-11-04'
            ],
            [
                'warehouse_id' => $this->wareHouses[0]->id,
                'ink_color_cc' => 2012.5,
                'ink_white_cc' => 112.1,
                'order_date' => '2023-12-04'
            ],
        ],
    );
    $this->artisan('calculate-supply-ink-report --start=2023-01-01');

    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2023');

    expect($result['data_report'])->toBeTruthy();

    $dataReport = $result['data_report'];

    foreach ($dataReport as $dataMonth) {
        foreach ($dataMonth['warehouses'] as $dataWarehouse) {
            if ($dataWarehouse['name'] == 'San Jose, CA') {
                expect($dataWarehouse['qty_ink_color'])->toBeGreaterThan(0)
                    ->and($dataWarehouse['qty_ink_white'])->toBeGreaterThan(0);
            } else {
                expect($dataWarehouse['qty_ink_color'])->toBe(0)
                    ->and($dataWarehouse['qty_ink_white'])->toBe(0);
            }
        }
    }
});

test('Case color chart each warehouse', function () {
    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2024');

    expect($result['data_report'])->toBeTruthy();

    $dataReport = $result['data_report'];

    foreach ($dataReport as $dataMonth) {
        foreach ($dataMonth['warehouses'] as $dataWarehouse) {
            switch ($dataWarehouse['name']) {
                case 'San Jose, CA':

                    expect($dataWarehouse['color'])->toBe('#6092C0');
                    break;

                case 'Dallas, Texas':

                    expect($dataWarehouse['color'])->toBe('#54B399');
                    break;

                case 'Las Vegas, Nevada':

                    expect($dataWarehouse['color'])->toBe('#D36086');
                    break;

                case 'Ensenada, Mexico':

                    expect($dataWarehouse['color'])->toBe('#9170B8');
                    break;

                case 'Springfield, Virginia':

                    expect($dataWarehouse['color'])->toBe('#D4A274');
                    break;
            }
        }
    }
});

test('Case call api with pram.year=2023', function () {
    $response = $this->get($this->endpoint . '?year=2023');
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption('2023');

    expect($dataResponse)->toEqual($result);
});

test('Case call api with no pram.year', function () {
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $data = new SupplyReportRepository();
    $result = $data->getInkConsumption(now()->format('Y'));

    expect($dataResponse)->toEqual($result);
});
