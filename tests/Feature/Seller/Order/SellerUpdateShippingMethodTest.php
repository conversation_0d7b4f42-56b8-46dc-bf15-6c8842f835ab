<?php

use App\Models\Client;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Models\TeamMember;
use App\Models\TeamMemberRole;
use App\Models\TeamMemberRolePermission;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'client_id' => $this->client->id]);
    $this->role = TeamMemberRole::create([
        'name' => 'test',
        'client_id' => $this->client->id,
    ]);
    foreach (TeamMemberRolePermission::listFuction() as $function) {
        $permissions[] = [
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::ALL_PERMISSION,
        ];
        TeamMemberRolePermission::create([
            'team_member_role_id' => $this->role->id,
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::ALL_PERMISSION,
        ]);
    }
    $this->permissions = $permissions;
    $this->member = TeamMember::create([
        'client_id' => $this->client->id,
        'name' => 'test',
        'username' => 'test',
        'password' => bcrypt('123456'),
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
    ]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456', 'root_username' => $this->client->username]);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];
    $this->tracking_url = 'https://www.ups.com/track?tracknumber={tracking_code}';
    $this->saleOrder = SaleOrder::factory([
        'warehouse_id' => 1,
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'shipping_method' => SaleOrder::SHIPPING_METHOD_STANDARD,
        'encode_id' => '0dv27665',
    ])
        ->has(
            SaleOrderItem::factory()->count(2)->sequence([
                'product_id' => 2,
                'quantity' => 1,
                'store_id' => $this->store->id,
                'warehouse_id' => 1,
                'product_style_sku' => 'UNPT',
                'unit_price' => 2.5,
                'blank_price' => 3.5,
                'handling_fee' => 4.6,
            ], [
                'product_id' => 1,
                'quantity' => 3,
                'store_id' => $this->store->id,
                'warehouse_id' => 1,
                'product_style_sku' => 'WORB',
                'unit_price' => 2.0,
                'blank_price' => 3.1,
                'handling_fee' => 0.5,
            ])
                ->has(
                    SaleOrderItemImage::factory([
                        'warehouse_id' => 1,
                        'store_id' => $this->store->id,
                        'print_side' => 0,
                    ])
                        ->has(ProductPrintSide::factory([
                            'name' => 'Fleece',
                        ]), 'printSizeType'),
                    'images',
                )
                ->has(ProductStyle::factory([
                    'name' => '5100',
                ]), 'getTypeProduct')
                ->has(Product::factory([
                    'name' => 'product',
                ]), 'product'),
            'items',
        )
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US'
        ]), 'address')
        ->create();

    $this->endpoint = '/api/seller/orders/update-shipping-method';
});

//member has only view permission
test('failed - member has view only permission or no permission', function () {
    TeamMemberRolePermission::where([
        'team_member_role_id' => $this->role->id,
        'function_name' => TeamMemberRolePermission::ORDERS_FUNCTION,
    ])->update([
        'permission' => TeamMemberRolePermission::VIEW_PERMISSION,
    ]);

    $params = [
        'order_id' => '0dv27665',
        'shipping_method' => 'priority',
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
        ->post($this->endpoint, $params);

    $response->assertStatus(403);

    TeamMemberRolePermission::where([
        'team_member_role_id' => $this->role->id,
        'function_name' => TeamMemberRolePermission::ORDERS_FUNCTION,
    ])->update([
        'permission' => TeamMemberRolePermission::NO_PERMISSION,
    ]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
        ->post($this->endpoint, $params);

    $response->assertStatus(403);
});

// id error - truyền sai id
test('success', function () {
    $params = [
        'order_id' => '0dv27665',
        'shipping_method' => 'priority',
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
    ->post($this->endpoint, $params);
    $response->assertStatus(200);
});

test('unsuccess - failed upgraded shipping priority for internal order', function () {
    $saleOrderAddress = SaleOrderAddress::where('order_id', $this->saleOrder->id)->update(['country' => 'CN']);
    $params = [
        'order_id' => '0dv27665',
        'shipping_method' => 'priority',
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
    ->post($this->endpoint, $params);
    $response->assertStatus(422);
});

test('unsuccess - failed to downgraded from higher shipping_method', function () {
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)->update(['shipping_method' => SaleOrder::SHIPPING_METHOD_PRIORITY]);
    $params = [
        'order_id' => '0dv27665',
        'shipping_method' => 'standard',
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
    ->post($this->endpoint, $params);
    $response->assertStatus(422);
});

test('unsuccess - order_label', function () {
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)->update(['order_type' => SaleOrder::ORDER_TYPE_LABEL_ORDER]);
    $params = [
        'order_id' => '0dv27665',
        'shipping_method' => 'standard',
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
    ->post($this->endpoint, $params);
    $response->assertStatus(422);
});

test('unsuccess - revert rbt', function () {
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)->update(['is_rbt' => true, 'tag' => 211]);
    $params = [
        'order_id' => '0dv27665',
        'shipping_method' => 'standard',
    ];
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->token)
    ->post($this->endpoint, $params);
    $response->assertStatus(200);
    $this->assertDatabaseHas('sale_order', [
        'encode_id' => '0dv27665',
        'is_rbt' => false,
        'tag' => '',
    ]);
});
