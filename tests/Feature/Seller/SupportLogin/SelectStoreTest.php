<?php

use App\Models\Store;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    Store::factory()->count(5)->sequence(
        ['is_active' => true],
        ['is_active' => true],
        ['is_active' => false],
        ['is_active' => true],
        ['is_active' => true],
    )->create();

    $this->user = User::factory()->create();
    $this->user->stores()->sync(Store::all()->pluck('id'));
    $this->user->save();
    $response = $this->post('/api/seller/login', ['email' => '<EMAIL>', 'password' => 'test1337', 'support_login' => true]);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];

    $resultStores = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get('/api/seller/get-store');
    $this->stores = json_decode($resultStores->getContent(), true);
});

// get store success
test('select store success', function () {
    $endpoint = 'api/seller/select-store/' . $this->stores[0]['id'];
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->post($endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    expect($response)->toHaveKeys(['access_token', 'token_type', 'user']);
    expect($response['user'])->toHaveKeys(['id', 'name']);
});

// select store fail - id not found
test('select store failed - id not found', function () {
    $endpoint = 'api/seller/select-store/2000';
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->post($endpoint);

    $response->assertStatus(404);
});

// select store fail - store not active
test('select store failed - store not active', function () {
    $store = Store::where('is_active', false)->first();
    $endpoint = 'api/seller/select-store/' . $store->id;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->post($endpoint);

    $response->assertStatus(404);
});

// select store fail - store not in user store
test('select store failed - store not in user store', function () {
    $store = Store::factory()->create();
    $endpoint = 'api/seller/select-store/' . $store->id;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->post($endpoint);

    $response->assertStatus(401);
});
