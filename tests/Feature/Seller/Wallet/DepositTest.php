<?php

use App\Models\Client;
use App\Models\PaymentAccount;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Stripe\StripeClient;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->endpoint = '/api/topups/deposit';
    DB::table('payment_accounts')->insert([
        'provider' => 'stripe',
        'api_key' => 'fake_api_key',
        'api_secret' => 'sk_test_51QEkXkD50wemo9v8sbap1Wn2aCms7FGOPEuoyAoogQwTPQCPViY3vOEdnbkTk0QMQl9av4YkJwSi7v2cGHCXCujj00elN4mKgo',
        'webhook_secret' => 'fake_webhook_secret',
    ]);
    $this->account = PaymentAccount::where('provider', 'stripe')->orderByDesc('id')->first();

    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create();
    $this->stripeClient = new StripeClient([
        'api_key' => $this->account->api_secret
    ]);
    $customer = $this->stripeClient->customers->create([
        'email' => $this->store->email,
        'name' => $this->store->username
    ]);
    $this->store->stripe_id = $customer->id;
    $this->store->save();
    DB::table('payment_fees')->insert([
        [
            'payment_gateway' => 'stripe',
            'payment_method' => 'card',
            'type' => 'topup',
            'fee_percent' => 3.75,
            'max_amount' => 0,
        ],
        [
            'payment_gateway' => 'stripe',
            'payment_method' => 'us_bank_account',
            'type' => 'topup',
            'fee_percent' => 0,
            'max_amount' => 0,
        ]
    ]);
});

test('test deposit with card', function () {
    $this->stripeClient->paymentMethods->attach('pm_card_mastercard', [
        'customer' => $this->store->stripe_id
    ]);

    expect(true)->toBeTrue();
});

test('test deposit with bank account', function () {
    $this->stripeClient->paymentMethods->attach('pm_usBankAccount_success', [
        'customer' => $this->store->stripe_id
    ]);

    expect(true)->toBeTrue();
});

afterEach(function () {
    $this->store = Store::where('id', $this->store->id)->first();
    $this->stripeClient->customers->delete($this->store->stripe_id);
    PaymentAccount::where('id', $this->account->id)->delete();
});
