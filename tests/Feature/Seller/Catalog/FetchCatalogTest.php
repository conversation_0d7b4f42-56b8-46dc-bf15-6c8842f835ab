<?php

use App\Models\Brand;
use App\Models\Client;
use App\Models\Product;
use App\Models\ProductColor;
use App\Models\ProductQuantity;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'client_id' => $this->client->id]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456']);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];
    $this->endpoint = '/api/seller/catalog/list';

    ProductColor::factory()->count(2)->sequence([
        'name' => 'RED',
        'sku' => '1R'
    ], [
        'name' => 'BLACK',
        'sku' => '1B'
    ])->create();
    Product::factory()->count(5)->sequence([
        'sku' => 'UNPT',
        'size' => '2XL,3XL,XS,S',
        'color' => 'RED,BLACK',
        'style' => '3001',
        'parent_id' => 0,
        'is_popular' => true,
    ], [
        'sku' => 'UNJKSJ',
        'size' => '2XL,3XL,XS,S',
        'color' => 'RED,BLACK',
        'style' => '5001',
        'parent_id' => 0,
        'is_popular' => true,
    ], [
        'sku' => 'FSDF',
        'size' => '2XL,3XL,XS,S',
        'color' => 'RED,BLACK',
        'style' => '6001',
        'parent_id' => 0,
        'is_popular' => true,
    ], [
        'sku' => 'REWUTG',
        'size' => 'XS,S',
        'color' => 'RED,BLACK',
        'style' => '2001',
        'parent_id' => 0,
        'is_popular' => false,
    ], [
        'sku' => 'FSSS',
        'size' => 'XS,S',
        'color' => 'RED,BLACK',
        'style' => '1001',
        'parent_id' => 0,
        'is_popular' => false,
    ])
        ->has(
            Product::factory()->count(2)->sequence(fn ($sequence) => [
                'color' => $sequence->index % 2 === 0 ? 'RED' : 'BLACK',
            ])
                ->state(function (array $attributes, Product $product) {
                    return [
                        'is_discontinued' => $product->is_discontinued,
                        'is_popular' => $product->is_popular,
                        'style' => $product->style,
                    ];
                })
                ->has(ProductQuantity::factory([
                    'quantity' => 20,
                    'incoming_stock' => 10,
                ]), 'productQuantities')
                ->for(Brand::factory([
                    'name' => 'brand_name',
                ]), 'brand'),
            'product_variants',
        )
        ->create();

    $this->result = [
        'id',
        'parent_id',
        'brand_name',
        'product_parent',
    ];

    $this->resultParent = [
        'id',
        'name',
        'style',
        'size',
        'color',
        'image',
        'color_code',
    ];
});

//valid brand request - invalid brand request
test('get order success - brand request', function () {
    $assert['valid_params'] = ['brand' => 'name'];
    $assert['invalid_params'] = ['brand' => 123];

    foreach ($assert as $keyAssert => $valueAssert) {
        $endpoint = $this->endpoint . '?' . http_build_query($valueAssert);
        $response = $this->withHeaders([
            'Authorization' => "Bearer $this->token",
        ])->get($endpoint);

        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $response);
        if ($keyAssert === 'valid_params') {
            expect($response['data'])->toHaveCount(5);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                expect($value)->toHaveKeys($this->result);
                $this->assertNotEmpty($value['product_parent']);
                expect($value['product_parent'])->toHaveKeys($this->resultParent);
                $this->assertCount(2, $value['product_parent']['color_code']);
                foreach ($value['product_parent']['color_code'] as $key => $color) {
                    $this->assertStringContainsString($key, $value['product_parent']['color']);
                }
            }
        } else {
            expect($response['data'])->toBeEmpty();
        }
    }
});

//valid style request - invalid style request
test('get order success - style request', function () {
    $assert['valid_params'] = ['style' => '3001'];
    $assert['invalid_params'] = ['style' => 123];

    foreach ($assert as $keyAssert => $valueAssert) {
        $endpoint = $this->endpoint . '?' . http_build_query($valueAssert);
        $response = $this->withHeaders([
            'Authorization' => "Bearer $this->token",
        ])->get($endpoint);

        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('data', $response);
        if ($keyAssert === 'valid_params') {
            expect($response['data'])->toHaveCount(1);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                if ($keyAssert === 'valid_params') {
                    expect($value)->toHaveKeys($this->result);
                    $this->assertNotEmpty($value['product_parent']);
                    expect($value['product_parent'])->toHaveKeys($this->resultParent);
                    $this->assertCount(2, $value['product_parent']['color_code']);
                    foreach ($value['product_parent']['color_code'] as $key => $color) {
                        $this->assertStringContainsString($key, $value['product_parent']['color']);
                    }
                }
            }
        } else {
            expect($response['data'])->toBeEmpty();
        }
    }
});

//valid new request - invalid new request
test('get order success - new request', function () {
    $assert['valid_params'] = ['new' => 'DESC'];
    $assert['invalid_params'] = ['new' => 123];

    foreach ($assert as $keyAssert => $valueAssert) {
        $endpoint = $this->endpoint . '?' . http_build_query($valueAssert);
        $response = $this->withHeaders([
            'Authorization' => "Bearer $this->token",
        ])->get($endpoint);

        if ($keyAssert === 'valid_params') {
            $response->assertStatus(200);
            $response = json_decode($response->getContent(), true);

            $this->assertArrayHasKey('data', $response);
            expect($response['data'])->toHaveCount(5);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                if ($keyAssert === 'valid_params') {
                    expect($value)->toHaveKeys($this->result);
                    $this->assertNotEmpty($value['product_parent']);
                    expect($value['product_parent'])->toHaveKeys($this->resultParent);
                    $this->assertCount(2, $value['product_parent']['color_code']);
                    foreach ($value['product_parent']['color_code'] as $key => $color) {
                        $this->assertStringContainsString($key, $value['product_parent']['color']);
                    }
                }
            }
        } else {
            $response->assertStatus(500);
        }
    }
});

//Không tìm thấy product color
test('get order success - not found product color', function () {
    ProductColor::query()->where(['color' => 'RED']);
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertArrayHasKey('data', $response);
    expect($response['data'])->toHaveCount(5);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        $this->assertNotEmpty($value['product_parent']);
        expect($value['product_parent'])->toHaveKeys($this->resultParent);
        $this->assertCount(2, $value['product_parent']['color_code']);
        foreach ($value['product_parent']['color_code'] as $key => $color) {
            $this->assertStringContainsString($key, $value['product_parent']['color']);
        }
    }
});

// Không có catalog - chỉ có product con
test('get order success - not found catalog', function () {
    Product::query()->where('parent_id', '!=', 0)->update(['parent_id' => 20]);
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertArrayHasKey('data', $response);
    expect($response['data'])->toHaveCount(1);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        $this->assertEmpty($value['product_parent']);
    }
});

// get order success - hide catalog
test('get order success - hide catalog', function () {
    Product::query()->update(['is_hide' => true]);
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertEmpty($response['data']);
});

// get order success
test('get order success', function () {
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->token",
    ])->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertArrayHasKey('data', $response);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        $this->assertNotEmpty($value['product_parent']);
        expect($value['product_parent'])->toHaveKeys($this->resultParent);
        $this->assertCount(2, $value['product_parent']['color_code']);
        foreach ($value['product_parent']['color_code'] as $key => $color) {
            $this->assertStringContainsString($key, $value['product_parent']['color']);
        }
    }
});
