<?php

namespace Tests\Feature\Seller\Member;

use App\Models\Client;
use App\Models\Store;
use App\Models\TeamMember;
use App\Models\TeamMemberRole;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->endpoint = '/api/seller/members';
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'client_id' => $this->client->id]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456']);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];
    $this->withHeaders([
        'Authorization' => "Bearer $this->token",
        'Accept' => 'application/json',
    ]);
    $this->member = TeamMember::create([
        'client_id' => $this->client->id,
        'name' => 'test',
        'username' => 'test',
        'password' => bcrypt('123456'),
        'team_member_role_id' => 1,
        'store_ids' => [$this->store->id],
    ]);
    $this->role = TeamMemberRole::create([
        'name' => 'test',
        'client_id' => $this->client->id,
    ]);
});

test('get list members', function () {
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
});

test('get detail member', function () {
    $response = $this->get($this->endpoint . '/' . $this->member->id);
    $response->assertStatus(200);
    $response->assertJsonFragment([
        'name' => $this->member->name,
        'username' => $this->member->username,
        'team_member_role_id' => $this->member->team_member_role_id,
        'store_ids' => $this->member->store_ids
    ]);
});

test('create member success', function () {
    $response = $this->post($this->endpoint, [
        'name' => 'member_test',
        'username' => 'member',
        'password' => '123456',
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
    ]);
    $response->assertStatus(200);
    $this->assertDatabaseHas('team_members', [
        'name' => 'member_test',
        'username' => 'member',
        'team_member_role_id' => $this->role->id,
        'store_ids' => json_encode([$this->store->id]),
    ]);
});

test('update member success', function () {
    $response = $this->put($this->endpoint . '/' . $this->member->id, [
        'name' => 'member_test',
        'username' => 'member',
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
        'status' => false,
    ]);
    $response->assertStatus(200);
    $this->assertDatabaseHas('team_members', [
        'name' => 'member_test',
        'username' => 'member',
        'status' => false,
        'team_member_role_id' => $this->role->id,
        'store_ids' => json_encode([$this->store->id]),
    ]);
});

test('delete member success', function () {
    $response = $this->delete($this->endpoint . '/' . $this->member->id);
    $response->assertStatus(200);
    $this->assertDatabaseMissing('team_members', [
        'id' => $this->member->id,
    ]);
});

test('validate username is unique', function () {
    $response = $this->post($this->endpoint, [
        'name' => 'member_test',
        'username' => 'test',
        'password' => '123456',
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
    ]);
    $response->assertStatus(422);
});

test('validate invalid role', function () {
    $response = $this->post($this->endpoint, [
        'name' => 'member_test',
        'username' => 'member',
        'password' => '123456',
        'team_member_role_id' => 0,
        'store_ids' => [$this->store->id],
    ]);
    $response->assertStatus(422);
});

test('validate invalid store', function () {
    $response = $this->post($this->endpoint, [
        'name' => 'member_test',
        'username' => 'member',
        'password' => '123456',
        'team_member_role_id' => $this->role->id,
        'store_ids' => [0],
    ]);
    $response->assertStatus(422);
});

test('other client cannot delete member', function () {
    $this->member->client_id = 0;
    $this->member->save();
    $response = $this->delete($this->endpoint . '/' . $this->member->id);
    $data = json_decode($response->getContent(), true);
    $response->assertStatus(500);
    $this->assertEquals('You do not have permission to delete this team member', $data['message']);
});
