<?php

namespace Tests\Feature\Seller\Member;

use App\Models\Client;
use App\Models\Store;
use App\Models\TeamMember;
use App\Models\TeamMemberRole;
use App\Models\TeamMemberRolePermission;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->endpoint = '/api/seller/roles';
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'client_id' => $this->client->id]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456']);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];
    $this->withHeaders([
        'Authorization' => "Bearer $this->token",
        'Accept' => 'application/json',
    ]);
    $this->role = TeamMemberRole::create([
        'name' => 'test',
        'client_id' => $this->client->id,
    ]);

    foreach (TeamMemberRolePermission::listFuction() as $function) {
        $permissions[] = [
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::ALL_PERMISSION,
        ];
        TeamMemberRolePermission::create([
            'team_member_role_id' => $this->role->id,
            'function_name' => $function,
            'permission' => TeamMemberRolePermission::ALL_PERMISSION,
        ]);
    }
    $this->permissions = $permissions;

    $this->member = TeamMember::create([
        'client_id' => $this->client->id,
        'name' => 'test',
        'username' => 'test',
        'password' => bcrypt('123456'),
        'team_member_role_id' => $this->role->id,
        'store_ids' => [$this->store->id],
    ]);
});

test('get list roles', function () {
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response->assertJsonCount(1, 'data');
    $response->assertJsonStructure([
        'data' => [
            '*' => [
                'id',
                'name',
            ]
        ]
    ]);
});

test('create role success', function () {
    $response = $this->post($this->endpoint, [
        'name' => 'role_test',
        'permissions' => $this->permissions,
    ]);
    $response->assertStatus(200);

    $this->assertDatabaseHas('team_member_roles', [
        'name' => 'role_test',
    ]);

    $role = TeamMemberRole::where('name', 'role_test')->first();

    $this->assertDatabaseHas('team_member_role_permissions', [
        'team_member_role_id' => $role->id,
        'permission' => TeamMemberRolePermission::ALL_PERMISSION,
    ]);
});

test('update role success', function () {
    $response = $this->put($this->endpoint . '/' . $this->role->id, [
        'name' => 'role_test',
        'permissions' => [
            [
                'function_name' => 'orders',
                'permission' => TeamMemberRolePermission::VIEW_PERMISSION,
            ]
        ]
    ]);
    $response->assertStatus(200);
    $this->assertDatabaseHas('team_member_roles', [
        'name' => 'role_test',
    ]);
    $existsViewPermission = TeamMemberRolePermission::where('team_member_role_id', $this->role->id)
        ->where('permission', TeamMemberRolePermission::VIEW_PERMISSION)
        ->where('function_name', 'orders')
        ->exists();
    $this->assertTrue($existsViewPermission);
    $existsAllPermission = TeamMemberRolePermission::where('team_member_role_id', $this->role->id)
        ->where('permission', TeamMemberRolePermission::ALL_PERMISSION)
        ->exists();
    $this->assertTrue($existsAllPermission);
});

test('delete role success', function () {
    $this->member->team_member_role_id = $this->role->id + 1;
    $this->member->save();
    $response = $this->delete($this->endpoint . '/' . $this->role->id);
    $response->assertStatus(200);
    $this->assertDatabaseMissing('team_member_roles', [
        'id' => $this->role->id,
    ]);
});

test('validate role name is unique', function () {
    $response = $this->post($this->endpoint, [
        'name' => 'test',
        'permissions' => $this->permissions,
    ]);
    $response->assertStatus(422);
});

test('validate role name must not be admin', function () {
    $response = $this->post($this->endpoint, [
        'name' => 'Admin',
        'permissions' => $this->permissions,
    ]);

    $response->assertStatus(403);
});

test('validate invalid role', function () {
    $response = $this->post($this->endpoint, [
        'name' => 'test',
        'permissions' => $this->permissions,
    ]);
    $response->assertStatus(422);
});

test('validate invalid permission function name', function () {
    $this->permissions[0]['function_name'] = 'invalid';
    $response = $this->post($this->endpoint, [
        'name' => 'test',
        'permissions' => $this->permissions,
    ]);
    $response->assertStatus(422);
});

test('validate cannot delete admin role', function () {
    $this->role->name = 'Admin';
    $this->role->save();
    $response = $this->delete($this->endpoint . '/' . $this->role->id);
    $data = json_decode($response->getContent(), true);
    $response->assertStatus(403);
    $this->assertEquals('You do not have permission to delete this role', $data['message']);
});

test('other client cannot delete role', function () {
    $this->role->client_id = 0;
    $this->role->save();
    $response = $this->delete($this->endpoint . '/' . $this->role->id);
    $data = json_decode($response->getContent(), true);
    $response->assertStatus(403);
    $this->assertEquals('You do not have permission to delete this role', $data['message']);
});
