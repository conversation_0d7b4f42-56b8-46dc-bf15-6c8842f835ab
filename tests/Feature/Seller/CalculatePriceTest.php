<?php

use App\Console\Commands\CalculatePriceForSeller;
use App\Models\Client;
use App\Models\EmbroideryTask;
use App\Models\PeakShippingFee;
use App\Models\Product;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\Promotion;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderCalculateFailed;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderItemSurchargeFee;
use App\Models\SaleOrderPromotion;
use App\Models\SaleOrderSurchargeFee;
use App\Models\Shipment;
use App\Models\ShippingCarrier;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\StoreShipment;
use App\Models\SurchargeFee;
use App\Models\SurchargeService;
use App\Models\Tag;
use Carbon\Carbon;
use Faker\Factory as faker;
use Illuminate\Console\OutputStyle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\NullOutput;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'is_calculate_shipping' => true, 'is_calculate_price' => true, 'client_id' => $this->client->id]);
    $response = $this->post('/api/seller/login', ['username' => 'test', 'password' => '123456']);
    $data = json_decode($response->getContent(), true);
    $this->token = $data['access_token'];

    $this->productStyle = ProductStyle::factory()->count(2)->sequence(
        ['type' => 'TEE', 'sku' => '3001'],
        ['type' => 'TEE', 'sku' => '5001'],
    )->create();

    $this->printArea = ProductPrintArea::factory()->count(6)->sequence(
        ['name' => 'Front', 'product_style_id' => $this->productStyle[0]->id],
        ['name' => 'Front', 'product_style_id' => $this->productStyle[1]->id],
        ['name' => 'Back', 'product_style_id' => $this->productStyle[0]->id],
        ['name' => 'Back', 'product_style_id' => $this->productStyle[1]->id],
        ['name' => 'Right Sleeve', 'product_style_id' => $this->productStyle[0]->id],
        ['name' => 'Right Sleeve', 'product_style_id' => $this->productStyle[1]->id],
    )->create();

    $this->productPrintSide = ProductPrintSide::factory()->count(3)->sequence(
        ['name' => 'Front', 'code' => 0, 'code_wip' => 'F'],
        ['name' => 'Back', 'code' => 1, 'code_wip' => 'B'],
        ['name' => 'Right Sleeve', 'code' => 4, 'code_wip' => 'R'],

    )->create();

    $this->product = Product::factory()->count(2)->sequence(
        ['name' => '3001 / AQUA / XS', 'sku' => 'PRODUCT1'],
        ['name' => '5001 / WHITE / XL', 'sku' => 'PRODUCT2'],
    )->create();

    StoreProduct::factory()->count(5)->sequence(
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5, 'print_surcharge' => 1],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[0]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[1]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[2]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[3]->id, 'print_price' => 1.5],
    )->create();

    StoreProduct::factory()->count(5)->sequence(
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5, 'print_surcharge' => 1],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[0]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[1]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[2]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[3]->id, 'print_price' => 0.5],
    )->create();

    $this->storeShipment = StoreShipment::factory()->count(6)->sequence(
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_EXPRESS,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 9,
            'addition_price' => 6,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_EXPRESS,
            'price' => 8,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::INTERNATIONAL,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 30,
            'addition_price' => 25,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::INTERNATIONAL,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 35,
            'addition_price' => 25,
        ],
    )->create();

    $this->saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'order_status' => SaleOrder::SHIPPED,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(2)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 10,
                    'product_id' => $this->product->first()->id,
                    'product_style_sku' => '3001',
                    'product_size_sku' => 'M',
                    'print_sides' => 'FB',
                ],
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 15,
                    'product_id' => $this->product->last()->id,
                    'product_style_sku' => '5001',
                    'product_size_sku' => 'S',
                    'print_sides' => 'FB',
                ],
            )
//                ->has(ProductStyle::factory([
//                    'type' => 'TEE',
//                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $this->promotionType = \App\Models\PromotionType::create([
        'id' => 1,
        'name' => 'Additional Print Area',

    ]);

    $this->surchargeService = SurchargeService::factory()->count(11)->sequence(
        ['name' => SurchargeService::TYPE_HANDLING, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_HANDLING],
        ['name' => SurchargeService::TYPE_TIKTOK_ORDER_SERVICE, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_TIKTOK_FEE],
        ['name' => SurchargeService::TYPE_LABEL_PRINTING_FEE, 'per' => SurchargeService::PER_ORDER, 'api_value' => SurchargeService::API_VALUE_LABEL_PRINTING_FEE],
        ['name' => SurchargeService::TYPE_PLASTIC_BAG, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_PLASTIC_BAG],
        ['name' => SurchargeService::TYPE_MUG_PACKAGING, 'per' => SurchargeService::PER_PRODUCT_TYPE, 'product_type' => 'Mug', 'api_value' => SurchargeService::API_VALUE_MUG_PACKAGING],
        ['name' => SurchargeService::TYPE_HOLOGRAM_STICKER, 'per' => SurchargeService::PER_PRODUCT_TYPE, 'product_type' => 'Tee,Fleece,BIB,SweatPants,Tote Bag,CAP,Short,Jacket,Tank', 'api_value' => SurchargeService::API_VALUE_HOLOGRAM_STICKER],
        ['name' => SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG, 'per' => SurchargeService::PER_ORDER, 'api_value' => SurchargeService::API_VALUE_STICKER_AND_BAG],
        ['name' => SurchargeService::TYPE_EMBROIDERY_10001_TO_15000_STITCHES, 'per' => SurchargeService::PER_PRINT_AREA, 'api_value' => SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES],
        ['name' => SurchargeService::TYPE_EMBROIDERY_15001_TO_20000_STITCHES, 'per' => SurchargeService::PER_PRINT_AREA, 'api_value' => SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES],
        ['name' => SurchargeService::TYPE_EMBROIDERY_20001_TO_25000_STITCHES, 'per' => SurchargeService::PER_PRINT_AREA, 'api_value' => SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES],
        ['name' => SurchargeService::TYPE_EMBROIDERY_25001_TO_30000_STITCHES, 'per' => SurchargeService::PER_PRINT_AREA, 'api_value' => SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES],
    )->create();

    SurchargeFee::factory()->count(11)->sequence(
        [
            'store_id' => $this->store->id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_HANDLING)->id,
            'value' => 2.5,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id,
            'value' => 10.5,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id,
            'value' => 5,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_PLASTIC_BAG)->id,
            'value' => 3,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG)->id,
            'value' => 4,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_MUG_PACKAGING)->id,
            'value' => 4,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('name', SurchargeService::TYPE_HOLOGRAM_STICKER)->id,
            'value' => 2,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES)->id,
            'value' => 0.5,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES)->id,
            'value' => 1,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES)->id,
            'value' => 1.5,
        ],
        [
            'store_id' => $this->saleOrder->store_id,
            'service_id' => $this->surchargeService
                ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES)->id,
            'value' => 2,
        ],
    )->create();

    Tag::factory()->count(5)->sequence(
        [
            'name' => Tag::TAG_LABEL,
        ],
        [
            'name' => Tag::TAG_PLASTIC_BAG,
            'is_additional_service' => true,
            'surcharge_service_id' => $this->surchargeService->firstWhere('name', SurchargeService::TYPE_PLASTIC_BAG)->id,
        ],
        [
            'name' => Tag::TAG_HOLOGRAM_STICKER,
            'is_additional_service' => true,
            'surcharge_service_id' => $this->surchargeService->firstWhere('name', SurchargeService::TYPE_HOLOGRAM_STICKER)->id,
        ],
        [
            'name' => Tag::TAG_STICKER_AND_PLASTIC_BAG,
            'is_additional_service' => true,
            'surcharge_service_id' => $this->surchargeService->firstWhere('name', SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG)->id,
        ],
        [
            'name' => Tag::TAG_MUG_PACKAGING,
            'is_additional_service' => true,
            'surcharge_service_id' => $this->surchargeService->firstWhere('name', SurchargeService::TYPE_MUG_PACKAGING)->id,
        ],
    )->create();
    $this->handlingFeeByStore = SurchargeFee::first();
    $this->command = new CalculatePriceForSeller();
    $this->command->setOutput(new OutputStyle(new ArgvInput(), new NullOutput()));
});

// store chưa được set giá cho các product
test('calculate failed - not set price product in store', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(2)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);

    $saleOrderItemResult = SaleOrderItem::where('product_id', $product->id)
        ->where('amount_paid', 0)
        ->where('unit_price', 0)
        ->where('handling_fee', 0)
        ->where('blank_price', 0)
        ->first();
    $this->assertNotNull($saleOrderItemResult);
});

// order đã được tính giá
test('calculate failed - order calculated', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])
        ->has(StoreProduct::factory()->count(3)->sequence(
            ['store_id' => $this->store->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->first()->id, 'print_price' => 1.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->last()->id, 'print_price' => 0.5],
        ), 'storeProducts')
        ->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'calculated_at' => '2022-10-18 00:00:00',
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->where('calculated_at', '2022-10-18 00:00:00')
        ->first();
    $this->assertNotNull($saleOderResult);
});

//order đã bị tính giá failed trong vòng 24h
test('calculate failed - Order has been calculated fail within 24h', function () {
    SaleOrderCalculateFailed::factory([
        'order_id' => $this->saleOrder->id,
        'failed_at' => now()->subHour(),
    ])->create();

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $this->saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);

    $saleOrderItemResult = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('amount_paid', 0)
        ->where('unit_price', 0)
        ->where('handling_fee', 0)
        ->where('blank_price', 0)
        ->first();
    $this->assertNotNull($saleOrderItemResult);
})->skip('ko còn logic này');

// order có trạng thái khác shipped & late cancelled
test('calculate failed - order status different shipped or late cancelled', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])
        ->has(StoreProduct::factory()->count(3)->sequence(
            ['store_id' => $this->store->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->first()->id, 'print_price' => 1.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->last()->id, 'print_price' => 0.5],
        ), 'storeProducts')
        ->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'order_status' => SaleOrder::NEW_ORDER,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);
});

// order không có địa chỉ ship
test('calculate failed - order address not found', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])
        ->has(StoreProduct::factory()->count(3)->sequence(
            ['store_id' => $this->store->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->first()->id, 'print_price' => 1.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->last()->id, 'print_price' => 0.5],
        ), 'storeProducts')
        ->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'order_status' => SaleOrder::SHIPPED,
    ])
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();
    $this->command->calculate();
    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);
    $saleOrderItemResult = SaleOrderItem::where('product_id', $product->id)
        ->where('amount_paid', 0)
        ->where('unit_price', 0)
        ->where('handling_fee', 0)
        ->where('blank_price', 0)
        ->first();
    $this->assertNotNull($saleOrderItemResult);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)
        ->first();
    $this->assertNotNull($saleOrderCalculateFailed);

    // tính giá cho order khác
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItemCalculated = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItemCalculated);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

// Chưa set giá ship
test('calculate failed - store shipment not found', function () {
    $product = Product::factory(['name' => '1234 / AKN / S'])
        ->has(StoreProduct::factory()->count(3)->sequence(
            ['store_id' => $this->store->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->first()->id, 'print_price' => 1.5],
            ['store_id' => $this->store->id, 'product_print_area_id' => $this->printArea->last()->id, 'print_price' => 0.5],
        ), 'storeProducts')
        ->create();

    $saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => now()->subDay(),
        'warehouse_id' => 1,
        'shipping_method' => StoreShipment::SERVICE_EXPRESS,
        'order_status' => SaleOrder::SHIPPED,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 20,
                    'product_id' => $product->id,
                    'product_style_sku' => '1234',
                    'product_size_sku' => 'S',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'FLEECE',
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->has(
            SaleOrderItem::factory(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 10,
                    'product_id' => $this->product->last()->id,
                    'product_style_sku' => '6000',
                    'product_size_sku' => 'XL',
                ],
            )
                ->has(ProductStyle::factory([
                    'type' => 'not found',  // type not exist
                ]), 'getTypeProduct')
                ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                    ['warehouse_id' => 1, 'print_side' => '0'],
                    ['warehouse_id' => 1, 'print_side' => '1'],
                ), 'images'),
            'items',
        )
        ->for(Shipment::factory([
            'store_id' => $this->store->id,
            'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
        ]), 'shipmentDefault')
        ->create();

    $this->command->calculate();

    $saleOderResult = SaleOrder::where('id', $saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOderResult);

    // trong 2 item , có 1 item không tính được giá do không lấy được giá ship -> sẽ lưu lại log
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $saleOrder->id)->first();
    $this->assertNotNull($saleOrderCalculateFailed);
});

//store không được tính giá
test('calculate success - store not calculate', function () {
    Store::query()->update(['is_calculate_price' => false]);
    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->whereNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 0)
        ->where('blank_price', 0)
        ->where('handling_fee', 0)
        ->where('amount_paid', 0)
        ->first();
    $this->assertNotNull($saleOrderItem);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//store không cần tính giá ship (free ship)
test('calculate success - free ship', function () {
    Store::query()->update(['is_calculate_shipping' => false]);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 195)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//international order
test('calculate success - international order', function () {
    SaleOrderAddress::where('order_id', $this->saleOrder->id)->update(['country' => 'VN']);
    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 825)
        ->where('shipping_calculate', 630)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//Không tính giá ship cho late cancelled order
test('calculate success - free ship for late cancelled order', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_LATE_CANCELLED]);
    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 195)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//order gooten=> shipping fee = 0
test('calculate success - gooten order =>shipping free = 0', function () {
    Shipment::where('id', $this->saleOrder->shipment_id)->update(['shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD_STORE]);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 220)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 220)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 120)
        ->first();

    $this->assertNotNull($saleOrderItem);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

// Tính Promotion
// select product type - select print area
test('Calculate success - promotion: select product type & select print area', function () {
    Promotion::factory()->count(3)->sequence([
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.1,
        'start_time' => now()->subDay()->toDateTimeString(),
        'end_time' => now()->addDays(4)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"print_areas":["Front","Left Sleeve"],"amount":0.1}',

        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.5,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->subDays(5)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"print_areas":["Front","Left Sleeve"],"amount":0.5}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 1,
        'start_time' => now()->addDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"print_areas":["Front","Left Sleeve"],"amount":1}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 2,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"print_areas":["Front","Left Sleeve"],"amount":2}',
        'is_public' => 0,
        'is_active' => 0
    ])
        ->create();

    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $this->assertDatabaseHas('sale_order_promotions', [
        'order_id' => $this->saleOrder->id,
        'print_area' => 'Front',
        'amount' => 1.00
    ]);
    $this->assertDatabaseHas('sale_order_promotions', [
        'order_id' => $this->saleOrder->id,
        'print_area' => 'Front',
        'amount' => 1.50
    ]);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

test('Calculate success - promotion: select product type & select multiple print area. Sale order has multiple. The item has multiple variations that are eligible for the promotion.', function () {
    SaleOrderItem::query()->update([
        'print_sides' => 'FBR'
    ]);
    Promotion::factory()->count(3)->sequence([
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.1,
        'start_time' => now()->subDay()->toDateTimeString(),
        'end_time' => now()->addDays(4)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"print_areas":["Front","Left Sleeve", "Back"],"amount":0.1}',

        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.5,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->subDays(5)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"print_areas":["Front","Left Sleeve", "Back"],"amount":0.5}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 1,
        'start_time' => now()->addDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"print_areas":["Front","Left Sleeve", "Back"],"amount":1}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 2,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"print_areas":["Front","Left Sleeve", "Back"],"amount":2}',
        'is_public' => 0,
        'is_active' => 0
    ])
        ->create();

    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $this->assertDatabaseHas('sale_order_promotions', [
        'order_id' => $this->saleOrder->id,
        'print_area' => 'Front',
        'amount' => 1.00
    ]);
    $this->assertDatabaseHas('sale_order_promotions', [
        'order_id' => $this->saleOrder->id,
        'print_area' => 'Front',
        'amount' => 1.50
    ]);

    $this->assertDatabaseHas('sale_order_promotions', [
        'order_id' => $this->saleOrder->id,
        'print_area' => 'Back',
        'amount' => 1.00
    ]);
    $this->assertDatabaseHas('sale_order_promotions', [
        'order_id' => $this->saleOrder->id,
        'print_area' => 'Back',
        'amount' => 1.50
    ]);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//  select product type & not select print area
test('Calculate success - promotion: select product type & not select print area', function () {
    Promotion::factory()->count(3)->sequence([
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.1,
        'start_time' => now()->subDay()->toDateTimeString(),
        'end_time' => now()->addDays(4)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"print_areas":["Front","Left Sleeve", "Back"],"amount":0.1}',

        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.5,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->subDays(5)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"print_areas":["Front","Left Sleeve", "Back"],"amount":0.5}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 1,
        'start_time' => now()->addDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"print_areas":["Front","Left Sleeve", "Back"],"amount":1}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 2,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"print_areas":["Front","Left Sleeve", "Back"],"amount":2}',
        'is_public' => 0,
        'is_active' => 0
    ])
        ->create();
    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $promotionItemFirst = SaleOrderPromotion::where('order_item_id', $this->saleOrder->items[0]->id)->get();

    $this->assertEquals(count($promotionItemFirst), 1);
    $this->assertEquals($promotionItemFirst[0]->amount, 1);
    $promotionItemLast = SaleOrderPromotion::where('order_item_id', $this->saleOrder->items[1]->id)->get();

    $this->assertEquals(count($promotionItemLast), 1);
    $this->assertEquals($promotionItemLast[0]->amount, 1.5);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

test('Calculate success - promotion: not select product type & select print area', function () {
    Promotion::factory()->count(4)->sequence([
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.1,
        'start_time' => now()->subDay()->toDateTimeString(),
        'end_time' => now()->addDays(4)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"amount":0.1}',

        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.5,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->subDays(5)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"amount":0.5}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 1,
        'start_time' => now()->addDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"amount":1}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 2,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"product_types":["TEE"],"amount":2}',
        'is_public' => 0,
        'is_active' => 0
    ])
        ->create();
    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $promotionItemFirst = SaleOrderPromotion::where('order_item_id', $this->saleOrder->items[0]->id)->get();

    $this->assertEquals(count($promotionItemFirst), 1);
    $this->assertEquals($promotionItemFirst[0]->amount, 1);
    $promotionItemLast = SaleOrderPromotion::where('order_item_id', $this->saleOrder->items[1]->id)->get();

    $this->assertEquals(count($promotionItemLast), 1);
    $this->assertEquals($promotionItemLast[0]->amount, 1.5);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

test('Calculate success - promotion: select all product type & select all print area', function () {
    Promotion::factory()->count(3)->sequence([
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.1,
        'start_time' => now()->subDay()->toDateTimeString(),
        'end_time' => now()->addDays(4)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"amount":0.1}',

        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 0.5,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->subDays(5)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"amount":0.5}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 1,
        'start_time' => now()->addDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"amount":1}',
        'is_public' => 1,
        'is_active' => 1
    ], [
        'promotion_type_id' => $this->promotionType->id,
        'amount' => 2,
        'start_time' => now()->subDays(10)->toDateTimeString(),
        'end_time' => now()->addDays(15)->toDateTimeString(),
        'store_id' => $this->store->id,
        'json_detail' => '{"amount":2}',
        'is_public' => 0,
        'is_active' => 0
    ])
        ->create();
    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $promotionItemFirst = SaleOrderPromotion::where('order_item_id', $this->saleOrder->items[0]->id)->get();

    $this->assertEquals(count($promotionItemFirst), 1);
    $this->assertEquals($promotionItemFirst[0]->amount, 1);
    $promotionItemLast = SaleOrderPromotion::where('order_item_id', $this->saleOrder->items[1]->id)->get();

    $this->assertEquals(count($promotionItemLast), 1);
    $this->assertEquals($promotionItemLast[0]->amount, 1.5);

    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
});

//order labeling => shipping fee = 0
test('calculate success - order labeling', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_type' => SaleOrder::ORDER_TYPE_LABEL_ORDER]);

    $this->command->calculate();

    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 220)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 220)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 120)
        ->first();

    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNotNull($saleOrderSurcharge);
    expect($saleOrderSurcharge->value)->toEqual($labelFeeOfStore->value);
});
test('calculate success - order labeling has order_type = 1 and tag is label', function () {
    $tagLabel = Tag::where('name', Tag::TAG_LABEL)->first();
    SaleOrder::where('id', $this->saleOrder->id)->update(
        [
            'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
            'tag' => "$tagLabel->id"
        ],
    );
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 220)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 220)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 120)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNotNull($saleOrderSurcharge);
    expect($saleOrderSurcharge->value)->toEqual($labelFeeOfStore->value);
});

test('calculate success - order tiktok', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER]);
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();

    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNull($saleOrderSurcharge);
});

test('calculate success - order has plastic bag (plastic_bag = true)', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['plastic_bag' => true]);
    $this->command->calculate();

    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        $this->assertNull($tiktokFee);
        $plasticBagFee = SurchargeFee::where('service_id', $this->surchargeService
            ->firstWhere('name', SurchargeService::TYPE_PLASTIC_BAG)->id)->first();
        $saleOrderSurcharge = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $plasticBagFee->id)->first();
        $this->assertNotNull($saleOrderSurcharge);
        expect($saleOrderSurcharge->value)->toEqual($plasticBagFee->value * $orderItem->quantity);
    }
});
test('calculate success - order has plastic bag (tag has plastic_bag)', function () {
    $tagPlasticBag = Tag::where('name', Tag::TAG_PLASTIC_BAG)->first();
    SaleOrder::where('id', $this->saleOrder->id)->update(
        [
            'tag' => "$tagPlasticBag->id"
        ],
    );
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        $this->assertNull($tiktokFee);
        $plasticBagFee = SurchargeFee::where('service_id', $this->surchargeService
            ->firstWhere('name', SurchargeService::TYPE_PLASTIC_BAG)->id)->first();
        $saleOrderSurcharge = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $plasticBagFee->id)->first();
        $this->assertNotNull($saleOrderSurcharge);
        expect($saleOrderSurcharge->value)->toEqual($plasticBagFee->value * $orderItem->quantity);
    }
});
test('calculate success - order has tag is sticker_and_bag', function () {
    $tagStickerAndBag = Tag::where('name', Tag::TAG_STICKER_AND_PLASTIC_BAG)->first();
    SaleOrder::where('id', $this->saleOrder->id)->update(
        [
            'tag' => "$tagStickerAndBag->id"
        ],
    );
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        $this->assertNull($tiktokFee);
    }
    $plasticBagFee = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $plasticBagFee->id)->first();
    $this->assertNotNull($saleOrderSurcharge);
    expect($saleOrderSurcharge->value)->toEqual($plasticBagFee->value);
});

test('calculate success - order has tag is mug_packing and product_type is Mug', function () {
    $productStyle = ProductStyle::first();
    $productStyle->type = 'Mug';
    $productStyle->save();
    StoreShipment::factory()->count(2)->sequence(
        [
            'store_id' => $this->store->id,
            'product_type' => 'Mug',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'Mug',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_EXPRESS,
            'price' => 10,
            'addition_price' => 5,
        ],
    )->create();
    $tagMugPacking = Tag::where('name', Tag::TAG_MUG_PACKAGING)->first();
    SaleOrder::where('id', $this->saleOrder->id)->update(
        [
            'tag' => "$tagMugPacking->id"
        ],
    );
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $mugPackagingFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_MUG_PACKAGING)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $mugPackagingFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $mugPackagingFeeOfStore->id)->first();
        if ($orderItem->getTypeProduct->type == 'Mug') {
            $this->assertNotNull($mugPackagingFee);
            expect($mugPackagingFee->value)->toEqual($mugPackagingFeeOfStore->value * $orderItem->quantity);
        } else {
            $this->assertNull($mugPackagingFee);
        }
    }
});
test('calculate success - order has tag is hologram_sticker', function () {
    //Product type is Tee,Fleece,BIB,SweatPats,Tote Bag,CAP,Short,Jacket,Tank
    $hologramStickerProductTypes = ['Tee', 'Fleece', 'BIB', 'SweatPants', 'Tote Bag', 'CAP', 'Short', 'Jacket', 'Tank'];
    $productType = Arr::random($hologramStickerProductTypes);
    if (!in_array($productType, ['Tee', 'Fleece'])) {
        ///Tee, Fleece da khai bao shipping o tren roi
        $productStyle = ProductStyle::first();
        $productStyle->type = $productType;
        $productStyle->save();
        StoreShipment::factory()->count(2)->sequence(
            [
                'store_id' => $this->store->id,
                'product_type' => $productType,
                'destination' => StoreShipment::DOMESTIC,
                'service_type' => StoreShipment::SERVICE_STANDARD,
                'price' => 10,
                'addition_price' => 5,
            ],
            [
                'store_id' => $this->store->id,
                'product_type' => $productType,
                'destination' => StoreShipment::DOMESTIC,
                'service_type' => StoreShipment::SERVICE_EXPRESS,
                'price' => 10,
                'addition_price' => 5,
            ],
        )->create();
    }

    $tagHologramSticker = Tag::where('name', Tag::TAG_HOLOGRAM_STICKER)->first();
    SaleOrder::where('id', $this->saleOrder->id)->update(
        [
            'tag' => "$tagHologramSticker->id"
        ],
    );
    $this->command->calculate();

    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $hologramStickerFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_HOLOGRAM_STICKER)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();

    foreach ($saleOrderItems as $orderItem) {
        $hologramStickerFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $hologramStickerFeeOfStore->id)->first();
        if ($orderItem->getTypeProduct->type == $productType) {
            $this->assertNotNull($hologramStickerFee);
            expect($hologramStickerFee->value)->toEqual($hologramStickerFeeOfStore->value * $orderItem->quantity);
        }
    }
});
test('calculate success - in peak surcharge period USPS carrier', function () {
    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->subMonth()->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonth()->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->update(['carrier_code' => ShippingCarrier::USPS_ECOMMERCE_CODE]);
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();

    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $SaleOrderSurchargeFee = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    $this->assertNotNull($SaleOrderSurchargeFee);
});

test('calculate success - in peak surcharge period DHL carrier', function () {
    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->subMonth()->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonth()->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->update(['carrier_code' => ShippingCarrier::DHL_ECOMMERCE_CODE]);
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();

    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $SaleOrderSurchargeFee = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    $this->assertNotNull($SaleOrderSurchargeFee);
});

test('calculate success - out of applicable preriod', function () {
    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->addMonths(3)->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonths(6)->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->update(['carrier_code' => ShippingCarrier::DHL_ECOMMERCE_CODE]);
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();

    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderSurchargeFee = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    $this->assertNull($saleOrderSurchargeFee);
});

test('calculate success - order tiktok + peak surcharge fee', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER]);
    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->subMonth()->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonth()->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->update(['carrier_code' => ShippingCarrier::DHL_ECOMMERCE_CODE]);

    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();

    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderSurchargeFee = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    $this->assertNotNull($saleOrderSurchargeFee);

    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNull($saleOrderSurcharge);
});

test('calculate success - no peak surcharge USPS carrier order in_production_cancelled with shipment', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_IN_PRODUCTION_CANCELLED]);

    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->subMonth()->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonth()->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->update(['carrier_code' => ShippingCarrier::USPS_ECOMMERCE_CODE]);
    $this->command->calculate();

    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 195)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();

    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $SaleOrderSurchargeFee = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    $this->assertNull($SaleOrderSurchargeFee);
});

test('calculate success - no peak surcharge USPS carrier order in_production_cancelled - no shipment', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_IN_PRODUCTION_CANCELLED]);

    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->subMonth()->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonth()->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->delete();
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('shipping_calculate', 0)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 120)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderSurchargeFee = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    $this->assertNull($saleOrderSurchargeFee);
});
test('calculate success - no peak surcharge USPS carrier order - International shipping', function () {
    $excluded = StoreShipment::DOMESTIC_SHIPPING;
    $faker = Faker::create();
    do {
        $countryNotDomestic = strtoupper($faker->lexify('??'));
    } while (in_array($countryNotDomestic, $excluded));
    SaleOrderAddress::where('order_id', $this->saleOrder->id)->update(['country' => $countryNotDomestic]);
    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->subMonth()->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonth()->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->update(['carrier_code' => ShippingCarrier::USPS_ECOMMERCE_CODE]);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 825)
        ->where('shipping_calculate', 630)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderSurchargeFee = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    $this->assertNull($saleOrderSurchargeFee);
});
test('calculate success - no peak surcharge DHL carrier order - International shipping', function () {
    $excluded = StoreShipment::DOMESTIC_SHIPPING;
    $faker = Faker::create();
    do {
        $countryNotDomestic = strtoupper($faker->lexify('??'));
    } while (in_array($countryNotDomestic, $excluded));
    SaleOrderAddress::where('order_id', $this->saleOrder->id)->update(['country' => $countryNotDomestic]);
    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->subMonth()->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonth()->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->update(['carrier_code' => ShippingCarrier::DHL_ECOMMERCE_CODE]);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 825)
        ->where('shipping_calculate', 630)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItemFirst = SaleOrderItem::where('product_id', $this->product->first()->id)
        ->where('order_id', $this->saleOrder->id)->first();
    $this->assertNotNull($saleOrderItemFirst);
    $saleOrderSurchargeFee = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    $this->assertNull($saleOrderSurchargeFee);
});

test('calculate success - one item of order has shipping price via size', function () {
    StoreShipment::where('store_id', $this->saleOrder->store_id)->delete();
    StoreShipment::factory()->count(2)->sequence(
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 15,
            'addition_price' => 8,
            'size' => 'S'
        ],

    )->create();
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 372)
        ->where('shipping_calculate', 177)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
});

test('calculate success - all item of order has shipping price via size', function () {
    StoreShipment::where('store_id', $this->saleOrder->store_id)->delete();
    StoreShipment::factory()->count(3)->sequence(
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 15,
            'addition_price' => 8,
            'size' => 'M'
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 20,
            'addition_price' => 4,
            'size' => 'S'
        ],

    )->create();
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 351)
        ->where('shipping_calculate', 156)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
});
test('calculate success - order has calculated the labeling fee then its status changes to in_production_cancel', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_type' => SaleOrder::ORDER_TYPE_LABEL_ORDER]);
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 220)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 220)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 120)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNotNull($saleOrderSurcharge);
    ///todo :Order chuyen sang in_production_cancel, se remove labeling fee di
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_LATE_CANCELLED, 'calculated_at' => null]);
    $this->command->calculate();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNull($saleOrderSurcharge);
});

test('calculate success - order type normal, tag label  has calculated the labeling fee then its status changes to in_production_cancel', function () {
    $tagLabel = Tag::where('name', Tag::TAG_LABEL)->first();
    SaleOrder::where('id', $this->saleOrder->id)->update(
        [
            'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
            'tag' => "$tagLabel->id"
        ],
    );
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)
        ->where('order_total', 220)
        ->where('shipping_calculate', 0)
        ->where('amount_paid', 220)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrder);

    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 8)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 120)
        ->first();

    $this->assertNotNull($saleOrderItem);
    $saleOrderCalculateFailed = SaleOrderCalculateFailed::where('order_id', $this->saleOrder->id)->first();
    $this->assertNull($saleOrderCalculateFailed);
    $saleOrderItems = SaleOrderItem::where('order_id', $this->saleOrder->id)->get();
    $tiktokFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_TIKTOK_ORDER_SERVICE)->id)->first();
    foreach ($saleOrderItems as $orderItem) {
        $tiktokFee = SaleOrderItemSurchargeFee::where('order_item_id', $orderItem->id)
            ->where('surcharge_id', $tiktokFeeOfStore->id)->first();
        expect($tiktokFee->value)->toEqual($tiktokFeeOfStore->value * $orderItem->quantity);
    }
    $labelFeeOfStore = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('name', SurchargeService::TYPE_LABEL_PRINTING_FEE)->id)->first();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNotNull($saleOrderSurcharge);

    ///Order chuyen sang in_production_cancel, se remove labeling fee di
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_LATE_CANCELLED, 'calculated_at' => null]);
    $this->command->calculate();
    $saleOrderSurcharge = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)
        ->where('surcharge_id', $labelFeeOfStore->id)->first();
    $this->assertNull($saleOrderSurcharge);
});
test('calculate success - order has peak shipping fee then its status changes to in_production_cancel', function () {
    PeakShippingFee::create([
        'standard_fee' => 1.25,
        'priority_fee' => 2.55,
        'express_fee' => 5.00,
        'start_date' => Carbon::now()->subMonth()->format('Y-n-d H-i-s'),
        'end_date' => Carbon::now()->addMonth()->format('Y-n-d H-i-s'),
    ]);
    Shipment::where('id', $this->saleOrder->shipment_id)
        ->update(['carrier_code' => ShippingCarrier::USPS_ECOMMERCE_CODE]);
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $peakShipping = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    expect($peakShipping->value)->toEqual(1.25);
    ///Todo : Order chuyen sang in_production_cancel, se remove shipping fee di
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::STATUS_LATE_CANCELLED, 'calculated_at' => null]);
    $this->command->calculate();
    $peakShipping = SaleOrderSurchargeFee::where('order_id', $this->saleOrder->id)->where('type', PeakShippingFee::PEAK_SHIPPING_FEE)->first();
    expect($peakShipping)->toBeNull();
});
test('calculate success - sale order embroidery task has 10001 To 15000 stitches', function () {
    EmbroideryTask::factory()->count(1)->sequence(
        [
            'sale_order_id' => $this->saleOrder->id,
            'order_item_id' => SaleOrderItem::where('order_id', $this->saleOrder->id)->first()->id,
            'stitch_count' => 10033,
        ],
    )->create();
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderItem = SaleOrderItem::where('order_id', $this->saleOrder->id)->first();
    $surchargeSurcharge = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_10001_TO_15000_STITCHES)->id)->first();
    $surchargeItemFee = SaleOrderItemSurchargeFee::where('order_item_id', $saleOrderItem->id)->first();
    expect($surchargeItemFee)->toBeObject();
    expect($surchargeItemFee->value)->toEqual($saleOrderItem->quantity * $surchargeSurcharge->value);
});

test('calculate success - sale order embroidery task has 15000 To 20000 stitches', function () {
    EmbroideryTask::factory()->count(1)->sequence(
        [
            'sale_order_id' => $this->saleOrder->id,
            'order_item_id' => SaleOrderItem::where('order_id', $this->saleOrder->id)->first()->id,
            'stitch_count' => 16000,
        ],
    )->create();
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderItem = SaleOrderItem::where('order_id', $this->saleOrder->id)->first();
    $surchargeSurcharge = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES)->id)->first();
    $surchargeItemFee = SaleOrderItemSurchargeFee::where('order_item_id', $saleOrderItem->id)->first();
    expect($surchargeItemFee)->toBeObject();
    expect($surchargeItemFee->value)->toEqual($saleOrderItem->quantity * $surchargeSurcharge->value);
});
test('calculate success - sale order embroidery task has 20001 To 25000 stitches', function () {
    EmbroideryTask::factory()->count(1)->sequence(
        [
            'sale_order_id' => $this->saleOrder->id,
            'order_item_id' => SaleOrderItem::where('order_id', $this->saleOrder->id)->first()->id,
            'stitch_count' => 22000,
        ],
    )->create();
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderItem = SaleOrderItem::where('order_id', $this->saleOrder->id)->first();
    $surchargeSurcharge = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_20001_TO_25000_STITCHES)->id)->first();
    $surchargeItemFee = SaleOrderItemSurchargeFee::where('order_item_id', $saleOrderItem->id)->first();
    expect($surchargeItemFee)->toBeObject();
    expect($surchargeItemFee->value)->toEqual($saleOrderItem->quantity * $surchargeSurcharge->value);
});
test('calculate success - sale order embroidery task has 25001 To 30000 stitches', function () {
    EmbroideryTask::factory()->count(1)->sequence(
        [
            'sale_order_id' => $this->saleOrder->id,
            'order_item_id' => SaleOrderItem::where('order_id', $this->saleOrder->id)->first()->id,
            'stitch_count' => 26000,
        ],
    )->create();
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderItem = SaleOrderItem::where('order_id', $this->saleOrder->id)->first();
    $surchargeSurcharge = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES)->id)->first();
    $surchargeItemFee = SaleOrderItemSurchargeFee::where('order_item_id', $saleOrderItem->id)->first();
    expect($surchargeItemFee)->toBeObject();
    expect($surchargeItemFee->value)->toEqual($saleOrderItem->quantity * $surchargeSurcharge->value);
});
test('calculate success - sale order embroidery task has many type stitches', function () {
    EmbroideryTask::factory()->count(2)->sequence(
        [
            'sale_order_id' => $this->saleOrder->id,
            'order_item_id' => SaleOrderItem::where('order_id', $this->saleOrder->id)->first()->id,
            'stitch_count' => 26000,
        ],
        [
            'sale_order_id' => $this->saleOrder->id,
            'order_item_id' => SaleOrderItem::where('order_id', $this->saleOrder->id)->first()->id,
            'stitch_count' => 15001,
        ],
    )->create();
    $this->command->calculate();
    $saleOrderCalculated = SaleOrder::with('items')->where('id', $this->saleOrder->id)
        ->where('order_total', 325)
        ->where('shipping_calculate', 130)
        ->where('amount_paid', 195)
        ->whereNotNull('calculated_at')
        ->first();
    $this->assertNotNull($saleOrderCalculated);
    $saleOrderItem = SaleOrderItem::where('product_id', $this->product->last()->id)
        ->where('product_style_sku', '5001')
        ->where('unit_price', 7)
        ->where('blank_price', 3.5)
        ->where('handling_fee', 2.5)
        ->where('amount_paid', 105)
        ->first();
    $this->assertNotNull($saleOrderItem);
    $saleOrderItem = SaleOrderItem::where('order_id', $this->saleOrder->id)->first();
    $surchargeSurcharge = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_25001_TO_30000_STITCHES)->id)->first();
    $surchargeItemFee = SaleOrderItemSurchargeFee::where('order_item_id', $saleOrderItem->id)
        ->where('surcharge_id', $surchargeSurcharge->id)->first();
    expect($surchargeItemFee)->toBeObject();
    expect($surchargeItemFee->value)->toEqual($saleOrderItem->quantity * $surchargeSurcharge->value);
    $surchargeSurcharge = SurchargeFee::where('service_id', $this->surchargeService
        ->firstWhere('api_value', SurchargeService::API_VALUE_EMBROIDERY_15001_TO_20000_STITCHES)->id)->first();
    $surchargeItemFee = SaleOrderItemSurchargeFee::where('order_item_id', $saleOrderItem->id)
        ->where('surcharge_id', $surchargeSurcharge->id)->first();
    expect($surchargeItemFee->value)->toEqual($saleOrderItem->quantity * $surchargeSurcharge->value);
});

test('calculate failed - sale order has one item not detect color done', function () {
    $saleOrderItemFirst = SaleOrderItem::where('order_id', $this->saleOrder->id)->first();
    $saleOrderItemFirst->ink_color_status = 0;
    $saleOrderItemFirst->save();
    $this->command->calculate();
    $saleOrder = SaleOrder::where('id', $this->saleOrder->id)->first();
    expect($saleOrder->calculated_at)->toBeNull();
});
