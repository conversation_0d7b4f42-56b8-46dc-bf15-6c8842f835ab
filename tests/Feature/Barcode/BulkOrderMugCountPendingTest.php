<?php

use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Store;
use App\Models\Warehouse;
use App\Repositories\MugsRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouse = Warehouse::factory()->create();
    $this->store = Store::factory()->create();
    $this->saleorder = SaleOrder::factory()->create([
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'is_test' => SaleOrder::NOT_TEST,
        'shipment_id' => 1,
        'is_xqc' => 0,
        'order_quantity' => 20,
    ]);

    $this->productStyle = ProductStyle::factory()->create([
        'type' => '',
        'sku' => 'XXX',
    ]);

    $this->product = Product::factory()->create([
        'sku' => 'xxx'
    ]);

    $this->saleOrderItem = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorder->id,
            'store_id' => $this->store->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => 1,
            'quantity' => 13,
            'product_id' => $this->product->id,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorder->id,
            'store_id' => $this->store->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => 1,
            'quantity' => 7,
            'product_id' => $this->product->id,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => 0,
            'print_method' => ProductStyle::METHOD_MUGS,
        ];
    }, range(1, 13));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => 0,
            'print_method' => ProductStyle::METHOD_MUGS,
        ];
    }, range(1, 7));
    SaleOrderItemBarcode::factory()->createMany($mock);
});

test('Correctly count pending bulk order success', function () {
    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id]);
    expect($data['total'])->toBe(20);
});

test('Correctly count pending bulk order not pass param.warehouse_id', function () {
    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id + 1]);

    expect($data['total'])->toBe(0);
});

test('Correctly count pending bulk order have param.priorityStores include param.store_id', function () {
    $data = resolve(MugsRepository::class)->countPendingBulkOrder([
        'warehouse_id' => $this->warehouse->id,
        'priorityStores' => [$this->store->id],
        'store_id' => $this->store->id,
    ]);

    expect($data['total'])->toBe(20);
});

test('Correctly count pending bulk order have param.priorityStores not include param.store_id', function () {
    $data = resolve(MugsRepository::class)->countPendingBulkOrder([
        'warehouse_id' => $this->warehouse->id,
        'priorityStores' => [$this->store->id],
        'store_id' => $this->store->id + 1,
    ]);

    expect($data['total'])->toBe(0);
});

test('Correctly count pending bulk order exclude tiktok', function () {
    $this->saleorder->order_type = SaleOrder::ORDER_TYPE_TIKTOK_ORDER;
    $this->saleorder->save();
    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id]);

    expect($data['total'])->toBe(0);
});

test('Correctly count pending bulk order exclude xqc', function () {
    $this->saleorder->is_xqc = 1;
    $this->saleorder->save();
    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id]);

    expect($data['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with fba', function () {
    $this->saleorder->is_fba_order = 1;
    $this->saleorder->save();
    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id]);

    expect($data['total'])->toBe(20);

    $dataFba = resolve(MugsRepository::class)->countPendingFba(['warehouse_id' => $this->warehouse->id]);

    expect($dataFba['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with eps', function () {
    $this->saleorder->is_eps = 1;
    $this->saleorder->is_xqc = 0;
    $this->saleorder->save();
    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id]);

    expect($data['total'])->toBe(20);

    $dataEps = resolve(MugsRepository::class)->countPendingEPS(['warehouse_id' => $this->warehouse->id]);

    expect($dataEps['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with reprint', function () {
    $this->saleorder->order_type = 1;
    $this->saleorder->is_eps = 1;
    $this->saleorder->is_fba_order = 0;
    $this->saleorder->is_manual = 0;
    $this->saleorder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->label_root_id = null;
    $barcode->reprint_status = 0;
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id]);

    expect($data['total'])->toBe(20);

    $dataReprint = resolve(MugsRepository::class)->countPendingReprint(['warehouse_id' => $this->warehouse->id]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with manual', function () {
    $this->saleorder->order_type = 1;
    $this->saleorder->is_fba_order = 0;
    $this->saleorder->is_manual = 1;
    $this->saleorder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id]);

    expect($data['total'])->toBe(20);

    $dataReprint = resolve(MugsRepository::class)->countPendingManualProcess(['warehouse_id' => $this->warehouse->id]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with reroute', function () {
    $this->saleorder->order_type = 1;
    $this->saleorder->is_fba_order = 0;
    $this->saleorder->is_manual = 1;
    $this->saleorder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = 1;
    $barcode->save();

    $data = resolve(MugsRepository::class)->countPendingBulkOrder(['warehouse_id' => $this->warehouse->id]);

    expect($data['total'])->toBe(20);

    $dataReprint = resolve(MugsRepository::class)->countPendingReroute(['warehouse_id' => $this->warehouse->id]);

    expect($dataReprint['total'])->toBe(0);
});
