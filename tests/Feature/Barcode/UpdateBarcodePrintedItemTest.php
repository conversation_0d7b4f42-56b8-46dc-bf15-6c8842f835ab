<?php

use App\Models\Printer;
use App\Models\PrinterProduct;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Store;
use App\Models\Warehouse;
use App\Repositories\BarcodeRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouse_id = Warehouse::factory()->create()->id;
    $this->store_id = Store::factory()->create()->id;
    $this->account_id = SaleOrderAccount::factory()->create()->id;
    $this->barcodeRepository = new BarcodeRepository();

    $this->products = Product::factory()->createMany([
        [
            'sku' => 'UNGT7C00S',
            'style' => 'UNGT',
            'color' => '7C'
        ],
        [
            'sku' => 'UNGH9A00S',
            'style' => 'UNGH',
            'color' => '9A'
        ],
    ]);

    //đặt sku và name giống nhau để bên dưới dùng luôn style của product
    ProductStyle::factory()->createMany([
        [
            'sku' => 'UNGT',
            'name' => 'UNGT',
            'type' => 'Tee',
        ],
        [
            'sku' => 'UNGH',
            'name' => 'UNGH',
            'type' => 'Tee',
        ],
    ]);

    Printer::factory([
        'warehouse_id' => $this->warehouse_id,
    ])->has(PrinterProduct::factory([
        'style_sku' => $this->products[1]->style,
        'color_sku' => $this->products[1]->color,
    ]), 'printerProducts')->create();

    $this->saleOrder = SaleOrder::factory([
        'warehouse_id' => $this->warehouse_id,
        'store_id' => $this->store_id,
        'account_id' => $this->account_id,
        'order_status' => SaleOrder::STATUS_NEW_ORDER,
        'is_manual' => 0,
        'is_fba_order' =>  SaleOrder::INACTIVE,
        'is_xqc' => 0,
        'is_eps' => 0,

    ])->has(SaleOrderItem::factory()->count(2)->sequence(
        [
            'product_sku' => $this->products[0]->sku,
            'product_style_sku' => $this->products[0]->style,
            'product_color_sku' => $this->products[0]->color,
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
        [
            'product_sku' => $this->products[1]->sku,
            'product_style_sku' => $this->products[1]->style,
            'product_color_sku' => $this->products[1]->color,
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
    ), 'items')->create();
    $this->saleOrderOtherWarehouse = SaleOrder::factory([
        'warehouse_id' => $this->warehouse_id + 1,
        'store_id' => $this->store_id,
        'account_id' => $this->account_id,
        'order_status' => SaleOrder::STATUS_NEW_ORDER,
        'is_manual' => 0,
        'is_fba_order' =>  SaleOrder::INACTIVE,
        'is_xqc' => 0,
        'is_eps' => 0,

    ])->has(SaleOrderItem::factory()->count(2)->sequence(
        [
            'product_sku' => $this->products[0]->sku,
            'product_style_sku' => $this->products[0]->style,
            'product_color_sku' => $this->products[0]->color,
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
        [
            'product_sku' => $this->products[1]->sku,
            'product_style_sku' => $this->products[1]->style,
            'product_color_sku' => $this->products[1]->color,
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
    ), 'items')->create();
    $this->saleOrderOtherStore = SaleOrder::factory([
        'warehouse_id' => $this->warehouse_id,
        'store_id' => $this->store_id + 1,
        'account_id' => $this->account_id,
        'order_status' => SaleOrder::STATUS_NEW_ORDER,
        'is_manual' => 0,
        'is_fba_order' =>  SaleOrder::INACTIVE,
        'is_xqc' => 0,
        'is_eps' => 0,

    ])->has(SaleOrderItem::factory()->count(2)->sequence(
        [
            'product_sku' => $this->products[0]->sku,
            'product_style_sku' => $this->products[0]->style,
            'product_color_sku' => $this->products[0]->color,
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
        [
            'product_sku' => $this->products[1]->sku,
            'product_style_sku' => $this->products[1]->style,
            'product_color_sku' => $this->products[1]->color,
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
    ), 'items')->create();
    $this->saleOrderOtherAccount = SaleOrder::factory([
        'warehouse_id' => $this->warehouse_id,
        'store_id' => $this->store_id,
        'account_id' => $this->account_id + 1,
        'order_status' => SaleOrder::STATUS_NEW_ORDER,
        'is_manual' => 0,
        'is_fba_order' =>  SaleOrder::INACTIVE,
        'is_xqc' => 0,
        'is_eps' => 0,

    ])->has(SaleOrderItem::factory()->count(2)->sequence(
        [
            'product_sku' => $this->products[0]->sku,
            'product_style_sku' => $this->products[0]->style,
            'product_color_sku' => $this->products[0]->color,
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
        [
            'product_sku' => $this->products[1]->sku,
            'product_style_sku' => $this->products[1]->style,
            'product_color_sku' => $this->products[1]->color,
            'ink_color_status' => SaleOrderItem::ACTIVE,
        ],
    ), 'items')->create();


    $this->totalBarcodePc = rand(1, 10);
    $this->totalBarcodeMobile = rand(1, 10);


    SaleOrderItemBarcode::factory()->count($this->totalBarcodePc * 4)->sequence([
        'print_method' => ProductStyle::METHOD_DTG,
        'barcode_printed_id' => 0,
        'is_deleted' => 0,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'label_root_id' => null,
        'employee_reroute_id' => null
    ])->sequence(
        [
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrder->items[0]->id,
        ],
        [
            'order_id' => $this->saleOrderOtherWarehouse->id,
            'order_item_id' => $this->saleOrderOtherWarehouse->items[0]->id,
        ],
        [
            'order_id' => $this->saleOrderOtherStore->id,
            'order_item_id' => $this->saleOrderOtherStore->items[0]->id,
        ],
        [
            'order_id' => $this->saleOrderOtherAccount->id,
            'order_item_id' => $this->saleOrderOtherAccount->items[0]->id,
        ]
    )->create();
    SaleOrderItemBarcode::factory()->count($this->totalBarcodeMobile * 4)->sequence([
        'print_method' => ProductStyle::METHOD_DTG,
        'barcode_printed_id' => 0,
        'is_deleted' => 0,
        'reprint_status' => SaleOrderItemBarcode::NOT_REPRINTED,
        'label_root_id' => null,
        'employee_reroute_id' => null
    ])->sequence(
        [
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrder->items[1]->id,
        ],
        [
            'order_id' => $this->saleOrderOtherWarehouse->id,
            'order_item_id' => $this->saleOrderOtherWarehouse->items[1]->id,
        ],
        [
            'order_id' => $this->saleOrderOtherStore->id,
            'order_item_id' => $this->saleOrderOtherStore->items[1]->id,
        ],
        [
            'order_id' => $this->saleOrderOtherAccount->id,
            'order_item_id' => $this->saleOrderOtherAccount->items[1]->id,
        ]
    )->create();

    $this->params = [
        'employee_id' => 1,
        'barcode_printed_id' => 1,
        'limit' => rand(1, $this->totalBarcodePc),
        'style_sku' => $this->products[0]->style,
        'warehouse_id' => $this->warehouse_id,
        'store_id' => $this->store_id,
        'account_id' => $this->account_id,
        'is_manual' => null,
        'is_reroute' =>  null,
        'is_fba' =>  null,
        'is_xqc' =>  null,
        'is_eps' =>  null,
        'is_reprint' =>  null,
    ];
});

test('update barcode success by style', function () {
    $this->barcodeRepository->updateBarcodePrintedItem($this->params);
    $data = DB::table('sale_order_item_barcode')
    ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
    ->where('sale_order_item.product_style_sku', $this->params['style_sku'])
    ->where('sale_order_item.product_color_sku', $this->products[0]->color)
    ->where('barcode_printed_id', $this->params['barcode_printed_id'])
    ->count();
    expect($data)->toEqual($this->params['limit']);
})->skip();

test('update barcode success by warehouse', function () {
    $this->params['style_sku'] = null;
    $this->params['store_id'] = null;
    $this->params['account_id'] = null;
    $this->params['limit'] = rand(1, $this->totalBarcodePc * 2);
    $this->barcodeRepository->updateBarcodePrintedItem($this->params);
    $data = DB::table('sale_order_item_barcode')
        ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
        ->where('sale_order_item_barcode.barcode_printed_id', $this->params['barcode_printed_id'])
        ->where('sale_order.warehouse_id', $this->params['warehouse_id'])
        ->count();
    expect($data)->toEqual($this->params['limit']);
})->skip();

test('update barcode success by store', function () {
    $this->params['style_sku'] = null;
    $this->params['account_id'] = null;
    $this->params['limit'] = rand(1, $this->totalBarcodePc * 2);
    $this->barcodeRepository->updateBarcodePrintedItem($this->params);
    $data = DB::table('sale_order_item_barcode')
        ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
        ->where('sale_order_item_barcode.barcode_printed_id', $this->params['barcode_printed_id'])
        ->where('sale_order.store_id', $this->params['store_id'])
        ->where('sale_order.warehouse_id', $this->params['warehouse_id'])
        ->count();
    expect($data)->toEqual($this->params['limit']);
})->skip();

test('update barcode success by account', function () {
    $this->params['style_sku'] = null;
    $this->params['store_id'] = null;
    $this->params['limit'] = rand(1, $this->totalBarcodePc * 2);
    $this->barcodeRepository->updateBarcodePrintedItem($this->params);
    $data = DB::table('sale_order_item_barcode')
        ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
        ->where('sale_order_item_barcode.barcode_printed_id', $this->params['barcode_printed_id'])
        ->where('sale_order.account_id', $this->params['account_id'])
        ->where('sale_order.warehouse_id', $this->params['warehouse_id'])
        ->count();
    expect($data)->toEqual($this->params['limit']);
})->skip();
