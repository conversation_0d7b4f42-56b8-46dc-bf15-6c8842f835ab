<?php

use App\Jobs\DetectShippingMethodRedbubbleV2Job;
use App\Models\IntegrateLog;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\ShippingMethod;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->shipment1 = 's-12432-23';
    $this->orderRb = 121213;

    $this->saleOrder = SaleOrder::factory()->create([
        'store_id' => Store::STORE_REDBUBBLE,
        'shipping_method' => SaleOrder::SHIPPING_METHOD_STANDARD,
        'order_status' => SaleOrder::DRAFT,
        'external_number' => $this->shipment1,
        'external_key' => $this->orderRb
    ]);

    $this->shippingMethod = ShippingMethod::factory()->createMany([
        [
            'api_shipping_method' => 'tinhYeuMauNang1',
            'store_id' => Store::STORE_REDBUBBLE,
            'integrate_carrier_code' => 'usps',
            'integrate_service_code' => 'usps-ground-advantage-service-method',
        ],
        [
            'api_shipping_method' => 'tinhYeuMauNang2',
            'store_id' => Store::STORE_REDBUBBLE,
            'integrate_carrier_code' => 'upsmi',
            'integrate_service_code' => 'upsmi-parcel-select-service-method',
        ],
        [
            'api_shipping_method' => 'tinhYeuMauNang3',
            'store_id' => Store::STORE_REDBUBBLE,
            'integrate_carrier_code' => 'fedex',
            'integrate_service_code' => 'fedex-2day-service-method',
        ],
    ]);

    $this->dataJson
        = [
            'version' => '2023-04-11',
            'callback_uri' => 'https://webhook.site/069d995f-80f8-418d-b9de-4ff15bae9210',
            'order_id' => $this->orderRb,
            'sale_datetime' => '2018-07-10T14:41:32.000Z',
            'shipments' => [
                [
                    'shipment_id' => $this->shipment1,
                    'facility' => 'sanjose-ca-us',
                    'shipping_info' => [
                        'method' => 'standard',
                        'carrier' => 'usps',
                        'service' => 'usps-ground-advantage-service-method',
                        'expected_ship_date' => '2023-05-05T04:23:33Z',
                        'latest_customer_promise_date' => '2023-05-10T04:23:33Z',
                    ],
                ],
                [
                    'shipment_id' => $this->orderRb . '123',
                    'facility' => 'sanjose-ca-us',
                    'shipping_info' => [
                        'method' => 'standard',
                        'carrier' => 'fedex',
                        'service' => 'fedex-2day-service-method',
                        'expected_ship_date' => '2023-05-05T04:23:33Z',
                        'latest_customer_promise_date' => '2023-05-10T04:23:33Z',
                    ],
                ]
            ]
        ];

    $this->integrateLog = IntegrateLog::factory()->create([
        'store_id' => Store::STORE_REDBUBBLE,
        'json' => json_encode($this->dataJson),
        'order_id' => $this->saleOrder->id,
    ]);
});

test('detect shipping success - order draft', function () {
    $job = new DetectShippingMethodRedbubbleV2Job($this->saleOrder->id);
    $job->handle();

    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'store_id' => Store::STORE_REDBUBBLE,
        'order_status' => SaleOrder::NEW_ORDER,
        'shipping_method' => $this->shippingMethod[0]->api_shipping_method,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => 0,
        'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_SUCCESS,
        'message' => 'Shipping method changed from standard to tinhYeuMauNang1 by detect shipping method.',
    ]);
});

test('detect shipping success - order on_hold', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_status' => SaleOrder::ON_HOLD]);
    $job = new DetectShippingMethodRedbubbleV2Job($this->saleOrder->id);
    $job->handle();
    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'store_id' => Store::STORE_REDBUBBLE,
        'order_status' => SaleOrder::ON_HOLD,
        'shipping_method' => $this->shippingMethod[0]->api_shipping_method,
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => 0,
        'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_SUCCESS,
        'message' => 'Shipping method changed from standard to tinhYeuMauNang1 by detect shipping method.',
    ]);
});

test('detect shipping fail - not have integrate log', function () {
    $this->integrateLog->delete();
    $job = new DetectShippingMethodRedbubbleV2Job($this->saleOrder->id);
    $job->handle();

    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'store_id' => Store::STORE_REDBUBBLE,
        'shipping_method' => 'standard',
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => 0,
        'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
        'message' => 'Detect shipping method for external number: ' . $this->saleOrder->external_number . ' missing integrateLog.',
    ]);
});

test('detect shipping fail - carrier code empty', function () {
    $this->dataJson['shipments'][0]['shipping_info']['carrier'] = '';
    $this->integrateLog->json = json_encode($this->dataJson);
    $this->integrateLog->save();

    $job = new DetectShippingMethodRedbubbleV2Job($this->saleOrder->id);
    $job->handle();

    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'store_id' => Store::STORE_REDBUBBLE,
        'shipping_method' => 'standard',
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => 0,
        'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
        'message' => 'external number: ' . $this->saleOrder->external_number . ' - not found carrier or service code.',
    ]);
});

test('detect shipping fail - service code empty', function () {
    $this->dataJson['shipments'][0]['shipping_info']['service'] = '';
    $this->integrateLog->json = json_encode($this->dataJson);
    $this->integrateLog->save();

    $job = new DetectShippingMethodRedbubbleV2Job($this->saleOrder->id);
    $job->handle();

    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'store_id' => Store::STORE_REDBUBBLE,
        'shipping_method' => 'standard',
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => 0,
        'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
        'message' => 'external number: ' . $this->saleOrder->external_number . ' - not found carrier or service code.',
    ]);
});

test('detect shipping fail - integrate', function () {
    ShippingMethod::query()->delete();
    $job = new DetectShippingMethodRedbubbleV2Job($this->saleOrder->id);
    $job->handle();

    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'store_id' => Store::STORE_REDBUBBLE,
        'shipping_method' => 'standard',
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => 0,
        'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
        'message' => 'external number: ' . $this->saleOrder->external_number . ' - not found integrate shipping.',
    ]);
});

test('detect shipping fail - shipping method', function () {
    $this->shippingMethod[0]->delete();
    $job = new DetectShippingMethodRedbubbleV2Job($this->saleOrder->id);
    $job->handle();

    $this->assertDatabaseHas('sale_order', [
        'id' => $this->saleOrder->id,
        'store_id' => Store::STORE_REDBUBBLE,
        'shipping_method' => 'standard',
    ]);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $this->saleOrder->id,
        'user_id' => 0,
        'type' => SaleOrderHistory::DETECT_SHIPPING_METHOD_FAIL,
        'message' => 'external number: ' . $this->saleOrder->external_number . ' - not found integrate shipping.',
    ]);
});
