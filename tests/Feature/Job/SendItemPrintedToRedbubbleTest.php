<?php

use App\Jobs\SendItemPrintedToRedbubbleJob;
use App\Models\Employee;
use App\Models\IntegrateCallbackLog;
use App\Models\IntegrateLog;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Store;
use App\Models\Warehouse;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->wareHouse = Warehouse::factory()->create([
        'city' => 'HCM',
        'country' => 'VN',
        'state' => 'HCM',
    ]);
    $this->employee = Employee::factory([
        'warehouse_id' => $this->wareHouse->id
    ])->create();
    $this->store = Store::factory([
        'id' => Store::STORE_REDBUBBLE,
    ])->create();

    $this->saleOrder = SaleOrder::factory([
        'external_number' => faker::create()->userName(),
        'external_key' => faker::create()->userName(),
    ])->create();

    $this->saleOrderItem = SaleOrderItemBarcode::factory()->count(2)
        ->for(SaleOrderItem::factory([
            'order_id' => $this->saleOrder->id,
            'store_id' => $this->store->id,
            'warehouse_id' => $this->wareHouse->id,
            'quantity' => 2,
            'external_id' => faker::create()->userName(),
        ]), 'orderItem')
        ->create([
            //        'employee_print_id' => $this->employee->id,
            'created_at' => date('Y-m-d H:i:s'),
            'order_id' => $this->saleOrder->id,
            'label_id' => faker::create()->userName(),
            'warehouse_id' => $this->wareHouse->id,
        ]);
    $this->dataJson = '{"version": "2023-04-11","callback_uri":"https://webhook.site/1880e017c-ea85-4045-bc03-b38298816a89","message_id": "54336346843598745904323984502298445024","order_id": 18256702,"sale_datetime": "2018-07-10T14:41:32.000Z",  "address_info": {    "name": "Emmanuel Bojangles",    "street1": "271 Collins St",    "street2": "Level 3",    "city": "Melbourne",    "state": "VIC",    "postcode": "3000",    "country_code": "AU",    "phone": "+61 424 322 301",    "email": "<EMAIL>"  },  "order_properties": {},  "client": "Hub",  "shipments": [    {      "shipment_id": "S-ORDERID-A1B2",      "facility": "af-facility",      "shipping_info": {        "method": "express",        "carrier": "usps",        "service": "usps-first-class-package-service-method",        "expected_ship_date": "2023-05-05T04:23:33Z",        "latest_customer_promise_date": "2023-05-10T04:23:33Z"      },      "expected_packaging": [        {          "packaging_id": "RBPKGAU1001",          "quantity": "2"        }      ],      "expected_inserts": [        {          "type": "premium-care-instructions",          "quantity": "1"        }      ],      "items": [        {          "item_id": 71092674,          "quantity": 1,          "value": {            "price": 15.99,            "currency": "AUD"          },          "work_info": {            "title": "RB Test Pattern",            "url": "http://redbubble.com/works/155555",            "artist_name": "Matt Ryan",            "artist_url": "http://amoromniavincit.redbubble.com"          },          "product_info": {            "product": "baseball-cap",            "properties": {              "size": "medium",              "color": "black"            },            "product_info_id": "VXDS4"          },          "assets": [            {              "preview_url": "http://ih0.redbubble.net/image.709216515.2955/ssrco,baseball_cap,product,000000:44f0b734a5,front,square,1000x1000-bg,f8f8f8.jpg",              "url": "https://fulfillment-test-images.s3.amazonaws.com/dad_hat_tv_test_pattern.png",              "description": "baseball-cap",              "metadata": {                "ppi": 300              }            }          ]        }      ]    }  ]}';
    $this->intergateLog = IntegrateLog::factory()->create([
        'order_id' => $this->saleOrder->id,
        'store_id' => $this->store->id,
        'json' => $this->dataJson,
    ]);
});

test('sale order item not printed all', function () {
    $saleOrderItem = $this->saleOrderItem->first()->orderItem;
    $deductionJob = new SendItemPrintedToRedbubbleJob($saleOrderItem->id);
    $deductionJob->handle();

    $data = [
        'order_id' => $saleOrderItem->order_id ?? '',
        'event' => IntegrateCallbackLog::PRINTED_NOTIFY,
        'value' => $saleOrderItem->id,
        'store_id' => $saleOrderItem->store_id,
        'status' => IntegrateCallbackLog::STATUS_FAIL,
    ];
    $this->assertDatabaseMissing('callback_log', $data);
});

test('sale order item not printed all -  1 printed - 1 not printed', function () {
    $this->saleOrderItem->first()->employee_print_id = 1111;
    $this->saleOrderItem->first()->save();
    $saleOrderItem = $this->saleOrderItem->first()->orderItem;
    $deductionJob = new SendItemPrintedToRedbubbleJob($saleOrderItem->id);
    $deductionJob->handle();
    $data = [
        'order_id' => $saleOrderItem->order_id ?? '',
        'event' => IntegrateCallbackLog::PRINTED_NOTIFY,
        'value' => $saleOrderItem->id,
        'store_id' => $saleOrderItem->store_id,
        'status' => IntegrateCallbackLog::STATUS_FAIL,
    ];
    $this->assertDatabaseMissing('callback_log', $data);
});

test('not Intergate log', function () {
    $this->saleOrderItem->map(function ($item) {
        $item->employee_print_id = 1111;
        $item->save();
    });
    $this->saleOrderItem->first()->save();
    $saleOrderItem = $this->saleOrderItem->first()->orderItem;
    $this->intergateLog->delete();
    $deductionJob = new SendItemPrintedToRedbubbleJob($saleOrderItem->id);
    $deductionJob->handle();
    $this->assertDatabaseHas('integrate_callback_log', []);
});

test('has intergate log but not callback_url ', function () {
    $this->saleOrderItem->map(function ($item) {
        $item->employee_print_id = 1111;
        $item->save();
    });
    $this->saleOrderItem->first()->save();
    $saleOrderItem = $this->saleOrderItem->first()->orderItem;
    $this->intergateLog->json = '{"version": "2023-04-11","callback_uri":"","message_id": "54336346843598745904323984502298445024","order_id": 18256702,"sale_datetime": "2018-07-10T14:41:32.000Z",  "address_info": {"name": "Emmanuel Bojangles","street1": "271 Collins St","street2": "Level 3","city": "Melbourne","state": "VIC",    "postcode": "3000",    "country_code": "AU",    "phone": "+61 424 322 301",    "email": "<EMAIL>"  },  "order_properties": {},  "client": "Hub",  "shipments": [    {      "shipment_id": "S-ORDERID-A1B2",      "facility": "af-facility",      "shipping_info": {        "method": "express",        "carrier": "usps",        "service": "usps-first-class-package-service-method",        "expected_ship_date": "2023-05-05T04:23:33Z","latest_customer_promise_date": "2023-05-10T04:23:33Z"},"expected_packaging":[{"packaging_id": "RBPKGAU1001","quantity": "2" } ],"expected_inserts": [ { "type": "premium-care-instructions", "quantity": "1"        }      ], "items": [ {  "item_id": 71092674, "quantity": 1,  "value": { "price": 15.99,  "currency": "AUD" }, "work_info": {"title": "RB Test Pattern", "url": "http://redbubble.com/works/155555","artist_name": "Matt Ryan","artist_url": "http://amoromniavincit.redbubble.com" },"product_info": { "product": "baseball-cap","properties": {"size": "medium","color": "black"},"product_info_id": "VXDS4"},"assets": [{"preview_url": "http://ih0.redbubble.net/image.709216515.2955/ssrco,baseball_cap,product,000000:44f0b734a5,front,square,1000x1000-bg,f8f8f8.jpg","url": "https://fulfillment-test-images.s3.amazonaws.com/dad_hat_tv_test_pattern.png","description": "baseball-cap","metadata": {"ppi": 300}}]}]}]}';
    $this->intergateLog->save();
    $deductionJob = new SendItemPrintedToRedbubbleJob($saleOrderItem->id);
    $deductionJob->handle();
    $data = [
        'order_id' => $saleOrderItem->order_id ?? '',
        'event' => IntegrateCallbackLog::PRINTED_NOTIFY,
        'value' => $saleOrderItem->id,
        'store_id' => $saleOrderItem->store_id,
        'status' => IntegrateCallbackLog::STATUS_FAIL,
        'message' => "OrderId $saleOrderItem->order_id in storeId $saleOrderItem->store_id not found url callback " . IntegrateCallbackLog::PRINTED_NOTIFY
    ];
    $this->assertDatabaseHas('integrate_callback_log', $data);
});

test('send success', function () {
    Http::fake([
        'https://webhook.site/1880e017c-ea85-4045-bc03-b38298816a89' => Http::response([], 200)
    ]);
    $this->saleOrderItem->map(function ($item) {
        $item->employee_print_id = 1111;
        $item->save();
    });
    $this->saleOrderItem->first()->save();
    $saleOrderItem = $this->saleOrderItem->first()->orderItem;
    $deductionJob = new SendItemPrintedToRedbubbleJob($saleOrderItem->id);
    $deductionJob->handle();
    $data = [
        'order_id' => $saleOrderItem->order_id ?? '',
        'event' => IntegrateCallbackLog::PRINTED_NOTIFY,
        'value' => $saleOrderItem->id,
        'store_id' => $saleOrderItem->store_id,
        'status' => IntegrateCallbackLog::STATUS_SUCCESS,
    ];
    $this->assertDatabaseHas('integrate_callback_log', $data);
});
