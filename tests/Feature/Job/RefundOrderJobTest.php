<?php

namespace Tests\Feature\Job;

use App\Jobs\RefundOrderJob;
use App\Models\PeakShippingFee;
use App\Models\SaleOrder;
use App\Models\SaleOrderInsertCalculatePrice;
use App\Models\SaleOrderItemSurchargeFee;
use App\Models\SaleOrderSurchargeFee;
use App\Models\Store;
use App\Models\Wallet;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->store = Store::factory()->create(['username' => 'test', 'payment_terms' => Store::STORE_PREPAID]);
    $this->saleOrder = SaleOrder::factory([
        'payment_status' => SaleOrder::PAYMENT_STATUS_PAID,
        'order_total' => 20,
        'shipping_calculate' => 2,
        'order_status' => [SaleOrder::CANCELLED, SaleOrder::REJECTED][array_rand([SaleOrder::CANCELLED, SaleOrder::REJECTED])],
        'store_id' => $this->store->id,
        'shipment_id' => 1,
        'is_test' => SaleOrder::NOT_TEST,
        'encode_id' => '12313',
    ])->create();
    Wallet::factory(['store_id' => $this->saleOrder->store_id, 'balance' => 100])->create();
});

test('refund order - payment status is not paid', function () {
    $this->saleOrder->payment_status = [SaleOrder::PAYMENT_STATUS_PENDING, SaleOrder::PAYMENT_STATUS_PARTIAL_REFUNDED, SaleOrder::PAYMENT_STATUS_REFUNDED][array_rand([SaleOrder::PAYMENT_STATUS_PENDING, SaleOrder::PAYMENT_STATUS_PARTIAL_REFUNDED, SaleOrder::PAYMENT_STATUS_REFUNDED])];
    $this->saleOrder->save();
    $refundOrderJob = new RefundOrderJob($this->saleOrder->id);
    $refundOrderJob->handle();
    $wallet = Wallet::where('store_id', $this->saleOrder->store_id)->first();
    $this->assertEquals(100, $wallet->balance);
});

test('refund order - store is not prepaid', function () {
    $this->store->update(['payment_terms' => Store::STORE_POSTPAID]);
    $refundOrderJob = new RefundOrderJob($this->saleOrder->id);
    $refundOrderJob->handle();
    $wallet = Wallet::where('store_id', $this->saleOrder->store_id)->first();
    $this->assertEquals(100, $wallet->balance);
});

test('refund order - order is test', function () {
    $this->saleOrder->update(['is_test' => 1]);
    $refundOrderJob = new RefundOrderJob($this->saleOrder->id);
    $refundOrderJob->handle();
    $wallet = Wallet::where('store_id', $this->saleOrder->store_id)->first();
    $this->assertEquals(100, $wallet->balance);
});

test('refund order - no insert and surcharge', function () {
    $refundOrderJob = new RefundOrderJob($this->saleOrder->id);
    $refundOrderJob->handle();
    $wallet = Wallet::where('store_id', $this->saleOrder->store_id)->first();
    $this->saleOrder->refresh();
    $this->assertEquals(SaleOrder::PAYMENT_STATUS_REFUNDED, $this->saleOrder->payment_status);
    $this->assertEquals(120, $wallet->balance);
});

test('refund order - with surcharge fee', function () {
    SaleOrderSurchargeFee::factory()->count(3)->sequence(
        [
            'type' => PeakShippingFee::PEAK_SHIPPING_FEE,
            'order_id' => $this->saleOrder->id,
            'value' => 10,
        ], //peak shipping fee
        [
            'order_id' => $this->saleOrder->id,
            'value' => 5,
        ], //label
        [
            'order_id' => $this->saleOrder->id,
            'value' => 10,
        ], //tag and sticker
    )->create();
    SaleOrderItemSurchargeFee::factory()->count(4)->sequence(
        [
            'order_id' => $this->saleOrder->id,
            'value' => 10,
        ], //tiktok fee
        [
            'order_id' => $this->saleOrder->id,
            'value' => 5,
        ], // mug packaging fee
        [
            'order_id' => $this->saleOrder->id,
            'value' => 20,
        ], // hologram sticker fee
        [
            'order_id' => $this->saleOrder->id,
            'value' => 40,
        ], // plastic bag
    )->create();
    $refundOrderJob = new RefundOrderJob($this->saleOrder->id);
    $refundOrderJob->handle();
    $wallet = Wallet::where('store_id', $this->saleOrder->store_id)->first();
    $this->saleOrder->refresh();
    $this->assertEquals(SaleOrder::PAYMENT_STATUS_REFUNDED, $this->saleOrder->payment_status);
    $this->assertEquals(220, $wallet->balance);
});

test('refund order - with insert', function () {
    SaleOrderInsertCalculatePrice::factory()->count(3)->sequence(
        [
            'order_id' => $this->saleOrder->id,
            'amount_paid' => 5,
            'type' => 'gift_message',
        ],
        [
            'order_id' => $this->saleOrder->id,
            'amount_paid' => 10,
            'type' => 'packing_slip',
        ],
        [
            'order_id' => $this->saleOrder->id,
            'amount_paid' => 15,
            'type' => 'thankyou_card',
        ])->create();
    $refundOrderJob = new RefundOrderJob($this->saleOrder->id);
    $refundOrderJob->handle();
    $wallet = Wallet::where('store_id', $this->saleOrder->store_id)->first();
    $this->saleOrder->refresh();
    $this->assertEquals(SaleOrder::PAYMENT_STATUS_REFUNDED, $this->saleOrder->payment_status);
    $this->assertEquals(150, $wallet->balance);
});

test('refund order - partial refund', function () {
    $this->saleOrder->update(['order_status' => SaleOrder::STATUS_LATE_CANCELLED]);
    $refundOrderJob = new RefundOrderJob($this->saleOrder->id);
    $refundOrderJob->handle();
    $wallet = Wallet::where('store_id', $this->saleOrder->store_id)->first();
    $this->saleOrder->refresh();
    $this->assertEquals(SaleOrder::PAYMENT_STATUS_PARTIAL_REFUNDED, $this->saleOrder->payment_status);
    $this->assertEquals(102, $wallet->balance);
});

test('refund order - partial refund with peak shipping fee', function () {
    $this->saleOrder->update(['order_status' => SaleOrder::STATUS_LATE_CANCELLED]);
    SaleOrderSurchargeFee::factory()->count(1)->sequence(
        [
            'type' => PeakShippingFee::PEAK_SHIPPING_FEE,
            'order_id' => $this->saleOrder->id,
            'value' => 10,
        ], //peak shipping fee
    )->create();
    $refundOrderJob = new RefundOrderJob($this->saleOrder->id);
    $refundOrderJob->handle();
    $wallet = Wallet::where('store_id', $this->saleOrder->store_id)->first();
    $this->saleOrder->refresh();
    $this->assertEquals(SaleOrder::PAYMENT_STATUS_PARTIAL_REFUNDED, $this->saleOrder->payment_status);
    $this->assertEquals(112, $wallet->balance);
});
