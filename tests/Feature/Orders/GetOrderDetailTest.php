<?php

use App\Models\Store;
use App\Repositories\StoreRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->endpoint = '/api/sale-order/{id}';
});

// Kiểm tra 1 order không tồn tại
test('order not found', function () {

})->skip('Not written yet');

// Kiểm tra có thông tin 1 order hay không
test('has order', function () {

})->skip('Not written yet');
