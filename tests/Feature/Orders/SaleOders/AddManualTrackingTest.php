<?php

use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\Shipment;
use App\Models\ShippingCarrier;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->saleOrder = SaleOrder::factory()->create([
        'order_status' => SaleOrder::STATUS_IN_PRODUCTION,
        'store_id' => 1,
        'account_id' => 1
    ]);
    $this->shippingCarrier = ShippingCarrier::factory()->create([
        'code' => 'USPS'
    ]);
    $this->endpoint = 'api/sale-order/' . $this->saleOrder->id . '/manual-tracking';

    $this->params = [
        'carrier_code' => 'USPS',
        'tracking_number' => ************,
        'ship_date' => '2022-01-01',
        'price' => 20
    ];
});

// Validation
test('validate params', function () {
    $params = [
        [],
        [
            'carrier_code' => 'USPSSSS',
            'tracking_number' => ************,
            'ship_date' => 'xxxx',
            'price' => 'xxxx'
        ],
        [
            'carrier_code' => 'USPS',
            'tracking_number' => ************,
            'ship_date' => Carbon::now()->addDay(),
            'price' => 20
        ]
    ];

    foreach ($params as $key => $param) {
        $response = $this->post($this->endpoint, $param);

        $response->assertStatus(422);

        if ($key == 0) {
            expect(json_decode($response->getContent(), true))->toMatchArray([
                'message' => 'The given data was invalid.',
                'errors' => [
                    'carrier_code' => [
                        'The carrier code field is required.'
                    ],
                    'tracking_number' => [
                        'The tracking number field is required.'
                    ],
                    'ship_date' => [
                        'The ship date field is required.'
                    ]
                ]
            ]);
        } elseif ($key == 1) {
            expect(json_decode($response->getContent(), true))->toMatchArray([
                'message' => 'The given data was invalid.',
                'errors' => [
                    'carrier_code' => [
                        'The selected carrier code is invalid.'
                    ],
                    'ship_date' => [
                        'The ship date is not a valid date.'
                    ],
                    'price' => [
                        'The price must be a number.'
                    ]
                ]
            ]);
        } else {
            expect(json_decode($response->getContent(), true))->toMatchArray([
                'message' => 'The given data was invalid.',
                'errors' => [
                    'ship_date' => [
                        'The ship date must be a date before or equal to today.'
                    ]
                ]
            ]);
        }
    }
});

// order status invalid
test('status invalid', function () {
    $orderStatus = [
        SaleOrder::STATUS_CANCELLED,
        SaleOrder::STATUS_REJECT,
        SaleORder::STATUS_ON_HOLD,
        SaleOrder::DRAFT
    ];

    foreach ($orderStatus as $status) {
        $saleOrder = SaleOrder::factory()->create([
            'order_status' => $status
        ]);
        $endpoint = 'api/sale-order/' . $saleOrder->id . '/manual-tracking';

        $response = $this->post($endpoint, $this->params);
        $response->assertStatus(400);
        expect(json_decode($response->getContent(), true))->toMatchArray([
            'code' => 400,
            'success' => false,
            'message' => 'This order does not satisfy the condition!'
        ]);
    }
});

// success
test('success', function () {
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $this->assertEquals(SaleOrder::where('id', $this->saleOrder->id)->first()->order_status, SaleOrder::STATUS_SHIPPED);
    $this->assertEquals(SaleOrder::where('id', $this->saleOrder->id)->first()->shipment_id, Shipment::first()->id);
    $this->assertEquals(1, Shipment::count());
    $this->assertEquals(1, SaleOrderHistory::count());
});
