<?php

use App\Models\ShipmentManifest;
use App\Models\ShipmentManifestTracking;
use App\Models\ShippingCarrierService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Shipment;
use Illuminate\Support\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/manifest/list-tracking?';
    $this->params = [
        'limit' => '10',
        'page' => '1',
        'manifest_id' => '',
        'tracking_number' => ''
    ];
});

// Manifest invalid
test('Manifest invalid', function () {
    // input not manifest id
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['manifest_id'][0])->toMatchArray(['Manifest field is required']);

    // manifest id not in db
    $this->params['manifest_id'] = 123;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['manifest_id'][0])->toMatchArray(['Manifest not found!']);

    // manifest id not in warehouse select
    $dataManifest = ShipmentManifest::factory()->create([
        'warehouse_id' => 9999
    ]);
    $this->params['manifest_id'] = $dataManifest->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['manifest_id'][0])->toMatchArray(['Manifest not found!']);
});

// get tracking number manifest success
test('get tracking number manifest success!', function () {
    $data = ShipmentManifest::factory()->has(
        ShipmentManifestTracking::factory()->count(3)
            ->for(ShippingCarrierService::factory(), 'shippingService'),
        'shipmentManifestTrackings'
    )->create([
        'warehouse_id' => $this->warehouse->id
    ]);

    // get all tracking by manifest
    $this->params['manifest_id'] = $data->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes['data'])->toMatchArray(['total' => 3]);

    // search tracking number return 0 record
    $this->params['tracking_number'] = 9999999921312;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes['data'])->toMatchArray(['total' => 0]);

    // search tracking number return 1 record
    $this->params['tracking_number'] = $data->shipmentManifestTrackings[1]->tracking_number;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes['data'])->toMatchArray(['total' => 1]);
});

// get tracking has label over 1 day
test('get tracking has label over 1 day!', function () {
    $data = ShipmentManifest::factory()->has(
        ShipmentManifestTracking::factory()->count(3)
            ->for(ShippingCarrierService::factory(), 'shippingService'),
        'shipmentManifestTrackings'
    )->create([
        'warehouse_id' => $this->warehouse->id
    ]);
    $dataShipment = [];
    foreach ($data->shipmentManifestTrackings as $key => $tracking) {
        $dataShipment[] = [
            'tracking_number' => $tracking->tracking_number,
            'ship_date' => Carbon::now()
        ];
    }
    Shipment::insert($dataShipment);
    $shipment = Shipment::all();

    // get all tracking by manifest
    $this->params['manifest_id'] = $data->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes['data'])->toMatchArray(['total' => 3]);
    expect($dataRes['isHasLabelOverTime'])->toEqual(false);

    $shipment[0]->ship_date = Carbon::now()->subDays(3);
    $shipment[0]->save();
    // get all tracking by manifest
    $this->params['manifest_id'] = $data->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->get($this->endpoint . http_build_query($this->params));
    $response->assertStatus(200);
    $dataRes = json_decode($response->getContent(), true);
    expect($dataRes['data'])->toMatchArray(['total' => 3]);
    expect($dataRes['isHasLabelOverTime'])->toEqual(true);
});


