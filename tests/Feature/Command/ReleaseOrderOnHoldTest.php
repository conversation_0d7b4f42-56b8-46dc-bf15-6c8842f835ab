<?php

use App\Console\Commands\ReleaseOnHoldOrder;
use App\Models\SaleOrder;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderOnHold;
use App\Models\Store;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->store = Store::factory()->create();
    $this->user = User::factory()->create();
});

test('Release success: store is on hold, visua is on hold. Previous order status is new order', function () {
    $order = SaleOrder::factory()->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_TRUE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::STATUS_ON_HOLD, $order->order_status);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_TRUE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_FALSE,
    ]);
});

test('Release success: store is not on hold, visua is on hold. Previous order status is new order', function () {
    $order = SaleOrder::factory()->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::STATUS_ON_HOLD, $order->order_status);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_FALSE,
    ]);
});

test('Release success: store is not on hold, visua is not on hold. Previous order status is new order', function () {
    $order = SaleOrder::factory()->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_FALSE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::NEW_ORDER, $order->order_status);
    $this->assertDatabaseCount('sale_order_on_hold', 0);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $order->id,
        'message' => 'Order status automatically released from "on_hold".',
    ]);
});

test('Release success: store is on hold, visua is on hold. Previous order status is in_production', function () {
    $order = SaleOrder::factory()->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
        'order_production_at' => Carbon::now()->toDateString(),
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_TRUE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::STATUS_ON_HOLD, $order->order_status);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_TRUE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_FALSE,
    ]);
});

test('Release success: store is not on hold, visua is on hold. Previous order status is in production', function () {
    $order = SaleOrder::factory()->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
        'order_production_at' => Carbon::now()->toDateString(),
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::STATUS_ON_HOLD, $order->order_status);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_FALSE,
    ]);
});

test('Release success: store is not on hold, visua is not on hold. Previous order status is in production', function () {
    $order = SaleOrder::factory()->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
        'order_production_at' => Carbon::now()->toDateString(),
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_FALSE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::IN_PRODUCTION, $order->order_status);
    $this->assertDatabaseCount('sale_order_on_hold', 0);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $order->id,
        'message' => 'Order status automatically released from "on_hold".',
    ]);
});

test('Release success: store is on hold, visua is on hold. Previous order status is shipped', function () {
    $order = SaleOrder::factory()->has(SaleOrderItemBarcode::factory()->count(1)->sequence([
        'shipped_at' => Carbon::now()->toDateString(),
    ]), 'barcodeItems')->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
        'order_production_at' => Carbon::now()->toDateString(),
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_TRUE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::STATUS_ON_HOLD, $order->order_status);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_TRUE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_FALSE,
    ]);
});

test('Release success: store is not on hold, visua is on hold. Previous order status is shipped', function () {
    $order = SaleOrder::factory()->has(SaleOrderItemBarcode::factory()->count(1)->sequence([
        'shipped_at' => Carbon::now()->toDateString(),
    ]), 'barcodeItems')->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
        'order_production_at' => Carbon::now()->toDateString(),
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::STATUS_ON_HOLD, $order->order_status);
    $this->assertDatabaseHas('sale_order_on_hold', [
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold' => SaleOrderOnHold::IS_FALSE,
    ]);
});

test('Release success: store is not on hold, visua is not on hold. Previous order status is shipped', function () {
    $order = SaleOrder::factory()->has(SaleOrderItemBarcode::factory()->count(1)->sequence([
        'shipped_at' => Carbon::now()->toDateString(),
    ]), 'barcodeItems')->create([
        'store_id' => $this->store->id,
        'order_status' => SaleOrder::STATUS_ON_HOLD,
        'order_production_at' => Carbon::now()->toDateString(),
    ]);
    $saleOrderOnHold = SaleOrderOnHold::factory()->create([
        'order_id' => $order->id,
        'store_on_hold' => SaleOrderOnHold::IS_FALSE,
        'visua_on_hold' => SaleOrderOnHold::IS_FALSE,
        'manual_on_hold' => SaleOrderOnHold::IS_TRUE,
        'manual_on_hold_by' => $this->user->id,
        'release_on_hold_at' => Carbon::now()->toDateString(),
    ]);
    $releaseOnHold = new ReleaseOnHoldOrder();
    $releaseOnHold->handle();
    $order = SaleOrder::find($order->id);
    $this->assertEquals(SaleOrder::SHIPPED, $order->order_status);
    $this->assertDatabaseCount('sale_order_on_hold', 0);
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $order->id,
        'message' => 'Order status automatically released from "on_hold".',
    ]);
});
