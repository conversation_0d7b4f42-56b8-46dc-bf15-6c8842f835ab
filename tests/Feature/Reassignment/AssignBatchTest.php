<?php

use App\Models\BarcodePrinted;
use App\Models\Employee;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id]);
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/wips/assign-batch';
    $this->product = Product::factory()->createMany([
        ['sku' => 'UNPT2S03M', 'name' => 'Product 1'],
        ['sku' => 'UNGS2S03M', 'name' => 'Product 2'],
    ]);
    $this->barcodePrinted = BarcodePrinted::factory()->createMany(
        [
            [
                'style_sku' => 'UNPT',
                'warehouse_id' => $this->warehouse->id,
            ],
            [
                'style_sku' => 'UNGS',
                'warehouse_id' => $this->warehouse->id,
            ]
        ],
    );
    $this->saleOrder = $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'warehouse_id' => $this->warehouse->id,

    ])->count(1)
        ->sequence(fn ($sequence) => [
            'order_number' => '092724-SJ-S-0010' . $sequence->index
        ])->has(
            SaleOrderItem::factory()->count(3)
                ->sequence(fn ($sequence) => [
                    'quantity' => 1,
                    'product_sku' => $sequence->index < 2 ? $this->product[0]->sku : $this->product[1]->sku
                ])->state(function ($attributes, $order) {
                    return [
                        'warehouse_id' => $order->warehouse_id,
                    ];
                })
                ->has(SaleOrderItemBarcode::factory()
                    ->count(2)
                    ->sequence(fn ($sequence) => [
                        'barcode_printed_id' => $this->barcodePrinted[0]->id,
                    ])->state(fn ($attribute, $saleOrderItem) => [
                        'warehouse_id' => $saleOrderItem->warehouse_id,
                        'sku' => $saleOrderItem->sku,
                        'order_id' => $saleOrderItem->order_id,
                        'label_id' => $saleOrderItem->order->order_number . '-' . rand(1, 10000)
                    ]), 'images'),
            'items')
        ->create();
    $this->params = [
        'first_label_id' => $this->saleOrder[0]->items[0]->barcodes[0]->label_id,
        'last_label_id' => $this->saleOrder[0]->items[0]->barcodes[1]->label_id,
        'employee_id' => $this->employee->id,
        'warehouse_id' => $this->warehouse->id,
    ];
});

test('Reassign fail - Missing first_label_id, last_label_id, employee_id', function () {
    unset($this->params['first_label_id'], $this->params['last_label_id'], $this->params['employee_id']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'first_label_id' => [
                'The first label id field is required.'
            ],
            'last_label_id' => [
                'The last label id field is required.'
            ],
            'employee_id' => [
                'The employee id field is required.'
            ]
        ]
    ]);
});

test('Reassign fail - first label not exists in warehouse', function () {
    $this->params['first_label_id'] = SaleOrderItemBarcode::factory()->create([
        'label_id' => $this->saleOrder[0]->order_number . '-' . rand(1, 10000),
        'barcode_printed_id' => $this->barcodePrinted[0]->id,
        'warehouse_id' => $this->warehouse->id + 1,
    ])->label_id;
    $barcode = SaleOrderItemBarcode::query()->update(['employee_pull_id' => $this->employee->id]);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'First label id not found.',
    ]);
});

test('Reassign fail - first label is not printed yet', function () {
    $this->params['first_label_id'] = SaleOrderItemBarcode::factory()->create([
        'label_id' => $this->saleOrder[0]->order_number . '-' . rand(1, 10000),
        'warehouse_id' => $this->warehouse->id,
    ])->label_id;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'First label id not found.',
    ]);
});

test('Reassign fail - first label has been assigned to you', function () {
    $barcode = SaleOrderItemBarcode::query()->update(['employee_pull_id' => $this->employee->id]);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Barcode has been assigned to you',
    ]);
});

test('Reassign fail - first label and last label is incorrect: in wrong order', function () {
    $barcode = SaleOrderItemBarcode::query()->update(['employee_pull_id' => $this->employee->id + 1]);
    $this->params = [
        'first_label_id' => $this->saleOrder[0]->items[0]->barcodes[1]->label_id,
        'last_label_id' => $this->saleOrder[0]->items[0]->barcodes[0]->label_id,
        'employee_id' => $this->employee->id,
        'warehouse_id' => $this->warehouse->id,
    ];
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(500);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The first or the last SKU is incorrect',
    ]);
});

test('Reassign fail - first label and last label is incorrect: ot in same batch', function () {
    $barcode = SaleOrderItemBarcode::query()->update(['employee_pull_id' => $this->employee->id + 1]);
    SaleOrderItemBarcode::where('label_id', $this->saleOrder[0]->items[0]->barcodes[0]->label_id)->update(['barcode_printed_id' => $this->barcodePrinted[1]->id]);
    $this->params = [
        'first_label_id' => $this->saleOrder[0]->items[0]->barcodes[0]->label_id,
        'last_label_id' => $this->saleOrder[0]->items[0]->barcodes[1]->label_id,
        'employee_id' => $this->employee->id,
        'warehouse_id' => $this->warehouse->id,
    ];
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(500);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The first or the last SKU is incorrect',
    ]);
});

test('Reassign fail - Cannot reassign wip range being worked on by multiple employees', function () {
    SaleOrderItemBarcode::where('label_id', $this->saleOrder[0]->items[0]->barcodes[0]->label_id)->update(['employee_pull_id' => $this->employee->id + 1]);
    SaleOrderItemBarcode::where('label_id', $this->saleOrder[0]->items[0]->barcodes[1]->label_id)->update(['employee_pull_id' => $this->employee->id + 2]);
    $this->params = [
        'first_label_id' => $this->saleOrder[0]->items[0]->barcodes[0]->label_id,
        'last_label_id' => $this->saleOrder[0]->items[0]->barcodes[1]->label_id,
        'employee_id' => $this->employee->id,
        'warehouse_id' => $this->warehouse->id,
    ];
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(500);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Cannot reassign wip range being worked on by multiple employees',
    ]);
});

test('Reassign success', function () {
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
    ]);
    $barcode = SaleOrderItemBarcode::query()->update(['employee_pull_id' => $employee->id]);
    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(200);
    $this->assertDatabaseHas('barcode_assign', [
        'warehouse_id' => $this->warehouse->id,
        'employee_id' => $this->employee->id,
        'pre_employee_id' => $employee->id,
        'sku' => $this->saleOrder[0]->items[0]->barcodes[0]->label_id,
    ]);
    $this->assertDatabaseHas('barcode_assign', [
        'warehouse_id' => $this->warehouse->id,
        'employee_id' => $this->employee->id,
        'pre_employee_id' => $employee->id,
        'sku' => $this->saleOrder[0]->items[0]->barcodes[1]->label_id,
    ]);
});
