<?php
use App\Models\Location;

use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken] = createAccessToken();
    $this->access_token = $accessToken;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/warehouse';
    $this->params = [
        'city' => 'BURNSVILLE',
        'code' => 'TD',
        'color' => '"#CD3838"',
        'country' => 'AF',
        'from_name' => 'Test',
        'is_stock' => true,
        'name' => 'Dev test',
        'phone' => '0293450349',
        'state' => 'GHA',
        'street1' => '1371 Oakland',
        'street2' => '1372 Oakland',
        'time_zone' => 'America/Adak',
        'zip' => '55337-3933',
    ];
});


test('Create fail - Missing name, code', function () {
    unset($this->params['name'], $this->params['code']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'name' => [
            'The name field is required.'
        ],
        'code' => [
            'The code field is required.'
        ],
    ]);
});


test('Create fail - Name, code is invalid.', function () {
    $this->params['name'] = 'The code field is required.The code field is required.
    The code field is required.The code field is required.
    The code field is required.The code field is required.The code field is required.
    The code field is required.The code field is required.The code field is required.
    The code field is required.The code field is required.The code field is required.
    The code field is required.The code field is required.The code field is required.
    The code field is required.The code field is required.The code field is required.
    The code field is required.The code field is required.The code field is required.
    The code field is required.The code field is required.The code field is required.
    The code field is required.The code field is required.The code field is required.
    The code field is required.The code field is required.';
    $this->params['code'] = 'TEST LENGTH';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'name' => [
            'The name must not be greater than 250 characters.'
        ],
        'code' => [
            'The code must not be greater than 2 characters.'
        ],
    ]);
});

test('Create Success.', function () {
    $response = $this->post($this->endpoint, $this->params);
    $data = json_decode($response->getContent(), true);
    $response->assertStatus(201);
    expect($data['data'])->toMatchArray($this->params);
    $virtualLocationCount =  Location::where('warehouse_id', $data['data']['id'])
        ->where('type', Location::VIRTUAL_LOCATION)->count();
    $this->assertEquals($virtualLocationCount, 1);
    $pullingLocationCount = Location::where('warehouse_id', $data['data']['id'])
        ->where('type', Location::PULLING_SHELVES)->count();
    $this->assertEquals($pullingLocationCount, 1);
});


