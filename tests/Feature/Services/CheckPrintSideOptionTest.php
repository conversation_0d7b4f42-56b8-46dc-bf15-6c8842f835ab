<?php

use App\Http\Service\GetOrderService;

beforeEach(function () {
    $this->getOrderService = new GetOrderService();
});

test('Key invalid', function() {
    $inputs = [
        [
            (Object) [
                "names" => "PrintFiles.Front",
                "values" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "names" => "PrintFiles.Back",
                "values" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        [
            (Object) [
                "names" => "PrintFiles.Pocket",
                "values" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "names" => "PrintFiles.Back",
                "values" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        []
    ];

    foreach ($inputs as $input) {
        $output = $this->getOrderService->checkPrintSideOption($input);
        $this->assertEquals($output, 0);
    }
});

test('Value empty', function () {
    $inputs = [
        // value front empty
        [
            (Object) [
                "name" => "PrintFiles.Front",
                "value" => ""
            ],
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        // value pocket empty
        [
            (Object) [
                "name" => "PrintFiles.Pocket",
                "value" => ""
            ],
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        // value back empty
        [
            (Object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => ""
            ],
        ],
    ];

    foreach ($inputs as $key => $input) {
        $output = $this->getOrderService->checkPrintSideOption($input);
        if ($key < 2) {
            $this->assertEquals($output, 1);
        } else {
            $this->assertEquals($output, 0);
        }
    }
});

test ('Duplicate side', function () {
    $inputs = [
        [
            (Object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        [
            (Object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        [
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
    ];

    foreach ($inputs as $key => $input) {
        $output = $this->getOrderService->checkPrintSideOption($input);
        if ($key < 2) {
            $this->assertEquals($output, 0);
        } else {
            $this->assertEquals($output, 1);
        }
    }
});

test ('Valid 2 sides', function () {
    $inputs = [
        [
            (Object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        [
            (Object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ],
        [
            (Object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ],
        ]
    ];

    foreach ($inputs as $input) {
        $output = $this->getOrderService->checkPrintSideOption($input);
        $this->assertEquals($output, 2);
    }
});

test ('Valid 1 side', function () {
    $inputs = [
        [
            (Object) [
                "name" => "PrintFiles.Front",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ]
        ],
        [
            (Object) [
                "name" => "PrintFiles.Pocket",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ]
        ],
        [
            (Object) [
                "name" => "PrintFiles.Back",
                "value" => "https://api.shirtplatform.com/webservices/rest/public/product/ordered/1df7ff93-e4bb-42c2-911c-e1349e6cd9ed/7410299/elements/16125305/productionImage"
            ]
        ],
    ];

    foreach ($inputs as $key => $input) {
        $output = $this->getOrderService->checkPrintSideOption($input);
        if ($key < 2) {
            $this->assertEquals($output, 0);
        } else {
            $this->assertEquals($output, 1);
        }
    }
});
