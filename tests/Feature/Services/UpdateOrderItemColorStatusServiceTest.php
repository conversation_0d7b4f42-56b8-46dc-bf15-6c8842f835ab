<?php

use App\Models\PrintMethod;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Services\UpdateOrderItemColorStatusService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    SaleOrderItem::factory()->create([
        'id' => 1,
        'product_style_sku' => 'sku1',
    ]);
    ProductStyle::factory()->create([
        'id' => 1,
        'sku' => 'sku1',
    ]);

    SaleOrderItemImage::factory()->createMany([
        [
            'id' => 1,
            'order_item_id' => 1,
            'color_new' => SaleOrderItemImage::COLOR_BLACK,
            'print_side' => '0'
        ],
        [
            'id' => 2,
            'order_item_id' => 1,
            'color_new' => SaleOrderItemImage::COLOR_BLACK,
            'print_side' => '1'
        ],
    ]);

    ProductPrintSide::factory()->count(10)->sequence(
        fn ($sequence) => [
            'id' => $sequence->index + 1,
            'code' => $sequence->index,
            'name' => 'print side ' . $sequence->index,
        ]
    )->create();

    ProductPrintArea::factory()->count(10)->sequence(
        fn ($sequence) => [
            'id' => $sequence->index + 1,
            'name' => 'print side ' . $sequence->index,
            'product_style_id' => 1,
            'print_method' => $sequence->index < 3 ? PrintMethod::DTG : PrintMethod::DTF,
        ]
    )->create();

    $this->attempts = 1;
    $this->maxTries = 3;
});

// expect data success tất cả các mặt in DTG đều là màu đen
test('check service success', function () {
    SaleOrderItemImage::factory()->createMany([
        [
            'id' => 3,
            'order_item_id' => 1,
            'color_new' => SaleOrderItemImage::COLOR_ERROR,
            'print_side' => '3'
        ],
        [
            'id' => 4,
            'order_item_id' => 1,
            'color_new' => SaleOrderItemImage::COLOR_WHITE,
            'print_side' => '4'
        ],
        [
            'id' => 5,
            'order_item_id' => 1,
            'color_new' => SaleOrderItemImage::COLOR_MIX,
            'print_side' => '5'
        ],
    ]);
    $service = new UpdateOrderItemColorStatusService(1, $this->attempts, $this->maxTries);
    $service->handle();
    $count = SaleOrderItem::where('ink_color', SaleOrderItemImage::COLOR_BLACK)->where('ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)->count();

    expect($count)->toEqual(1);
})->skip();

// Trong các mặt DTG có mặt color_new error
test('has print area DTG detect color error', function () {
    SaleOrderItemImage::factory()->createMany([
        [
            'id' => 3,
            'order_item_id' => 1,
            'color_new' => SaleOrderItemImage::COLOR_ERROR,
            'print_side' => '2'
        ]
    ]);
    $service = new UpdateOrderItemColorStatusService(1, $this->attempts, $this->maxTries);
    $this->expectException(\Exception::class);
    $this->expectExceptionMessage("failItems variable > 0");
    $service->handle();
    $count = SaleOrderItem::where('ink_color', SaleOrderItemImage::COLOR_BLACK)->where('ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)->count();
    expect($count)->toEqual(0);
})->skip();

// Trong các mặt DTG có mặt in màu trắng
test('has print area DTG color white', function () {
    SaleOrderItemImage::factory()->createMany([
        [
            'id' => 3,
            'order_item_id' => 1,
            'color_new' => SaleOrderItemImage::COLOR_WHITE,
            'print_side' => '2'
        ]
    ]);
    $service = new UpdateOrderItemColorStatusService(1, $this->attempts, $this->maxTries);
    $service->handle();
    $count = SaleOrderItem::where('ink_color', 0)->where('ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)->count();
    expect($count)->toEqual(1);
})->skip();

// Trong các mặt DTG có mặt in màu mix
test('has print area DTG color mix', function () {
    SaleOrderItemImage::factory()->createMany([
        [
            'id' => 3,
            'order_item_id' => 1,
            'color_new' => SaleOrderItemImage::COLOR_MIX,
            'print_side' => '2'
        ]
    ]);
    $service = new UpdateOrderItemColorStatusService(1, $this->attempts, $this->maxTries);
    $service->handle();
    $count = SaleOrderItem::where('ink_color', 0)->where('ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)->count();
    expect($count)->toEqual(1);
})->skip();

