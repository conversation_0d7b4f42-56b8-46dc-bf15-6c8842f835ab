<?php

use App\Http\Service\ShipstationService;
use App\Jobs\SyncTrackingToShipStation;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\Shipment;
use Mockery\MockInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->mock('overload:' . ShipstationService::class, function (MockInterface $mock) {
        $mock->shouldReceive('markShipped')->andReturnUsing(
            function ($orderId, $carrierCode, $trackingNumber) {
                if ($orderId == 1) {
                    $result = true;
                } else {
                    $result = false;
                }
                return $result;
            }

        );
        $mock->shouldReceive('getDataByOrderNumber')->andReturnUsing(
            function ($page, $orderNumber) {
                if ($orderNumber < 4) {
                    $result = (object) [
                        'orders' => [
                            (object) [
                                'orderId' => $orderNumber,
                                'orderNumber' => $orderNumber,
                                'orderDate' => '',
                            ]
                        ]
                    ];
                } else {
                    $result = (object) [
                        'orders' => []
                    ];
                }
                return $result;
            }

        );
        $mock->shouldReceive('getFulfillments')->andReturnUsing(
            function ($params) {
                if (($params['orderNumber'] == 1 && $params['trackingNumber'] == 'tracking1')
                || ($params['orderNumber'] == 2 && $params['trackingNumber'] == 'tracking2')) {
                    $result = [
                        "fulfillments" => [],
                        "total" => 0,
                        "page" => 1,
                        "pages" => 0
                    ];
                } else {
                    $result = [
                        "fulfillments" => [],
                        "total" => 1,
                        "page" => 1,
                        "pages" => 0
                    ];
                }
                return (object) $result;
            }
        );
        $mock->shouldReceive('getLogOrder')->andReturnUsing(
            function ($externalNumber) {
                if ($externalNumber == 1) {
                    return (object)[
                        "external_id" => 1,
                    ];
                } else {
                    return null;
                }
            }
        );
    });
    SaleOrder::factory()->createMany([
        [
            'id' => 1,
            'account_id' => 1,
            'external_id' => 1,
            'external_number' => 1,
        ],
        [
            'id' => 2,
            'account_id' => 1,
            'external_id' => 2,
            'external_number' => 2,
        ],
        [
            'id' => 3,
            'account_id' => 1,
            'external_id' => 3,
            'external_number' => 3,
        ],
        [
            'id' => 4,
            'account_id' => 1,
            'external_id' => 4,
            'external_number' => 4,
        ],
        [
            'id' => 5,
            'account_id' => 5,
            'external_id' => 5,
            'external_number' => 5,
        ],
        
    ]);
    Shipment::factory()->createMany([
        [
            'id' => 1,
            'order_id' => 1,
            'tracking_number' => 'tracking1',
        ],
        [
            'id' => 2,
            'order_id' => 2,
            'tracking_number' => 'tracking2',
        ],
        [
            'id' => 3,
            'order_id' => 3,
            'tracking_number' => 'tracking3',
        ],
        [
            'id' => 4,
            'order_id' => 4,
            'tracking_number' => 'tracking4',
        ],
        [
            'id' => 5,
            'order_id' => 5,
            'tracking_number' => 'tracking5',
        ],
    ]);
    SaleOrderAccount::factory()->create([
        'id' => 1,
    ]);
});

test('mark shipped success: has log shipstation', function () {
    // mock l
    $job = new SyncTrackingToShipStation(1);
    $result = $job->pushTracking();
    expect($result)->toEqual([
            'message' => "Mark as shipped 1",
            'status' => true
    ]);
});
test('mark shipped success: not has log shipstation', function () {
    $job = new SyncTrackingToShipStation(1);
    $result = $job->pushTracking();
    expect($result)->toEqual([
            'message' => "Mark as shipped 1",
            'status' => true
    ]);
});
test('not mark as shipped', function () {
    $job = new SyncTrackingToShipStation(2);
    $result = $job->pushTracking();
    expect($result)->toEqual([
            'message' => "Not mark as shipped 2",
            'status' => false
    ]);
});
test('Tracking is synced', function () {
    $job = new SyncTrackingToShipStation(3);
    $result = $job->pushTracking();
    expect($result)->toEqual([
            'message' => "Tracking is synced",
            'status' => true
    ]);
});
test('Order not exist on shipstation', function () {
    $job = new SyncTrackingToShipStation(4);
    $result = $job->pushTracking();
    expect($result)->toEqual([
            'message' => "Order not exist on shipstation",
            'status' => false
    ]);
});
test('Account not found', function () {
    $job = new SyncTrackingToShipStation(5);
    $result = $job->pushTracking();
    expect($result)->toEqual([
            'message' => "Account not found",
            'status' => false
    ]);
});
test('Shipment not found', function () {
    $job = new SyncTrackingToShipStation(9);
    $result = $job->pushTracking();
    expect($result)->toEqual([
            'message' => "Shipment not found",
            'status' => false
    ]);
});
