<?php

use App\Repositories\LabelRepository;

use App\Models\WeightCubic;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->labelRepo   = new LabelRepository();
    $this->weightCubic = WeightCubic::factory()->createMany([
        [
            'weight_start' => 16,
            'weight_end'   => 32,
            'cubic'        => "15x11x1",
            'name_cubic'   => "10",
        ],
        [
            'weight_start' => 32,
            'weight_end'   => 44,
            'cubic'        => "7x5x14",
            'name_cubic'   => "30",
        ],
        [
            'weight_start' => 44,
            'weight_end'   => 60,
            'cubic'        => "12x3x15",
            'name_cubic'   => "40",
        ]
    ]);
});

// input khong nam trong khoang nao cua cubic -output [0,0,0]
test("cubic input '' - output data cubic [0,0,0]", function () {
    $data = $this->labelRepo->getDataCubicSaleOrder('', $this->weightCubic);
    expect($data)->toEqual([0,0,0]);
});

// input khong nam trong khoang nao cua cubic -output [0,0,0]
test("cubic input null - output data cubic [0,0,0]", function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(null, $this->weightCubic);
    expect($data)->toEqual([0,0,0]);
});

// input khong nam trong khoang nao cua cubic -output [0,0,0]
test("cubic input has value - output data cubic [0,0,0]", function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(0, $this->weightCubic);
    expect($data)->toEqual([0,0,0]);
});


// output mảng 3 giá trị vd:[11, 5, 15]
test('data cubic success - weight 20 - cubic 10', function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(20, $this->weightCubic);
    expect($data)->toEqual([15, 11, 1]);
});

// output mảng 3 giá trị vd:[0, 0, 0]
test('data cubic success - weight 80 - not cubic', function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(80, $this->weightCubic);
    expect($data)->toEqual([0, 0, 0]);
});

// output mảng 3 giá trị vd:[12, 3, 15]
test('data cubic success - weight 60 - cubic 40', function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(60, $this->weightCubic);
    expect($data)->toEqual([12, 3, 15]);
});

// output mảng 3 giá trị vd:[12, 3, 15]
test('data cubic success - weight 44 - cubic 40', function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(44, $this->weightCubic);
    expect($data)->toEqual([12, 3, 15]);
});

// output mảng 3 giá trị vd:[7, 5, 14]
test('data cubic success - weight 32 - cubic 30', function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(32, $this->weightCubic);
    expect($data)->toEqual([7, 5, 14]);
});

// output mảng 3 giá trị vd:[15, 11, 1]
test('data cubic success - weight 16 - cubic 10', function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(16, $this->weightCubic);
    expect($data)->toEqual([15, 11, 1]);
});

// output mảng 3 giá trị vd:[7, 5, 14]
test('data cubic success - weight 35 - cubic 30', function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(35, $this->weightCubic);
    expect($data)->toEqual([7, 5, 14]);
});

// output mảng 3 giá trị vd:[7, 5, 14]
test('data cubic success - weight 50 - cubic 40', function () {
    $data = $this->labelRepo->getDataCubicSaleOrder(50, $this->weightCubic);
    expect($data)->toEqual([12, 3, 15]);
});
