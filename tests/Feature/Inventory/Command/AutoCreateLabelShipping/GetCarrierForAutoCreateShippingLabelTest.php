<?php

use App\Models\SaleOrder;
use App\Models\ShippingCarrier;
use App\Models\ShippingMethod;
use App\Models\Store;
use App\Repositories\LabelRepository;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);
beforeEach(function () {
    $this->labelRepo = new LabelRepository();
    $this->store = Store::factory()->create();
    $this->carrier = ShippingCarrier::factory()->createMany([
        [
            'name' => faker::create()->userName(),
            'code' => 'USPS',
        ],
        [
            'name' => faker::create()->userName(),
            'code' => 'AsendiaUsa',
        ],
        [
            'name' => faker::create()->userName(),
            'code' => 'DhlEcs',
        ],
        [
            'name' => faker::create()->userName(),
            'code' => 'globegistics',
        ]
    ]);
    $this->shippingMethod = ShippingMethod::factory()->createMany([
        [
            'api_shipping_method' => SaleOrder::SHIPPING_METHOD_STANDARD,
            'carrier_id' => $this->carrier[0]->id,
        ],
        [
            'api_shipping_method' => SaleOrder::SHIPPING_METHOD_PRIORITY,
            'carrier_id' => $this->carrier[0]->id,
        ],
        [
            'api_shipping_method' => SaleOrder::SHIPPING_METHOD_EXPRESS,
            'carrier_id' => $this->carrier[0]->id,
        ],
        [
            'api_shipping_method' => SaleOrder::SHIPPING_METHOD_STANDARD,
            'carrier_id' => $this->carrier[1]->id,
        ],
        [
            'api_shipping_method' => SaleOrder::SHIPPING_METHOD_EXPRESS,
            'carrier_id' => $this->carrier[1]->id,
        ],
        [
            'api_shipping_method' => 'DHL123',
            'carrier_id' => $this->carrier[2]->id,
            'store_id' => $this->store->id,
        ],
        [
            'api_shipping_method' => 'DHL1234',
            'carrier_id' => $this->carrier[2]->id,
            'store_id' => $this->store->id,
        ],
        [
            'api_shipping_method' => 'DHL123',
            'carrier_id' => $this->carrier[2]->id,
            'store_id' => 9999,
        ],
        [
            'api_shipping_method' => SaleOrder::SHIPPING_METHOD_STANDARD,
            'carrier_id' => $this->carrier[2]->id,
        ],
        [
            'api_shipping_method' => SaleOrder::SHIPPING_METHOD_EXPRESS,
            'carrier_id' => $this->carrier[2]->id,
        ],
        [
            'api_shipping_method' => SaleOrder::SHIPPING_METHOD_PRIORITY,
            'carrier_id' => $this->carrier[0]->id,
            'store_id' => $this->store->id,
        ],
    ]);
});
// get data with carrier domestic
test('get data with carrier domestic', function () {
    //standard
    $carrierIDStandard = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, SaleOrder::SHIPPING_METHOD_STANDARD, $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierIDStandard)->toEqual($this->carrier[0]->id);
    //priority
    $carrierIDPriority = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, SaleOrder::SHIPPING_METHOD_PRIORITY, $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierIDPriority)->toEqual($this->carrier[0]->id);
    //express
    $carrierIDExpress = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, SaleOrder::SHIPPING_METHOD_EXPRESS, $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierIDExpress)->toEqual($this->carrier[0]->id);
    // other
    $carrierIDOther = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, 'other', $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierIDOther)->toEqual(null);
    //standard
    $carrierIDStandard = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, SaleOrder::SHIPPING_METHOD_STANDARD, $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierIDStandard)->toEqual($this->carrier[0]->id);
    //priority
    $carrierIDPriority = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, SaleOrder::SHIPPING_METHOD_PRIORITY, $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierIDPriority)->toEqual($this->carrier[0]->id);
    //express
    $carrierIDExpress = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, SaleOrder::SHIPPING_METHOD_EXPRESS, $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierIDExpress)->toEqual($this->carrier[0]->id);
    // other
    $carrierIDOther = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, 'other', $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierIDOther)->toEqual(null);
});
// get data with carrier international
test('get data with carrier international', function () {
    //standard
    $carrierIDStandard = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, SaleOrder::SHIPPING_METHOD_STANDARD, $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierIDStandard)->toEqual($this->carrier[1]->id);
    //priority
    $carrierIDPriority = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, SaleOrder::SHIPPING_METHOD_PRIORITY, $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierIDPriority)->toEqual($this->carrier[0]->id);
    //express
    $carrierIDExpress = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, SaleOrder::SHIPPING_METHOD_EXPRESS, $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierIDExpress)->toEqual($this->carrier[1]->id);
    // other
    $carrierIDOther = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, 'other', $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierIDOther)->toEqual(null);
    //standard
    $carrierIDStandard = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, SaleOrder::SHIPPING_METHOD_STANDARD, $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierIDStandard)->toEqual($this->carrier[1]->id);
    //priority
    $carrierIDPriority = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, SaleOrder::SHIPPING_METHOD_PRIORITY, $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierIDPriority)->toEqual($this->carrier[0]->id);
    //express
    $carrierIDExpress = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, SaleOrder::SHIPPING_METHOD_EXPRESS, $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierIDExpress)->toEqual($this->carrier[1]->id);
    // other
    $carrierIDOther = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, 'other', $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierIDOther)->toEqual(null);
    //priority - store not in shipping method
    $carrierIDPriority = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, SaleOrder::SHIPPING_METHOD_PRIORITY, $this->carrier, $this->shippingMethod, 99999, false, false, false);
    expect($carrierIDPriority)->toEqual(null);
});
// get data with shipping of store
test('get data with shipping of store', function () {
    //shipping carrier not of store domestic
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, $this->shippingMethod[6]->api_shipping_method, $this->carrier, $this->shippingMethod, 12312, false, false, false);
    expect($carrierID)->toEqual(null);
    //shipping carrier not of store international
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, $this->shippingMethod[6]->api_shipping_method, $this->carrier, $this->shippingMethod, 12312, false, false, false);
    expect($carrierID)->toEqual(null);
    //shipping carrier of store domestic
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, $this->shippingMethod[6]->api_shipping_method, $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierID)->toEqual($this->shippingMethod[6]->carrier_id);
    //shipping carrier of store International
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, $this->shippingMethod[6]->api_shipping_method, $this->carrier, $this->shippingMethod, $this->store->id, false, false, false);
    expect($carrierID)->toEqual($this->shippingMethod[6]->carrier_id);
    //shipping carrier not of store domestic
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(true, $this->shippingMethod[6]->api_shipping_method, $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierID)->toEqual(null);
    //shipping carrier not of store international
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(false, $this->shippingMethod[6]->api_shipping_method, $this->carrier, $this->shippingMethod, null, false, false, false);
    expect($carrierID)->toEqual(null);
});
// get data with shipping of store
test('get carrier with hard goods', function () {
    // noi dia, don cung, khong co apikey, < 1lb
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, false, 10);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // noi dia, don cung, khong co apikey, >= 1lb
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, false, 16);
    expect($carrierID)->toEqual($this->carrier[2]->id);

    // quoc te, don cung, khong co apikey
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        false, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, false, 10);
    expect($carrierID)->toEqual($this->carrier[1]->id);

    // noi dia, don cung, khong co apikey, priority, < 1lb
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[1]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, false, 10);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // noi dia, don cung, khong co apikey, priority, > 1lb
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[1]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, false, 17);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // quoc te, don cung, khong co apikey
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        false, $this->shippingMethod[1]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, false);
    expect($carrierID)->toEqual($this->carrier[1]->id);

    // noi dia, don cung, khong co apikey, co store, < 1lb
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, $this->store->id, true, false, false, 8);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // noi dia, don cung, khong co apikey, co store, > 1lb
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, $this->store->id, true, false, false, 20);
    expect($carrierID)->toEqual($this->carrier[2]->id);

    // quoc te, don cung, khong co apikey, co store
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        false, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, $this->store->id, true, false, false);
    expect($carrierID)->toEqual($this->carrier[1]->id);

    // noi dia, don cung, co apikey, < 1lb
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, true, false, 8);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // noi dia, don cung, co apikey, < 1lb
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, true, false, 17);
    expect($carrierID)->toEqual($this->carrier[2]->id);

    // quoc te, don cung,co apikey
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        false, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, true, false);
    expect($carrierID)->toEqual($this->carrier[1]->id);
});
// get data with shipping of store
test('get carrier with hard goods - store use USPS', function () {
    // noi dia, don cung, khong co apikey, < 1lbs
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method, $this->carrier, $this->shippingMethod, 12312, true, false, true, 10);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // noi dia, don cung, khong co apikey, >= 1lbs
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method, $this->carrier, $this->shippingMethod, 12312, true, false, true, 18);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // quoc te, don cung, khong co apikey
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        false, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, true, 16);
    expect($carrierID)->toEqual($this->carrier[1]->id);

    // noi dia, don cung, khong co apikey, priority, < 1lbs
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[1]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, true, 10);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // noi dia, don cung, khong co apikey, priority, > 1lbs
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[1]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, true, 16);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // quoc te, don cung, khong co apikey
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        false, $this->shippingMethod[1]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, false, true, 12);
    expect($carrierID)->toEqual($this->carrier[1]->id);

    // noi dia, don cung, khong co apikey, co store, < 1lbs
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, $this->store->id, true, false, true, 10);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // noi dia, don cung, khong co apikey, co store, > 1lbs
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, $this->store->id, true, false, true, 16);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // noi dia, don cung, khong co apikey, co store
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, $this->store->id, true, false, true);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // quoc te, don cung, khong co apikey, co store
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        false, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, $this->store->id, true, false, true);
    expect($carrierID)->toEqual($this->carrier[1]->id);

    // noi dia, don cung, co apikey
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        true, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, true, true);
    expect($carrierID)->toEqual($this->carrier[0]->id);

    // quoc te, don cung, co apikey
    $carrierID = $this->labelRepo->getCarrierForAutoCreateShippingLabel(
        false, $this->shippingMethod[0]->api_shipping_method,
        $this->carrier, $this->shippingMethod, 12312, true, true, true);
    expect($carrierID)->toEqual($this->carrier[1]->id);
});
