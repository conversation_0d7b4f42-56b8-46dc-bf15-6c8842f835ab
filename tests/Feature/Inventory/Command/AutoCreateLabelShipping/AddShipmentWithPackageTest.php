<?php

use App\Http\Service\GetOrderService;
use App\Models\SaleOrder;
use App\Models\Tag;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\RedbubblePacking;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->labelRepo = new \App\Repositories\LabelRepository();

    $this->RedbubblePacking = RedbubblePacking::factory()->createMany([
        [
            'condition_package' => '= 1',
            'packaging_active' => 1,
            'rb_sku' => 'RBPKGUS1028'
        ],
        [
            'condition_package' => '>= 2 <= 5',
            'packaging_active' => 1,
            'rb_sku' => 'RBPKGUS1023'
        ],
        [
            'condition_package' => '> 5',
            'packaging_active' => 1,
            'rb_sku' => 'RBPKGUS1001'
        ],
        [
            'condition_package' => '>= 2 <= 5',
            'packaging_active' => 0,
            'rb_sku' => 'RBPKGUS10223'
        ],
    ]);

});

test('get rb sku with quantity = 1', function () {
    $condition = $this->labelRepo->getConditonPacking();
    $quantity = 1;
    $returnSku = $this->labelRepo->addShipmentWithPackage($quantity,$condition);
    expect($returnSku)->toEqual('RBPKGUS1028');
});

test('get rb sku with quantity >= 2 <= 5', function () {
    $condition = $this->labelRepo->getConditonPacking();

    $quantity = 2;
    $returnSku = $this->labelRepo->addShipmentWithPackage($quantity,$condition);
    expect($returnSku)->toEqual('RBPKGUS1023');

    $quantity = 5;
    $returnSku = $this->labelRepo->addShipmentWithPackage($quantity,$condition);
    expect($returnSku)->toEqual('RBPKGUS1023');
});

test('get rb sku with quantity > 5', function () {
    $condition = $this->labelRepo->getConditonPacking();

    $quantity = 6;
    $returnSku = $this->labelRepo->addShipmentWithPackage($quantity,$condition);
    expect($returnSku)->toEqual('RBPKGUS1001');

    $quantity = 8;
    $returnSku = $this->labelRepo->addShipmentWithPackage($quantity,$condition);
    expect($returnSku)->toEqual('RBPKGUS1001');
});
