<?php

use App\Models\Product;
use App\Models\ProductSize;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\Store;
use App\Repositories\LabelRepository;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->labelRepo = new LabelRepository();
    $this->productStyles = ProductStyle::factory()->count(4)->state(new Sequence(
        ['sku' => 'UNGH', 'type' => 'Fleece'],
        ['sku' => 'UNPT', 'type' => 'Tee'],
        ['sku' => 'YOGH', 'type' => 'Fleece'],
        ['sku' => 'UNGT', 'type' => 'Tee'],
    ))->create();
    $this->productSize = ProductSize::factory()->count(4)->state(new Sequence(
        ['sku' => '5XL', 'name' => '5XL'],
        ['sku' => '00L', 'name' => 'L'],
        ['sku' => '0XL', 'name' => 'XL'],
        ['sku' => '00S', 'name' => 'S'],
    ))->create();
    $this->product = Product::factory()->createMany(
        [
            [
                'sku' => 'shoes',
                'style' => 'Fleece',
                'weight_single' => 8,
                'weight_multiple' => 10
            ],
            [
                'sku' => 'shoes',
                'style' => 'Fleece',
                'weight_single' => 8,
                'weight_multiple' => 10
            ]
        ],
    );
    $this->store = Store::factory()->create();
    $this->saleOrder = SaleOrder::factory()->has(
        SaleOrderItem::factory()->count(2)->state(new Sequence([
            'product_style_sku' => $this->productStyles->first()->sku,
            'product_size_sku' => $this->productSize->first()->sku,
            'product_id' => $this->product->first()->id,
            'quantity' => 1
        ], [
            'product_style_sku' => $this->productStyles->last()->sku,
            'product_size_sku' => $this->productSize->last()->sku,
            'product_id' => $this->product->last()->id,
            'quantity' => 1
        ])), 'items')->for(Store::factory())->create([
            'order_status' => 'in_production',
            'external_number' => '071722-SJ-M-000368',
            'order_quantity' => 2,
            'store_id' => $this->store->id
        ]);
});

// Not found data product.
test('Not found data product', function () {
    Product::query()->delete();
    $this->labelRepo->dataCustomsInfoV2($this->saleOrder, true);
})->throws('Not found data product.');

// Not found data product weight
test('Not found data product weight multiple', function () {
    $this->product->first()->weight_single = null;
    $this->product->first()->weight_multiple = null;
    $this->product->first()->save();
    $saleOrder = SaleOrder::with('items.product', 'items.productStyle', 'items.productSize')->where('id', $this->saleOrder->id)->first();
    $this->labelRepo->dataCustomsInfoV2($saleOrder);
})->throws('Weight not defined for product SKU shoes (Fleece 5XL) for multiple item orders.');

// Not found data product weight - not auto
test('Not found data product weight single', function () {
    $this->product->first()->weight_single = null;
    $this->product->first()->weight_multiple = null;
    $this->product->first()->save();
    SaleOrder::where('id', $this->saleOrder->id)->update(['order_quantity' => 1]);
    $saleOrder = SaleOrder::with('items.product', 'items.productStyle', 'items.productSize')->where('id', $this->saleOrder->id)->first();
    $this->labelRepo->dataCustomsInfoV2($saleOrder);
})->throws('Weight not defined for product SKU shoes (Fleece 5XL) for single item order.');

// tạo data customs info thành công multiple - auto
test('create data customs info success multiple', function () {
    $dataResult = [
        'customs_certify' => true,
        'customs_signer' => $this->saleOrder->store->name,
        'contents_type' => LabelRepository::CUSTOMS_TYPE,
        'restriction_type' => 'none',
        'contents_explanation' => '',
        'eel_pfc' => LabelRepository::EEL_PFC,
        'non_delivery_option ' => LabelRepository::NON_DELIVERY_OPTION,
        'customs_items' => [
            // Fleece - 5XL - UNGH
            ['description' => 'Fleece' . ' - ' . 'UNGH',
                'quantity' => 1,
                'weight' => 10,
                'value' => 10,
                'hs_tariff_number' => LabelRepository::CUSTOMS_DEFAULT,
                'origin_country' => LabelRepository::DEFAULT_ORIGIN_COUNTRY,
                'code' => 'UNGH',
            ],
            // Tee - S - UNGT
            ['description' => 'Tee' . ' - ' . 'UNGT',
                'quantity' => 1,
                'weight' => 10,
                'value' => 10,
                'hs_tariff_number' => LabelRepository::CUSTOMS_DEFAULT,
                'origin_country' => LabelRepository::DEFAULT_ORIGIN_COUNTRY,
                'code' => 'UNGT',
            ]
        ]
    ];
    $data = $this->labelRepo->dataCustomsInfoV2($this->saleOrder, true);
    expect($data)->toEqual($dataResult);
});

// tạo data customs info thành công signle
test('create data customs info success signle', function () {
    $saleOrder = SaleOrder::factory()->has(
        SaleOrderItem::factory()->count(1)->state(new Sequence([
            'product_style_sku' => $this->productStyles->first()->sku,
            'product_size_sku' => $this->productSize->first()->sku,
            'product_id' => $this->product->first()->id,
            'quantity' => 1
        ])), 'items')->for(Store::factory())->create([
            'order_status' => 'in_production',
            'external_number' => '071722-SJ-M-000368',
            'order_quantity' => 1,
            'store_id' => $this->store->id
        ]);
    $dataResult = [
        'customs_certify' => true,
        'customs_signer' => $this->store->name,
        'contents_type' => LabelRepository::CUSTOMS_TYPE,
        'restriction_type' => 'none',
        'contents_explanation' => '',
        'eel_pfc' => LabelRepository::EEL_PFC,
        'non_delivery_option ' => LabelRepository::NON_DELIVERY_OPTION,
        'customs_items' => [
            // Fleece - 5XL - UNGH
            ['description' => 'Fleece' . ' - ' . 'UNGH',
                'quantity' => 1,
                'weight' => 8,
                'value' => 10,
                'hs_tariff_number' => LabelRepository::CUSTOMS_DEFAULT,
                'origin_country' => LabelRepository::DEFAULT_ORIGIN_COUNTRY,
                'code' => 'UNGH',
            ]
        ]
    ];
    $data = $this->labelRepo->dataCustomsInfoV2($saleOrder, true);
    expect($data)->toEqual($dataResult);
});
