<?php

use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Employee;
use App\Models\InventoryAddition;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\PartNumber;
use App\Models\Product;
use App\Models\Setting;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;

    $this->endpoint = '/api/box-moving';
    $this->location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->product = Product::factory()->create([
        'sku' => 'UNGH1M0XL', 'gtin' => '00821780067052',
        'color' => 'HEATHER GREY', 'size' => 'L', 'style' => '340', 'parent_id' => 2
    ]);
    $box = Box::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'BARCODE',
        'product_id' => $this->product->id,
        'quantity' => 5,
        'location_id' => $this->location->id,
    ]);

    BoxMoving::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'box_id' => $box->id,
        'location_id' => $this->location->id,
        'product_id' => $this->product->id,
        'quantity' => 5,
    ]);

    LocationProduct::factory()->create(['location_id' => $this->location->id, 'product_id' => $this->product->id, 'quantity' => 10]);
    //Location mới mà box moving đến
    $this->location_new = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO456']);
    LocationProduct::factory()->create(['location_id' => $this->location_new->id, 'product_id' => $this->product->id, 'quantity' => 10]);
    $this->location_max_box = 100;
    Setting::factory()->create([
        'name' => Setting::LOCATION_MAX_BOX,
        'value' => $this->location_max_box,
    ]);

    $this->params = [
        'barcode' => $box->barcode,
        'location_barcode' => $this->location_new->barcode,
        'location_type' => Location::RACK,
        'employee_id' => $this->employee->code,
        'id_time_checking' => rand(1, 100)
    ];
});

//validate required field : barcode (Box) , location_type, employee_id, id_time_checking
test('box moving failed - missing field', function () {
    $fieldValidate = [
        'barcode',
        'location_type',
        'employee_id',
        'id_time_checking'
    ];
    foreach ($fieldValidate as $field) {
        $input = $this->params;
        unset($input[$field]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->access_token,
            'Accept' => 'application/json',
        ])->post($this->endpoint, $input);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKeys([
            'message',
            "errors.$field"
        ]);
    }
});

///location_type là Rack (type=0) thì required location_barcode
test('box moving failed - location_barcode is required if location type = 0', function () {
    unset($this->params['location_barcode']);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKeys([
        'message',
        'errors.location_barcode'
    ]);
});
/// Kiểm tra box (barcode) có tồn tại trong warehouse không? nếu không thì dừng moving
test('box moving failed - box is not found', function () {
    $this->params['barcode'] = 'barcode_not_found';

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'barcode' => [
                'This box does not exist.',
            ],
        ],
    ]);
});

/// Kiểm tra box (barcode) còn active trong warehouse không? nếu không thì dừng moving
test('box moving failed - box is not active', function () {
    $box = Box::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'BARCODE 2',
        'is_deleted' => 1,
        'location_id' => $this->location->id
    ]);
    $this->params['barcode'] = $box->barcode;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('barcode');
});

///Kiểm tra location mới (location_barcode) có tồn tại trong warehouse không? nếu không thì dừng moving
test('box moving failed - The location new is not found.', function () {
    $this->params['location_barcode'] = 'ABC';
    $this->params['location_type'] = 0;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'location_barcode' => [
                'The location does not exist or has been deleted'
            ]
        ]
    ]);
});

// 'box moving failed - location max box 
test('box moving failed - location max box', function () {
    Box::factory()->count($this->location_max_box)->create(['location_id' => $this->location_new->id]);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('location');
});

//Location hiện tại của Box là PULLING_SHELVES  thì dừng moving
test('box moving failed - location current is PULLING_SHELVES', function () {
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO10']);
    $box = Box::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'CODE202',
        'location_id' => $location->id
    ]);
    $this->params['barcode'] = $box->barcode;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('barcode');
});

//Box chưa có lịch sử moving ( lúc tạo box ban đầu tự động insert box moving) thì dừng moving
test('box moving failed - box has not moving history', function () {
    $box = Box::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'CODE203',
        'location_id' => $this->location->id
    ]);
    $this->params['barcode'] = $box->barcode;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('barcode');
});

//employee thuộc warehouse khác thì dừng moving
test('box moving failed - The selected employee id is invalid.', function () {
    $warehouse = Warehouse::factory()->create(['code' => 'TX']);
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2022]);
    $this->params['employee_id'] = $employee->code;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKeys([
        'message',
        'errors.employee_id'
    ]);
});

///box moving thành công
test('box moving SUCCESS ', function () {
    $oldLocationQuantityBeforeTest = LocationProduct::where('location_id', $this->location->id)
        ->where('product_id', $this->product->id)
        ->sum('quantity');
    $newLocationQuantityBeforeTest = LocationProduct::where('location_id', $this->location_new->id)
        ->where('product_id', $this->product->id)
        ->sum('quantity');

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(201)->assertJsonStructure([
        'id',
        'box_id',
        'pre_location_id',
        'location_id',
        'warehouse_id',
        'user_id',
        'employee_id',
        'product_id',
        'quantity',
        'created_at',
        'is_deleted',
        'inventory_addition_id',
        'updated_at'
    ]);

    $box = Box::where('barcode', $this->params['barcode'])
        ->where('location_id', $this->location_new->id)
        ->first();

    //Assert location_id mới của box được thay đổi
    $this->assertNotEmpty($box);

    $boxMoving = BoxMoving::where('pre_location_id', $this->location->id)
        ->where('location_id', $this->location_new->id)
        ->where('box_id', $box->id)
        ->first();
    //Assert thêm mới record trong box_moving với pre_location_id = location cũ của box, location_id = location mới của box
    $this->assertNotEmpty($boxMoving);

    $oldLocationQuantity = LocationProduct::where('location_id', $this->location->id)->where('product_id', $box->product_id)->sum('quantity');
    $newLocationQuantity = LocationProduct::where('location_id', $this->location_new->id)->where('product_id', $box->product_id)->sum('quantity');

    ///Assert: product quantity trong location mới của Box  += product quantity trong box
    $this->assertEquals($newLocationQuantity, $newLocationQuantityBeforeTest + $box->quantity);

    ///Assert: product quantity trong location cũ của Box  -= product quantity trong box
    $this->assertEquals($oldLocationQuantity, $oldLocationQuantityBeforeTest - $box->quantity);
});

//box moving Mexico success
test('create box moving success - mexico warehouse', function () {
    $warehouseMexico = Warehouse::WAREHOUSE_MEXICO;
    $userMexico = User::factory()->create();
    $token = JWTAuth::customClaims(['warehouse' => ['id' => $warehouseMexico[0]]])->fromUser($userMexico);

    $locationMexico = Location::factory()->count(2)->sequence(
        ['type' => Location::RACK, 'warehouse_id' => $warehouseMexico[0]],
        ['type' => Location::PULLING_SHELVES, 'warehouse_id' => $warehouseMexico[0]],
    )->create();

    $boxMexico = Box::factory()->create([
        'warehouse_id' => $warehouseMexico[0],
        'barcode' => 'A120023',
        'product_id' => $this->product->id,
        'location_id' => $locationMexico->first()->id,
    ]);

    $boxMoving = BoxMoving::factory()->create([
        'box_id' => $boxMexico->id,
        'quantity' => 5,
    ]);

    $employee = Employee::factory()->create(['warehouse_id' => $warehouseMexico[0], 'code' => 12]);
    InventoryAddition::factory([
        'box_id' => $boxMexico->id,
        'warehouse_id' => $warehouseMexico[0],
        'country' => 'US',
    ])
        ->create();

    PartNumber::factory([
        'country' => 'US',
        'part_number' => 'part_number',
    ])
    ->create();

    $params = [
        'barcode' => $boxMexico->barcode,
        'location_barcode' => '',
        'location_type' => Location::PULLING_SHELVES,
        'employee_id' => $employee->code,
        'id_time_checking' => rand(1, 100)
    ];

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $params);
    $response->assertStatus(201);
    $response->assertJsonStructure([
        'id',
        'box_id',
        'pre_location_id',
        'location_id',
        'warehouse_id',
        'user_id',
        'employee_id',
        'product_id',
        'quantity',
        'created_at',
        'is_deleted',
        'inventory_addition_id',
        'updated_at'
    ]);
});

//not found country
test('create box moving failed - mexico warehouse country not found', function () {
    $warehouseMexico = Warehouse::WAREHOUSE_MEXICO;
    $userMexico = User::factory()->create();
    $token = JWTAuth::customClaims(['warehouse' => ['id' => $warehouseMexico[0]]])->fromUser($userMexico);

    $locationMexico = Location::factory()->count(2)->sequence(
        ['type' => Location::RACK, 'warehouse_id' => $warehouseMexico[0]],
        ['type' => Location::PULLING_SHELVES, 'warehouse_id' => $warehouseMexico[0]],
    )->create();

    $boxMexico = Box::factory()->create([
        'warehouse_id' => $warehouseMexico[0],
        'barcode' => 'A120023',
        'product_id' => $this->product->id,
        'location_id' => $locationMexico->first()->id,
    ]);

    BoxMoving::factory()->create([
        'box_id' => $boxMexico->id,
        'quantity' => 5,
    ]);
    $employee = Employee::factory()->create(['warehouse_id' => $warehouseMexico[0], 'code' => 12]);

    $params = [
        'barcode' => $boxMexico->barcode,
        'location_barcode' => '',
        'location_type' => Location::PULLING_SHELVES,
        'employee_id' => $employee->code,
        'id_time_checking' => rand(1, 100)
    ];

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $params);
    $response->assertStatus(422);
});

test('box moving failed - location is deleted', function () {
    $location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO999', 'is_deleted' => 1]);
    $oldLocationQuantityBeforeTest = LocationProduct::where('location_id', $this->location->id)
        ->where('product_id', $this->product->id)
        ->sum('quantity');
    $newLocationQuantityBeforeTest = LocationProduct::where('location_id', $location)
        ->where('product_id', $this->product->id)
        ->sum('quantity');
    $this->params['location_barcode'] = $location->barcode;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'location_barcode' => [
                'The location does not exist or has been deleted',
            ],
        ],
    ]);
});
