<?php

use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Employee;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\PartNumber;
use App\Models\PartNumberFifo;
use App\Models\Product;
use App\Models\Setting;
use App\Models\TimeTracking;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;

    $this->product = Product::factory([
        'style' => '5000',
        'color' => 'RED',
        'size' => 'M',
    ])->create();

    $this->employee = Employee::factory([
        'warehouse_id' => $this->warehouse->id,
        'code' => 1000,
    ])->create();

    $this->timeTracking = TimeTracking::factory([
        'job_type' => 'pulling_shelve_to_rack',
    ])->create();

    $this->rack = Location::factory([
        'type' => Location::RACK,
        'warehouse_id' => $this->warehouse->id,
    ])->has(LocationProduct::factory([
        'quantity' => 30,
        'product_id' => $this->product->id,
    ]), 'locationProducts')
        ->create();

    $this->shelves = Location::factory([
        'type' => Location::PULLING_SHELVES,
        'warehouse_id' => $this->warehouse->id,
    ])->has(LocationProduct::factory([
        'quantity' => 20,
        'product_id' => $this->product->id,
    ]), 'locationProducts')
        ->create();

    $this->partNumber = PartNumber::factory([
        'part_number' => 'ABC',
        'product_id' => $this->product->id,
        'country' => 'US',
    ])
        ->has(PartNumberFifo::factory()->count(3)->sequence(
            [
                'warehouse_id' => 18,
                'quantity' => 20,
                'product_id' => $this->product->id,
            ],
            [
                'warehouse_id' => 18,
                'quantity' => 1,
                'product_id' => $this->product->id,
            ],
            [
                'warehouse_id' => 18,
                'quantity' => 3,
                'product_id' => $this->product->id,
            ],
        ), 'partNumberFifos')
        ->create();

    $this->location_max_box = 100;
    Setting::factory()->create([
        'name' => Setting::LOCATION_MAX_BOX,
        'value' => $this->location_max_box,
    ]);

    $this->endpoint = '/api/box-moving/pulling-shelves-to-rack';
    $this->params = [
        'barcode' => 'AD12312',
        'employee_id' => $this->employee->code,
        'id_time_checking' => $this->timeTracking->id,
        'location_id' => $this->rack->id,
        'product_color' => $this->product->color,
        'product_size' => $this->product->size,
        'product_style' => $this->product->style,
        'pulling_shelve_id' => $this->shelves->id,
        'quantity' => 10,
        'cost_value' => 15,
    ];
    $this->result = [
        'id',
        'box_id',
        'pre_location_id',
        'location_id',
        'warehouse_id',
        'user_id',
        'employee_id',
        'product_id',
        'quantity',
        'created_at',
        'updated_at',
        'is_deleted',
        'inventory_addition_id',
    ];
});

// employee không tồn tại
test('create box moving failed - employee not found', function () {
    $this->params['employee_id'] = '100';
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});

// box barcode đã tồn tại chưa xóa
test('create box moving failed - barcode already exist not delete', function () {
    Box::factory([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'AD12312',
    ])->create();
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});

// box barcode đã tồn tại đã xóa
test('create box moving failed - barcode already exist delete', function () {
    Box::factory([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => 'AD12312',
        'is_deleted' => 1,
    ])->create();
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
    expect($response)->toEqual(['message' => ['The box already exists or has been deleted']]);
});

// location mới không tồn tại
test('create box moving failed - location_id not found', function () {
    $this->params['location_id'] = 10;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});

// location mới trong kho Rack không tồn tại
test('create box moving failed - location_id not found in Rack', function () {
    $rack = Location::factory([
        'type' => Location::PULLING_SHELVES,
        'warehouse_id' => $this->warehouse->id,
    ])->create();
    $this->params['location_id'] = $rack->id;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});
// create box moving failed - location max box 
test('create box moving failed - location max box', function () {
    Box::factory()->count($this->location_max_box)->create(['location_id' => $this->rack->id]);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('message');
});

// product không tồn tại
test('create box moving failed - product not found', function () {
    $this->params['product_color'] = 'BLACK';
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});

// product đã bị xóa
test('create box moving failed - product deleted', function () {
    $product = Product::factory([
        'style' => '5001',
        'color' => 'WHITE',
        'size' => 'S',
        'is_deleted' => true,
    ])->create();
    $this->params['product_style'] = $product->style;
    $this->params['product_color'] = $product->color;
    $this->params['product_size'] = $product->size;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});

// location_product không tồn tại - pulling_selves_id không tồn tại
test('create box moving failed - location_product - pulling_selves_id not found', function () {
    $this->params['pulling_shelve_id'] = 10;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});

// location_product không tồn tại -  product_id không tồn tại
test('create box moving failed - location_product - product_id not found', function () {
    $product = Product::factory([
        'style' => '5001',
        'color' => 'WHITE',
        'size' => 'S',
    ])->create();
    $this->params['product_style'] = $product->style;
    $this->params['product_color'] = $product->color;
    $this->params['product_size'] = $product->size;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});

//quantity location_product < quantity cần chuyển
test('create box moving failed - quantity location_product not enough', function () {
    $this->params['quantity'] = 100;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys(['message']);
});

// location_product  kho Rack không tồn tại
test('create box moving success - location_product in Rack not exist', function () {
    $rack = Location::factory([
        'type' => Location::RACK,
        'warehouse_id' => $this->warehouse->id,
    ])->create();
    $this->params['location_id'] = $rack->id;
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(201);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys($this->result);
    $product = Product::where('color', $this->params['product_color'])
        ->where('style', $this->params['product_style'])
        ->where('size', $this->params['product_size'])
        ->first();
    $this->assertNotEmpty($product);
    $box = Box::where('warehouse_id', $this->warehouse->id)
        ->where('barcode', $this->params['barcode'])
        ->where('location_id', $this->params['location_id'])
        ->where('product_id', $product->id)
        ->where('quantity', $this->params['quantity'])
        ->first();
    $this->assertNotEmpty($box);
    $this->assertEquals($box->cost_value, 15);
    $boxMoving = BoxMoving::where('box_id', $box->id)
        ->where('location_id', $this->params['location_id'])
        ->where('warehouse_id', $this->warehouse->id)
        ->where('user_id', $this->user->id)
        ->where('product_id', $product->id)
        ->where('quantity', $this->params['quantity'])
        ->where('employee_id', $this->employee->code)
        ->first();
    $this->assertNotEmpty($boxMoving);
    $locationProductShelves = LocationProduct::where('location_id', $this->shelves->id)
        ->where('product_id', $this->product->id)
        ->first();
    $this->assertNotEmpty($locationProductShelves);
    $this->assertEquals(10, $locationProductShelves->quantity);
    $locationProductRack = LocationProduct::where('location_id', $rack->id)
        ->where('product_id', $this->product->id)
        ->first();
    $this->assertNotEmpty($locationProductRack);
    $this->assertEquals(10, $locationProductRack->quantity);
});

// create box moving success
test('create box moving success', function () {
    $response = $this->withHeaders([
        'Authorization' => "Bearer $this->access_token",
        'Accept' => 'Application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(201);
    $response = json_decode($response->getContent(), true);
    expect($response)->toHaveKeys($this->result);
    $product = Product::where('color', $this->params['product_color'])
        ->where('style', $this->params['product_style'])
        ->where('size', $this->params['product_size'])
        ->first();
    $this->assertNotEmpty($product);
    $box = Box::where('warehouse_id', $this->warehouse->id)
        ->where('barcode', $this->params['barcode'])
        ->where('location_id', $this->params['location_id'])
        ->where('product_id', $product->id)
        ->where('quantity', $this->params['quantity'])
        ->first();
    $this->assertNotEmpty($box);
    $this->assertEquals($box->cost_value, 15);
    $boxMoving = BoxMoving::where('box_id', $box->id)
        ->where('location_id', $this->params['location_id'])
        ->where('warehouse_id', $this->warehouse->id)
        ->where('user_id', $this->user->id)
        ->where('product_id', $product->id)
        ->where('quantity', $this->params['quantity'])
        ->where('employee_id', $this->employee->code)
        ->first();
    $this->assertNotEmpty($boxMoving);
    $locationProductShelves = LocationProduct::where('location_id', $this->shelves->id)
        ->where('product_id', $this->product->id)
        ->first();
    $this->assertNotEmpty($locationProductShelves);
    $this->assertEquals(10, $locationProductShelves->quantity);
    $locationProductRack = LocationProduct::where('location_id', $this->rack->id)
        ->where('product_id', $this->product->id)
        ->first();
    $this->assertNotEmpty($locationProductRack);
    $this->assertEquals(40, $locationProductRack->quantity);
});
