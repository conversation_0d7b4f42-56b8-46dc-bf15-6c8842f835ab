<?php

use App\Models\User;
use App\Models\Warehouse;
use App\Models\Location;
use App\Models\Employee;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;
use \App\Models\TimeTracking;
use \App\Models\LocationProduct;
use \App\Models\ProductQuantity;
use \App\Models\Inventory;
use \App\Models\Product;
use \App\Models\Box;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouse = Warehouse::factory()->create();
    $user = User::factory()->create();
    $this->access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse->id]])->fromUser($user);
    $this->endpoint = '/api/box';
    $this->location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => "LO123"]);
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->product = Product::factory()->create(['sku' => 'UNGH1M0XL', 'gtin' => '00821780067052',
        'color' => "HEATHER GREY", "size" => "L", "style" => "340", "parent_id" => 2]);

    $this->params = array(
        "box_id" => "AVB345",
        "location_id" => $this->location->id,
        "quantity" => rand(1, 10),
        "style" => $this->product->style,
        "color" => $this->product->color,
        "size" => $this->product->size,
        "employee_id" => $this->employee->code,
        "id_time_checking" => rand(1, 100)
    );
});


/// Check required field
test('Create box failed - check required field : box_id, style, color, size, quantity, location_id, employee_id, id_time_checking', function () {
    $fieldValidate = [
        'box_id',
        'style',
        'color',
        'size',
        'quantity',
        'location_id',
        'employee_id',
        'id_time_checking',
    ];
    foreach ($fieldValidate as $field) {

        $input = $this->params;
        unset($input[$field]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->access_token,
            'Accept' => 'application/json',

        ])->post($this->endpoint, $input);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKeys([
            "message",
            "errors.$field"
        ]);
    }
});

/// quantity không phải là số nguyên dương => không cho tạo box
test('Create box failed -  quantity must be an integer and greater than 0.', function () {
    $this->params['quantity'] = -2;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toHaveKeys([
        "message",
        "errors.quantity"
    ]);
});

/// Employee thuộc warehouse khác => không cho tạo box

test('Create box failed - The selected employee id is invalid.', function () {
    $warehouse = Warehouse::factory()->create(['code' => 'TX']);
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2022]);
    $this->params['employee_id'] = $employee->code;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toHaveKeys([
        "message",
        "errors.employee_id"
    ]);
});

/// Box đã tồn tại trong warehouse => không cho tạo box
test('Create box failed - Box already exists', function () {
    $box = Box::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => "AVB345",
        'location_id' => $this->location->id
    ]);
    $this->params['box_id'] = $box->barcode;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("message");
});

/// Product not found hoặc là product cha (parent_id =0) => không cho tạo box
test('Create box failed - The product is not found or parent product or deleted!', function () {

    /// product not found
    $this->params['style'] = "Hoodie";

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toHaveKey("message");

    /// Product is parent product
    $productParent = Product::factory()->create(['sku' => 'UNGH2M0XL', 'gtin' => '90821780067052',
        'color' => "HEATHER", "size" => "L", "style" => "340", "parent_id" => 0]);

    $this->params['style'] = $productParent->style;
    $this->params['color'] = $productParent->color;
    $this->params['size'] = $productParent->size;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toHaveKey("message");
});

/// Product is deleted => không cho tạo box
test('Create box failed - The product is deleted!', function () {

    /// Product is deleted
    $productDeleted = Product::factory()->create(['sku' => 'UNGH2M0XL', 'gtin' => '90821780067052',
        'color' => "HEATHER", "size" => "L", "style" => "340", "is_deleted" => 1, 'parent_id'=> 2]);

    $this->params['style'] = $productDeleted->style;
    $this->params['color'] = $productDeleted->color;
    $this->params['size'] = $productDeleted->size;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toHaveKey("message");
});

/// location not found  => không cho tạo box
test('Create box failed - The location is not found!', function () {
    $this->params['location_id'] = "20";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toHaveKey("message");

});

/// location đã bị xóa  => không cho tạo box
test('Create box failed - The location is deleted!', function () {
    ///Location is deleted
    $location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => "U787", 'is_deleted'=>1]);
    $this->params['location_id'] = $location->id;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toHaveKey("message");
});

/// location không phải là RACK => không cho tạo box
test('Create box failed - The location must be in rack!', function () {
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => "LO"]);
    $this->params['location_id'] = $location->id;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',

    ])->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("message");
});
///Tạo box thành công

test('Create box SUCCESS', function () {

    TimeTracking::factory()->create([
        'job_type' => 'create box',
        'employee_id' => $this->employee->id,
        'id' => $this->params['id_time_checking'],
        'quantity' => 0
    ]);

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(200)->assertJsonStructure([
        "message",
        "data" => [
            "warehouse_id",
            "barcode",
            "location_id",
            "product_id",
            "quantity",
            "employee_id",
            "updated_at",
            "created_at",
            "id",
        ]
    ]);

    $box = Box::where('warehouse_id', $this->warehouse->id)
        ->where('location_id', $this->location->id)
        ->where('barcode', $this->params['box_id'])
        ->first();

    $this->assertNotEmpty($box);
    $this->assertDatabaseHas('box_moving', ['box_id' => $box->id]);

    $timeTrackingQuantity = TimeTracking::where('id', $this->params['id_time_checking'])->sum('quantity');
    $this->assertEquals($timeTrackingQuantity, 1);
    $locationProductQuantityNew = LocationProduct::where('product_id', $this->product->id)
        ->where('location_id', $this->location->id)
        ->sum('quantity');
    $this->assertEquals($locationProductQuantityNew, $this->params['quantity']);

    $productQuantityNew = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $this->product->id)
        ->sum('quantity');
    $this->assertEquals($productQuantityNew, $this->params['quantity']);
    $inventoryCount = Inventory::count();
    $this->assertEquals($inventoryCount, 1);
});
