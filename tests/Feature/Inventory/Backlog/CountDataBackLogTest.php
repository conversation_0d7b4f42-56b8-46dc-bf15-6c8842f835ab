<?php

use App\Models\BackLog;
use App\Models\ProductColor;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Repositories\BackLogRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->backLogRepository = new BackLogRepository();
    ProductStyle::factory()->createMany([
        [
            'name' => 'Tee',
            'sku' => 'T',
            'type' => 'Tee',
        ],
        [
            'name' => 'Fleece',
            'sku' => 'F',
            'type' => 'Fleece',
        ],
        [
            'name' => 'Test',
            'sku' => 'Te',
            'type' => 'Test',
        ],
    ]);
    ProductColor::factory()->createMany([
        [
            'name' => 'WHITE',
            'sku' => 'WH',
            'color_code' => '#ffffff',
        ],
        [
            'name' => 'BLACK',
            'sku' => 'BL',
            'color_code' => '#000000',
        ],
    ]);
    SaleOrderItem::factory()->createMany([
        [
            'id' => 1,
            'product_color_sku' => 'BL',
            'product_style_sku' => 'T',
        ],
        [
            'id' => 2,
            'product_color_sku' => 'BL',
            'product_style_sku' => 'F',
        ],
    ]);
    $this->dataExpect = [
        [
            'type' => 'Fleece',
            'total' => 2
        ],
        [
            'type' => 'Tee',
            'total' => 2
        ],

    ];
});

test('count back log deduction', function () {
    SaleOrderItemBarcode::factory()->count(4)->sequence([
        'warehouse_id' => 1,
        'print_barcode_at' => '2022-12-28 00:00:00',
        'pulled_at' => null
    ])->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();

    //data gây nhiễu, cụ thể là các trường hợp sai không được count
    $nextSteps = ['printed_at', 'qc_at', 'staged_at', 'shipped_at', 'folded_at'];
    SaleOrderItemBarcode::factory()->count(10)->sequence(
        ['warehouse_id' => 2],
        [
            'warehouse_id' => 1,
            'print_barcode_at' => '2022-12-28 00:00:00',
            'pulled_at' => '2022-12-28 00:00:00'
        ],
        [
            'print_barcode_at' => null,
            'pulled_at' => null
        ],
        [
            'print_barcode_at' => '2022-12-28 00:00:00',
            'pulled_at' => null,
            $nextSteps[rand(0, count($nextSteps) - 1)] => '2022-12-28 00:00:00',
        ],
    )->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();
    $data = $this->backLogRepository->countBackLog(1, BackLog::JOB_TYPES['deduction'], '2022-12-01');

    $this->assertEqualsCanonicalizing($this->dataExpect, $data->toArray());
});
test('count back log pretreat', function () {
    SaleOrderItemImage::factory()->createMany([
        [
            'order_item_id' => 1,
            'color_new' => 2,
        ],
        [
            'order_item_id' => 2,
            'color_new' => 2,
        ],
        [
            'order_item_id' => 3,
            'color_new' => 1,
        ],
        [
            'order_item_id' => 4,
            'color_new' => 2,
        ],
    ]);

    SaleOrderItemBarcode::factory()->count(4)->sequence([
        'warehouse_id' => 1,
        'pulled_at' => '2022-12-28 00:00:00',
        'printed_at' => null
    ])->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();

    //data gây nhiễu, cụ thể là các trường hợp sai không được count
    $nextSteps = ['qc_at', 'staged_at', 'shipped_at', 'folded_at'];
    SaleOrderItem::factory()->createMany([
        [
            'id' => 3,
            'product_color_sku' => 'BL',
            'product_style_sku' => 'T',
        ],
        [
            'id' => 4,
            'product_color_sku' => 'WH',
            'product_style_sku' => 'F',
        ],
    ]);
    SaleOrderItemBarcode::factory()->count(10)->sequence(
        ['warehouse_id' => 2],
        [
            'warehouse_id' => 1,
            'pulled_at' => '2022-12-28 00:00:00',
            'printed_at' => '2022-12-28 00:00:00'
        ],
        [
            'pulled_at' => null,
            'printed_at' => null
        ],
        [
            'print_barcode_at' => '2022-12-28 00:00:00',
            'pulled_at' => null,
            $nextSteps[rand(0, count($nextSteps) - 1)] => '2022-12-28 00:00:00',
        ],
    )->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();
    SaleOrderItemBarcode::factory()->count(10)->sequence([
        'warehouse_id' => 1,
        'pulled_at' => '2022-12-28 00:00:00',
        'printed_at' => null
    ])->sequence(
        ['order_item_id' => 3],
        ['order_item_id' => 4],
    )->create();
    $data = $this->backLogRepository->countBackLog(1, BackLog::JOB_TYPES['pretreat'], '2022-12-01');

    $this->assertEqualsCanonicalizing($this->dataExpect, $data->toArray());
});
test('count back log printing', function () {
    SaleOrderItemBarcode::factory()->count(4)->sequence([
        'warehouse_id' => 1,
        'pulled_at' => '2022-12-28 00:00:00',
        'printed_at' => null
    ])->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();

    //data gây nhiễu, cụ thể là các trường hợp sai không được count
    $nextSteps = ['qc_at', 'staged_at', 'shipped_at', 'folded_at'];
    SaleOrderItemBarcode::factory()->count(10)->sequence(
        ['warehouse_id' => 2],
        [
            'warehouse_id' => 1,
            'pulled_at' => '2022-12-28 00:00:00',
            'printed_at' => '2022-12-28 00:00:00'
        ],
        [
            'pulled_at' => null,
            'printed_at' => null
        ],
        [
            'print_barcode_at' => '2022-12-28 00:00:00',
            'pulled_at' => null,
            $nextSteps[rand(0, count($nextSteps) - 1)] => '2022-12-28 00:00:00',
        ],
    )->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();
    $data = $this->backLogRepository->countBackLog(1, BackLog::JOB_TYPES['printing'], '2022-12-01');

    $this->assertEqualsCanonicalizing($this->dataExpect, $data->toArray());
});
test('count back log quality-control', function () {
    SaleOrderItemBarcode::factory()->count(4)->sequence([
        'warehouse_id' => 1,
        'printed_at' => '2022-12-28 00:00:00',
        'qc_at' => null
    ])->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();

    //data gây nhiễu, cụ thể là các trường hợp sai không được count
    $nextSteps = ['staged_at', 'shipped_at', 'folded_at'];
    SaleOrderItemBarcode::factory()->count(10)->sequence(
        ['warehouse_id' => 2],
        [
            'warehouse_id' => 1,
            'printed_at' => '2022-12-28 00:00:00',
            'qc_at' => '2022-12-28 00:00:00'
        ],
        [
            'printed_at' => null,
            'qc_at' => null
        ],
        [
            'print_barcode_at' => '2022-12-28 00:00:00',
            'pulled_at' => null,
            $nextSteps[rand(0, count($nextSteps) - 1)] => '2022-12-28 00:00:00',
        ],
    )->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();
    $data = $this->backLogRepository->countBackLog(1, BackLog::JOB_TYPES['quality-control'], '2022-12-01');

    $this->assertEqualsCanonicalizing($this->dataExpect, $data->toArray());
});
test('count back log multiple-stagging', function () {
    SaleOrderItemBarcode::factory()->count(4)->sequence([
        'warehouse_id' => 1,
        'qc_at' => '2022-12-28 00:00:00',
        'staged_at' => null,
        'order_quantity' => 2
    ])->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();

    //data gây nhiễu, cụ thể là các trường hợp sai không được count
    $nextSteps = ['shipped_at', 'folded_at'];
    SaleOrderItemBarcode::factory()->count(10)->sequence(
        ['warehouse_id' => 2],
        [
            'warehouse_id' => 1,
            'qc_at' => '2022-12-28 00:00:00',
            'staged_at' => '2022-12-28 00:00:00'
        ],
        [
            'qc_at' => null,
            'staged_at' => null
        ],
        [
            'print_barcode_at' => '2022-12-28 00:00:00',
            'pulled_at' => null,
            $nextSteps[rand(0, count($nextSteps) - 1)] => '2022-12-28 00:00:00',
        ],
        [
            'order_quantity' => 1,
        ],
    )->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();
    $data = $this->backLogRepository->countBackLog(1, BackLog::JOB_TYPES['multiple-stagging'], '2022-12-01');

    $this->assertEqualsCanonicalizing($this->dataExpect, $data->toArray());
});
test('count back log shipping-labels-print', function () {
    SaleOrder::factory()->createMany([
        [
            'id' => 1,
            'order_quantity' => 1,
            'warehouse_id' => 1,
            'order_status' => 'in_production',
        ],
        [
            'id' => 2,
            'order_quantity' => 2,
            'warehouse_id' => 1,
            'order_status' => 'in_production',
        ],
        [
            'id' => 3,
            'order_quantity' => 1,
            'warehouse_id' => 1,
            'order_status' => 'in_production',
        ],
        [
            'id' => 4,
            'order_quantity' => 2,
            'warehouse_id' => 1,
            'order_status' => 'in_production',
        ],
        [
            'id' => 5,
            'order_quantity' => 2,
            'warehouse_id' => 1,
            'order_status' => 'in_production',
        ]
    ]);
    //data chuan
    SaleOrderItemBarcode::factory()->createMany([
        [
            'qc_at' => '2022-12-28 00:00:00',
            'shipped_at' => null,
            'order_id' => 1
        ],
        [
            'staged_at' => '2022-12-28 00:00:00',
            'shipped_at' => null,
            'order_id' => 2
        ],
        [
            'staged_at' => '2022-12-28 00:00:00',
            'shipped_at' => null,
            'order_id' => 2
        ]
    ]);

    //data gây nhiễu, cụ thể là các trường hợp sai không được count
    SaleOrderItemBarcode::factory()->count(3)->sequence(
        [
            'staged_at' => '2022-12-28 00:00:00',
            'shipped_at' => null,
        ],
    )->sequence(
        ['order_id' => 4],
        ['order_id' => 5],
        ['order_id' => 6],
    )->create();

    SaleOrderItemBarcode::factory()->createMany([
        [
            'staged_at' => '2022-12-28 00:00:00',
            'shipped_at' => '2022-12-28 00:00:00',
            'order_id' => 4
        ],
        [
            'staged_at' => null,
            'shipped_at' => null,
            'order_id' => 5
        ],
        [
            'staged_at' => '2022-12-28 00:00:00',
            'shipped_at' => null,
            'folded_at' => '2022-12-28 00:00:00',
            'order_id' => 6
        ]
    ]);
    $data = $this->backLogRepository->countBackLogShippingLabel(1, BackLog::JOB_TYPES['shipping-labels-print'], '2022-12-01');
    $dataExpect = [
        [
            'type' => 'multiple order',
            'total' => 1
        ],
        [
            'type' => 'single order',
            'total' => 1
        ],
    ];
    $this->assertEqualsCanonicalizing($dataExpect, $data->toArray());
});
test('count back log folding', function () {
    SaleOrderItemBarcode::factory()->count(4)->sequence([
        'warehouse_id' => 1,
        'shipped_at' => '2022-12-28 00:00:00',
        'folded_at' => null
    ])->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();

    //data gây nhiễu, cụ thể là các trường hợp sai không được count
    SaleOrderItemBarcode::factory()->count(10)->sequence(
        ['warehouse_id' => 2],
        [
            'warehouse_id' => 1,
            'shipped_at' => '2022-12-28 00:00:00',
            'folded_at' => '2022-12-28 00:00:00'
        ],
        [
            'shipped_at' => null,
            'folded_at' => null
        ],
    )->sequence(
        ['order_item_id' => 1],
        ['order_item_id' => 2],
    )->create();
    $data = $this->backLogRepository->countBackLog(1, BackLog::JOB_TYPES['folding'], '2022-12-01');

    $this->assertEqualsCanonicalizing($this->dataExpect, $data->toArray());
});
