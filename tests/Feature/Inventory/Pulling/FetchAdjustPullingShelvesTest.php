<?php

use App\Models\AdjustPullingShelves;
use App\Models\Employee;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;

    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->product = Product::factory()->count(2)->sequence(
        [
            "size" => "L",
            "style" => "340",
            "color" => "BLACK"
        ],
        [
            "size" => "XS",
            "style" => "3001",
            "color" => "AQUA"
        ]
    )->create();

    AdjustPullingShelves::factory()->count(2)->sequence(
        [
            'sku' => 'UNGH1M0XL',
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->first()->id,
            'employee_id' => $this->employee->code,
            'user_id' => $this->user->id,
            'created_at' => '2023-03-12 09:21:18',
        ],
        [
            'sku' => 'UNPT9C0XS',
            'warehouse_id' => $this->warehouse->id,
            'product_id' => $this->product->last()->id,
            'employee_id' => $this->employee->code,
            'user_id' => $this->user->id,
            'created_at' => '2023-03-22 09:21:18',
        ],
    )
        ->create();

    $this->endpoint = '/api/adjust-pulling-shelves';

    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
});

function adjustPullingShelvesSuccess($data)
{
    $result = [
        'id',
        'warehouse_id',
        'product_available',
        'product_on_hand',
        'product_adjust',
        'product',
        'user',
        'employee',
        'created_at'
    ];

    foreach ($data as $key => $value) {
        expect($value)->toHaveKeys($result);
        expect($value['product'])->toHaveKeys(['id', 'name']);
        expect($value['user'])->toHaveKeys(['id', 'username']);
        expect($value['employee'])->toHaveKeys(['code', 'name']);
    }
};

//style param
test('fetch adjust pulling shelves success - style param', function () {
    $asserts['valid_params'] = ['type' => 'product_style', 'style' => '3001'];
    $asserts['invalid_params'] = ['type' => 'product_style', 'style' => 123];

    foreach ($asserts as $key => $assert) {
        $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($this->endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);
        if ($key === 'invalid_params') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            adjustPullingShelvesSuccess($response['data']);
        }
    }
});

//color param
test('fetch adjust pulling shelves success - color param', function () {
    $asserts['valid_params'] = ['type' => 'product_style', 'color' => 'AQUA'];
    $asserts['invalid_params'] = ['type' => 'product_style', 'color' => 123];

    foreach ($asserts as $key => $assert) {
        $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($this->endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);
        if ($key === 'invalid_params') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            adjustPullingShelvesSuccess($response['data']);
        }
    }
});

//size param
test('fetch adjust pulling shelves success - size param', function () {
    $asserts['valid_params'] = ['type' => 'product_style', 'size' => 'XS'];
    $asserts['invalid_params'] = ['type' => 'product_style', 'size' => 123];

    foreach ($asserts as $key => $assert) {
        $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($this->endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);
        if ($key === 'invalid_params') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            adjustPullingShelvesSuccess($response['data']);
        }
    }
});

//keyword param
test('fetch adjust pulling shelves success - keyword param', function () {
    $asserts['valid_params'] = ['type' => 'product_sku', 'keyword' => 'UNPT9C0XS'];
    $asserts['invalid_params'] = ['type' => 'product_sku', 'keyword' => 'not found'];

    foreach ($asserts as $key => $assert) {
        $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($this->endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);
        if ($key === 'invalid_params') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            adjustPullingShelvesSuccess($response['data']);
        }
    }
});

//date param
test('fetch adjust pulling shelves success - date param', function () {
    $asserts['valid_params'] = ['type' => 'date', 'date' => ['2023-03-20 09:21:18', '2023-03-23 09:21:18']];
    $asserts['invalid_params'] = ['type' => 'date', 'date' => ['not found', 'not found']];

    foreach ($asserts as $key => $assert) {
        $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($this->endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);
        if ($key === 'invalid_params') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            adjustPullingShelvesSuccess($response['data']);
        }
    }
});

//limit parram
test('fetch adjust pulling shelves success - limit param', function () {
    $this->endpoint = $this->endpoint . '?' . http_build_query(['limit' => 1]);

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(1, $response['data']);
    adjustPullingShelvesSuccess($response['data']);
});


//data success
test('fetch adjust pulling shelves success', function () {
    $response = $this->get($this->endpoint);

    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(2, $response['data']);
    adjustPullingShelvesSuccess($response['data']);
});
