<?php

use App\Models\Employee;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\PartNumber;
use App\Models\PartNumberHistory;
use App\Models\User;
use App\Models\Warehouse;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $this->access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $this->employee = Employee::factory(['name' => 'test'])->create();

    $this->partNumber = PartNumber::factory(['part_number' => 'T5011UNGT1B00M'])
        ->has(PartNumberHistory::factory()->count(4)->sequence(
            ['created_at' => '2023-04-06', 'type' => PartNumberHistory::TYPE_EXPORT, 'warehouse_id' => $this->warehouse, 'action' => PartNumberHistory::ACTION_ADDITION_PULLING_SHELVES],
            ['created_at' => '2023-05-06', 'type' => PartNumberHistory::TYPE_IMPORT, 'warehouse_id' => $this->warehouse, 'action' => PartNumberHistory::ACTION_ADJUST_PULLING_SHELVES],
            ['created_at' => '2023-06-06', 'type' => PartNumberHistory::TYPE_EXPORT, 'warehouse_id' => $this->warehouse, 'action' => PartNumberHistory::ACTION_BOX_MOVING],
            ['created_at' => '2023-07-06', 'type' => PartNumberHistory::TYPE_IMPORT, 'warehouse_id' => $this->warehouse, 'action' => PartNumberHistory::ACTION_EXPORTATION_REPORT],
            ['created_at' => '2023-08-06', 'type' => PartNumberHistory::TYPE_IMPORT, 'warehouse_id' => 1, 'action' => PartNumberHistory::ACTION_ADJUST_PULLING_SHELVES],
            ['created_at' => '2023-09-06', 'type' => PartNumberHistory::TYPE_EXPORT, 'warehouse_id' => 1, 'action' => PartNumberHistory::ACTION_BOX_MOVING],
        )
            ->state(['employee_id' => $this->employee->id]), 'partNumberHistory')
        ->create();

    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->endpoint = 'api/part-number-overview/history/' . $this->partNumber->part_number;

    $this->result = [
        'id',
        'part_number_id',
        'action',
        'type',
        'quantity',
        'balance',
        'employee_id',
        'warehouse_id',
        'created_at',
        'employee',
    ];
});

//fetch part number history failed - part number not found
test('fetch part number history failed - part number not found', function () {
    PartNumber::where('id', $this->partNumber->id)->delete();
    $response = $this->get($this->endpoint);
    $response->assertStatus(404);
});

//fetch part number history success - history not found
test('fetch part number history success - history not found', function () {
    PartNumberHistory::query()->delete();
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertEmpty($response['data']);
});

//fetch part number history success - type param
test('fetch part number history success - type param', function () {
    $asserts['valid'] = ['type' => "import"];
    $asserts['in_valid'] = ['type' => 'not found'];

    foreach ($asserts as $key => $assert) {
        $endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($key === 'in_valid') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(2, $response['data']);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                expect($value['employee'])->toHaveKeys(['name', 'id']);
            }
        }
    }
});

//fetch part number history success - action param
test('fetch part number history success - action param', function () {
    $asserts['valid'] = ['action' => PartNumberHistory::ACTION_ADDITION_PULLING_SHELVES];
    $asserts['in_valid'] = ['action' => 'not found'];

    foreach ($asserts as $key => $assert) {
        $endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($key === 'in_valid') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                expect($value['employee'])->toHaveKeys(['name', 'id']);
            }
        }
    }
});

//fetch part number history success - date param
test('fetch part number history success - date param', function () {
    $asserts['valid'] = ['date' => ['2023-04-01', '2023-04-20']];
    $asserts['in_valid'] = ['date' => ['2023-03-01', '2023-03-01']];

    foreach ($asserts as $key => $assert) {
        $endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($key === 'in_valid') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
                expect($value['employee'])->toHaveKeys(['name', 'id']);
            }
        }
    }
});

//fetch part number history success
test('fetch part number history success', function () {
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(4, $response['data']);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        expect($value['employee'])->toHaveKeys(['name', 'id']);
    }
});
