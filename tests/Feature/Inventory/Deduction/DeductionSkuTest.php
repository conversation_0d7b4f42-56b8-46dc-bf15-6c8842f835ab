<?php

use App\Models\Employee;
use App\Models\Inventory;
use App\Models\InventoryDeduction;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\TimeTracking;
use App\Models\User;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/inventory-deduction/sku';
    $this->product = Product::factory()->create(['sku' => 'UNGH1M0XL', 'gtin' => '00821780067052']);
    $this->location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id, 'barcode' => 'LO123']);
    $this->productReplace = Product::factory()->create(['sku' => 'AB12MLXL', 'gtin' => '12821780067052']);
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);

    $this->labelId = '10012022-SJ-XL-000013';
    $this->sku = 'MTk3MTc4OA==' . $this->product->sku;

    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'shipment_id' => 1,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'sku' => $this->sku,
        'employee_pull_id' => $this->employee->id,
        'label_id' => $this->labelId,
    ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $this->params = [
        'sku' => $this->productReplace->sku,
        'sale_order_sku' => $this->labelId,
    ];

    $this->timeTracking = TimeTracking::factory()->create([
        'job_type' => TimeTracking::DEDUCTION_JOB_TYPE,
        'start_time' => date('Y-m-d H:i:s'),
        'employee_id' => $this->employee->id,
        'quantity' => 0
    ]);
});

/// missing sku, sale_order_sku
test('deduction failed - missing sku, sale_order_sku', function () {
    $fieldValidate = [
        'sku',
        'sale_order_sku'
    ];
    foreach ($fieldValidate as $field) {
        $input = $this->params;
        unset($input[$field]);

        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $input);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey($field);
    }
});

///sale_order_sku là label_id, label_id không tồn tại trong sale_order_item_barcode => dừng deduction
test('deduction failed - Label not found (sale_order_sku is a label id)', function () {
    $this->params = [
        'sale_order_sku' => '080922-SJ-M-000044-2',
        'sku' => $this->product->sku,
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
});

///sale_order_sku là sku, sku không tồn tại trong sale_order_item_barcode => dừng deduction
test('deduction failed - SKU not found (sale_order_sku is a sku)', function () {
    $this->params = [
        'sale_order_sku' => 'MTk3MTc4OA==8UNGH1M0XL',
        'sku' => $this->product->sku,
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
});

///Kiểm tra product bị thay thế khi deduction thế có tồn tại không ? nếu không thì dừng deduction
test('deduction failed - (sale_order_sku) product not found', function () {
    SaleOrderItemBarcode::where('label_id', $this->labelId)->update(['sku' => '1231231Abfs']);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
});
///Kiểm tra product thay thế khi deduction có tồn tại không ? nếu không thì dừng deduction
test('deduction failed - (sku) product not found', function () {
    $this->params['sku'] = 'XMLABC123L';

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
});

///Order phải có trạng thái khác : draft, rejected, on_hold, cancelled mới được deduction
test('The sale order has been: draft, rejected, on_hold, cancelled', function () {
    $orderStatus = [
        SaleOrder::DRAFT,
        SaleOrder::REJECTED,
        SaleOrder::ON_HOLD,
        SaleOrder::CANCELLED,
        SaleOrder::STATUS_LATE_CANCELLED
    ];
    foreach ($orderStatus as $status) {
        $this->saleOrder->order_status = $status;
        $this->saleOrder->save();

        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $this->params);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey('sale_order_sku');
    }
});
///sale_order_sku là label_id, Deduction thành công
test('deduction success, sale_order_sku là label_id', function () {
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(201);
    ///Assert them inventory_deduction thanh cong
    $deduction = InventoryDeduction::where('label_id', $this->params['sale_order_sku'])->first();
    $this->assertNotEmpty($deduction);
    $this->assertEquals($deduction->wip_product_id, $this->product->id);
    $this->assertEquals($deduction->product_id, $this->productReplace->id);

    ///Assert them inventory thanh cong
    $inventory = Inventory::where('object_id', $deduction->id)->first();
    $this->assertNotEmpty($inventory);

    ///Assert pulled_at  trong sale_order_item_barcode được update now
    $saleOrderItemBarcode = SaleOrderItemBarcode::where('label_id', $this->params['sale_order_sku'])->first();
    $this->assertEquals(Carbon::parse($saleOrderItemBarcode->pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    ///Assert Sale Order : pulled_at = now
    $saleOrder = SaleOrder::where('id', $saleOrderItemBarcode->order_id)->first();
    $this->assertEquals($saleOrder->order_pulled_status, 1);
    $this->assertEquals(Carbon::parse($saleOrder->order_pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    //Assert quantity -= 1
    $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $this->productReplace->id)->first();
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity - 1);

    ///Assert quantity -= 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->productReplace->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $locationQuantityBeforeTest->quantity - 1);

    $timeTracking = TimeTracking::where('id', $this->timeTracking->id)->first();
    $this->assertEquals($timeTracking->quantity, 1);

    //assert history order timeline
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE
    ]);
});

///sale_order_sku là sku, Deduction thành công
test('deduction success - sale_order_sku là sku', function () {
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10]);

    $this->params['sale_order_sku'] = $this->sku;
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(201);
    ///Assert them inventory_deduction thanh cong
    $deduction = InventoryDeduction::where('sale_order_sku', $this->params['sale_order_sku'])->first();
    $this->assertNotEmpty($deduction);
    $this->assertEquals($deduction->wip_product_id, $this->product->id);
    $this->assertEquals($deduction->product_id, $this->productReplace->id);

    ///Assert them inventory thanh cong
    $inventory = Inventory::where('object_id', $deduction->id)->first();
    $this->assertNotEmpty($inventory);

    ///Assert pulled_at  trong sale_order_item_barcode được update now
    $saleOrderItemBarcode = SaleOrderItemBarcode::where('label_id', $this->labelId)->first();
    $this->assertEquals(Carbon::parse($saleOrderItemBarcode->pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    ///Assert Sale Order : pulled_at = now
    $saleOrder = SaleOrder::where('id', $saleOrderItemBarcode->order_id)->first();
    $this->assertEquals($saleOrder->order_pulled_status, 1);
    $this->assertEquals(Carbon::parse($saleOrder->order_pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    //Assert quantity -= 1
    $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $this->productReplace->id)->first();
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity - 1);

    ///Assert quantity -= 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->productReplace->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $locationQuantityBeforeTest->quantity - 1);

    $timeTracking = TimeTracking::where('id', $this->timeTracking->id)->first();
    $this->assertEquals($timeTracking->quantity, 1);

    //assert history order timeline
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE
    ]);
});

// deduction fail with mexico - barcode box required
test('deduction fail with mexico - barcode box required', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);

    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();

    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'The box barcode field is required.'
        ]
    ]);
});

// deduction fail with Mexico - Box not found
test('deduction fail with Mexico - Box not found', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);

    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();

    $this->params = [
        'box_barcode' => '123456789',
        'sku' => $this->productReplace->sku,
        'sale_order_sku' => $this->labelId,
    ];

    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);

    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'Box not found'
        ]
    ]);
});

// deduction fail with Mexico - Inventory addition not found
test('deduction fail with Mexico - Inventory addition not found', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);

    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $box = \App\Models\Box::factory([
        'warehouse_id' => $warehouse->id,
        'barcode' => 'AGSBTCU',
    ])->create();

    $this->params = [
        'box_barcode' => $box->barcode,
        'sku' => $this->productReplace->sku,
        'sale_order_sku' => $this->labelId,
    ];

    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10]);

    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'shipment_id' => 1,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => $this->sku,
        'employee_pull_id' => $this->employee->id,
        'label_id' => $this->labelId,
    ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'Inventory addition not found'
        ]
    ]);
});

// deduction fail with Mexico - This SKU and country of origin do not have a corresponding part number.
test('deduction fail with Mexico - This SKU and country of origin do not have a corresponding part number.', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);

    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $box = \App\Models\Box::factory([
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->productReplace->id,
        'barcode' => 'AGSBTCU',
    ])->create();

    $this->params = [
        'box_barcode' => $box->barcode,
        'sku' => $this->productReplace->sku,
        'sale_order_sku' => $this->labelId,
    ];
    $addition = \App\Models\InventoryAddition::factory([
        'box_id' => $box->id,
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'quantity' => 10,
        'country' => 'VN'
    ])->create();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10]);

    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'shipment_id' => 1,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => $this->sku,
        'employee_pull_id' => $this->employee->id,
        'label_id' => $this->labelId,
    ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'sale_order_sku' => [
            'This SKU and country of origin do not have a corresponding part number.'
        ]
    ]);
});
// deduction fail with Mexico - The scanned box must contain the product corresponding to the Label ID.
test('deduction fail with Mexico - The scanned box must contain the product corresponding to the Label ID', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);

    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $box = \App\Models\Box::factory([
        'warehouse_id' => $warehouse->id,
        'product_id' => 9999,
        'barcode' => 'AGSBTCU',
    ])->create();

    $this->params = [
        'box_barcode' => $box->barcode,
        'sku' => $this->productReplace->sku,
        'sale_order_sku' => $this->labelId,
    ];
    $addition = \App\Models\InventoryAddition::factory([
        'box_id' => $box->id,
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'quantity' => 10,
        'country' => 'VN'
    ])->create();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10]);

    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'shipment_id' => 1,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => $this->sku,
        'employee_pull_id' => $this->employee->id,
        'label_id' => $this->labelId,
    ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toMatchArray([
        'box_barcode' => [
            'The scanned box must contain the product corresponding to the Label ID'
        ]
    ]);
});

// deduction success with Mexico
test('deduction success with Mexico', function () {
    $this->warehouse = Warehouse::WAREHOUSE_MEXICO[0];
    $userMexico = User::factory()->create();
    $access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse]])->fromUser($userMexico);
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
        'code' => 'MX',
    ]);

    $this->location->warehouse_id = $warehouse->id;
    $this->location->save();
    $box = \App\Models\Box::factory([
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->productReplace->id,
        'barcode' => 'AGSBTCU',
    ])->create();

    $this->labelId = '10012022-MX-XL-000013';
    $this->sku = 'MTk3MTc4OA==' . $this->product->sku;
    $this->params = [
        'box_barcode' => $box->barcode,
        'sku' => $this->productReplace->sku,
        'sale_order_sku' => $this->labelId,
    ];
    $addition = \App\Models\InventoryAddition::factory([
        'box_id' => $box->id,
        'warehouse_id' => $warehouse->id,
        'product_id' => $this->product->id,
        'quantity' => 10,
        'country' => 'VN'
    ])->create();
    $partNumber = \App\Models\PartNumber::factory([
        'product_id' => $this->productReplace->id,
        'country' => $addition->country,
    ])->create();
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10, 'incoming_stock' => 10]);

    $locationQuantityBeforeTest = LocationProduct::factory()->create(['location_id' => $this->location->id,
        'product_id' => $this->productReplace->id, 'quantity' => 10]);

    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'shipment_id' => 1,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->product->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $warehouse->id,
        'sku' => $this->sku,
        'employee_pull_id' => $this->employee->id,
        'label_id' => $this->labelId,
    ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $response = $this->withHeader('Authorization', 'Bearer ' . $access_token)
        ->post($this->endpoint, $this->params);
    $dataResponse = json_decode($response->getContent(), true);

    $response->assertStatus(201);
    ///Assert them inventory_deduction thanh cong
    $deduction = InventoryDeduction::where('coo_iso2', $addition->country)->where('box_id', $box->id)->first();
    $this->assertEquals($deduction->wip_product_id, $this->product->id);
    $this->assertEquals($deduction->product_id, $this->productReplace->id);

    ///Assert them inventory thanh cong
    $inventory = Inventory::where('object_id', $deduction->id)->first();
    $this->assertNotEmpty($inventory);

    ///Assert pulled_at  trong sale_order_item_barcode được update now
    $saleOrderItemBarcode = SaleOrderItemBarcode::where('label_id', $this->labelId)->where('part_number_id', $partNumber->id)->first();
    $this->assertEquals(Carbon::parse($saleOrderItemBarcode->pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    ///Assert Sale Order : pulled_at = now
    $saleOrder = SaleOrder::where('id', $saleOrderItemBarcode->order_id)->first();
    $this->assertEquals($saleOrder->order_pulled_status, 1);
    $this->assertEquals(Carbon::parse($saleOrder->order_pulled_at)->format('Y-m-d'), Carbon::now()->format('Y-m-d'));

    //Assert quantity -= 1
    $productQuantity = ProductQuantity::where('warehouse_id', $warehouse->id)
        ->where('product_id', $this->productReplace->id)->first();
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity - 1);

    ///Assert quantity -= 1
    $locationProductQuantity = LocationProduct::where('product_id', $this->productReplace->id)->first();
    $this->assertEquals($locationProductQuantity->quantity, $locationQuantityBeforeTest->quantity - 1);

    $timeTracking = TimeTracking::where('id', $this->timeTracking->id)->first();
    $this->assertEquals($timeTracking->quantity, 1);

    //assert history order timeline
    $this->assertDatabaseHas('sale_order_history', [
        'order_id' => $saleOrder->id,
        'type' => SaleOrderHistory::UPDATE_ORDER_DEDUCTION_TYPE
    ]);
});
