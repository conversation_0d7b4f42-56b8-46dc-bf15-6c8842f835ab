<?php

use App\Models\Location;
use App\Models\Employee;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Box;
use App\Models\Warehouse;
use App\Models\StockTransfer;
use App\Models\StockTransferItem;

use Illuminate\Database\Eloquent\Factories\Sequence;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->destinationWarehouse = Warehouse::factory()->create();

    $this->endpoint = '/api/stock-transfer';
    $this->location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id, 'barcode' => "LO123"]);
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);
    $this->products = Product::factory()->count(4)->state(new Sequence(
        ['sku' => 'B00060734', 'gtin' => '00884074019267', 'name' => "A", 'gtin_case' => 72, 'parent_id' => 1],
        ['sku' => 'A00060735', 'gtin' => '00821780020764', 'name' => "B", 'gtin_case' => 72, 'parent_id' => 1],
        ['sku' => 'A00060736', 'gtin' => '00821780009970', 'name' => "C", 'gtin_case' => 72, 'parent_id' => 1],
        ['sku' => 'A00060740', 'gtin' => '00821780009955', 'name' => "D", 'gtin_case' => 72, 'parent_id' => 1],
    ))->create();

    $this->boxs = Box::factory()->count(4)->state(new Sequence(
        ['warehouse_id' => $this->warehouse->id, 'barcode' => "BARCODE1", 'product_id' => $this->products[0]->id, 'quantity' => 72, 'location_id' => $this->location->id],
        ['warehouse_id' => $this->warehouse->id, 'barcode' => "BARCODE2", 'product_id' => $this->products[1]->id, 'quantity' => 72, 'location_id' => $this->location->id],
        ['warehouse_id' => $this->warehouse->id, 'barcode' => "BARCODE3", 'product_id' => $this->products[2]->id, 'quantity' => 72, 'location_id' => $this->location->id],
        ['warehouse_id' => $this->warehouse->id, 'barcode' => "BARCODE4", 'product_id' => $this->products[3]->id, 'quantity' => 72, 'location_id' => $this->location->id],

    ))->create();

    $this->params = array(
        "employee_id" => $this->employee->id,
        "destination_warehouse_id" => $this->destinationWarehouse->id,
        "from_warehouse_id" => $this->warehouse->id,
        "id_time_checking" => rand(1, 100),
        'items' => [
            [
                "product_id" => $this->products[0]->id,
                "product_name" => $this->products[0]->name,
                "quantity" => $this->products[0]->gtin_case * 1,
                "request_box" => 1,
                "gtin_case" => $this->products[0]->gtin_case
            ],
            [
                "product_id" => $this->products[1]->id,
                "product_name" => $this->products[1]->name,
                "quantity" => $this->products[1]->gtin_case * 1,
                "request_box" => 1,
                "gtin_case" => $this->products[1]->gtin_case
            ],

        ]
    );
});
test('stock transfer create request - missing destination_warehouse_id', function () {
    $this->params['destination_warehouse_id'] = "";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("destination_warehouse_id");
});

test('stock transfer create request - missing from_warehouse_id', function () {
    $this->params['from_warehouse_id'] = "";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("from_warehouse_id");
});
test('stock transfer create request - missing employee_id', function () {
    $this->params['employee_id'] = "";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("employee_id");
});
test('stock transfer create request - missing id_time_checking', function () {
    $this->params['id_time_checking'] = "";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("id_time_checking");
});
test('stock transfer create request - missing items', function () {
    $this->params['items'] = "";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("items");
});
test('stock transfer create request - from warehouse same destination warehouse', function () {
    $this->params['destination_warehouse_id'] = $this->warehouse->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("destination_warehouse_id");
});
test('stock transfer create request : employee is belong another warehouse', function () {
    $employee = Employee::factory()->create();
    $this->params['employee_id'] = $employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "employee_id" => ["The selected employee id is invalid."]
    ]);
});
test('stock transfer create request : product is deleted', function () {
    $this->products[0]->is_deleted = Product::DELETED;
    $this->products[0]->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("items.0.product_id");
});
test('stock transfer create request : not enough boxes in inventory', function () {
    $this->params['items'][0]['request_box'] = 2;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey("items.0.request_box");
});

test('stock transfer create request : successfully', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $stockTransfer = StockTransfer::with('items')->first();
    expect($stockTransfer)->toBeObject();
    expect(json_decode($response->getContent(), true))->toHaveKey("message");
});
