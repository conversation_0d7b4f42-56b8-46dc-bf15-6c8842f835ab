<?php

use App\Models\Box;
use App\Models\BoxMoving;
use App\Models\Employee;
use App\Models\Inventory;
use App\Models\InventoryAddition;
use App\Models\Location;
use App\Models\LocationProduct;
use App\Models\PartNumber;
use App\Models\PartNumberFifo;
use App\Models\Product;
use App\Models\ProductQuantity;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Setting;
use App\Models\TimeTracking;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/inventory-addition/manual';
    $product = Product::factory()->create();
    $this->location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $this->warehouse->id]);
    $purchaseOrder = PurchaseOrder::factory()->create(['warehouse_id' => $this->warehouse->id]);
    PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $product->id, 'quantity' => 2]);
    $vendor = Vendor::factory()->create();
    $employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);
    $timeTracking = TimeTracking::factory()->create(['job_type' => 'addition']);
    $this->location_max_box = 100;
    Setting::factory()->create([
        'name' => Setting::LOCATION_MAX_BOX,
        'value' => $this->location_max_box ,
    ]);
    $this->params = [
        'vendor_id' => $vendor->id,
        'po_number' => $purchaseOrder->po_number,
        'po_id' => $purchaseOrder->id,
        'gtin' => '',
        'quantity' => 1,
        'product_id' => $product->id,
        'barcode' => 'Box 12389',
        'tracking_number' => '',
        'invoice_number' => 3291,
        'location_id' => $this->location->id,
        'location_name' => '',
        'employee_id' => $employee->code,
        'id_time_checking' => $timeTracking->id,
        'country' => '',
    ];
});

///Validate required field : po_number, product_id, location_id, quantity, employee_id, id_time_checking
test('addition failed - validate required po_number, product_id, location_id, quantity, employee_id', function () {
    $fieldValidate = [
        'po_number',
        'product_id',
        'quantity',
        'employee_id',
        'id_time_checking'
    ];
    foreach ($fieldValidate as $field) {
        $input = $this->params;
        unset($input[$field]);

        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $input);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey($field);
    }
});

// addition failed - location max box 
test('addition failed - location max box', function () {
    Box::factory()->count($this->location_max_box)->create(['location_id' => $this->location->id]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('location_id');
});

///Employee thuộc warehouse khác => dừng addition
test('addition failed - employee in another warehouse', function () {
    $employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id + 1, 'code' => 'WA']);
    $this->params['employee_id'] = $employee->code;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('employee_id');
});
///PO thuộc warehouse khác => dừng addition
test('addition failed - PO in another warehouse', function () {
    $warehouse = Warehouse::factory()->create();
    PurchaseOrder::where('id', $this->params['po_id'])->update(['warehouse_id' => $warehouse->id]);
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('purchase_order');
});

///truyền BoxID lên thì location bắt buộc phải có type là RACK
test('addition failed - location must is RACK', function () {
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id]);
    $this->params['location_id'] = $location->id;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('location_id');
});

///check BoxID (barcode) truyền lên đã được thêm hay chưa ? thêm rồi thì dừng addition
test('addition failed - The box id has already been taken', function () {
    Box::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'barcode' => $this->params['barcode'],
        'location_id' => $this->location->id
    ]);
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('barcode');
});

///check product có tồn tại trong PO không ? Nếu không thì dừng addition
test('addition failed - product does not exist in the order', function () {
    $product = Product::factory()->create(['sku' => 'test']);
    $this->params['product_id'] = $product->id;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('product');
});

///khi BoxID (barcode) truyền lên rỗng thì location bắt buộc phải có type là pulling shelve
test('addition failed - Location must pulling shelve when barcode is empty', function () {
    unset($this->params['barcode']);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('location_id');
});

///PO có trạng thái là completed hoặc canceled thì không được phép addition
test('addition failed - The purchase order has been completed or canceled', function () {
    $orderStatus = [
        PurchaseOrder::COMPLETED_STATUS,
        PurchaseOrder::CANCELLED_STATUS
    ];

    foreach ($orderStatus as $status) {
        $product = Product::factory()->create();
        $purchaseOrder = PurchaseOrder::factory()->create(['warehouse_id' => $this->warehouse->id, 'order_status' => $status]);
        PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $product->id, 'quantity' => 2]);
        $this->params['product_id'] = $product->id;
        $this->params['po_id'] = $purchaseOrder->id;

        $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
            ->post($this->endpoint, $this->params);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey('purchase_order');
    }
});

///Nếu tồn tại một product trong PO đã có quantity_onhand = quantity thì không cho phép addition
test('addition failed - The quantity is exceeded the one in purchase order', function () {
    $product = Product::factory()->create();
    $purchaseOrder = PurchaseOrder::factory()->create(['warehouse_id' => $this->warehouse->id]);
    PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $product->id, 'quantity' => 2, 'quantity_onhand' => 2]);
    $this->params['product_id'] = $product->id;
    $this->params['quantity'] = 1;
    $this->params['po_id'] = $purchaseOrder->id;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('product');
});

test('addition failed - The quantity is zero', function () {
    $product = Product::factory()->create();
    $purchaseOrder = PurchaseOrder::factory()->create(['warehouse_id' => $this->warehouse->id]);
    PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $product->id, 'quantity' => 2, 'quantity_onhand' => 2]);
    $this->params['product_id'] = $product->id;
    $this->params['quantity'] = 0;
    $this->params['po_id'] = $purchaseOrder->id;

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('product');
});
//addition thành công khi chọn location là RACK
test('addition SUCCESS - location is Rack', function () {
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
        'product_id' => $this->params['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(201)
        ->assertJsonStructure([
            'id',
            'vendor_id',
            'po_id',
            'po_number',
            'invoice_number',
            'location_id',
            'gtin',
            'quantity',
            'product_id',
            'box_id',
            'created_at',
            'updated_at',
            'user_id',
            'warehouse_id',
            'is_deleted',
            'tracking_number',
            'employee_id'
        ]);

    ///Assert them inventory_addition thanh cong
    $addition = InventoryAddition::where('po_number', $this->params['po_number'])->first();
    $this->assertNotEmpty($addition);

    ///Assert them box thanh cong (Vì ban đầu chưa có box sẽ tự động tạo ra box trong quá trình addition)
    $box = Box::where('barcode', $this->params['barcode'])->first();
    $this->assertNotEmpty($box);

    ///Assert them box_moving thanh cong (Tạo box đồng thời thêm history trong box_moving)
    $boxMoving = BoxMoving::where('box_id', $box->id)->first();
    $this->assertNotEmpty($boxMoving);

    $purchaseOderItem = PurchaseOrderItem::where('product_id', $this->params['product_id'])->first();
    ///Assert quantity_onhand = input quantity , received_status = partial_received
    $this->assertEquals($purchaseOderItem->quantity_onhand, $this->params['quantity']);
    $this->assertEquals($purchaseOderItem->received_status, PurchaseOrderItem::PARTIAL_RECEIVED_STATUS);

    ///Assert order_status = partial_received
    $po = PurchaseOrder::where('po_number', $this->params['po_number'])->first();
    $this->assertEquals($po->order_status, PurchaseOrder::PARTIAL_RECEIVED_STATUS);

    $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $this->params['product_id'])
        ->first();

    ///Assert quantity += input quantity  và incoming_stock -= input quantity
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity + $this->params['quantity']);
    $this->assertEquals($productQuantity->incoming_stock, $productQuantityBeforeTest->incoming_stock - $this->params['quantity']);

    $locationProductQuantity = LocationProduct::where('product_id', $this->params['product_id'])
        ->where('location_id', $this->location->id)
        ->sum('quantity');
    ///Assert location quantity += input quantity ( hien tai location quantity = 0)
    $this->assertEquals($locationProductQuantity, $this->params['quantity']);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);
    $poItem = PurchaseOrderItem::where('product_id', $this->params['product_id'])->first();
    $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $addition->cost_value);
    $box = Box::where('barcode', $this->params['barcode'])->first();
    $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $box->cost_value);
    $this->assertEquals($box->po_id, $po->id);
    $this->assertEquals($box->po_item_id, $poItem->id);
    $inventory = Inventory::where('object_id', $addition->id)
        ->where('direction', 0)
        ->where('type', Inventory::TYPE_INPUT)
        ->where('product_id', $this->params['product_id'])
        ->where('quantity', $this->params['quantity'])
        ->first();
    $this->assertNotEmpty($inventory);
});

//addition thành công khi chọn location là Pulling Shelves
test('addition SUCCESS - location is Pulling Shelves', function () {
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
        'product_id' => $this->params['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);
    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $this->warehouse->id]);
    $this->params['location_id'] = $location->id;
    unset($this->params['barcode']);

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(201)
        ->assertJsonStructure([
            'id',
            'vendor_id',
            'po_id',
            'po_number',
            'invoice_number',
            'location_id',
            'gtin',
            'quantity',
            'product_id',
            'box_id',
            'created_at',
            'updated_at',
            'user_id',
            'warehouse_id',
            'is_deleted',
            'tracking_number',
            'employee_id'
        ]);

    ///Assert them inventory_addition thanh cong
    $addition = InventoryAddition::where('po_number', $this->params['po_number'])->first();
    $this->assertNotEmpty($addition);

    $purchaseOderItem = PurchaseOrderItem::where('product_id', $this->params['product_id'])->first();
    ///Assert quantity_onhand = input quantity , received_status = partial_received
    $this->assertEquals($purchaseOderItem->quantity_onhand, $this->params['quantity']);
    $this->assertEquals($purchaseOderItem->received_status, PurchaseOrderItem::PARTIAL_RECEIVED_STATUS);

    ///Assert order_status = partial_received
    $po = PurchaseOrder::where('po_number', $this->params['po_number'])->first();
    $this->assertEquals($po->order_status, PurchaseOrder::PARTIAL_RECEIVED_STATUS);

    $productQuantity = ProductQuantity::where('warehouse_id', $this->warehouse->id)
        ->where('product_id', $this->params['product_id'])
        ->first();

    ///Assert quantity += input quantity  và incoming_stock -= input quantity
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity + $this->params['quantity']);
    $this->assertEquals($productQuantity->incoming_stock, $productQuantityBeforeTest->incoming_stock - $this->params['quantity']);

    $locationProductQuantity = LocationProduct::where('product_id', $this->params['product_id'])
        ->where('location_id', $location->id)
        ->sum('quantity');
    ///Assert location quantity += input quantity ( hien tai location quantity = 0)
    $this->assertEquals($locationProductQuantity, $this->params['quantity']);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);

    $poItem = PurchaseOrderItem::where('product_id', $this->params['product_id'])->first();
    $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $addition->cost_value);

    $inventory = Inventory::where('object_id', $addition->id)
        ->where('direction', 0)
        ->where('type', Inventory::TYPE_INPUT)
        ->where('product_id', $this->params['product_id'])
        ->where('quantity', $this->params['quantity'])
        ->first();
    $this->assertNotEmpty($inventory);
});

//addition với warehouse mexico validate
test('addition with warehouse mexico validate', function () {
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
    ]);
    $user = User::factory()->create();
    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2023]);
    ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->params['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);
    $purchaseOrder = PurchaseOrder::factory()->for(Vendor::factory()->create())->create(['warehouse_id' => $warehouse->id]);
    PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $this->params['product_id'], 'quantity' => 2]);

    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $warehouse->id]);
    $this->params['location_id'] = $location->id;
    $this->params['employee_id'] = $employee->code;
    unset($this->params['barcode']);
    $this->params['po_number'] = $purchaseOrder->po_number;
    $this->params['po_id'] = $purchaseOrder->id;

    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['country'][0])->toMatchArray(['The country field is required.']);

    PartNumber::factory()->create([
        'part_number' => 'datpt123',
        'product_id' => $this->params['product_id'],
        'country' => 'vn',
    ]);
    $this->params['country'] = 'us';

    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['partNumber'][0])->toMatchArray(['There is no part number available for both the country and the product.']);
});

//addition với warehouse mexico thành công khi chọn location là Pulling Shelves
test('addition with warehouse mexico SUCCESS - location is Pulling Shelves', function () {
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
    ]);
    $user = User::factory()->create();
    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2023]);
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->params['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);
    $purchaseOrder = PurchaseOrder::factory()->for(Vendor::factory()->create())->create(['warehouse_id' => $warehouse->id]);
    PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $this->params['product_id'], 'quantity' => 2]);
    $dataPartNumber = PartNumber::factory([
        'part_number' => 'datpt123',
        'product_id' => $this->params['product_id'],
        'country' => 'vn',
    ])
        ->has(PartNumberFifo::factory([
            'quantity' => 100,
            'warehouse_id' => $warehouse->id,
            'product_id' => $this->params['product_id'],
        ]), 'partNumberFifos')
    ->create();

    $location = Location::factory()->create(['type' => Location::PULLING_SHELVES, 'warehouse_id' => $warehouse->id]);
    $this->params['location_id'] = $location->id;
    $this->params['employee_id'] = $employee->code;
    unset($this->params['barcode']);
    $this->params['country'] = $dataPartNumber->country;
    $this->params['po_number'] = $purchaseOrder->po_number;
    $this->params['po_id'] = $purchaseOrder->id;

    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(201)
        ->assertJsonStructure([
            'id',
            'vendor_id',
            'po_id',
            'po_number',
            'invoice_number',
            'location_id',
            'gtin',
            'quantity',
            'product_id',
            'box_id',
            'created_at',
            'updated_at',
            'user_id',
            'warehouse_id',
            'is_deleted',
            'tracking_number',
            'employee_id'
        ]);

    ///Assert them inventory_addition thanh cong
    $addition = InventoryAddition::where('po_number', $this->params['po_number'])->first();
    $this->assertEquals($addition->country, $this->params['country']);
    $this->assertNotEmpty($addition);

    $purchaseOderItem = PurchaseOrderItem::where('product_id', $this->params['product_id'])->where('po_id', $purchaseOrder->id)->first();
    ///Assert quantity_onhand = input quantity , received_status = partial_received
    $this->assertEquals($purchaseOderItem->quantity_onhand, $this->params['quantity']);
    $this->assertEquals($purchaseOderItem->received_status, PurchaseOrderItem::PARTIAL_RECEIVED_STATUS);

    ///Assert order_status = partial_received
    $po = PurchaseOrder::where('po_number', $this->params['po_number'])->where('warehouse_id', $warehouse->id)->first();
    $this->assertEquals($po->order_status, PurchaseOrder::PARTIAL_RECEIVED_STATUS);

    $productQuantity = ProductQuantity::where('warehouse_id', $warehouse->id)
        ->where('product_id', $this->params['product_id'])
        ->first();

    ///Assert quantity += input quantity  và incoming_stock -= input quantity
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity + $this->params['quantity']);
    $this->assertEquals($productQuantity->incoming_stock, $productQuantityBeforeTest->incoming_stock - $this->params['quantity']);

    $locationProductQuantity = LocationProduct::where('product_id', $this->params['product_id'])
        ->where('location_id', $location->id)
        ->sum('quantity');
    ///Assert location quantity += input quantity ( hien tai location quantity = 0)
    $this->assertEquals($locationProductQuantity, $this->params['quantity']);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);

    $poItem = PurchaseOrderItem::where('product_id', $this->params['product_id'])->first();
    $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $addition->cost_value);

    $inventory = Inventory::where('object_id', $addition->id)
        ->where('direction', 0)
        ->where('type', Inventory::TYPE_INPUT)
        ->where('product_id', $this->params['product_id'])
        ->where('quantity', $this->params['quantity'])
        ->first();
    $this->assertNotEmpty($inventory);
});

//addition thành công khi chọn location là RACK
test('addition with warehouse mexico SUCCESS - location is Rack', function () {
    $warehouse = Warehouse::factory()->create([
        'id' => 18,
    ]);
    $user = User::factory()->create();
    $accessToken = JWTAuth::customClaims(['warehouse' => ['id' => $warehouse->id]])->fromUser($user);
    $employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 2023]);
    $productQuantityBeforeTest = ProductQuantity::factory()->create(['warehouse_id' => $warehouse->id,
        'product_id' => $this->params['product_id'], 'quantity' => 10, 'incoming_stock' => 10]);
    $purchaseOrder = PurchaseOrder::factory()->for(Vendor::factory()->create())->create(['warehouse_id' => $warehouse->id]);
    PurchaseOrderItem::factory()->create(['po_id' => $purchaseOrder->id, 'product_id' => $this->params['product_id'], 'quantity' => 2]);
    $dataPartNumber = PartNumber::factory([
        'part_number' => 'datpt123',
        'product_id' => $this->params['product_id'],
        'country' => 'vn',
    ])
        ->has(PartNumberFifo::factory([
            'quantity' => 100,
            'warehouse_id' => $warehouse->id,
            'product_id' => $this->params['product_id'],
        ]), 'partNumberFifos')
        ->create();

    $location = Location::factory()->create(['type' => Location::RACK, 'warehouse_id' => $warehouse->id]);
    $this->params['location_id'] = $location->id;
    $this->params['employee_id'] = $employee->code;
    $this->params['country'] = $dataPartNumber->country;
    $this->params['po_number'] = $purchaseOrder->po_number;
    $this->params['po_id'] = $purchaseOrder->id;

    $response = $this->withHeader('Authorization', 'Bearer ' . $accessToken)
        ->post($this->endpoint, $this->params);

    $response->assertStatus(201)
        ->assertJsonStructure([
            'id',
            'vendor_id',
            'po_id',
            'po_number',
            'invoice_number',
            'location_id',
            'gtin',
            'quantity',
            'product_id',
            'box_id',
            'created_at',
            'updated_at',
            'user_id',
            'warehouse_id',
            'is_deleted',
            'tracking_number',
            'employee_id'
        ]);

    ///Assert them inventory_addition thanh cong
    $addition = InventoryAddition::where('po_number', $this->params['po_number'])->first();
    $this->assertEquals($addition->country, $this->params['country']);
    $this->assertNotEmpty($addition);

    $purchaseOderItem = PurchaseOrderItem::where('product_id', $this->params['product_id'])->where('po_id', $purchaseOrder->id)->first();
    ///Assert quantity_onhand = input quantity , received_status = partial_received
    $this->assertEquals($purchaseOderItem->quantity_onhand, $this->params['quantity']);
    $this->assertEquals($purchaseOderItem->received_status, PurchaseOrderItem::PARTIAL_RECEIVED_STATUS);

    ///Assert order_status = partial_received
    $po = PurchaseOrder::where('po_number', $this->params['po_number'])->where('warehouse_id', $warehouse->id)->first();
    $this->assertEquals($po->order_status, PurchaseOrder::PARTIAL_RECEIVED_STATUS);

    $productQuantity = ProductQuantity::where('warehouse_id', $warehouse->id)
        ->where('product_id', $this->params['product_id'])
        ->first();

    ///Assert quantity += input quantity  và incoming_stock -= input quantity
    $this->assertEquals($productQuantity->quantity, $productQuantityBeforeTest->quantity + $this->params['quantity']);
    $this->assertEquals($productQuantity->incoming_stock, $productQuantityBeforeTest->incoming_stock - $this->params['quantity']);

    $locationProductQuantity = LocationProduct::where('product_id', $this->params['product_id'])
        ->where('location_id', $location->id)
        ->sum('quantity');
    ///Assert location quantity += input quantity ( hien tai location quantity = 0)
    $this->assertEquals($locationProductQuantity, $this->params['quantity']);
    // assert time tracking ++quantity
    $this->assertDatabaseHas('time_tracking', ['quantity' => 1, 'id' => $this->params['id_time_checking']]);

    $poItem = PurchaseOrderItem::where('product_id', $this->params['product_id'])->first();
    $this->assertEquals(number_format($poItem->price * $poItem->quantity_onhand, 2), $addition->cost_value);

    $inventory = Inventory::where('object_id', $addition->id)
        ->where('direction', 0)
        ->where('type', Inventory::TYPE_INPUT)
        ->where('product_id', $this->params['product_id'])
        ->where('quantity', $this->params['quantity'])
        ->first();
    $this->assertNotEmpty($inventory);

    $box = Box::where('product_id', $this->params['product_id'])
        ->where('warehouse_id', $warehouse->id)
        ->where('barcode', $this->params['barcode'])
        ->where('country', $this->params['country'])
        ->first();
    $this->assertNotEmpty($box);
});
