<?php

use App\Models\Employee;
use App\Models\Supply;
use App\Models\SupplyAdjustInventory;
use App\Models\SupplyCategory;
use App\Models\SupplyInventory;
use App\Models\SupplyQuantity;
use App\Models\SupplyUnit;
use App\Models\TimeTracking;
use App\Models\Vendor;
use Faker\Factory as faker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    config(['jwt.warehouse_id' => $this->warehouse->id]);
    $this->vendor = Vendor::factory()->create();
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);
    $this->supplyCategory = SupplyCategory::factory()->create(['name' => faker::create()->word()]);
    $this->supplyUnit = SupplyUnit::factory()->create(['name' => faker::create()->word()]);
    $this->timeTracking = TimeTracking::factory()
        ->create([
            'employee_id' => $this->employee->id,
            'job_type' => 'supply_adjust_inventory',
            'quantity' => 0,
            'start_time' => Carbon::now()
        ]);
    $this->supply = Supply::factory()->count(2)->sequence(
        [
            'name' => faker::create()->word(),
            'sku' => faker::create()->unique()->word() . '001',
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->supplyCategory->id,
            'unit_id' => $this->supplyUnit->id,
        ],
        [
            'name' => faker::create()->word(),
            'sku' => faker::create()->unique()->word() . '002',
            'vendor_id' => $this->vendor->id,
            'category_id' => $this->supplyCategory->id,
            'unit_id' => $this->supplyUnit->id,
        ],
    )->create();

    $this->supplyQuantity = SupplyQuantity::factory()->count(2)->sequence(
        [
            'supply_id' => $this->supply[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => faker::create()->randomDigitNotNull(),
            'incoming_stock' => faker::create()->randomDigitNotNull(),
        ],
        [
            'supply_id' => $this->supply[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => faker::create()->randomDigitNotNull(),
            'incoming_stock' => faker::create()->randomDigitNotNull(),
        ],

    )->create();

    $this->endpoint = '/api/supply-adjust';
    $this->params = [
        'quantity' => 1,
        'supply_id' => $this->supply[0]->id,
        'employee_id' => $this->employee->id,
        'id_time_checking' => $this->timeTracking->id,
    ];
});
test('supply adjust : validate required', function () {
    $fields = ['supply_id', 'quantity', 'employee_id', 'id_time_checking'];
    foreach ($fields as $field) {
        $inputs = $this->params;
        unset($inputs[$field]);
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->access_token,
            'Accept' => 'application/json',
        ])->post($this->endpoint, $inputs);
        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toMatchArray([
            "$field" => ['The ' . str_replace('_', ' ', $field) . ' field is required.']
        ]);
    }
});

test('supply adjust : employee is belong another warehouse', function () {
    $employee = Employee::factory()->create(['warehouse_id' => faker::create()->randomDigitNot($this->warehouse->id)]);
    $this->params['employee_id'] = $employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'employee_id' => ['The selected employee id is invalid.']
    ]);
});
test('supply adjust : supply invalid', function () {
    $this->params['supply_id'] = faker::create()->randomNumber(5, true);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'supply_id' => ['The selected supply id is invalid.']
    ]);
});

test('supply adjust : supply not inventory before', function () {
    SupplyQuantity::where('supply_id', $this->params['supply_id'])->delete();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'supply_id' => ['The SKU cannot be adjusted because it has not been added to the inventory yet.']
    ]);
});

test('supply adjust : The quantity is matched', function () {
    $supplyQuantity = SupplyQuantity::where('supply_id', $this->params['supply_id'])->first();
    $this->params['quantity'] = $supplyQuantity->quantity;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'quantity' => ['The quantity is matched, so no need to adjust!']
    ]);
});

test('supply adjust :successfully', function () {
    $supplyQuantityBegin = SupplyQuantity::where('supply_id', $this->params['supply_id'])->first();
    $this->params['quantity'] = faker::create()->randomDigitNot($supplyQuantityBegin->quantity);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    $adjust = SupplyAdjustInventory::where('supply_id', $this->params['supply_id'])->first();
    expect($adjust)->toBeObject();
    $inventory = SupplyInventory::where('object_id', $adjust->id)->first();
    expect($inventory)->toBeObject();
    $supplyQuantity = SupplyQuantity::where('supply_id', $this->params['supply_id'])->first();
    expect($supplyQuantity->quantity)->toEqual($supplyQuantityBegin->quantity + $adjust->quantity_adjust);
    $timeTracking = TimeTracking::find($this->timeTracking->id);
    expect($timeTracking->quantity)->toEqual(1);
});
