<?php

use App\Jobs\AggregateWarehouseSupplyReportJob;
use App\Models\QueueJob;
use App\Models\Supply;
use App\Models\SupplyInventory;
use App\Models\Warehouse;
use App\Repositories\SupplyInventoryRepository;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->supplyInventoryRepo = app()->make(SupplyInventoryRepository::class);
    $this->supplyList = Supply::factory()->count(2)->sequence(
        fn ($sequence) => [
            'id' => $sequence->index + 1,
            'sku' => 'sku' . ($sequence->index + 1),
        ],
    )->create();

    $this->warehouse = Warehouse::factory()->create([
        'id' => 1,
        'code' => 'SJ'
    ]);

    $this->targetStartDate = Carbon::now()->subMonth()->startOfMonth()->toDateString();
    $this->targetEndDate = Carbon::now()->subMonth()->endOfMonth()->toDateString();
});

test('AggregateWarehouseSupplyReportJob - test handle function', function () {
    // Create Supply Inventory addition/deduction
    $this->supplyInventory = SupplyInventory::factory()->count(4)->sequence([
        'direction' => 0,
        'quantity' => 200,
        'created_at' => '2024-03-10',
        'supply_id' => $this->supplyList[0]->id,
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => 0
    ], [
        'direction' => 1,
        'quantity' => 99,
        'created_at' => '2024-04-01',
        'supply_id' => $this->supplyList[0]->id,
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => 0
    ], [
        'direction' => 0,
        'quantity' => 1000,
        'created_at' => '2024-03-01',
        'supply_id' => $this->supplyList[1]->id,
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => 0
    ], [
        'direction' => 1,
        'quantity' => 100,
        'created_at' => '2024-04-01',
        'supply_id' => $this->supplyList[1]->id,
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => 0
    ])->create();

    $aggregateSupplyInventoryReport
        = new AggregateWarehouseSupplyReportJob($this->warehouse->id, $this->targetStartDate, $this->targetEndDate);
    $mockRepository = app()->make(SupplyInventoryRepository::class);

    $aggregateSupplyInventoryReport->handle($mockRepository);
    //Assert having report for first Supply in March 2024
    $this->assertDatabaseHas('fifo_supply_inventory', [
        'supply_id' => $this->supplyList[0]->id,
        'warehouse_id' => $this->warehouse->id,
        'sku' => $this->supplyList[0]->sku,
        'start_date' => '2024-03-01',
        'end_date' => '2024-03-31',
        'end_unit' => 200,
        'cost_avg' => 0.0
    ]);

    //Assert having report for second Supply in March 2024
    $this->assertDatabaseHas('fifo_supply_inventory', [
        'supply_id' => $this->supplyList[1]->id,
        'warehouse_id' => $this->warehouse->id,
        'sku' => $this->supplyList[1]->sku,
        'start_date' => '2024-03-01',
        'end_date' => '2024-03-31',
        'end_unit' => 1000,
        'cost_avg' => 0.0
    ]);

    //Assert having report for first Supply in April 2024
    $this->assertDatabaseHas('fifo_supply_inventory', [
        'supply_id' => $this->supplyList[0]->id,
        'warehouse_id' => $this->warehouse->id,
        'sku' => $this->supplyList[0]->sku,
        'start_date' => '2024-04-01',
        'end_date' => '2024-04-30',
        'end_unit' => 101,
        'cost_avg' => 0.0
    ]);

    //Assert having report for second Supply in April 2024
    $this->assertDatabaseHas('fifo_supply_inventory', [
        'supply_id' => $this->supplyList[1]->id,
        'warehouse_id' => $this->warehouse->id,
        'sku' => $this->supplyList[1]->sku,
        'start_date' => '2024-04-01',
        'end_date' => '2024-04-30',
        'end_unit' => 900,
        'cost_avg' => 0.0
    ]);
})->skip();

test('Supply monthly report command test - Call command and job queue is saved to DB.', function () {
    $this->supplyInventory = SupplyInventory::factory()->count(2)->sequence([
        'direction' => 0,
        'quantity' => 999,
        'created_at' => '2024-01-10',
        'supply_id' => $this->supplyList[0]->id,
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => 0
    ], [
        'direction' => 1,
        'quantity' => 111,
        'created_at' => '2024-03-01',
        'supply_id' => $this->supplyList[0]->id,
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => 0
    ])->create();

    $this->artisan('supply-inventory:aggregate');

    $this->assertDatabaseHas('jobs', [
        'queue' => QueueJob::AGGREGATE_SUPPLY_INVENTORY_REPORT,
        'attempts' => 0,
    ]);
});
