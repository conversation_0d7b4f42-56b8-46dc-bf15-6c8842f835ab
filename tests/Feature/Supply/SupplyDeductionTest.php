<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\SupplyUnit;
use App\Models\Supply;
use App\Models\Vendor;
use App\Models\SupplyCategory;
use App\Models\SupplyPurchaseOrder;
use App\Models\SupplyPurchaseOrderItem;
use App\Models\SupplyQuantity;
use App\Models\SupplyInventoryDeduction;
use App\Models\SupplyInventory;
use App\Models\TimeTracking;
use App\Models\Employee;
use Faker\Factory as faker;
use Carbon\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    config(['jwt.warehouse_id' => $this->warehouse->id]);
    $this->vendor = Vendor::factory()->create();
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);
    $this->supplyCategory = SupplyCategory::factory()->create(['name' => faker::create()->word()]);
    $this->supplyUnit = SupplyUnit::factory()->create(['name' => faker::create()->word()]);
    $this->supply = Supply::factory()->count(2)->sequence(
        [
            'name' => faker::create()->word(),
            'sku' => faker::create()->unique()->word() . "001",
             'vendor_id' => $this->vendor->id,
            'category_id' => $this->supplyCategory->id,
            'unit_id' => $this->supplyUnit->id,
        ],
        [
            'name' => faker::create()->word(),
            'sku' => faker::create()->unique()->word() ."002",
             'vendor_id' => $this->vendor->id,
            'category_id' => $this->supplyCategory->id,
            'unit_id' => $this->supplyUnit->id,
        ]
    )->create();

    $this->supplyPo = SupplyPurchaseOrder::factory([
        'warehouse_id' => $this->warehouse->id,
        'vendor_id' => $this->vendor->id,
        'po_number' => faker::create()->unique()->text(12),
        'order_number' => faker::create()->unique()->text(12),
        'invoice_number' => faker::create()->unique()->text(10),
        'order_date' => faker::create()->date(),
        'delivery_date' => faker::create()->date(),
        'payment_terms' => faker::create()->randomDigitNotNull(),
        'tracking_carrier' => faker::create()->randomDigitNotNull(),
        'tracking_number' => faker::create()->unique()->text(15),
        'order_status' => SupplyPurchaseOrder::SHIPPED_STATUS,
    ])
        ->has(SupplyPurchaseOrderItem::factory()->count(2)->sequence(
            [
                'supply_id' => $this->supply[0]->id,
                'quantity' => 10,
                'price' => 0,
                'total' => 0,
            ],
            [
                'supply_id' => $this->supply[1]->id,
                'quantity' => 10,
                'price' => 0,
                'total' => 0,
            ]
        ),
            'items')
        ->create();

    $this->supplyQuantity = SupplyQuantity::factory()->count(2)->sequence(
        [
            'supply_id' => $this->supply[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => 10,
            'incoming_stock' => 10,
        ],
        [
            'supply_id' => $this->supply[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'quantity' => 10,
            'incoming_stock' => 10,
        ],

    )->create();
    $this->timeTracking = TimeTracking::factory()
        ->create([
            'employee_id' => $this->employee->id,
            'job_type' => 'supply_addition',
            'quantity' => 0,
            'start_time' => Carbon::now()
        ]);
    $this->endpoint = '/api/supply-deduction';
    $this->params = array(
        'quantity' => 1,
        'supply_id' => $this->supply[0]->id,
        'employee_id' => $this->employee->id,
        'id_time_checking' => $this->timeTracking->id,
    );

});
/// validate field is required
test('supply deduction : validate required', function () {
    $fields = ['supply_id', 'quantity', 'employee_id', 'id_time_checking'];
    foreach ($fields as $field) {
        $inputs = $this->params;
        unset($inputs[$field]);
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->access_token,
            'Accept' => 'application/json',
        ])->post($this->endpoint, $inputs);
        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toMatchArray([
            "$field" => ["The " . str_replace("_", " ", $field) . " field is required."]
        ]);
    }
});

test('supply deduction : employee is belong another warehouse', function () {
    $employee = Employee::factory()->create(['warehouse_id'=> faker::create()->randomDigitNot($this->warehouse->id)]);
    $this->params['employee_id'] = $employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "employee_id" => ["The selected employee id is invalid."]
    ]);
});
test('supply deduction : validate supply invalid', function () {
    Supply::where('id', $this->params['supply_id'])->delete();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "supply_id" => ["supply not found"]
    ]);
});
test('supply deduction : SKU not inventory', function () {
    SupplyQuantity::where('supply_id', $this->params['supply_id'])->delete();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "supply_id" => [
            "supply inventory not found"
        ]
    ]);
});
test('supply deduction : max quantity deduction', function () {
    $this->params['quantity']  = 12;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "quantity" => ["The quantity may not be greater than 10."]
    ]);
});

test('supply deduction : successfully', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(201);
    $dataResponse = json_decode($response->getContent(), true);
    $inventoryDeduction = SupplyInventoryDeduction::where('id', $dataResponse['id'])->first();
    $inventory = SupplyInventory::where('object_id', $inventoryDeduction->id)->first();
    expect($inventoryDeduction)->toBeObject();
    expect($inventory)->toBeObject();
    $supplyQuantity = SupplyQuantity::where('supply_id', $dataResponse['supply_id'])->first();
    expect($supplyQuantity->quantity)->toEqual(9);
    $timeTracking = TimeTracking::find($this->timeTracking->id);
    expect($timeTracking->quantity)->toEqual($this->params['quantity']);
});


