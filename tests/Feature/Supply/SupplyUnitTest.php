<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Database\Eloquent\Factories\Sequence;
use <PERSON>mon\JWTAuth\Facades\JWTAuth;
use App\Models\SupplyUnit;
use Faker\Factory as faker;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/supply-unit';
    $this->params = array(
        "name" => faker::create()->word(),
    );
});
/// validate unit name is required
test('create supply unit : missing name', function () {
    unset($this->params['name']);
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "errors" => [
            "name" => ["The name field is required."]
        ]
    ]);
});
/// validate unit name is unique
test('create supply unit : validate name is unique', function () {
    $supplyUnit = SupplyUnit::factory()->create(['name' => faker::create()->word()]);
    $this->params = array(
        "name" => $supplyUnit->name,
    );
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        "errors" => [
            "name" => ["The name has already been taken."]
        ]
    ]);
});
/// create supply unit successfully
test('create supply unit : successfully', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(201);
    expect(json_decode($response->getContent(), true)['data'])->toHaveKeys(['id', 'name', 'created_at', 'updated_at']);
    $supplyUnit = SupplyUnit::find(json_decode($response->getContent(), true)['data']['id']);
    expect($supplyUnit->name)->toBe($this->params['name']);
});
