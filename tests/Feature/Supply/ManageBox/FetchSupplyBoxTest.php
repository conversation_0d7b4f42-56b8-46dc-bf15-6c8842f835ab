<?php

use App\Models\Supply;
use App\Models\SupplyBox;
use App\Models\SupplyLocation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Config;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    Config::set('jwt.warehouse_id', $warehouse->id);
    $this->supply = Supply::create([
        'name' => 'GTX Pro 1.8L Cyan Ink',
        'category_id' => 1,
        'unit_id' => 1,
        'vendor_id' => 21,
        'sku' => 'GTCCSIE005L'
    ]);

    $this->supplyLocation = SupplyLocation::create([
        'barcode' => 'Location 1 - San Jose',
        'warehouse_id' => $this->warehouse->id
    ]);

    $this->fixedBoxRecord = 3;

    $this->supplyBoxList = SupplyBox::factory()->count(3)->sequence(
        [
            'warehouse_id' => $this->warehouse->id,
            'barcode' => 'Box1',
            'location_id' => function () {
                return $this->supplyLocation->id;
            },
            'supply_id' => function () {
                return $this->supply->id;
            },
            'quantity' => 10
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'barcode' => 'Box2',
            'location_id' => function () {
                return $this->supplyLocation->id;
            },
            'supply_id' => function () {
                return $this->supply->id;
            },
            'quantity' => 20
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'barcode' => 'Box3',
            'location_id' => function () {
                return $this->supplyLocation->id;
            },
            'supply_id' => function () {
                return $this->supply->id;
            },
            'quantity' => 30
        ],
    )->create();

    $this->pageConfig = 1;
    $this->limitConfig = 10;
    $this->endpoint = '/api/supply-box/list?';

    $this->params = [
        'supply_name' => '',
        'supply_sku' => '',
        'limit' => $this->limitConfig,
        'page' => $this->pageConfig,
    ];
});

test('Get supply box list - Return empty array when name not found.', function () {
    $this->params['supply_name'] = 'Some non-existed name';
    //Call API
    $response = $this->get($this->endpoint . http_build_query($this->params));

    $responseData = json_decode($response->getContent());
    $response->assertStatus(Response::HTTP_OK);
    $this->assertEmpty($responseData->data);
});

test('Get supply box list - Return empty array when sku not found.', function () {
    $this->params['supply_sku'] = 'Some non-existed name or SKU';
    //Call API
    $response = $this->get($this->endpoint . http_build_query($this->params));

    $responseData = json_decode($response->getContent());
    $response->assertStatus(Response::HTTP_OK);

    $this->assertEmpty($responseData->data);
});

test('Get supply box list - Get list successfully with valid params', function () {
    $this->params['supply_name'] = $this->supply->name;
    $this->params['supply_sku'] = $this->supply->sku;
    // Call API
    $response = $this->get($this->endpoint . http_build_query($this->params));

    $responseData = json_decode($response->getContent());
    $response->assertStatus(Response::HTTP_OK);
    $this->assertCount($this->fixedBoxRecord, $responseData->data);
    //Last created record must be newest
    $this->assertEquals($this->supplyBoxList->last()->barcode, $responseData->data[0]->barcode);
});
