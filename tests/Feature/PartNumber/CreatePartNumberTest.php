<?php
use App\Models\Country;
use App\Models\Product;
use App\Models\PartNumber;
use App\Models\ProductSpec;

use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken] = createAccessToken();
    $this->access_token = $accessToken;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/part-number';
    $this->country = Country::factory()->create();
    $this->product = Product::factory()->create([
        'style' => '3001',
        'color' => 'AQUA',
        'size' => 'XS',
        'sku' => 'UNPT9C0XS'
    ]);
    $this->params = [
        'sku' => $this->product->sku,
        'country' => $this->country->iso2,
        'part_number' => '1UNPT9C0XS'
    ];
});

test('Create fail - Missing sku, country, part_number, fabric content', function () {
    unset($this->params['sku'], $this->params['country'], $this->params['part_number'], $this->params['fabric_content']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'sku' => [
                'The sku field is required.'
            ],
            'country' => [
                'The country field is required.'
            ],
            'part_number' => [
                'The part number field is required.'
            ]
        ]
    ]);
});

test('Create fail - sku not exists in product table', function () {
    $this->params['sku'] = '1UNPT9C0XS';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'sku' => [
                'The selected sku is invalid.'
            ]
        ]
    ]);
});

test('Create fail - part number consists of a mismatched SKU.', function () {
    $this->params['part_number'] = '1UNPT9C0XS111';
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'part_number' => [
                'The part number consists of a mismatched SKU. Please check again.'
            ]
        ]
    ]);
});

test('Create fail - product, fabric content and country already exist in another part number.', function () {
    PartNumber::create([
        'product_id' => $this->product->id,
        'country' => $this->country->iso2,
    ]);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'sku' => [
                'The SKU, Fabric content and country of origin already exist in another part number.'
            ],
            'country' => [
                'The SKU, Fabric content and country of origin already exist in another part number.'
            ]
        ]
    ]);
});

test('Create fail - part_number already exist.', function () {
    $product = Product::factory()->create();
    PartNumber::factory()->create([
        'product_id' => $product->id,
        'country' => $this->country->iso2,
        'part_number' => $this->params['part_number'],
    ]);

    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'part_number' => [
                'The part number has already been taken.'
            ],
        ]
    ]);
});

test('Create fail - Fabric content missing in the product spec. Please contact the support team!', function () {
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'Fabric content missing in the product spec. Please contact the support team!',
    ]);
});

test('Create success.', function () {
    $this->product->productSpec()->save(ProductSpec::factory()->create([
        'fabric_content' => "Love you and love me",
    ]));
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $sku = $this->params['sku'];
    $partNumber = PartNumber::where('country', $this->params['country'])
        ->where('part_number', $this->params['part_number'])
        ->whereHas('product', function ($q) use ($sku) {
            $q->where('sku', $sku);
        })->first();
    $this->assertNotNull($partNumber);
});

