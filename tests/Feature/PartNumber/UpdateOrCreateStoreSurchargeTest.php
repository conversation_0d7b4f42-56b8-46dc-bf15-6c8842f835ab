<?php

use App\Models\Store;
use App\Models\SurchargeFee;
use App\Models\SurchargeFeeHistory;
use App\Models\SurchargeService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken] = createAccessToken();
    $this->access_token = $accessToken;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = '/api/surcharge-fee';
    $this->store = Store::factory()->create();

    $this->surchargeService = SurchargeService::factory()->count(7)->sequence(
        ['name' => SurchargeService::TYPE_HANDLING, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_HANDLING],
        ['name' => SurchargeService::TYPE_TIKTOK_ORDER_SERVICE, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_TIKTOK_FEE],
        ['name' => SurchargeService::TYPE_LABEL_PRINTING_FEE, 'per' => SurchargeService::PER_ORDER, 'api_value' => SurchargeService::API_VALUE_LABEL_PRINTING_FEE],
        ['name' => SurchargeService::TYPE_PLASTIC_BAG, 'per' => SurchargeService::PER_ITEM, 'api_value' => SurchargeService::API_VALUE_PLASTIC_BAG],
        ['name' => SurchargeService::TYPE_MUG_PACKAGING, 'per' => SurchargeService::PER_PRODUCT_TYPE, 'product_type' => 'Mug', 'api_value' => SurchargeService::API_VALUE_MUG_PACKAGING],
        ['name' => SurchargeService::TYPE_HOLOGRAM_STICKER, 'per' => SurchargeService::PER_PRODUCT_TYPE, 'product_type' => 'Tee,Fleece,BIB,SweatPants,Tote Bag,CAP,Short,Jacket,Tank', 'api_value' => SurchargeService::API_VALUE_HOLOGRAM_STICKER],
        ['name' => SurchargeService::TYPE_STICKER_AND_PLASTIC_BAG, 'per' => SurchargeService::PER_ORDER, 'api_value' => SurchargeService::API_VALUE_STICKER_AND_BAG],
    )->create();
    $this->params = [
        'value' => '0.55',
        'store_id' => $this->store->id,
    ];
});

test('Create fail - Missing name, value, store_id', function () {
    unset($this->params['store_id'], $this->params['value']);
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'type' => [
                'The type field is required.'
            ],
            'value' => [
                'The value field is required.'
            ],
            'store_id' => [
                'The store id field is required.'
            ]
        ]
    ]);
});

//test('Create fail - surcharge service invalid', function () {
//    $this->params['type'] = 'test';
//    $response = $this->post($this->endpoint, $this->params);
//    $response->assertStatus(500);
//    expect(json_decode($response->getContent(), true))->toMatchArray([
//        'message' => 'Surcharge Service not found.',
//        'status' => false
//    ]);
//});

test('Create success', function () {
    $this->params['type'] = SurchargeService::TYPE_HANDLING;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $handlingService = SurchargeService::where('name', SurchargeService::TYPE_HANDLING)->first();
    $surcharge = SurchargeFee::where('store_id', $this->params['store_id'])->where('service_id', $handlingService->id)->first();
    $this->assertNotNull($surcharge);
    $this->assertDatabaseHas('store_surcharge_history', [
        'store_surcharge_id' => $surcharge->id,
        'message' => "Fee updated from 0 to {$this->params['value']}",
    ]);
});

test('Update success', function () {
    $handlingService = SurchargeService::where('name', SurchargeService::TYPE_HANDLING)->first();
    SurchargeFee::factory()->count(1)->sequence(
        [
            'store_id' => $this->store->id,
            'service_id' => $handlingService->id,
            'value' => 6,
        ])->create();
    $this->params['type'] = SurchargeService::TYPE_HANDLING;
    $this->params['value'] = 10;
    $response = $this->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    $surcharge = SurchargeFee::where('store_id', $this->params['store_id'])->where('service_id', $handlingService->id)->first();
    $this->assertNotNull($surcharge);
    expect($surcharge->value)->toEqual(10);
    $history = SurchargeFeeHistory::where('store_surcharge_id', $surcharge->id)->get();
    $this->assertNotNull($history);
    $this->assertDatabaseHas('store_surcharge_history', [
        'store_surcharge_id' => $surcharge->id,
        'message' => "Fee updated from 6.00 to {$this->params['value']}",
    ]);
});
