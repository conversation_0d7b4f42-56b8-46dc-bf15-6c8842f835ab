<?php

use App\Models\ProductStyleSizeWeight;
use App\Models\ProductStyleSizeWeightHistory;
use Faker\Factory as faker;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $faker = Faker::create();
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;
    $this->fileExtension = 'xlsx';

    $this->historyFiles = ProductStyleSizeWeightHistory::factory()->count(3)->state(new Sequence(
        [
            's3_file_key' => ProductStyleSizeWeight::S3_BUCKET_URL . date('YmdHis') . '/' . $faker->text(12) . $this->fileExtension,
            'user_id' => $this->user->id,
            'created_at' => $faker->dateTime->format('Y-m-d H:i:s'),
        ],
        [
            's3_file_key' => ProductStyleSizeWeight::S3_BUCKET_URL . date('YmdHis') . '/' . $faker->text(12) . $this->fileExtension,
            'user_id' => $this->user->id,
            'created_at' => $faker->dateTime->format('Y-m-d H:i:s'),
        ],
        [
            's3_file_key' => ProductStyleSizeWeight::S3_BUCKET_URL . date('YmdHis') . '/' . $faker->text(12) . $this->fileExtension,
            'user_id' => $this->user->id,
            'created_at' => $faker->dateTime->format('Y-m-d H:i:s'),
        ],
    ))->create();

    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->endpoint = '/api/products/style-size-weights/history';
});

test('Get import history files - Return valid data', function () {
    $response = $this->get($this->endpoint);
    $responseData = json_decode($response->getContent());
    $response->assertStatus(200);

    $this->assertCount(3, $responseData->data);
    $this->assertEquals(env('AWS_S3_URL') . $this->historyFiles->last()->s3_file_key, $responseData->data[0]->s3_file_url);
    $this->assertEquals(env('AWS_S3_URL') . $this->historyFiles->first()->s3_file_key, $responseData->data[2]->s3_file_url);
});
