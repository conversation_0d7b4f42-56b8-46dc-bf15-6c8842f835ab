<?php

use App\Models\Product;
use App\Models\ProductType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/products/type/';
    $this->params = [
        "name" => '',
        "is_hard_good" => '',
        "icon" => ''
    ];
    Storage::fake('public');
    Storage::fake('public/IconProduct');
});

// validate
test('validate', function () {
    // validate
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $res = json_decode($response->getContent(), true)['errors'];
    expect($res)->toMatchArray([
        "name" => ["The name field is required."],
        "is_hard_good" => ["The is hard good field is required."],
        "icon" => ["The icon field is required."],
    ]);

    $file = UploadedFile::fake()->image('icon.jpg');
    $this->params['name'] = 'test';
    $this->params["is_hard_good"] = 1;
    $this->params["icon"] = $file;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $res = json_decode($response->getContent(), true)['errors'];
    expect($res)->toMatchArray([
        "icon" => ["The icon must be a file of type: png."],
    ]);

    $this->params["icon"] = '';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    $res = json_decode($response->getContent(), true)['errors'];
    expect($res)->toMatchArray([
        "icon" => ["The icon field is required."],
    ]);

})->skip('image validation is not working');

// Product type not found!
test('Product type not found!', function () {
    $this->params['name'] = 'test';
    $this->params["is_hard_good"] = 1;
    $this->params["icon"] = "icon1.png";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint."9999", $this->params);
    $response->assertStatus(400);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        "message" => "Product type not found!",
    ]);
});

// Update product type success - not change icon
test('Update product type success - not change icon', function () {
    $productType = ProductType::factory()->create([
        'name' =>  'test',
        'is_hard_goods' => true,
        'icon' => 'icon.png'
    ]);
    $this->params['name'] = 'love';
    $this->params["is_hard_good"] = 0;
    $this->params["icon"] = "icon.png";
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint."$productType->id", $this->params);
    $response->assertStatus(200);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        "message" => "Update product type successfully!",
    ]);
    $this->assertDatabaseHas('product_type', ['name' =>ucfirst($this->params['name']), 'icon' => 'icon.png' ,"is_hard_goods" => $this->params["is_hard_good"]]);
});

// Update product type success - change icon
test('Update product type success - change icon', function () {
    $productType = ProductType::factory()->create([
        'name' =>  'test',
        'is_hard_goods' => true,
    ]);
    $nameIcon = 'icon2.png';
    $file = UploadedFile::fake()->image($nameIcon);

    $this->params['name'] = 'love';
    $this->params["is_hard_good"] = 0;
    $this->params["icon"] = $file;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint."$productType->id", $this->params);
    $response->assertStatus(200);
    $res = json_decode($response->getContent(), true);
    expect($res)->toMatchArray([
        "message" => "Update product type successfully!",
    ]);
    $this->assertDatabaseHas('product_type', ['name' =>ucfirst($this->params['name']), "icon" => $nameIcon,"is_hard_goods" => $this->params["is_hard_good"]]);
    $fileName = 'IconProduct/'.$nameIcon;
    $exists = Storage::disk('public')->exists($fileName);
    expect($exists)->toEqual(true);
})->skip();
