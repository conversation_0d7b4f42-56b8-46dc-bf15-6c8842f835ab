<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use App\Models\Employee;
use App\Models\Warehouse;
use App\Models\SaleOrder;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 2022]);

    $this->warehouseReroute = Warehouse::factory()->create();
    $this->endpoint = '/api/sale-order/reroute/verify-csv-file';
    $this->withHeader('Authorization', 'Bearer ' . $this->access_token);

    $file = new UploadedFile(
        base_path('tests/files/order_number.xlsx'),
        'order_number.xlsx',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        null,
        true
    );
    $this->params = [
        'file' => $file,
        'warehouse' => $this->warehouseReroute->id,
        'employee_id' => $employee->id,
    ];
});

// Validate required : warehouse, employee_id, file
test('validate reroute : missing warehouse, employee_id, file', function () {
    $fields = ['warehouse', 'employee_id', 'file'];

    foreach ($fields as $field) {
        $inputs = $this->params;
        unset($inputs[$field]);

        $response = $this->post($this->endpoint, $inputs);

        $response->assertStatus(422);
        expect(json_decode($response->getContent(), true))->toHaveKey($field);
    }
});

// Validate warehouse đích trùng với warehouse của user login
test('validate reroute : reroute warehouse is same warehouse user login', function () {
    $this->params['warehouse'] = $this->warehouse->id;

    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('warehouse');
});

//// Warehouse khong ton tai => dừng import
test('validate reroute : warehouse not found', function () {

    $this->params['warehouse'] = "ABC";

    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('warehouse');

});

//// Employee thuộc warehouse khác => dừng import
test('validate reroute : employee in another warehouse', function () {

    $employee = Employee::factory()->create(['warehouse_id' => $this->warehouseReroute->id, 'code' => "WA"]);
    $this->params['employee_id'] = $employee->id;

    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('employee_id');
});

//// file khong  phai la csv, xlsx, txt, xls thì dừng import
test('validate reroute : file extension is not valid', function () {
    $this->params['file'] = UploadedFile::fake()->create('test.jpg');

    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toHaveKey('file');
});

//// file không có data
test('validate reroute : file empty data', function () {
    $this->params['file'] = new UploadedFile(
        base_path('tests/files/order_number_empty.xlsx'),
        'order_number_empty.xlsx',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        null,
        true
    );
    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);

    expect($dataResponse)->toHaveKeys(['total', 'valid', 'invalid']);
    expect($dataResponse['total'])->toEqual(0);
    expect($dataResponse['valid'])->toEqual(0);
    expect($dataResponse['invalid'])->toEqual(0);
});

// Validate import những order không tồn tại trên hệ thống
test('Invalid order number', function () {
    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);

    expect($dataResponse)->toHaveKeys(
        [
            "total",
            "valid",
            "invalid",
            "valid_order",
            "invalid_order",
            "invalid_order_user_warehouse",
            "order_error"
        ])
        ->and($dataResponse['total'])
        ->toEqual(9)
        ->and($dataResponse['valid'])
        ->toEqual(0)
        ->and($dataResponse['invalid_order'])
        ->toHaveCount(9);
});

// Validate import những order có warehouse khác với user login
test('The order number imported does not match the warehouse when user login', function () {

    $warehouse = Warehouse::factory()->create();
    SaleOrder::factory()->count(6)->sequence(
        [
            'order_number' => '100422-SJ-S-000072',
            'warehouse_id' => $warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000066',
            'warehouse_id' => $warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000068',
            'warehouse_id' => $warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000058',
            'warehouse_id' => $warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000055',
            'warehouse_id' => $warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '080922-SJ-S-000068',
            'warehouse_id' => $warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],

    )->create();
    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);

    expect($dataResponse)->toHaveKeys(
        [
            "total",
            "valid",
            "invalid",
            "valid_order",
            "invalid_order",
            "invalid_order_user_warehouse",
            "order_error"
        ])
        ->and($dataResponse['total'])
        ->toEqual(9)
        ->and($dataResponse['valid'])
        ->toEqual(0)
        ->and($dataResponse['invalid_order'])
        ->toHaveCount(3)
        ->and($dataResponse['invalid_order_user_warehouse'])
        ->toHaveCount(6);
});


// File có chứa dữ liệu valid => có thể  import data ở bước sau
// order number ở trạng thái not active
test('validate reroute : data is valid => CAN import', function () {

    SaleOrder::factory()->count(6)->sequence(
        [
            'order_number' => '100422-SJ-S-000072',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000066',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000068',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000058',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '100422-SJ-S-000055',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::NEW_ORDER,
        ],
        [
            'order_number' => '080922-SJ-S-000068',
            'warehouse_id' => $this->warehouse->id,
            'order_status' => SaleOrder::CANCELLED, //order not active
        ],

    )->create();

    $response = $this->post($this->endpoint, $this->params);

    $response->assertStatus(200);
    $dataResponse = json_decode($response->getContent(), true);
    expect($dataResponse)->toHaveKeys(
        [
            "total",
            "valid",
            "invalid",
            "valid_order",
            "invalid_order",
            "invalid_order_user_warehouse",
            "order_error"
        ])
        ->and($dataResponse['total'])
        ->toEqual(9)
        ->and($dataResponse['invalid_order'])
        ->toHaveCount(4)
        ->and($dataResponse['valid'])
        ->toEqual(5)
        ->and($dataResponse['valid_order'])
        ->toHaveCount(5);
});
