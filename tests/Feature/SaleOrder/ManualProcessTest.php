<?php

// Manual process thành công
test('manual sale order success', function () {
})->skip('Write in the next time');

// Manual process that bai

// Không nhập orderIds
// orderIds không hợp lệ. VD orderIds không phải là mảng
// có phần tử không thuộc id trong sale order
test('manual sale order failed - invalid orderIds', function () {
})->skip('Write in the next time');

// Không nhập employee_id
// employee_id không hợp lệ. VD employee_id không thuộc warehouse đã chọn, employee không tồn tại
test('manual sale order failed - invalid employee_id', function () {
})->skip('Write in the next time');

// reason không hợp lệ. VD reason qua dai
test('manual sale order failed - invalid reason', function () {
})->skip('Write in the next time');


