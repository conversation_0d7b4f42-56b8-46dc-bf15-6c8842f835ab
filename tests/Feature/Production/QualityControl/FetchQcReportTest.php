<?php

use App\Models\Country;
use App\Models\PartNumber;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;

    $this->country = Country::factory(['name' => 'United States', 'iso2' => 'US'])->create();

    $this->partNumber = PartNumber::factory(
        [
            'part_number' => 'xxx',
            'product_id' => 1,
            'country' => $this->country->iso2,
        ],
    )
        ->create();

    $this->saleOrderItemBarcode = SaleOrderItemBarcode::factory()->count(2)->sequence(
        [
            'warehouse_id' => $this->warehouse->id,
            'label_id' => '021622-SJ-M-000505-4',
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'label_id' => '564356-KM-L-123456-1',
        ],
    )
        ->create();

    DB::table('sale_order_item_quality_control')->insert(
        [
            [
                'status' => 'pass',
                'label_id' => $this->saleOrderItemBarcode->first()->label_id,
                'part_number_id' => $this->partNumber->id,
                'sku' => 'MjExMTA3Nw',
                'created_at' => '2023-03-01 00:00:00',
                'warehouse_id' => $this->warehouse->id,
            ],
            [
                'status' => 'wrong print',
                'label_id' => $this->saleOrderItemBarcode->last()->label_id,
                'part_number_id' => $this->partNumber->id,
                'sku' => 'vdUNGH4Y00S',
                'created_at' => '2023-03-15 00:00:00',
                'warehouse_id' => $this->warehouse->id,
            ],
            [
                'status' => 'missing pretreat',
                'label_id' => $this->saleOrderItemBarcode->last()->label_id,
                'part_number_id' => $this->partNumber->id,
                'sku' => 'vdUNGH',
                'created_at' => '2023-03-15 00:00:00',
                'warehouse_id' => 20,
            ],
        ],
    );

    $this->endpoint = 'api/quality-control/report';
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);

    $this->result = [
        'qc_id',
        'status',
        'qc_user_id',
        'qc_created_at',
        'qc_updated_at',
        'qc_employee_id',
        'id',
        'order_id',
        'order_item_id',
        'label_id',
        'sku',
        'employee_pull_id',
        'employee_pretreat_id',
        'employee_print_id',
        'qc_employee_id_name',
        'scanned_at'
    ];
});

//valid & invalid sku param
test('get quality control report success - sku param', function () {
    $asserts['valid_params'] = ['sku' => 'MjExMTA3Nw'];
    $asserts['invalid_params'] = ['sku' => 'not found'];

    foreach ($asserts as $key => $assert) {
        $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($this->endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($key == 'invalid_params') {
            $this->assertEmpty($response['data']);
        } else {
            $this->assertCount(1, $response['data']);
            foreach ($response['data'] as $key => $value) {
                expect($value)->toHaveKeys($this->result);
            }
        }
    }
});

//date param
test('get quality control report success - date param', function () {
    $assert = ['date' => ['2023/03/05', '2023/03/25']];

    $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(2, $response['data']);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
    }
});

//export data param
test('get quality control report success - exportData param', function () {
    $assert = ['exportData' => true];
    $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(3, $response);
    foreach ($response as $key => $value) {
        expect($value)->toHaveKeys($this->result);
    }
});

//limit param
test('get quality control report success - limit param', function () {
    $assert = ['item_of_page' => 1];
    $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(1, $response['data']);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
    }
});

//valid & invalid status param
test('get quality control report success - status param', function () {
    $asserts['valid_params'] = ['status' => 'pass'];
    $asserts['invalid_params'] = ['status' => 'not found'];

    foreach ($asserts as $key => $assert) {
        $this->endpoint = $this->endpoint . '?' . http_build_query($assert);

        $response = $this->get($this->endpoint);
        $response->assertStatus(200);
        $response = json_decode($response->getContent(), true);

        if ($key == 'invalid_params') {
            $this->assertCount(3, $response['data']);
        } else {
            $this->assertCount(1, $response['data']);
        }
        foreach ($response['data'] as $key => $value) {
            expect($value)->toHaveKeys($this->result);
        }
    }
});

//mexico warehouse
test('get quality control report success - mexico warehouse', function () {
    $token = JWTAuth::customClaims(['warehouse' => ['id' => Warehouse::WAREHOUSE_MEXICO[0]]])->fromUser($this->user);
    DB::table('sale_order_item_quality_control')
        ->where('sku', 'MjExMTA3Nw')
        ->orWhere('sku', 'vdUNGH4Y00S')
        ->update([
            'warehouse_id' => Warehouse::WAREHOUSE_MEXICO[0]
        ]);

    $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Accept' => 'application/json',
    ]);

    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);
    $this->assertCount(0, $response['data']);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
        $this->assertEquals($this->country->name, $value['country']);
    }
});

//data true
test('get quality control report success', function () {
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
    $response = json_decode($response->getContent(), true);

    $this->assertCount(3, $response['data']);
    foreach ($response['data'] as $key => $value) {
        expect($value)->toHaveKeys($this->result);
    }
});
