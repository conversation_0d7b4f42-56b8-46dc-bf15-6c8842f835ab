<?php

use App\Models\Country;
use App\Models\PartNumber;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductQuantity;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse, 'user' => $user] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->user = $user;

    $this->store = Store::factory()->create();

    $this->product = Product::factory(['name' => 'product-test'])->create();
    $this->productStyle = ProductStyle::factory()->count(2)->sequence(
        ['type' => 'Tee', 'sku' => 'UNGU'],
        ['type' => 'Fleece', 'sku' => 'UNPT'],
    )
        ->create();
    $this->ProductQuantity = ProductQuantity::factory()->create(['warehouse_id' => $this->warehouse->id,
        'product_id' => $this->product->id, 'quantity' => 10, 'incoming_stock' => 10]);
    $this->productType = ProductType::factory()->count(2)->sequence(
        ['name' => 'Tee', 'icon' => 'tee.png'],
        ['name' => 'Fleece', 'icon' => 'fleece.png'],
    )
        ->create();

    $this->saleOrder = SaleOrder::factory([
        'order_status' => SaleOrder::NEW_ORDER,
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItem::factory([
        'warehouse_id' => $this->warehouse->id,
        'sku' => 'UNPTCS0XL',
        'product_id' => $this->product->id,
        'product_style_sku' => $this->productStyle->last()->sku,
        'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/d1ud88wu9m1k4s.cloudfront.net\/fulfill\/design\/2022\/08\/22\/fbcc7d9ed3af43eb26ce2625e2f1134e.png"},{"name":"PreviewFiles.Front","value":"https:\/\/d1ud88wu9m1k4s.cloudfront.net\/fulfill\/mockup\/2022\/08\/22\/65511047b6bdb6a7eaf97e496d30597f.jpg"}]'
    ]), 'items')
        ->create();
    $this->itemBarcode = SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'label_id' => '021622-SJ-M-000505-4',
        'sku' => 'UNPTCS0XL',
        'order_item_id' => $this->saleOrder->items->first()->id,
        'order_id' => $this->saleOrder->id,
    ])
    ->create();

    $this->printSide = ProductPrintSide::factory(['code_name' => 'Front', 'order' => 2])->create();

    $this->country = Country::factory(['name' => 'United States', 'iso2' => 'US'])->create();

    $this->partNumber = PartNumber::factory(
        [
            'part_number' => 'xxx',
            'product_id' => 1,
            'country' => $this->country->iso2,
        ],
    )
        ->create();

    DB::table('sale_order_item_barcode_status')->insert(
        [
            [
                'status' => 'pass',
                'label_id' => $this->itemBarcode->label_id,
                'created_at' => '2023-03-15 11:00:00',
                'last_qc_at' => '2023-03-15 16:00:00',
                'total_reject' => 2
            ],
        ],
    );
    DB::table('sale_order_item_quality_control')->insert(
        [
            [
                'order_id' => $this->saleOrder->id,
                'order_item_id' => $this->saleOrder->items->first()->id,
                'status' => 'wrong print',
                'label_id' => $this->itemBarcode->label_id,
                'part_number_id' => $this->partNumber->id,
                'sku' => 'UNPTCS0XL',
                'created_at' => '2023-03-15 05:00:00',
                'warehouse_id' => $this->warehouse->id,
                'label_root_id' => $this->itemBarcode->label_id

            ],
            [
                'order_id' => $this->saleOrder->id,
                'order_item_id' => $this->saleOrder->items->first()->id,
                'status' => 'missing pretreat',
                'label_id' => $this->itemBarcode->label_id,
                'part_number_id' => $this->partNumber->id,
                'sku' => 'UNPTCS0XL',
                'created_at' => '2023-03-15 10:00:00',
                'warehouse_id' => $this->warehouse->id,
                'label_root_id' => $this->itemBarcode->label_id

            ],
            [
                'order_id' => $this->saleOrder->id,
                'order_item_id' => $this->saleOrder->items->first()->id,
                'status' => 'pass',
                'label_id' => $this->itemBarcode->label_id,
                'part_number_id' => $this->partNumber->id,
                'sku' => 'UNPTCS0XL',
                'created_at' => '2023-03-15 16:00:00',
                'warehouse_id' => $this->warehouse->id,
                'label_root_id' => $this->itemBarcode->label_id
            ],
        ],
    );

    $this->endpoint = 'api/quality-control/qc-sla';
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
});

//valid & invalid sku param
test('get data qc success - type line', function () {
    $asserts['start_date'] = '2023-03-13';
    $asserts['end_date'] = '2023-03-17';
    $asserts['type'] = 'line';

    $this->endpoint = $this->endpoint . '?' . http_build_query($asserts);

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $responseData = json_decode($response->getContent(), true);
    $this->assertEquals('0.46', $responseData['data']['2023-03-15']['2nd_failed']);
});

test('get data qc success - type pie', function () {
    $asserts['start_date'] = '2023-03-13';
    $asserts['end_date'] = '2023-03-17';
    $asserts['type'] = 'pie';

    $this->endpoint = $this->endpoint . '?' . http_build_query($asserts);

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $responseData = json_decode($response->getContent(), true);
    $this->assertEquals(1, $responseData['data']['2nd_failed']['total_passed']);
});
