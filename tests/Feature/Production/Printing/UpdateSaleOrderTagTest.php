<?php

use App\Helpers\SaleOrderHelper;
use App\Models\SaleOrder;
use App\Models\Tag;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->newTag = 165; // purple

    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->tagManual = Tag::factory()->create([
        'is_additional_service' => false,
        'surcharge_service_id' => null
    ]);
    $this->tagRestrictRemove = Tag::factory()->create([
        'is_additional_service' => true,
        'surcharge_service_id' => 1
    ]);
    $this->tagLabel = Tag::factory()->create([
        'id' => 203
    ]);
    $this->saleOrder = SaleOrder::factory()->create();

    $this->endpoint = '/api/sale-order/' . $this->saleOrder->id . '/tag';
});

test('old tag is empty', function () {
    $oldTag = [
        null,
        ''
    ];

    foreach ($oldTag as $tag) {
        $res = SaleOrderHelper::handleTag($tag, $this->newTag);
        $this->assertEquals($res, $this->newTag);
    }
});

test('old tag includes new tag', function () {
    $oldTag = "$this->newTag,123";
    $res = SaleOrderHelper::handleTag($oldTag, $this->newTag);
    $this->assertEquals($res, $oldTag);
});

test('old tag not includes new tag', function () {
    $oldTag = "123,234";
    $res = SaleOrderHelper::handleTag($oldTag, $this->newTag);
    $this->assertEquals($res, "$oldTag,$this->newTag");
});

test('validate sale order not found', function () {
    $params = [
        'tag' => [$this->tagManual->id]
    ];

    $endpoint = '/api/sale-order/' . ($this->saleOrder->id + 1) . '/tag';
    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->put($endpoint, $params);

    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(422);
    expect($dataResponse)->toHaveKeys(["message"]);
});

test('validate input tag not array', function () {
    $params = [
        'tag' => $this->tagManual->id
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->put($this->endpoint, $params);

    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(422);
    expect($dataResponse)->toHaveKeys(["message", "errors"]);
});

test('do not add tag label', function () {
    $params = [
        'tag' => [$this->tagLabel->id]
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->put($this->endpoint, $params);

    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(500);
    expect($dataResponse)->toHaveKeys(["message"]);
});

test('do not remove tag label', function () {
    $saleOrder = SaleOrder::factory()->create([
        'tag' => '203'
    ]);
    $endpoint = '/api/sale-order/' . $saleOrder->id . '/tag';

    $params = [
        'tag' => [$this->tagManual->id]
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->put($endpoint, $params);

    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(500);
    expect($dataResponse)->toHaveKeys(["message"]);
});

test('do not add tag restrict remove', function () {
    $params = [
        'tag' => [$this->tagRestrictRemove->id]
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->put($this->endpoint, $params);

    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(500);
    expect($dataResponse)->toHaveKeys(["message"]);
});

test('do not remove restrict remove', function () {
    $saleOrder = SaleOrder::factory()->create([
        'tag' => $this->tagRestrictRemove->id
    ]);
    $endpoint = '/api/sale-order/' . $saleOrder->id . '/tag';

    $params = [
        'tag' => [$this->tagManual->id]
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->put($endpoint, $params);

    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(500);
    expect($dataResponse)->toHaveKeys(["message"]);
});

test('add tag manual success', function () {
    $params = [
        'tag' => [$this->tagManual->id]
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->put($this->endpoint, $params);

    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(200);
    expect($dataResponse)->toHaveKeys(["message", "data"]);
});

test('remove tag manual success', function () {
    $saleOrder = SaleOrder::factory()->create([
        'tag' => $this->tagManual->id
    ]);
    $endpoint = '/api/sale-order/' . $saleOrder->id . '/tag';

    $params = [
        'tag' => []
    ];

    $response = $this->withHeader('Authorization', 'Bearer ' . $this->access_token)
        ->put($endpoint, $params);

    $dataResponse = json_decode($response->getContent(), true);
    $response->assertStatus(200);
    expect($dataResponse)->toHaveKeys(["message"]);
});