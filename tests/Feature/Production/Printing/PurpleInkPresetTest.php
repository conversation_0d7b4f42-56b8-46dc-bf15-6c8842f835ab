<?php

use App\Models\BarcodePrinted;
use App\Models\Employee;
use App\Models\PrintingPreset;
use App\Models\PrintingPresetSku;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Repositories\PrintingRepository;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->warehouseId = 1;
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouseId, 'code' => 1011]);

    // data match
    $this->productStyle = ProductStyle::factory([
        'print_method' => 'DTG'
        ])
        ->has(Product::factory([
            'parent_id' => 111,
        ])->has(PrintingPresetSku::factory([
            'purple_ink' => 'FALLPURPLEDL',
            'purple_ink_xqc' => 'FALLPURPLEDLXQC',
            'platen_front_size' => '14x16'
        ]), 'presetSku'), 'product')
        ->create();

    $this->printingPreset = PrintingPreset::factory([
        'name' => 'FALLPURPLEDL',
        'data' => 'WWWWWWW'
    ])->create();

    $this->store = Store::factory()->create();
    $this->productPrintSide = ProductPrintSide::factory([
        'code' => 0,
        'name' => 'Front',
        'code_name' => 'front'
    ])->create();

    $this->saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'warehouse_id' => $this->warehouseId,
        'store_id' => $this->store->id
    ])->has(SaleOrderItem::factory([
        'product_style_sku' => $this->productStyle->sku,
        'warehouse_id' => $this->warehouseId,
        'options' => '[{"name":"PrintFiles.Front","value":"https:\/\/res.cloudinary.com\/dh2tzfqa8\/image\/upload\/v1691048813\/print_design_1686623439jh17sd73qvazuhhhygvq_1_720_1_2_lc0sl6.png"},{"name":"PreviewFiles.Front","value":"https:\/\/res.cloudinary.com\/dh2tzfqa8\/image\/upload\/v1691048813\/print_design_1686623439jh17sd73qvazuhhhygvq_1_720_1_2_lc0sl6.png"}]'
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouseId,
        'label_id' => '080123-SJ-M-000004-3',
        'sku' => 'YYYY'
    ]), 'barcodes')->has(SaleOrderItemImage::factory([
        'warehouse_id' => $this->warehouseId,
        'is_purple' => 1,
        'print_side' => 0,
        'sku' => 'YYYY',
        'color_new' => 2,
        'image_width' => 4200,
        'image_height' => 4800
    ]), 'images')
    , 'items')
    ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $this->saleOrder->items->map(function ($item) {
        return $item->images->map(function ($image) {
            $image->order_id = $this->saleOrder->id;
            $image->save();
        });
    });

    $this->barcodePrinted = BarcodePrinted::factory([
        'style_sku' => $this->productStyle->sku,
        'created_at' => Carbon::now(),
        'warehouse_id' => $this->warehouseId,
        'print_method' => $this->productStyle->print_method
    ])
    ->create();
});

test('purple - preset name is null', function () {
    $printingRepo = new PrintingRepository();
    $barcode = SaleOrderItemBarcode::first();
    $image = SaleOrderItemImage::first();
    $printingPresetSku = PrintingPresetSku::first();
    $printingPresetSku->purple_ink = null;
    $printingPresetSku->purple_ink_xqc = null;
    $printingPresetSku->save();
    $res = $printingRepo->getImageBySkuSideNew($barcode, $image->print_side, $this->employee->id);
    $this->assertEquals(false, $res['status']);
    $this->assertEquals('Preset name not defined', $res['message']);
});

test('purple - success normal', function () {
    $printingRepo = new PrintingRepository();
    $barcode = SaleOrderItemBarcode::first();
    $image = SaleOrderItemImage::first();
    $res = $printingRepo->getImageBySkuSideNew($barcode, $image->print_side, $this->employee->id);
    $this->assertEquals($res['data']->arx4['preset'], 'FALLPURPLEDL');
});

test('purple - success xqc', function () {
    SaleOrder::where('id', $this->saleOrder->id)->update(['is_xqc' => 1]);
    PrintingPreset::where('id', $this->printingPreset->id)->update(['name' => 'FALLPURPLEDLXQC']);
    $printingRepo = new PrintingRepository();
    $barcode = SaleOrderItemBarcode::first();
    $image = SaleOrderItemImage::first();
    $res = $printingRepo->getImageBySkuSideNew($barcode, $image->print_side, $this->employee->id);
    $this->assertEquals($res['data']->arx4['preset'], 'FALLPURPLEDLXQC');
});
