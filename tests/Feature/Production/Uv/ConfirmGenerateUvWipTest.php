<?php

use App\Models\BarcodePrinted;
use App\Models\Employee;
use App\Models\PrintingPresetSku;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Store;
use App\Models\UvPresetSku;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;

uses(RefreshDatabase::class);

beforeEach(function () {
    Cache::store(config('cache.redis_store'))->flush();
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/ornament';
    $this->employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 1011]);
    $this->product = Product::factory([
        'sku' => 'ORMTALONE'
    ])
        ->has(PrintingPresetSku::factory(), 'printingPresetSku')
        ->has(UvPresetSku::factory(), 'uvPresetSku')
        ->create();
    $this->store = Store::factory()->create();
});

// Không truyền quantity lên
// input = [] => result = The quantity field is required (422)
test('no param quantity', function () {
    $param = [
        'required' => [
            'product_id' => $this->product->id,
            'employee_id' => $this->employee->id
        ],
        'numeric' => [
            'quantity' => 'xxx',
            'product_id' => $this->product->id,
            'employee_id' => $this->employee->id
        ],
    ];

    // required
    $response = $this->post($this->endpoint, $param['required']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'quantity' => [
                'The quantity field is required.'
            ]
        ]
    ]);

    // numeric
    $response = $this->post($this->endpoint, $param['numeric']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'quantity' => [
                'The quantity must be a number.'
            ]
        ]
    ]);
});

// Không truyền product_id lên
// input = [] => result = The product_id field is required (422)
test('no param product_id', function () {
    $param = [
        'required' => [
            'quantity' => 1,
            'employee_id' => $this->employee->id
        ],
    ];

    // required
    $response = $this->post($this->endpoint, $param['required']);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'product_id' => [
                'The product id field is required when none of is tiktok / is bulk order / is reprint are present.'
            ],
            'is_tiktok' => [
                'The is tiktok field is required when none of product id / is bulk order / is reprint are present.'
            ],
            'is_bulk_order' => [
                'The is bulk order field is required when none of product id / is tiktok / is reprint are present.'
            ],
            'is_reprint' => [
                'The is reprint field is required when none of product id / is bulk order / is tiktok are present.'
            ]
        ]
    ]);
});

// Không truyền employee_id lên
// input = [] => result = The employee_id field is required (422)
test('no param employee_id', function () {
    $param = [
        'required' => [
            'quantity' => 1,
            'product_id' => $this->product->id
        ],
        'exists' => [
            'quantity' => 1,
            'product_id' => $this->product->id,
            'employee_id' => 10000
        ]
    ];

    // required
    $response = $this->post($this->endpoint, $param['required']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => [
                'The employee id field is required.'
            ]
        ]
    ]);

    // exists
    $response = $this->post($this->endpoint, $param['exists']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => [
                'You are not in this warehouse!'
            ]
        ]
    ]);
});

// product không tồn tại hoặc chưa set preset
// result = Product not found or not to set preset yet! (404)
test('product not found', function () {
    $param = [
        'quantity' => 1,
        'product_id' => 10000,
        'employee_id' => $this->employee->id
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(404);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'code' => 404,
        'success' => false,
        'message' => 'Product not found or not to set preset yet!'
    ]);
});

// success
// tạo ra bản ghi mới trong bảng barcode printed
// update barcode_printed_id trong bảng sale_order_item_barcode bằng id của barcode printed vừa tạo
test('success', function () {
    $param = [
        'quantity' => 1,
        'product_id' => $this->product->id,
        'employee_id' => $this->employee->id
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(200);
    $this->assertEquals(1, BarcodePrinted::count());
    expect(json_decode($response->getContent(), true))->toHaveKeys([
        'code',
        'success',
        'message',
        'data.quantity_input',
        'data.employee_id',
        'data.warehouse_id',
        'data.user_id',
        'data.convert_percent',
        'data.print_method',
        'data.product_id',
        'data.created_at',
        'data.print_status',
        'data.id'
    ]);
});

// confirm bulk order success

test('confirm reprint order success', function () {
    $productStyle = ProductStyle::factory()->create([
        'sku' => 'ORM2',
        'type' => 'Ornament',
        'print_method' => 'UV'
    ]);
    $product = Product::factory([
        'sku' => '12345',
        'parent_id' => 222,
    ])
        ->has(PrintingPresetSku::factory(), 'printingPresetSku')
        ->has(UvPresetSku::factory(), 'uvPresetSku')
        ->create();
    $saleOrder = SaleOrder::factory()->create([
        'is_test' => 0,
        'order_type' => 6,
        'order_quantity' => 25,
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
    ]);
    $saleOrderItem = SaleOrderItem::factory()->create([
        'ink_color_status' => 1,
        'product_style_sku' => $productStyle->sku,
        'product_sku' => $product->sku,
        'product_id' => $product->id,
        'quantity' => 25,
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
    ]);
    foreach (range(1, 25) as $i) {
        SaleOrderItemBarcode::factory()->create([
            'order_id' => $saleOrder->id,
            'order_item_id' => $saleOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'is_deleted' => 0,
            'barcode_printed_id' => 0,
            'retry_convert' => 0,
            'label_root_id' => '12345' . $i,
        ]);
    }

    $saleOrder = SaleOrder::factory()->create([
        'is_test' => 0,
        'order_type' => 1,
        'order_quantity' => 20,
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
    ]);
    $saleOrderItem = SaleOrderItem::factory()->create([
        'ink_color_status' => 1,
        'product_style_sku' => $productStyle->sku,
        'product_sku' => $product->sku,
        'product_id' => $product->id,
        'quantity' => 20,
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
    ]);
    foreach (range(1, 20) as $i) {
        SaleOrderItemBarcode::factory()->create([
            'order_id' => $saleOrder->id,
            'order_item_id' => $saleOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'is_deleted' => 0,
            'barcode_printed_id' => 0,
            'retry_convert' => 0,
            'label_root_id' => '123456' . $i,
        ]);
    }

    $saleOrder = SaleOrder::factory()->create([
        'is_test' => 0,
        'order_type' => 1,
        'order_quantity' => 10,
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
    ]);
    $saleOrderItem = SaleOrderItem::factory()->create([
        'ink_color_status' => 1,
        'product_style_sku' => $productStyle->sku,
        'product_sku' => $product->sku,
        'product_id' => $product->id,
        'quantity' => 10,
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
    ]);
    foreach (range(1, 10) as $i) {
        SaleOrderItemBarcode::factory()->create([
            'order_id' => $saleOrder->id,
            'order_item_id' => $saleOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'is_deleted' => 0,
            'barcode_printed_id' => 0,
            'retry_convert' => 0,
            'label_root_id' => '1234567' . $i,
        ]);
    }
    $param = [
        'quantity' => 1,
        'is_reprint' => 1,
        'is_bulk_order' => null,
        'is_tiktok' => null,
        'employee_id' => $this->employee->id
    ];
    $response = $this->post($this->endpoint, $param);
    $response->assertStatus(200);
    $this->assertEquals(1, BarcodePrinted::count());
    $barcodePrinted = BarcodePrinted::where('is_reprint', 1)
        ->where('print_method', 'UV')
        ->first();
    $this->assertNotNull($barcodePrinted);
    $countBarcode = SaleOrderItemBarcode::join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
        ->where('sale_order_item_barcode.barcode_printed_id', $barcodePrinted->id)
        ->where('sale_order.order_quantity', '<', 20)
        ->whereNotNull('sale_order_item_barcode.label_root_id')
        ->whereNotIn('order_type', [SaleOrder::ORDER_TYPE_LABEL_ORDER, SaleOrder::ORDER_TYPE_TIKTOK_ORDER])->count();
    $this->assertEquals($countBarcode, 1);

    expect(json_decode($response->getContent(), true))->toHaveKeys([
        'code',
        'success',
        'message',
        'data.quantity_input',
        'data.employee_id',
        'data.warehouse_id',
        'data.user_id',
        'data.convert_percent',
        'data.print_method',
        'data.product_id',
        'data.created_at',
        'data.print_status',
        'data.id'
    ]);
});

test('confirm bulk order success', function () {
    $productStyle = ProductStyle::factory()->create([
        'sku' => 'ORM2',
        'type' => 'Ornament',
        'print_method' => 'UV'
    ]);
    $product = Product::factory([
        'sku' => '12345',
        'parent_id' => 222,
    ])
        ->has(PrintingPresetSku::factory(), 'printingPresetSku')
        ->has(UvPresetSku::factory(), 'uvPresetSku')
        ->create();
    $saleOrder = SaleOrder::factory()->create([
        'is_test' => 0,
        'order_type' => 6,
        'order_quantity' => 25,
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
    ]);
    $saleOrderItem = SaleOrderItem::factory()->create([
        'ink_color_status' => 1,
        'product_style_sku' => $productStyle->sku,
        'product_sku' => $product->sku,
        'product_id' => $product->id,
        'quantity' => 25,
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
    ]);
    foreach (range(1, 25) as $i) {
        SaleOrderItemBarcode::factory()->create([
            'order_id' => $saleOrder->id,
            'order_item_id' => $saleOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'is_deleted' => 0,
            'barcode_printed_id' => 0,
            'retry_convert' => 0,
            'label_root_id' => '12345' . $i,
        ]);
    }

    $saleOrder = SaleOrder::factory()->create([
        'is_test' => 0,
        'order_type' => 1,
        'order_quantity' => 20,
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
    ]);
    $saleOrderItem = SaleOrderItem::factory()->create([
        'ink_color_status' => 1,
        'product_style_sku' => $productStyle->sku,
        'product_sku' => $product->sku,
        'product_id' => $product->id,
        'quantity' => 20,
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
    ]);
    foreach (range(1, 20) as $i) {
        SaleOrderItemBarcode::factory()->create([
            'order_id' => $saleOrder->id,
            'order_item_id' => $saleOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'is_deleted' => 0,
            'barcode_printed_id' => 0,
            'retry_convert' => 0,
            'label_root_id' => '123456' . $i,
        ]);
    }

    $saleOrder = SaleOrder::factory()->create([
        'is_test' => 0,
        'order_type' => 1,
        'order_quantity' => 10,
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
    ]);
    $saleOrderItem = SaleOrderItem::factory()->create([
        'ink_color_status' => 1,
        'product_style_sku' => $productStyle->sku,
        'product_sku' => $product->sku,
        'product_id' => $product->id,
        'quantity' => 10,
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
    ]);
    foreach (range(1, 10) as $i) {
        SaleOrderItemBarcode::factory()->create([
            'order_id' => $saleOrder->id,
            'order_item_id' => $saleOrderItem->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'is_deleted' => 0,
            'barcode_printed_id' => 0,
            'retry_convert' => 0,
            'label_root_id' => '1234567' . $i,
        ]);
    }
    $param = [
        'quantity' => 1,
        'is_reprint' => null,
        'is_bulk_order' => 1,
        'is_tiktok' => null,
        'employee_id' => $this->employee->id
    ];
    $response = $this->post($this->endpoint, $param);
    $response->assertStatus(200);
    $this->assertEquals(1, BarcodePrinted::count());
    $barcodePrinted = BarcodePrinted::where('is_bulk_order', 1)
        ->where('print_method', 'UV')
        ->first();
    $this->assertNotNull($barcodePrinted);
    $countBarcode = SaleOrderItemBarcode::join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
        ->where('sale_order_item_barcode.barcode_printed_id', $barcodePrinted->id)
        ->where('sale_order.order_quantity', '>=', 20)
        ->whereNotIn('order_type', [SaleOrder::ORDER_TYPE_LABEL_ORDER, SaleOrder::ORDER_TYPE_TIKTOK_ORDER])->count();
    $this->assertEquals($countBarcode, 1);

    expect(json_decode($response->getContent(), true))->toHaveKeys([
        'code',
        'success',
        'message',
        'data.quantity_input',
        'data.employee_id',
        'data.warehouse_id',
        'data.user_id',
        'data.convert_percent',
        'data.print_method',
        'data.product_id',
        'data.created_at',
        'data.print_status',
        'data.id'
    ]);
});
