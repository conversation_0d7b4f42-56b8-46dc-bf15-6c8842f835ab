<?php

use App\Models\Employee;
use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/ornament/generate-pdf';
    $this->employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 1011]);
    $this->productStyle = ProductStyle::factory([
        'print_method' => 'UV'
    ])
        ->has(Product::factory([
            'parent_id' => 111,
        ]), 'product')
        ->create();
});

test('validation options', function () {
    $param = [
        'required' => [
            'product_id' => $this->productStyle->product->first()->id,
            'employee_id' => $this->employee->id
        ],
        'array' => [
            'options' => 'xxx',
            'product_id' => $this->productStyle->product->first()->id,
            'employee_id' => $this->employee->id
        ],
    ];

    // required
    $response = $this->post($this->endpoint, $param['required']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'options' => [
                'The options field is required.'
            ]
        ]
    ]);

    // array
    $response = $this->post($this->endpoint, $param['array']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'options' => [
                'The options must be an array.'
            ]
        ]
    ]);
});

test('validation product_id', function () {
    $param = [
        'required' => [
            'options' => ['xxx'],
            'employee_id' => $this->employee->id
        ],
        'numeric' => [
            'options' => ['xxx'],
            'product_id' => 'xxx',
            'employee_id' => $this->employee->id
        ]
    ];

    // required
    $response = $this->post($this->endpoint, $param['required']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'product_id' => [
                'The product id field is required.'
            ]
        ]
    ]);

    // numeric
    $response = $this->post($this->endpoint, $param['numeric']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'product_id' => [
                'The product id must be a number.'
            ]
        ]
    ]);
});

test('validation employee_id', function () {
    $param = [
        'required' => [
            'options' => ['xxx'],
            'product_id' => $this->productStyle->product->first()->id
        ],
        'exists' => [
            'options' => ['xxx'],
            'product_id' => $this->productStyle->product->first()->id,
            'employee_id' => 10000
        ]
    ];

    // required
    $response = $this->post($this->endpoint, $param['required']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => [
                'The employee id field is required.'
            ]
        ]
    ]);

    // exists
    $response = $this->post($this->endpoint, $param['exists']);

    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'employee_id' => [
                'You are not in this warehouse!'
            ]
        ]
    ]);
});

test('success', function () {
    $saleOrder = SaleOrder::factory([
        'order_status' => 'new_order'
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
    ])->has(SaleOrderItemImage::factory([
        'warehouse_id' => $this->warehouse->id,
        'print_side' => '0'
    ]), 'images')
        ->has(SaleOrderItemBarcode::factory([
            'warehouse_id' => $this->warehouse->id,
            'label_id' => 'xxx'
        ]), 'barcodes'), 'items')
        ->create();
    SaleOrderItem::where('product_id', $this->productStyle->product->first()->id)->update([
        'order_id' => $saleOrder->id,
    ]);
    SaleOrderItemBarcode::where('warehouse_id', $this->warehouse->id)->update([
        'order_id' => $saleOrder->id,
    ]);

    $param = [
        'options' => [
            [
                'label_id' => 'xxx',
                'image_id' => $saleOrder->items->first()->images->first()->id,
                'side' => '0'
            ]
        ],
        'product_id' => $this->productStyle->product->first()->id,
        'employee_id' => $this->employee->id
    ];

    $response = $this->post($this->endpoint, $param);

    $response->assertStatus(200);
    $this->assertEquals(1, PdfConverted::count());
    $this->assertEquals(1, PdfConvertedItem::count());
    expect(json_decode($response->getContent(), true))->toHaveKeys([
        'code',
        'success',
        'message',
        'data.quantity_input',
        'data.product_id',
        'data.employee_convert_id',
        'data.warehouse_id',
        'data.user_id',
        'data.convert_percent',
        'data.print_method',
        'data.download_status',
        'data.convert_status',
        'data.updated_at',
        'data.created_at',
        'data.id',
        'data.quantity',
        'data.options'
    ]);
});
