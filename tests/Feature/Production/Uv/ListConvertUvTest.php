<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Employee;
use App\Models\PdfConverted;
use App\Models\ProductType;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/ornament/list-pdf?limit=11';
    $this->employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 1011]);
});

test('success', function () {
    PdfConverted::factory()->count(7)->sequence(
        [
            // warehouse id is not match
            'warehouse_id' => 100000,
            'download_status' => 0,
            'print_method' => 'UV',
            'type' => ProductType::ORNAMENT,
            'employee_convert_id' => $this->employee->id,
        ],
        [
            // warehouse id is match
            'warehouse_id' => $this->warehouse->id,
            'download_status' => 0,
            'print_method' => 'UV',
            'type' => ProductType::ORNAMENT,
            'employee_convert_id' => $this->employee->id,
        ],
        [
            // download status is not match
            'download_status' => 1,
            'warehouse_id' => $this->warehouse->id,
            'print_method' => 'UV',
            'type' => ProductType::ORNAMENT,
            'employee_convert_id' => $this->employee->id,
        ],
        [
            // download status is match
            'download_status' => 0,
            'warehouse_id' => $this->warehouse->id,
            'print_method' => 'UV',
            'type' => ProductType::ORNAMENT,
            'employee_convert_id' => $this->employee->id,
        ],
        [
            // print method is not match
            'print_method' => 'MUGS',
            'download_status' => 0,
            'warehouse_id' => $this->warehouse->id,
            'employee_convert_id' => $this->employee->id,
        ],
        [
            // print method is not match
            'print_method' => 'DTG',
            'download_status' => 0,
            'warehouse_id' => $this->warehouse->id,
            'employee_convert_id' => $this->employee->id,
        ],
        [
            // print method is match
            'print_method' => 'UV',
            'type' => ProductType::ORNAMENT,
            'download_status' => 0,
            'warehouse_id' => $this->warehouse->id,
            'employee_convert_id' => $this->employee->id,
        ]
    )->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys([
        'code',
        'success',
        'message',
        'data',
        'data.current_page',
        'data.data',
        'data.first_page_url',
        'data.from',
        'data.last_page',
        'data.last_page_url',
        'data.links',
        'data.next_page_url',
        'data.path',
        'data.per_page',
        'data.prev_page_url',
        'data.to',
        'data.total'
    ]);

    expect($jsonRes['data']['data'])->toBeArray();

    expect($jsonRes['data']['data'][0])->toHaveKeys([
        'id',
        'quantity',
        'convert_status',
        'convert_percent',
        'convert_at',
        'download_status',
        'employee_convert_id',
        'warehouse_id',
        'download_at',
        'type',
        'product_id',
        'employee_download_id',
        'quantity_input',
        'user_id',
        'print_method',
        'created_at',
        'updated_at',
        'employee_convert.id',
        'employee_convert.name',
        'product'
    ]);

    expect($jsonRes['data']['total'])->toEqual(3);
});
