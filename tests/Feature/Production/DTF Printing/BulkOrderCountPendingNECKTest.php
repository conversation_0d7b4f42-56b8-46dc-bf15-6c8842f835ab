<?php

use App\Models\Employee;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Warehouse;
use App\Repositories\BarcodeDTFRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['warehouse' => $warehouse] = createAccessToken();
    Config::set('jwt.warehouse_id', $warehouse->id);
    $this->warehouse = $warehouse;

    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);

    $this->store = Warehouse::factory()->create();

    $this->saleorder = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => false,
        'order_quantity' => 20,
    ]);

    $this->productStyle = ProductStyle::factory()->create([
        'type' => '',
        'sku' => 'XXX',
    ]);

    $this->product = Product::factory()->create([
        'sku' => 'xxx'
    ]);

    $this->saleOrderItem = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 13,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 7,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 13));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 7));
    SaleOrderItemBarcode::factory()->createMany($mock);

    putenv('ID_SALE_ORDER_VALID=110');
});

test('Correctly count pending bulk order success', function () {
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending bulk order pass param.warehouse_id', function () {
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending bulk order not pass param.warehouse_id', function () {
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id + 1
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending bulk order exclude xqc', function () {
    $this->saleorder->is_xqc = true;
    $this->saleorder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();
});

test('Correctly count pending bulk order priority with fba', function () {
    $this->saleorder->is_fba_order = true;
    $this->saleorder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    $dataFba = resolve(BarcodeDTFRepository::class)->countPendingFba([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataFba['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with eps', function () {
    $this->saleorder->is_eps = true;
    $this->saleorder->is_xqc = false;
    $this->saleorder->save();
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    $dataEps = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataEps['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with reprint', function () {
    $this->saleorder->order_type = true;
    $this->saleorder->is_eps = true;
    $this->saleorder->is_fba_order = false;
    $this->saleorder->is_manual = false;
    $this->saleorder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->label_root_id = null;
    $barcode->reprint_status = false;
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with manual', function () {
    $this->saleorder->order_type = true;
    $this->saleorder->is_fba_order = false;
    $this->saleorder->is_manual = true;
    $this->saleorder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with reroute', function () {
    $this->saleorder->order_type = true;
    $this->saleorder->is_fba_order = false;
    $this->saleorder->is_manual = true;
    $this->saleorder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = 1;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending bulk order priority with styles', function () {
    $this->saleorder->order_type = true;
    $this->saleorder->is_fba_order = false;
    $this->saleorder->is_manual = false;
    $this->saleorder->save();
    $barcode = SaleOrderItemBarcode::first();
    $barcode->employee_reroute_id = null;
    $barcode->save();

    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    $dataReprint = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($dataReprint['total'])->toBe(0);
});

test('Correctly count pending bulk order after confirm barcode line FBA', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_manual' => 1,
        'is_eps' => true,
        'is_fba_order' => 1,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'employee_reroute_id' => 1,
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'employee_reroute_id' => 1,
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // before confirm fba
    $data = resolve(BarcodeDTFRepository::class)->countPendingFba([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(19)
        ->and($data['printed_at'])->toBeNull();

    // before confirm reroute
    $data = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm ManualProcess
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Reprint
    $data = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleXQC
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleEps
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Styles
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0);

    // confirm print fba
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 18,
        'is_fba' => 1,
    ]);

    // after confirm fba check count fba
    $data = resolve(BarcodeDTFRepository::class)->countPendingFba([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1)
        ->and($data['printed_at'])->toBeTruthy();

    // after confirm fba check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // confirm print bulk order
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 19,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1)
        ->and($data['printed_at'])->toBeTruthy();
});

test('Correctly count pending bulk order after confirm barcode line Reroute', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => 1,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'employee_reroute_id' => 1,
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
            'label_root_id' => 1,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'employee_reroute_id' => 1,
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
            'label_root_id' => 1,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // before confirm fba
    $data = resolve(BarcodeDTFRepository::class)->countPendingFba([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm reroute
    $data = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(19);

    // before confirm ManualProcess
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Reprint
    $data = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleXQC
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleEps
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Styles
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0);

    // confirm print Reroute
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 18,
        'is_reroute' => true,
    ]);

    // after confirm Reroute check count Reroute
    $data = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);

    // after confirm Reroute check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // confirm print bulk order
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 19,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);
});

test('Correctly count pending bulk order after confirm barcode line ManualProcess', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => 1,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
            'label_root_id' => 1,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
            'label_root_id' => 1,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // before confirm manual
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(19);

    // before confirm reroute
    $data = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm ManualProcess
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(19);

    // before confirm Reprint
    $data = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleXQC
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleEps
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Styles
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0);

    // confirm print manual
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 18,
        'is_manual' => 1,
    ]);

    // after confirm manual check count manual
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);

    // after confirm manual check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // confirm print bulk order
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 19,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);
});

test('Correctly count pending bulk order after confirm barcode line Reprint', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
            'label_root_id' => 1,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
            'label_root_id' => 1,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // before confirm manual
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm reroute
    $data = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm ManualProcess
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Reprint
    $data = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(19);

    // before confirm StyleXQC
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleEps
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Styles
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0);

    // confirm print manual
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 18,
        'is_reprint' => 1,
    ]);

    // after confirm manual check count manual
    $data = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);

    // after confirm manual check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // confirm print bulk order
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 19,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);
});

test('Correctly count pending bulk order after confirm barcode line XQC', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // before confirm manual
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm reroute
    $data = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm ManualProcess
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Reprint
    $data = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleXQC
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(19);

    // before confirm StyleEps
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Styles
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0);

    // confirm print xqc
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 18,
        'is_xqc' => true,
    ]);

    // after confirm xqc check count xqc
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);

    // after confirm xqc check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // confirm print bulk order
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 19,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);
});

test('Correctly count pending bulk order after confirm barcode line Express', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => false,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // before confirm manual
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm reroute
    $data = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm ManualProcess
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Reprint
    $data = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleXQC
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleEps
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(19);

    // before confirm Styles
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0);

    // confirm print express
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 18,
        'is_eps' => true,
    ]);

    // after confirm express check count express
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);

    // after confirm express check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // confirm print bulk order
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 19,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1);
});

test('Correctly count pending bulk order after confirm barcode line Styles', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => false,
        'is_eps' => false,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $this->productStyleOther = ProductStyle::factory()->create([
        'type' => '',
        'sku' => 'XX2',
    ]);

    $this->productOther = Product::factory()->create([
        'sku' => 'xx2'
    ]);

    $this->saleorderOther2 = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => false,
        'is_eps' => false,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 18,
    ]);

    $this->saleOrderItemOther2 = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther2->id,
            'product_style_sku' => $this->productStyleOther->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->productOther->sku,
        ],
        [
            'order_id' => $this->saleorderOther2->id,
            'product_style_sku' => $this->productStyleOther->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->productOther->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther2->id,
            'order_item_id' => $this->saleOrderItemOther2[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther2->id,
            'order_item_id' => $this->saleOrderItemOther2[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => 0,
            'is_deleted' => false,
            'print_method' => PrintMethod::NECK,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // before confirm manual
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm reroute
    $data = resolve(BarcodeDTFRepository::class)->countPendingReroute([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm ManualProcess
    $data = resolve(BarcodeDTFRepository::class)->countPendingManualProcess([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Reprint
    $data = resolve(BarcodeDTFRepository::class)->countPendingReprint([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleXQC
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleXQC([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm StyleEps
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyleEps([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(0)
        ->and($data['printed_at'])->toBeNull();

    // before confirm Styles
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(37)
        ->and($data['data'][0]->total)->toBe(19)
        ->and($data['data'][0]->sku)->toBe('XXX')
        ->and($data['data'][0]->printed_at)->toBeNull()
        ->and($data['data'][1]->total)->toBe(18)
        ->and($data['data'][1]->sku)->toBe('XX2')
        ->and($data['data'][1]->printed_at)->toBeNull();

    // confirm print Styles
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 18,
        'style_sku' => 'XXX',
    ]);

    // after confirm Styles check count Styles
    $data = resolve(BarcodeDTFRepository::class)->countPendingStyles([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(19)
        ->and($data['data'][0]->total)->toBe(18)
        ->and($data['data'][0]->sku)->toBe('XX2')
        ->and($data['data'][0]->printed_at)->toBeNull()
        ->and($data['data'][1]->total)->toBe(1)
        ->and($data['data'][1]->printed_at)->toBeTruthy()
        ->and($data['data'][1]->sku)->toBe('XXX');

    // after confirm Styles check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(20)
        ->and($data['printed_at'])->toBeNull();

    // confirm print bulk order
    resolve(BarcodeDTFRepository::class)->confirmPrintDtf([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::NECK,
        'limit' => 19,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order check count bulk order
    $data = resolve(BarcodeDTFRepository::class)->countPendingBulkOrder([
        'print_method' => PrintMethod::NECK,
        'warehouse_id' => $this->warehouse->id,
    ]);

    expect($data['total'])->toBe(1)
        ->and($data['printed_at'])->toBeTruthy();
});
