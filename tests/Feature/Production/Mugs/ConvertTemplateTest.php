<?php

use App\Models\BarcodePrinted;
use App\Models\Employee;
use App\Models\PrintingPresetSku;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/mug/convert-pdf';
    $this->employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 1011]);
    $this->productStyle = ProductStyle::factory([
        'print_method' => 'MUGS',
        'name' => 'MUGS'

    ])
        ->has(Product::factory()->count(2)->sequence(
            [
                'id' => 1000,
                'parent_id' => 111,
                'style' => 'MUGS',
                'sku' => 'MUGS1B11O',
                'color' => 'White',
                'size' => 'XL'
            ],
            [
                'id' => 1001,
                'parent_id' => 112,
                'style' => 'MUGS',
                'sku' => 'MUGS1B21O',
                'color' => 'BLACK',
                'size' => 'XXL'
            ],
        )->has(PrintingPresetSku::factory(), 'printingPresetSku'), 'product')
        ->create();
    $this->store = Store::factory()->create();
    $this->priorityStore = Store::factory()->create();
    Setting::factory()->create([
        'label' => Setting::PRIORITY_STORE,
        'name' => Setting::PRIORITY_STORE,
        'value' => $this->priorityStore->id
    ]);

    $this->orderOfPriorityStore = SaleOrder::factory()->count(15)->sequence(
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_manual' => true,
            'is_xqc' => true,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_eps' => true,
            'is_xqc' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'new_order',
            'is_test' => 1,
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_manual' => true,
            'is_xqc' => true,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_eps' => true,
            'is_xqc' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'on_hold',
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'cancelled',
            'is_eps' => true,
            'store_id' => $this->priorityStore->id,
        ],
    )->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->priorityStore->id,
        'warehouse_id' => $this->warehouse->id,

    ])->has(SaleOrderItemBarcode::factory()->count(3)->sequence(
        [
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->priorityStore->id,
            'print_method' => 'MUGS',
        ],
        [
            'warehouse_id' => $this->warehouse->id + 1,
            'store_id' => $this->priorityStore->id,
            'print_method' => 'MUGS',
        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->priorityStore->id,
            'print_method' => 'MUGS',
            'is_deleted' => 1
        ],
    ), 'barcodes'), 'items')
        ->create();
    $this->orderOfPriorityStore->map(function ($order) {
        return $order->items->map(function ($item) use ($order) {
            return $item->barcodes->map(function ($barcode) use ($order) {
                $barcode->order_id = $order->id;
                $barcode->save();
            });
        });
    });

    $this->orderReroute = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->priorityStore->id,
        'is_manual' => true,
        'is_xqc' => true,
        'is_eps' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->priorityStore->id,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->priorityStore->id,
        'print_method' => 'MUGS',
        'employee_reroute_id' => 1,
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderReroute->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderReroute->id;
            $barcode->save();
        });
    });

    $this->orderReprint = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->priorityStore->id,
        'is_xqc' => true,
        'is_eps' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->priorityStore->id,
        'warehouse_id' => $this->warehouse->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->priorityStore->id,
        'print_method' => 'MUGS',
        'label_root_id' => 1000
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderReprint->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderReprint->id;
            $barcode->save();
        });
    });
    $this->orderReroute = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
        'is_manual' => true,
        'is_xqc' => true,
        'is_eps' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'print_method' => 'MUGS',
        'employee_reroute_id' => 1,
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderReroute->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderReroute->id;
            $barcode->save();
        });
    });

    $this->orderReprint = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
        'is_xqc' => true,
        'is_eps' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'print_method' => 'MUGS',
        'label_root_id' => 1000
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderReprint->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderReprint->id;
            $barcode->save();
        });
    });

    $this->saleOrder = SaleOrder::factory()->count(9)->sequence(
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'is_test' => 1,
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'is_manual' => true,
            'is_xqc' => true,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'is_eps' => true,
            'is_xqc' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
        ],
        [
            'order_status' => 'on_hold',
            'store_id' => $this->store->id,
        ],
        [
            'order_status' => 'cancelled',
            'is_eps' => true,
            'store_id' => $this->store->id,
        ],
    )->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory()->count(3)->sequence(
        [
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'print_method' => 'MUGS',

        ],
        [
            'warehouse_id' => $this->warehouse->id + 1,
            'store_id' => $this->store->id,
            'print_method' => 'MUGS',

        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->priorityStore->id,
            'print_method' => 'MUGS',
            'is_deleted' => 1

        ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->map(function ($order) {
        return $order->items->map(function ($item) use ($order) {
            return $item->barcodes->map(function ($barcode) use ($order) {
                $barcode->order_id = $order->id;
                $barcode->save();
            });
        });
    });
});

test('success: convert line tiktok', function () {
    $param = [
        'quantity' => 1,
        'is_tiktok' => 1,
        'employee_id' => $this->employee->id
    ];
    $response = $this->post($this->endpoint, $param);
    $response->assertStatus(200);
    $this->assertEquals(1, BarcodePrinted::count());
    $response = json_decode($response->getContent(), true);
    unset($response['data'][0]['id'], $response['data'][0]['user_id'], $response['data'][0]['created_at'], $response['data'][0]['quantity_input'], $response['data'][0]['employee_convert']['name']);

    expect($response)->toMatchArray([
        'code' => 200,
        'success' => true,
        'message' => 'success',
        'data' => [
            0 => [
                'style_sku' => 'MUGS',
                'employee_id' => $this->employee->id,
                'warehouse_id' => $this->warehouse->id,
                'store_id' => null,
                'convert_percent' => 1,
                'print_method' => 'MUGS',
                'product_id' => $this->productStyle->product->first()->id,
                'print_status' => 0,
                'is_tiktok' => 1,
                'is_fba' => null,
                'is_reroute' => null,
                'is_manual' => null,
                'is_reprint' => null,
                'is_xqc' => null,
                'is_eps' => null,
                'employee_convert' => [
                    'id' => $this->employee->id,
                ],
                'product' => [
                    'id' => 1000,
                    'color' => 'White',
                    'size' => 'XL'
                ],
                'is_bulk_order' => null
            ]
        ]
    ]);

    $barcodePrinted = BarcodePrinted::where('style_sku', 'MUGS')
        ->where('employee_id', $this->employee->id)
        ->where('warehouse_id', $this->warehouse->id)
        ->whereNull('store_id')
        ->where('convert_percent', 1)
        ->where('print_method', 'MUGS')
        ->where('product_id', $this->productStyle->product->first()->id)
        ->where('print_status', 0)
        ->where('is_tiktok', 1)
        ->whereNull('is_fba')
        ->whereNull('is_reroute')
        ->whereNull('is_manual')
        ->whereNull('is_reprint')
        ->whereNull('is_xqc')
        ->whereNull('is_eps')
        ->first();
    $this->assertNotNull($barcodePrinted);
    $barcode = SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrinted->id)
        ->count();
    $this->assertEquals($barcode, 1);
});

test('success: convert line tiktok: multiple sku', function () {
    $order = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
        'order_type' => 6,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product[1]->id,
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'print_method' => 'MUGS',
        'employee_reroute_id' => 1,
    ]), 'barcodes'), 'items')
        ->create();
    $order->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) use ($item) {
            $barcode->order_id = $item->order_id;
            $barcode->save();
        });
    });
    $param = [
        'quantity' => 3,
        'is_tiktok' => 1,
        'employee_id' => $this->employee->id
    ];
    $response = $this->post($this->endpoint, $param);
    $response->assertStatus(200);

    $barcodePrinted = BarcodePrinted::where('style_sku', 'MUGS')
        ->where('employee_id', $this->employee->id)
        ->where('warehouse_id', $this->warehouse->id)
        ->whereNull('store_id')
        ->where('print_method', 'MUGS')
        ->where('product_id', $this->productStyle->product->first()->id)
        ->where('print_status', 0)
        ->where('is_tiktok', 1)
        ->whereNull('is_fba')
        ->whereNull('is_reroute')
        ->whereNull('is_manual')
        ->whereNull('is_reprint')
        ->whereNull('is_xqc')
        ->whereNull('is_eps')
        ->first();
    $this->assertNotNull($barcodePrinted);
    $barcode = SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrinted->id)
        ->count();
    $this->assertEquals($barcode, 2);
    $barcodePrinted = BarcodePrinted::where('style_sku', 'MUGS')
        ->where('employee_id', $this->employee->id)
        ->where('warehouse_id', $this->warehouse->id)
        ->whereNull('store_id')
        ->where('print_method', 'MUGS')
        ->where('product_id', $this->productStyle->product[1]->id)
        ->where('print_status', 0)
        ->where('is_tiktok', 1)
        ->whereNull('is_fba')
        ->whereNull('is_reroute')
        ->whereNull('is_manual')
        ->whereNull('is_reprint')
        ->whereNull('is_xqc')
        ->whereNull('is_eps')
        ->first();
    $this->assertNotNull($barcodePrinted);
    $barcode = SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrinted->id)
        ->count();
    $this->assertEquals($barcode, 1);
});

// in o line style của store không thuộc priority success
test('success: convert line style', function () {
    $param = [
        'quantity' => 1,
        'product_id' => $this->productStyle->product->first()->id,
        'employee_id' => $this->employee->id
    ];
    $response = $this->post($this->endpoint, $param);
    $response->assertStatus(200);
    $this->assertEquals(1, BarcodePrinted::count());
    $response = json_decode($response->getContent(), true);
    unset($response['data'][0]['id'], $response['data'][0]['user_id'], $response['data'][0]['created_at'], $response['data'][0]['quantity_input'], $response['data'][0]['employee_convert']['name']);

    expect($response)->toMatchArray([
        'code' => 200,
        'success' => true,
        'message' => 'success',
        'data' => [
            0 => [
                'style_sku' => 'MUGS',
                'employee_id' => $this->employee->id,
                'warehouse_id' => $this->warehouse->id,
                'store_id' => null,
                'convert_percent' => 1,
                'print_method' => 'MUGS',
                'product_id' => 1000,
                'print_status' => 0,
                'is_tiktok' => null,
                'is_fba' => null,
                'is_reroute' => null,
                'is_manual' => null,
                'is_reprint' => null,
                'is_xqc' => null,
                'is_eps' => null,
                'employee_convert' => [
                    'id' => $this->employee->id,
                ],
                'product' => [
                    'id' => 1000,
                    'color' => 'White',
                    'size' => 'XL'
                ],
                'is_bulk_order' => null
            ]
        ]
    ],
    );
    $this->assertDatabaseHas('barcode_printed', [
        'style_sku' => 'MUGS',
        'employee_id' => $this->employee->id,
        'warehouse_id' => $this->warehouse->id,
        'store_id' => null,
        'convert_percent' => 1,
        'print_method' => 'MUGS',
        'product_id' => $this->productStyle->product->first()->id,
        'print_status' => 0,
        'is_tiktok' => null,
        'is_fba' => null,
        'is_reroute' => null,
        'is_manual' => null,
        'is_reprint' => null,
        'is_xqc' => null,
        'is_eps' => null,
    ]);
});

test('success: convert line fba', function () {
    $param = [
        'quantity' => 1,
        'is_fba' => 1,
        'employee_id' => $this->employee->id
    ];
    $response = $this->post($this->endpoint, $param);
    $response->assertStatus(200);
    $this->assertEquals(1, BarcodePrinted::count());
    $response = json_decode($response->getContent(), true);
    unset($response['data'][0]['id'], $response['data'][0]['user_id'], $response['data'][0]['created_at'], $response['data'][0]['quantity_input'], $response['data'][0]['employee_convert']['name']);

    expect($response)->toMatchArray([
        'code' => 200,
        'success' => true,
        'message' => 'success',
        'data' => [
            0 => [
                'style_sku' => 'MUGS',
                'employee_id' => $this->employee->id,
                'warehouse_id' => $this->warehouse->id,
                'store_id' => null,
                'convert_percent' => 1,
                'print_method' => 'MUGS',
                'product_id' => $this->productStyle->product->first()->id,
                'print_status' => 0,
                'is_fba' => 1,
                'is_tiktok' => null,
                'is_reroute' => null,
                'is_manual' => null,
                'is_reprint' => null,
                'is_xqc' => null,
                'is_eps' => null,
                'employee_convert' => [
                    'id' => $this->employee->id,
                ],
                'product' => [
                    'id' => 1000,
                    'color' => 'White',
                    'size' => 'XL'
                ],
                'is_bulk_order' => null
            ]
        ]
    ],
    );

    $barcodePrinted = BarcodePrinted::where('style_sku', 'MUGS')
        ->where('employee_id', $this->employee->id)
        ->where('warehouse_id', $this->warehouse->id)
        ->whereNull('store_id')
        ->where('convert_percent', 1)
        ->where('print_method', 'MUGS')
        ->where('product_id', $this->productStyle->product->first()->id)
        ->where('print_status', 0)
        ->where('is_fba', 1)
        ->whereNull('is_tiktok')
        ->whereNull('is_reroute')
        ->whereNull('is_manual')
        ->whereNull('is_reprint')
        ->whereNull('is_xqc')
        ->whereNull('is_eps')
        ->first();
    $this->assertNotNull($barcodePrinted);
    $barcode = SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrinted->id)
        ->count();
    $this->assertEquals($barcode, 1);
});

test('success: convert line fba: multiple sku', function () {
    $order = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
        'is_fba_order' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product[1]->id,
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'print_method' => 'MUGS',
    ]), 'barcodes'), 'items')
        ->create();
    $order->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) use ($item) {
            $barcode->order_id = $item->order_id;
            $barcode->save();
        });
    });
    $param = [
        'quantity' => 3,
        'is_fba' => 1,
        'employee_id' => $this->employee->id
    ];
    $response = $this->post($this->endpoint, $param);
    $response->assertStatus(200);

    $barcodePrinted = BarcodePrinted::where('style_sku', 'MUGS')
        ->where('employee_id', $this->employee->id)
        ->where('warehouse_id', $this->warehouse->id)
        ->whereNull('store_id')
        ->where('print_method', 'MUGS')
        ->where('product_id', $this->productStyle->product->first()->id)
        ->where('print_status', 0)
        ->where('is_fba', 1)
        ->whereNull('is_tiktok')
        ->whereNull('is_reroute')
        ->whereNull('is_manual')
        ->whereNull('is_reprint')
        ->whereNull('is_xqc')
        ->whereNull('is_eps')
        ->first();
    $this->assertNotNull($barcodePrinted);
    $barcode = SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrinted->id)
        ->count();
    $this->assertEquals($barcode, 2);
    $barcodePrinted = BarcodePrinted::where('style_sku', 'MUGS')
        ->where('employee_id', $this->employee->id)
        ->where('warehouse_id', $this->warehouse->id)
        ->whereNull('store_id')
        ->where('print_method', 'MUGS')
        ->where('product_id', $this->productStyle->product[1]->id)
        ->where('print_status', 0)
        ->where('is_fba', 1)
        ->whereNull('is_tiktok')
        ->whereNull('is_reroute')
        ->whereNull('is_manual')
        ->whereNull('is_reprint')
        ->whereNull('is_xqc')
        ->whereNull('is_eps')
        ->first();
    $this->assertNotNull($barcodePrinted);
    $barcode = SaleOrderItemBarcode::where('barcode_printed_id', $barcodePrinted->id)
        ->count();
    $this->assertEquals($barcode, 1);
});
