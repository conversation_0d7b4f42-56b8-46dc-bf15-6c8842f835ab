<?php

use App\Models\BarcodePrinted;
use App\Models\Employee;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use App\Models\Store;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/mug/count';
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);

    // data match
    $this->productStyle = ProductStyle::factory([
        'print_method' => 'MUGS',
        'name' => 'MUGS'

    ])
        ->has(Product::factory([
            'id' => 1000,
            'parent_id' => 111,
            'style' => 'MUGS'
        ]), 'product')
        ->create();
    $this->store = Store::factory()->create();
    $this->priorityStore = Store::factory()->create();
    Setting::factory()->create([
        'label' => Setting::PRIORITY_STORE,
        'name' => Setting::PRIORITY_STORE,
        'value' => $this->priorityStore->id
    ]);

    $this->orderOfPriorityStore = SaleOrder::factory()->count(15)->sequence(
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_manual' => true,
            'is_xqc' => true,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_eps' => true,
            'is_xqc' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'new_order',
            'is_test' => 1,
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_manual' => true,
            'is_xqc' => true,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_eps' => true,
            'is_xqc' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'on_hold',
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'cancelled',
            'is_eps' => true,
            'store_id' => $this->priorityStore->id,
        ],
    )->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->priorityStore->id,
    ])->has(SaleOrderItemBarcode::factory()->count(3)->sequence(
        [
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->priorityStore->id,
            'print_method' => 'MUGS',

        ],
        [
            'warehouse_id' => $this->warehouse->id + 1,
            'store_id' => $this->priorityStore->id,
            'print_method' => 'MUGS',

        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->priorityStore->id,
            'print_method' => 'MUGS',
            'is_deleted' => 1

        ],
    ), 'barcodes'), 'items')
        ->create();
    $this->orderOfPriorityStore->map(function ($order) {
        return $order->items->map(function ($item) use ($order) {
            return $item->barcodes->map(function ($barcode) use ($order) {
                $barcode->order_id = $order->id;
                $barcode->save();
            });
        });
    });

    $this->orderReroute = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->priorityStore->id,
        'is_manual' => true,
        'is_xqc' => true,
        'is_eps' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->priorityStore->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->priorityStore->id,
        'print_method' => 'MUGS',
        'employee_reroute_id' => 1,
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderReroute->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderReroute->id;
            $barcode->save();
        });
    });

    $this->orderReprint = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->priorityStore->id,
        'is_xqc' => true,
        'is_eps' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->priorityStore->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->priorityStore->id,
        'print_method' => 'MUGS',
        'label_root_id' => 1000
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderReprint->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderReprint->id;
            $barcode->save();
        });
    });

    $this->orderReroute = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
        'is_manual' => true,
        'is_xqc' => true,
        'is_eps' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'print_method' => 'MUGS',
        'employee_reroute_id' => 1,
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderReroute->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderReroute->id;
            $barcode->save();
        });
    });

    $this->orderReprint = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
        'is_xqc' => true,
        'is_eps' => true,
    ])->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory([
        'warehouse_id' => $this->warehouse->id,
        'store_id' => $this->store->id,
        'print_method' => 'MUGS',
        'label_root_id' => 1000
    ]), 'barcodes'), 'items')
        ->create();

    $this->orderReprint->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->orderReprint->id;
            $barcode->save();
        });
    });

    $this->saleOrder = SaleOrder::factory()->count(9)->sequence(
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'is_test' => 1,
            'store_id' => $this->priorityStore->id,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'is_fba_order' => true,
            'is_xqc' => true,
            'is_eps' => true,
            'is_manual' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'is_manual' => true,
            'is_xqc' => true,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'is_eps' => true,
            'is_xqc' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
            'is_eps' => true,
        ],
        [
            'order_status' => 'new_order',
            'store_id' => $this->store->id,
        ],
        [
            'order_status' => 'on_hold',
            'store_id' => $this->store->id,
        ],
        [
            'order_status' => 'cancelled',
            'is_eps' => true,
            'store_id' => $this->store->id,
        ],

    )->has(SaleOrderItem::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory()->count(3)->sequence(
        [
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'print_method' => 'MUGS',

        ],
        [
            'warehouse_id' => $this->warehouse->id + 1,
            'store_id' => $this->store->id,
            'print_method' => 'MUGS',

        ],
        [
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->priorityStore->id,
            'print_method' => 'MUGS',
            'is_deleted' => 1

        ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->map(function ($order) {
        return $order->items->map(function ($item) use ($order) {
            return $item->barcodes->map(function ($barcode) use ($order) {
                $barcode->order_id = $order->id;
                $barcode->save();
            });
        });
    });

    $this->barcodePrinted = BarcodePrinted::factory([
        'product_id' => $this->productStyle->product->first()->id,
        'created_at' => Carbon::now(),
        'warehouse_id' => $this->warehouse->id,
        'print_method' => $this->productStyle->print_method
    ])
        ->create();

    $this->keySuccess = [
        'code',
        'success',
        'message',
        'data.total',
        'data.data'
    ];

    $this->keySuccessChild = [
        'product_id',
        'count',
        'size',
        'color',
        'last_created_at'
    ];
});

// Khong truyen store id
test('param not store id', function () {
    $productStyle = ProductStyle::factory([
        'print_method' => 'UV'
    ])
        ->has(Product::factory([
            'parent_id' => 222,
            'name' => 'MUGS'
        ]), 'product')
        ->create();
    $saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
    ],
    )->create();
    SaleOrderItem::factory([
        'product_id' => $productStyle->product->first()->id,
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory([
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
        'print_method' => $productStyle->print_method,
        'warehouse_id' => $this->warehouse->id,
    ]), 'barcodes')
        ->create();

    BarcodePrinted::factory([
        'product_id' => $productStyle->product->first()->id,
        'created_at' => Carbon::now(),
        'warehouse_id' => $this->warehouse->id,
        'print_method' => $productStyle->print_method
    ])
        ->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    foreach ($jsonRes['data'] as $key => $item) {
        if (isset($jsonRes['data'][$key]['sql'])) {
            unset($jsonRes['data'][$key]['sql']);
        }
    }

    expect($jsonRes)->toMatchArray([
        'code' => 200,
        'success' => true,
        'message' => 'success',
        'data' => [
            'tiktok' => [
                'total' => 1,
                'printed_at' => null
            ],
            'fba' => [
                'total' => 1,
                'printed_at' => null
            ],
            'reroute' => [
                'total' => 1,
                'printed_at' => null
            ],
            'manual' => [
                'total' => 1,
                'printed_at' => null
            ],
            'reprint' => [
                'total' => 1,
                'printed_at' => null
            ],
            'xqc' => [
                'total' => 1,
                'printed_at' => null
            ],
            'eps' => [
                'total' => 1,
                'printed_at' => null
            ],
            'style' => [
                'total' => 1,
                'data' => [
                    [
                        'product_id' => 1000,
                        'count' => 1,
                        'color' => null,
                        'size' => null,
                        'last_created_at' => null,
                        'style_name' => 'MUGS'
                    ]
                ]
            ],
            'bulk_order' => [
                'total' => 0,
                'printed_at' => null
            ],
        ]
    ],
    );
});

// truyen store id
test('param store id', function () {
    $productStyle = ProductStyle::factory([
        'print_method' => 'UV'
    ])
        ->has(Product::factory([
            'parent_id' => 222,
            'name' => 'MUGS'
        ]), 'product')
        ->create();
    $saleOrder = SaleOrder::factory([
        'order_status' => 'new_order',
        'store_id' => $this->store->id,
    ],
    )->create();
    SaleOrderItem::factory([
        'product_id' => $productStyle->product->first()->id,
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
    ])->has(SaleOrderItemBarcode::factory([
        'order_id' => $saleOrder->id,
        'store_id' => $this->store->id,
        'print_method' => $productStyle->print_method,
        'warehouse_id' => $this->warehouse->id,
    ]), 'barcodes')
        ->create();

    BarcodePrinted::factory([
        'product_id' => $productStyle->product->first()->id,
        'created_at' => Carbon::now(),
        'warehouse_id' => $this->warehouse->id,
        'print_method' => $productStyle->print_method
    ])
        ->create();
    $param['store_id'] = $this->priorityStore->id;
    $endpoint = $this->endpoint . '?' . http_build_query($param);
    $response = $this->get($endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    foreach ($jsonRes['data'] as $key => $item) {
        if (isset($jsonRes['data'][$key]['sql'])) {
            unset($jsonRes['data'][$key]['sql']);
        }
    }

    expect($jsonRes)->toMatchArray([
        'code' => 200,
        'success' => true,
        'message' => 'success',
        'data' => [
            'tiktok' => [
                'total' => 2,
                'printed_at' => null
            ],
            'fba' => [
                'total' => 2,
                'printed_at' => null
            ],
            'reroute' => [
                'total' => 1,
                'printed_at' => null
            ],
            'manual' => [
                'total' => 2,
                'printed_at' => null
            ],
            'reprint' => [
                'total' => 1,
                'printed_at' => null
            ],
            'xqc' => [
                'total' => 2,
                'printed_at' => null
            ],
            'eps' => [
                'total' => 2,
                'printed_at' => null
            ],
            'style' => [
                'total' => 2,
                'data' => [
                    [
                        'product_id' => 1000,
                        'count' => 2,
                        'color' => null,
                        'size' => null,
                        'last_created_at' => null,
                        'style_name' => 'MUGS'
                    ]
                ]
            ],
            'bulk_order' => [
                'total' => 0,
                'printed_at' => null
            ],
        ]
    ],
    );
});
