<?php

use App\Exports\ShipmentExport;
use App\Models\Client;
use App\Models\Department;
use App\Models\DepartmentJobType;
use App\Models\Product;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Shipment;
use App\Models\ShippingCarrier;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\StoreShipment;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tymon\JWTAuth\Facades\JWTAuth;

uses(RefreshDatabase::class);

beforeEach(function () {
    ShippingCarrier::factory()->create([
        'code' => 'USPS'
    ]);
    ShippingCarrier::factory()->create([
        'code' => 'DHL'
    ]);

    $this->warehouse = Warehouse::factory()->create();
    $this->department = Department::factory(['name' => 'Inventory', 'performance_report' => 1])
        ->has(DepartmentJobType::factory()->count(2)->state(new Sequence(
            ['job_type' => 'addition'],
            ['job_type' => 'create_box_moving'],
        )), 'jobTypes')->create();

    $this->user = User::factory()->create(['department_id' => 35]);
    $this->access_token = JWTAuth::customClaims(['warehouse' => ['id' => $this->warehouse->id]])->fromUser($this->user);

    $this->client = Client::factory()->create(['username' => 'test']);
    $this->store = Store::factory()->create(['username' => 'test', 'is_calculate_shipping' => true, 'is_calculate_price' => true, 'client_id' => $this->client->id]);

    $this->productStyle = ProductStyle::factory()->count(2)->sequence(
        ['type' => 'TEE', 'sku' => '3001'],
        ['type' => 'TEE', 'sku' => '5001'],
    )->create();

    $this->printArea = ProductPrintArea::factory()->count(4)->sequence(
        ['name' => 'Front', 'product_style_id' => $this->productStyle[0]->id],
        ['name' => 'Front', 'product_style_id' => $this->productStyle[1]->id],
        ['name' => 'Back', 'product_style_id' => $this->productStyle[0]->id],
        ['name' => 'Back', 'product_style_id' => $this->productStyle[1]->id],
    )->create();

    $this->productPrintSide = ProductPrintSide::factory()->count(2)->sequence(
        ['name' => 'Front', 'code' => 0, 'code_wip' => 'F'],
        ['name' => 'Back', 'code' => 1, 'code_wip' => 'B'],
    )->create();

    $this->product = Product::factory()->count(2)->sequence(
        ['name' => '3001 / AQUA / XS', 'sku' => 'PRODUCT1'],
        ['name' => '5001 / WHITE / XL', 'sku' => 'PRODUCT2'],
    )->create();
    StoreProduct::factory()->count(5)->sequence(
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5, 'print_surcharge' => 1],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[0]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[1]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[2]->id, 'print_price' => 1.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->first()->id, 'product_print_area_id' => $this->printArea[3]->id, 'print_price' => 1.5],
    )->create();

    StoreProduct::factory()->count(5)->sequence(
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => 0, 'price' => 3.5, 'handling_fee' => 2.5, 'print_surcharge' => 1],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[0]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[1]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[2]->id, 'print_price' => 0.5],
        ['store_id' => $this->store->id, 'product_id' => $this->product->last()->id, 'product_print_area_id' => $this->printArea[3]->id, 'print_price' => 0.5],
    )->create();

    $this->storeShipment = StoreShipment::factory()->count(6)->sequence(
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_EXPRESS,
            'price' => 10,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 9,
            'addition_price' => 6,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::DOMESTIC,
            'service_type' => StoreShipment::SERVICE_EXPRESS,
            'price' => 8,
            'addition_price' => 5,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'TEE',
            'destination' => StoreShipment::INTERNATIONAL,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 30,
            'addition_price' => 25,
        ],
        [
            'store_id' => $this->store->id,
            'product_type' => 'FLEECE',
            'destination' => StoreShipment::INTERNATIONAL,
            'service_type' => StoreShipment::SERVICE_STANDARD,
            'price' => 35,
            'addition_price' => 25,
        ],
    )->create();

    $this->saleOrder = SaleOrder::factory([
        'store_id' => $this->store->id,
        'created_at' => '2024-10-20 00:00:00',
        'warehouse_id' => $this->warehouse->id,
        'shipping_method' => StoreShipment::SERVICE_STANDARD,
        'order_status' => SaleOrder::SHIPPED,
    ])
        ->has(SaleOrderAddress::factory([
            'type_address' => SaleOrderAddress::TO_ADDRESS,
            'country' => 'US',
        ]), 'address')
        ->has(
            SaleOrderItem::factory()->count(1)->sequence(
                [
                    'warehouse_id' => 1,
                    'store_id' => $this->store->id,
                    'quantity' => 10,
                    'product_id' => $this->product->first()->id,
                    'product_style_sku' => '3001',
                    'product_size_sku' => 'M',
                    'print_sides' => 'FB',
                ],
            )
            ->has(SaleOrderItemImage::factory()->count(2)->sequence(
                ['warehouse_id' => 1, 'print_side' => '0'],
                ['warehouse_id' => 1, 'print_side' => '1'],
            ), 'images'),
            'items',
        )
        ->afterCreating(function (SaleOrder $saleOrder) {
            Shipment::factory([
                'store_id' => $this->store->id,
                'warehouse_id' => $this->warehouse->id,
                'carrier_code' => 'USPS',
                'service_code' => 'USPS',
                'tracking_status' => 'return_to_sender',
                'shipment_account' => Shipment::SHIPMENT_ACCOUNT_SWIFTPOD,
                'created_at' => '2024-10-20 00:00:00',
                'order_id' => $saleOrder->id, // Set the order_id here
            ])->create();
        })
        ->create();

    $this->endpoint = '/api/invoices/product-detail/temp';
});

test('success', function () {
    $params = [];
    $params['warehouse_id'] = $this->warehouse->id;
    $params['shipment_date_start'] = '2024-01-30 00:00:00';
    $params['shipment_date_end'] = '2024-12-30 00:00:00';
    $params['keyword'] = '';

    $export = new ShipmentExport($params);
    $collection = $export->collection();
    $this->assertCount(1, $collection);
});

test('success - filter store_id', function () {
    $params = [];
    $params['warehouse_id'] = $this->warehouse->id;
    $params['shipment_date_start'] = '2024-01-30 00:00:00';
    $params['shipment_date_end'] = '2024-12-30 00:00:00';
    $params['keyword'] = '';
    $params['store_id'] = $this->store->id;

    $export = new ShipmentExport($params);
    $collection = $export->collection();
    $this->assertCount(1, $collection);
});

test('success - filter carrier_code', function () {
    $params = [];
    $params['warehouse_id'] = $this->warehouse->id;
    $params['shipment_date_start'] = '2024-01-30 00:00:00';
    $params['shipment_date_end'] = '2024-12-30 00:00:00';
    $params['keyword'] = '';
    $params['store_id'] = $this->store->id;
    $params['carrier_code'] = 'USPS';

    $export = new ShipmentExport($params);
    $collection = $export->collection();
    $this->assertCount(1, $collection);
});

test('success - filter service_code', function () {
    $params = [];
    $params['warehouse_id'] = $this->warehouse->id;
    $params['shipment_date_start'] = '2024-01-30 00:00:00';
    $params['shipment_date_end'] = '2024-12-30 00:00:00';
    $params['keyword'] = '';
    $params['store_id'] = $this->store->id;
    $params['service_code'] = 'USPS';

    $export = new ShipmentExport($params);
    $collection = $export->collection();
    $this->assertCount(1, $collection);
});

test('success - filter shipment_status', function () {
    $params = [];
    $params['warehouse_id'] = $this->warehouse->id;
    $params['shipment_date_start'] = '2024-01-30 00:00:00';
    $params['shipment_date_end'] = '2024-12-30 00:00:00';
    $params['keyword'] = '';
    $params['store_id'] = $this->store->id;
    $params['shipment_status'] = 'delivered';

    $export = new ShipmentExport($params);
    $collection = $export->collection();
    $this->assertCount(0, $collection);
});

test('success - filter external_number', function () {
    $params = [];
    $params['warehouse_id'] = $this->warehouse->id;
    $params['shipment_date_start'] = '2024-01-30 00:00:00';
    $params['shipment_date_end'] = '2024-12-30 00:00:00';
    $params['keyword'] = '';
    $params['store_id'] = $this->store->id;
    $params['keyword'] = '12312312312312';

    $export = new ShipmentExport($params);
    $collection = $export->collection();
    $this->assertCount(0, $collection);
});

test('success - filter keyword', function () {
    $params = [];
    $params['warehouse_id'] = $this->warehouse->id;
    $params['shipment_date_start'] = '2024-01-30 00:00:00';
    $params['shipment_date_end'] = '2024-12-30 00:00:00';
    $params['keyword'] = '';
    $params['store_id'] = $this->store->id;
    $params['keyword'] = '12312312312312';

    $export = new ShipmentExport($params);
    $collection = $export->collection();
    $this->assertCount(0, $collection);
});
