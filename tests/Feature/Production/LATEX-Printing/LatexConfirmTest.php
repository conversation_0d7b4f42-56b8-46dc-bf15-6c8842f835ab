<?php

use App\Models\Employee;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\Warehouse;
use App\Repositories\BarcodeLatexRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

uses(RefreshDatabase::class);

beforeEach(function () {
    Cache::store(config('cache.redis_store'))->flush();
    ['warehouse' => $warehouse] = createAccessToken();
    Config::set('jwt.warehouse_id', $warehouse->id);
    $this->warehouse = $warehouse;

    $this->employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'code' => 1011,
    ]);

    $this->store = Warehouse::factory()->create();

    $this->saleorder = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => false,
        'order_quantity' => 20,
    ]);

    $this->productStyle = ProductStyle::factory()->create([
        'type' => '',
        'sku' => 'XXX',
    ]);

    $this->product = Product::factory()->create([
        'sku' => 'xxx'
    ]);

    $this->saleOrderItem = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 13,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorder->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 7,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 13));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorder->id,
            'order_item_id' => $this->saleOrderItem[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 7));
    SaleOrderItemBarcode::factory()->createMany($mock);

    putenv('ID_SALE_ORDER_VALID=110');
});

test('Correctly count pending bulk order after confirm barcode line FBA', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_manual' => true,
        'is_eps' => true,
        'is_fba_order' => true,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'employee_reroute_id' => 1,
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'employee_reroute_id' => 1,
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'fba':
                expect($item['total'])->toBe(19)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print fba
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_fba' => 1,
    ]);

    // after confirm fba
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'fba':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print bulk order
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(2)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'fba':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }
});

test('Correctly count pending bulk order after confirm barcode line Reroute', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => true,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'employee_reroute_id' => 1,
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
            'label_root_id' => 1,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'employee_reroute_id' => 1,
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
            'label_root_id' => 1,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'reroute':
                expect($item['total'])->toBe(19)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print reroute
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_reroute' => 1,
    ]);

    // after confirm reroute
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'reroute':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print bulk order
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(2)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'reroute':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }
});

test('Correctly count pending bulk order after confirm barcode line Tiktok', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_TIKTOK_ORDER,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => true,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
            'label_root_id' => 1,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
            'label_root_id' => 1,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'tiktok':
                expect($item['total'])->toBe(19)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print tiktok
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_tiktok' => 1,
    ]);

    // after confirm tiktok
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'tiktok':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print bulk order
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(2)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'tiktok':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }
});

test('Correctly count pending bulk order after confirm barcode line ManualProcess', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => true,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
            'label_root_id' => 1,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
            'label_root_id' => 1,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'manual':
                expect($item['total'])->toBe(19)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print manual
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_manual' => 1,
    ]);

    // after confirm manual
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'manual':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print bulk order
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(2)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'manual':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }
});

test('Correctly count pending bulk order after confirm barcode line Reprint', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
            'label_root_id' => 1,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
            'label_root_id' => 1,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'reprint':
                expect($item['total'])->toBe(19)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print reprint
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_reprint' => 1,
    ]);

    // after confirm reprint
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'reprint':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print bulk order
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(2)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'reprint':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }
});

test('Correctly count pending bulk order after confirm barcode line XQC', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => true,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'xqc':
                expect($item['total'])->toBe(19)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print reprint
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_xqc' => 1,
    ]);

    // after confirm reprint
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'xqc':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print bulk order
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(2)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'xqc':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }
});

test('Correctly count pending bulk order after confirm barcode line Express', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => false,
        'is_eps' => true,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'eps':
                expect($item['total'])->toBe(19)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print eps
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_eps' => 1,
    ]);

    // after confirm eps
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'eps':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print bulk order
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(2)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'eps':
                expect($item['total'])->toBe(1)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(0);
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }
});

test('Correctly count pending bulk order after confirm barcode line Styles', function () {
    $this->saleorderOther = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => false,
        'is_eps' => false,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 19,
    ]);

    $this->saleOrderItemOther = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->product->sku,
        ],
        [
            'order_id' => $this->saleorderOther->id,
            'product_style_sku' => $this->productStyle->sku,
            'ink_color_status' => true,
            'quantity' => 10,
            'product_sku' => $this->product->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther->id,
            'order_item_id' => $this->saleOrderItemOther[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 10));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $this->productStyleOther = ProductStyle::factory()->create([
        'type' => '',
        'sku' => 'XX2',
    ]);

    $this->productOther = Product::factory()->create([
        'sku' => 'xx2'
    ]);

    $this->saleorderOther2 = SaleOrder::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'order_status' => SaleOrder::NEW_ORDER,
        'order_type' => SaleOrder::ORDER_TYPE_NORMAL,
        'shipment_id' => true,
        'is_xqc' => false,
        'is_eps' => false,
        'is_fba_order' => false,
        'is_manual' => false,
        'order_quantity' => 18,
    ]);

    $this->saleOrderItemOther2 = SaleOrderItem::factory()->createMany([
        [
            'order_id' => $this->saleorderOther2->id,
            'product_style_sku' => $this->productStyleOther->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->productOther->sku,
        ],
        [
            'order_id' => $this->saleorderOther2->id,
            'product_style_sku' => $this->productStyleOther->sku,
            'ink_color_status' => true,
            'quantity' => 9,
            'product_sku' => $this->productOther->sku,
        ],
    ]);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther2->id,
            'order_item_id' => $this->saleOrderItemOther2[0]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    $mock = array_map(function () {
        return [
            'order_id' => $this->saleorderOther2->id,
            'order_item_id' => $this->saleOrderItemOther2[1]->id,
            'warehouse_id' => $this->warehouse->id,
            'store_id' => $this->store->id,
            'barcode_printed_id' => false,
            'is_deleted' => false,
            'print_method' => PrintMethod::LATEX,
        ];
    }, range(1, 9));
    SaleOrderItemBarcode::factory()->createMany($mock);

    // before confirm
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(37)
                    ->and($item['data'][0]->total)->toBe(19)
                    ->and($item['data'][0]->sku)->toBe('XXX')
                    ->and($item['data'][0]->printed_at)->toBeNull()
                    ->and($item['data'][1]->total)->toBe(18)
                    ->and($item['data'][1]->sku)->toBe('XX2')
                    ->and($item['data'][1]->printed_at)->toBeNull();
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print Styles
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'style_sku' => 'XXX',
    ]);

    // after confirm Styles
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(20)
                    ->and($item['printed_at'])->toBeNull();
                break;

            case 'styles':
                expect($item['total'])->toBe(19)
                    ->and($item['data'][0]->total)->toBe(18)
                    ->and($item['data'][0]->sku)->toBe('XX2')
                    ->and($item['data'][0]->printed_at)->toBeNull()
                    ->and($item['data'][1]->total)->toBe(1)
                    ->and($item['data'][1]->printed_at)->toBeTruthy()
                    ->and($item['data'][1]->sku)->toBe('XXX');
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }

    // confirm print bulk order
    resolve(BarcodeLatexRepository::class)->confirm([
        'employee_id' => $this->employee->id,
        'print_method' => PrintMethod::LATEX,
        'limit' => 18,
        'is_bulk_order' => true,
    ]);

    // after confirm bulk order
    $data = resolve(BarcodeLatexRepository::class)->countLatexByOrderType([
        'print_method' => PrintMethod::LATEX,
        'warehouse_id' => $this->warehouse->id,
    ]);

    foreach ($data as $key => $item) {
        switch ($key) {
            case 'bulk_order':
                expect($item['total'])->toBe(2)
                    ->and($item['printed_at'])->toBeTruthy();
                break;

            case 'styles':
                expect($item['total'])->toBe(19)
                    ->and($item['data'][0]->total)->toBe(18)
                    ->and($item['data'][0]->sku)->toBe('XX2')
                    ->and($item['data'][0]->printed_at)->toBeNull()
                    ->and($item['data'][1]->total)->toBe(1)
                    ->and($item['data'][1]->printed_at)->toBeTruthy()
                    ->and($item['data'][1]->sku)->toBe('XXX');
                break;

            default:
                expect($item['total'])->toBe(0)
                    ->and($item['printed_at'])->toBeNull();
                break;
        }
    }
});
