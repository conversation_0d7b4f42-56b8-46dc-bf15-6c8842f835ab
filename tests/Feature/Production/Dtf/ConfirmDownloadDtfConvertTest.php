<?php

use App\Models\Employee;
use App\Models\PdfConverted;
use App\Models\TimeTracking;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/dtf/download';
    $this->employee = Employee::factory()->create(['warehouse_id' => $warehouse->id, 'code' => 1011]);
    $this->timeTracking = TimeTracking::factory()->create([
        'employee_id' => $this->employee->id,
        'start_time' => now(),
        'end_time' => now()
    ]);
});

test('pdf_converted_id is required', function () {
    $param = [
        'required' => [],
        'numeric' => [
            'pdf_converted_id' => 'xxx'
        ]
    ];
    // required
    $response = $this->put($this->endpoint, $param['required']);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'pdf_converted_id' => [
                'The pdf converted id field is required.'
            ],
            'id_time_checking' => [
                'The id time checking field is required.'
            ]

        ]
    ]);
    // numeric
    $response = $this->put($this->endpoint, $param['numeric']);
    $response->assertStatus(422);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'pdf_converted_id' => [
                'The pdf converted id must be a number.'
            ],
            'id_time_checking' => [
                'The id time checking field is required.'
            ]
        ]
    ]);
});

// Not found pdf converted
test('pdf converted is not found', function () {
    $this->barcodePrinted = PdfConverted::create();
    $param = [
        'pdf_converted_id' => 10000,
        'id_time_checking' => $this->timeTracking->id,
    ];

    $response = $this->put($this->endpoint, $param);
    $response->assertStatus(404);

    expect(json_decode($response->getContent(), true))->toMatchArray([
        'code' => 404,
        'success' => false,
        'message' => 'Pdf not found'
    ]);
});

// Success
test('success', function () {
    $this->pdfConverted = PdfConverted::create([
        'download_status' => 1,
        'print_method' => 'DTF',
        'employee_convert_id' => 99999
    ]);
    $param = [
        'pdf_converted_id' => $this->pdfConverted->id,
        'id_time_checking' => $this->timeTracking->id,

    ];

    $response = $this->put($this->endpoint, $param);
    $response->assertStatus(200);
    $this->assertEquals(1, PdfConverted::first()->download_status);
    expect(json_decode($response->getContent(), true))->toHaveKeys([
        'code',
        'success',
        'message',
        'data.id',
        'data.quantity',
        'data.convert_status',
        'data.convert_percent',
        'data.convert_at',
        'data.download_status',
        'data.employee_convert_id',
        'data.warehouse_id',
        'data.download_at',
        'data.type',
        'data.product_id',
        'data.employee_download_id',
        'data.quantity_input',
        'data.user_id',
        'data.print_method',
        'data.options',
        'data.created_at',
        'data.updated_at'
    ]);
});
