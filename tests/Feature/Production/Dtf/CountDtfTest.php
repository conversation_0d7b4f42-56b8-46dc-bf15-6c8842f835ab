<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Employee;
use App\Models\ProductStyle;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use App\Models\BarcodePrinted;
use Illuminate\Support\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    $this->endpoint = 'api/dtf/count';
    $this->employee = Employee::factory()->create(['warehouse_id' => $this->warehouse->id, 'code' => 1011]);

    // data match
    $this->productStyle = ProductStyle::factory([
            'print_method' => 'DTF',
            'sku' => 'XXXX'
        ])
        ->create();

    $this->saleOrder = SaleOrder::factory([
            'order_status' => 'new_order'
        ])->has(SaleOrderItem::factory([
            'product_style_sku' => $this->productStyle->sku,
        ])->has(SaleOrderItemBarcode::factory([
            'warehouse_id' => $this->warehouse->id,
        ]), 'barcodes'), 'items')
        ->create();

    $this->saleOrder->items->map(function ($item) {
        return $item->barcodes->map(function ($barcode) {
            $barcode->order_id = $this->saleOrder->id;
            $barcode->save();
        });
    });

    $this->barcodePrinted = BarcodePrinted::factory([
            'style_sku' => $this->productStyle->sku,
            'created_at' => Carbon::now(),
            'warehouse_id' => $this->warehouse->id,
            'print_method' => $this->productStyle->print_method
        ])
        ->create();

    $this->keySuccess = [
        'code',
        'success',
        'message',
        'data.total',
        'data.data'
    ];

    $this->keySuccessChild = [
        'order_item_id',
        'order_item.id',
        'order_item.last_create_dtf.created_at',
        'order_item.last_create_dtf.style_sku',
        'order_item.product_style.print_method',
        'order_item.product_style.sku',
        'order_item.product_style_sku',
        'order_item.total'
    ];
});

// Chỉ lấy ra những product có print method là DTF
test('no style sku with print method DTF', function () {
    $productStyle = ProductStyle::factory([
            'print_method' => 'UV',
            'sku' => 'YYYY'
        ])
        ->create();

    SaleOrderItem::factory([
            'product_style_sku' => $productStyle->sku,
            'order_id' => $this->saleOrder->id,
        ])->has(SaleOrderItemBarcode::factory([
            'order_id' => $this->saleOrder->id,
            'warehouse_id' => $this->warehouse->id,
        ]), 'barcodes')
        ->create();

    BarcodePrinted::factory([
            'style_sku' => $productStyle->sku,
            'created_at' => Carbon::now(),
            'warehouse_id' => $this->warehouse->id,
            'print_method' => $productStyle->print_method
        ])
        ->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['total'])->toEqual(1);
});

// Barcode thuộc sale order có trạng thái cancel hoặc reject hoặc on hold hoặc draft
test('order status in status inactive', function () {
    foreach (SaleOrder::ARRAY_STATUS_INACTIVE as $status) {
        $saleOrder = SaleOrder::factory([
            'order_status' => $status
        ])->has(SaleOrderItem::factory([
            'product_style_sku' => $this->productStyle->sku,
        ])->has(SaleOrderItemBarcode::factory([
            'warehouse_id' => $this->warehouse->id,
        ]), 'barcodes'), 'items')
        ->create();

        $saleOrder->items->map(function ($item) use ($saleOrder) {
            return $item->barcodes->map(function ($barcode) use ($saleOrder) {
                $barcode->order_id = $saleOrder->id;
                $barcode->save();
            });
        });
    }

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    expect($jsonRes['data']['total'])->toEqual(1);
});

// Barcode thuộc sale order test
test('order is test', function () {
    $saleOrder = SaleOrder::factory([
            'order_status' => 'new_order',
            'is_test' => 1
        ])->has(SaleOrderItem::factory([
            'product_style_sku' => $this->productStyle->sku,
        ])->has(SaleOrderItemBarcode::factory([
            'warehouse_id' => $this->warehouse->id,
        ]), 'barcodes'), 'items')
        ->create();

    $saleOrder->items->map(function ($item) use ($saleOrder) {
        return $item->barcodes->map(function ($barcode) use ($saleOrder) {
            $barcode->order_id = $saleOrder->id;
            $barcode->save();
        });
    });

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    expect($jsonRes['data']['total'])->toEqual(1);
});

// Ảnh chưa được detect màu
test('not detect color yet', function () {
    SaleOrderItem::factory([
            'product_style_sku' => $this->productStyle->sku,
            'order_id' => $this->saleOrder->id,
            'ink_color_status' => 0
        ])->has(SaleOrderItemBarcode::factory([
            'order_id' => $this->saleOrder->id,
            'warehouse_id' => $this->warehouse->id,
        ]), 'barcodes')
        ->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    expect($jsonRes['data']['total'])->toEqual(1);
});

// Barcode đã bị xóa
test('barcode has been deleted', function () {
    SaleOrderItemBarcode::factory([
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrder->items->first()->id,
            'is_deleted' => 1,
            'warehouse_id' => $this->warehouse->id,
        ])
        ->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    expect($jsonRes['data']['total'])->toEqual(1);
});

// Barcode đã được convert
test('barcode has been converted', function () {
    SaleOrderItemBarcode::factory([
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrder->items->first()->id,
            'warehouse_id' => $this->warehouse->id,
            'barcode_printed_id' => $this->barcodePrinted->id
        ])
        ->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    expect($jsonRes['data']['total'])->toEqual(1);
});

// Số lần retry convert lớn hơn hoặc bằng max retry
test('time retry more than max retry', function () {
    SaleOrderItemBarcode::factory([
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrder->items->first()->id,
            'retry_convert' => 6,
            'warehouse_id' => $this->warehouse->id,
        ])
        ->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    expect($jsonRes['data']['total'])->toEqual(1);
});

// Warehouse không trùng với warehouse đang đăng nhập
test('warehouse not match', function () {
    SaleOrderItemBarcode::factory([
            'order_id' => $this->saleOrder->id,
            'order_item_id' => $this->saleOrder->items->first()->id,
            'warehouse_id' => 100000,
        ])
        ->create();

    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    expect($jsonRes['data']['total'])->toEqual(1);
});

// Success
test('success', function () {
    $response = $this->get($this->endpoint);

    $response->assertStatus(200);

    $jsonRes = json_decode($response->getContent(), true);

    expect($jsonRes)->toHaveKeys($this->keySuccess);
    expect($jsonRes['data']['data'])->toBeArray();
    expect($jsonRes['data']['data'][0])->toHaveKeys($this->keySuccessChild);
    expect($jsonRes['data']['total'])->toEqual(1);
});
