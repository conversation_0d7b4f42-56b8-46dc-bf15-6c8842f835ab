<?php
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Employee;
use App\Models\Shipment;
use App\Models\SaleOrder;
use App\Models\Exportation;
use App\Models\ExportationTracking;
use App\Models\ShipmentItemLabel;


uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/shipment-exportation/tracking-number';
    $this->params = [
        'employee_id' => '',
        'tracking_number' => '',
        'exportation_id' => '',
        'number_report' => '',
    ];
});

// employee invalid
test('employee invalid', function () {

    // input not employee_id
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['employee_id'])->toMatchArray(['Employee field is required.']);

    //employee not in db
    $this->params['employee_id'] = 99999;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['employee_id'])->toMatchArray(['You are not in this warehouse or not found!']);

    //employee in warehouse other
    $employee = Employee::factory()->create([
        'warehouse_id' => 9999
    ]);
    $this->params['employee_id'] = $employee->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['employee_id'])->toMatchArray(['You are not in this warehouse or not found!']);

    //employee delete
    $employee->warehouse_id = $this->warehouse->id;
    $employee->is_deleted = true;
    $employee->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['employee_id'])->toMatchArray(['You are not in this warehouse or not found!']);
});

// tracking number in invalid
test('tracking number in invalid', function () {
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => false
    ]);
    $this->params['employee_id'] = $employee->id;

    //tracking number not input
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['tracking_number'])->toMatchArray(['Tracking number field is required.']);

    //tracking number not in db
    $this->params['tracking_number'] = 'henUocTuHuVo';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['tracking_number'])->toMatchArray(['Tracking number not existed']);

    //tracking number in warehouse other
    $shipment = Shipment::factory()->create([
        'warehouse_id' => 9999,
        'tracking_status' => 'unknown'
    ]);
    $this->params['tracking_number'] = $shipment->tracking_number;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['tracking_number'])->toMatchArray(['Tracking number not existed']);

    $shipment->warehouse_id = $this->warehouse->id;
    $shipment->is_deleted = true;
    $shipment->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['tracking_number'])->toMatchArray(['Tracking number not existed']);

    $shipment->is_deleted = false;
    $shipment->refund_status = 'submitted';
    $shipment->save();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['tracking_number'])->toMatchArray(['Tracking number not existed']);

   // tracking number in exportation tracking - has exportation id
    $data = ExportationTracking::factory()->create([
        'tracking_number' => $shipment->tracking_number
    ]);
    $shipment->refund_status = null;
    $shipment->save();
    $exportation = Exportation::factory()->create(['status' => Exportation::SCANNING]);
    $data->exportation_id = $exportation->id;
    $data->save();
    $this->params['exportation_id'] = $data->exportation_id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['tracking_number'])->toMatchArray(['Duplicate Tracking Number.']);
});

// exportation invalid
test('exportation invalid', function () {
    $shipment = Shipment::factory()->create();
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => false
    ]);
    $this->params['employee_id'] = $employee->id;
    $this->params['tracking_number'] = $shipment->tracking_number;
    $this->params['exportation_id'] = 12232;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['exportation_id'])->toMatchArray(['Export tracking id not found.']);
});

// Number report already exist
test('number report already exist', function () {
    $shipment = Shipment::factory()->create();
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => false
    ]);
    Exportation::factory()->create([
        'number_report' => 'chuyenNguoiAnhThuong'
    ]);
    $this->params['employee_id'] = $employee->id;
    $this->params['tracking_number'] = $shipment->tracking_number;
    $this->params['number_report'] = 'chuyenNguoiAnhThuong';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['errors']['number_report'])->toMatchArray(['Number report already exist.']);
});

// Tracking number is not associated with any order in my system
test('tracking number is not associated with any order in my system', function () {
    $shipment = Shipment::factory()->create([
       'warehouse_id' => $this->warehouse->id,
       'tracking_status' => 'unknown'
    ]);
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => false
    ]);
    $this->params['employee_id'] = $employee->id;
    $this->params['tracking_number'] = $shipment->tracking_number;
    $this->params['number_report'] = 'quenDatTen';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['message'])->toMatchArray(['Tracking number is not associated with any order in my system.']);
});

// Order has multiple products, please scan the box barcode
test('Order has multiple products, please scan the box barcode', function () {
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => false
    ]);
   $saleOrder = SaleOrder::factory(['warehouse_id' => $this->warehouse->id, 'order_quantity' => 2])
        ->has(Shipment::factory()->count(2)->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id,
            'tracking_status' => 'Unknown'
        ]), 'shipment')
        ->create();
    $this->params['employee_id'] = $employee->id;
    $this->params['tracking_number'] = $saleOrder->shipment[0]->tracking_number;
    $this->params['number_report'] = 'quenDatTen';

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true)['message'])->toMatchArray(['Order has multiple products, please scan the box barcode.']);
});

// success with not has exportation
test('scan tracking number success with not has exportation', function () {
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => false
    ]);
    $saleOrder = SaleOrder::factory(['warehouse_id' => $this->warehouse->id])
        ->has(Shipment::factory()->count(2)->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id,
            'tracking_status' => 'Unknown'
        ]), 'shipment')
        ->create();
    $this->params['employee_id'] = $employee->id;
    $this->params['tracking_number'] = $saleOrder->shipment[0]->tracking_number;
    $this->params['number_report'] = 'quenDatTen';
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);

    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['message'])->toMatchArray(['Scan tracking success']);
    $this->assertDatabaseHas('exportation', ['number_report' => $this->params['number_report'], 'status' => Exportation::SCANNING]);
    $this->assertDatabaseHas('exportation_tracking', [
        'tracking_number' => $this->params['tracking_number'],
        'shipment_id' => $saleOrder->shipment[0]->id,
        'order_id' => $saleOrder->id,
        'employee_scan_id' => $this->params['employee_id'],
    ]);
});

// success with has exportation
test('scan tracking number success with has exportation', function () {
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => false
    ]);
    $saleOrder = SaleOrder::factory(['warehouse_id' => $this->warehouse->id])
        ->has(Shipment::factory()->count(2)->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id,
            'tracking_status' => 'Unknown'
        ]), 'shipment')
        ->create();
    $exportation = Exportation::factory()->create();
    $this->params['employee_id'] = $employee->id;
    $this->params['tracking_number'] = $saleOrder->shipment[0]->tracking_number;
    $this->params['exportation_id'] = $exportation->id;
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['message'])->toMatchArray(['Scan tracking success']);
    $this->assertDatabaseHas('exportation_tracking', [
        'exportation_id' => $exportation->id,
        'tracking_number' => $this->params['tracking_number'],
        'shipment_id' => $saleOrder->shipment[0]->id,
        'order_id' => $saleOrder->id,
        'employee_scan_id' => $this->params['employee_id'],
    ]);
});

// success with has exportation - tracking number has been scanned
test('scan tracking number success with has exportation - tracking number has been scanned', function () {
    $employee = Employee::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'is_deleted' => false
    ]);

    $shipment = Shipment::factory()->create([
        'warehouse_id' => $this->warehouse->id,
        'tracking_status' => Shipment::DELIVERED
    ]);

    $exportation = Exportation::factory()->create([
        'status' => Exportation::DOWNLOAD
    ]);
    ExportationTracking::factory()->create([
        'exportation_id' => $exportation->id,
        'tracking_number' => $shipment->tracking_number,
        'shipment_id' => $shipment->id,
        'order_id' => $shipment->order_id,
        'employee_scan_id' => $employee->id,
    ]);

    $saleOrder = SaleOrder::factory(['warehouse_id' => $this->warehouse->id])
        ->has(Shipment::factory()->count(2)->state(fn($attribute, $saleOrder) => [
            'warehouse_id' => $saleOrder->warehouse_id,
            'tracking_status' => 'Unknown'
        ]), 'shipment')
        ->create();
    $exportation = Exportation::factory()->create();
    $this->params['employee_id'] = $employee->id;
    $this->params['tracking_number'] = $saleOrder->shipment[0]->tracking_number;
    $this->params['exportation_id'] = $exportation->id;

    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->post($this->endpoint, $this->params);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['message'])->toMatchArray(['Scan tracking success']);
    $this->assertDatabaseHas('exportation_tracking', [
        'exportation_id' => $exportation->id,
        'tracking_number' => $this->params['tracking_number'],
        'shipment_id' => $saleOrder->shipment[0]->id,
        'order_id' => $saleOrder->id,
        'employee_scan_id' => $this->params['employee_id'],
    ]);
});

//// success with not has exportation
//test('scan tracking number invalid - has label id not map with part number', function () {
//    $employee = Employee::factory()->create([
//        'warehouse_id' => $this->warehouse->id,
//        'is_deleted' => false
//    ]);
//    $saleOrder = SaleOrder::factory(['warehouse_id' => $this->warehouse->id])
//        ->has(Shipment::factory()->count(2)->state(fn($attribute, $saleOrder) => [
//            'warehouse_id' => $saleOrder->warehouse_id,
//            'tracking_status' => 'Unknown'
//        ]), 'shipment')
//        ->create();
//
//    $shipmentItemLabel = ShipmentItemLabel::factory()->create([
//        'label_id' => '123456789',
//        'shipment_id' => $saleOrder->shipment[0]->id,
//        'part_number_id' => null,
//    ]);
//    $this->params['employee_id'] = $employee->id;
//    $this->params['tracking_number'] = $saleOrder->shipment[0]->tracking_number;
//    $this->params['number_report'] = 'quenDatTen';
//    $response = $this->withHeaders([
//        'Authorization' => 'Bearer ' . $this->accessToken,
//        'Accept' => 'application/json',
//    ])->post($this->endpoint, $this->params);
//    $response->assertStatus(422);
//    expect(json_decode($response->getContent(), true)['message'])->toMatchArray(['Label ' . $shipmentItemLabel->label_id . ' not associated with any part number in my system.']);
//});
