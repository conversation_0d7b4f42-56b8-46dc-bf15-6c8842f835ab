<?php
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\ExportationTracking;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->accessToken = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/shipment-exportation/tracking-number/';
});

// delete tracking number error
test('delete tracking number error', function () {
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->delete($this->endpoint . 9999);
    expect(json_decode($response->getContent(), true)['message'])->toMatchArray(['Delete fail']);
});

// delete tracking number success
test('Delete tracking success!', function () {
    ExportationTracking::factory()->count(5)->create();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->accessToken,
        'Accept' => 'application/json',
    ])->delete($this->endpoint . 1);
    $response->assertStatus(200);
    expect(json_decode($response->getContent(), true)['message'])->toMatchArray(['Delete success']);
    $this->assertDatabaseMissing('exportation_tracking', ['id' => 1]);
    $this->assertDatabaseCount('exportation_tracking', 4);
});
