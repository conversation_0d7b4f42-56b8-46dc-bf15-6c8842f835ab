<?php

use App\Models\UserAccessLog;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->params = [
        'ip_address' => '***********',
        'country' => 'us',
        'user_id' => 12,
        'source' => 'spa'
    ];
});

test('create log user access', function () {
    UserAccessLog::logUserAccess($this->params['ip_address'], $this->params['country'], $this->params['user_id'], $this->params['source']);
    $this->assertDatabaseHas('user_access_logs', [
        'ip_address' => $this->params['ip_address'],
        'country' => strtoupper($this->params['country']),
        'user_id' => $this->params['user_id'],
        'source' => $this->params['source']
    ]);
});

test('update log user access', function () {
    $last_active_at = now()::yesterday()->toDateTimeString();
    $dataUserAccessLog = UserAccessLog::factory()->create([
        'ip_address' => $this->params['ip_address'],
        'country' => strtoupper($this->params['country']),
        'user_id' => $this->params['user_id'],
        'source' => $this->params['source'],
        'last_active_at' => $last_active_at,
        'created_at' => now(),
    ]);
    UserAccessLog::logUserAccess($this->params['ip_address'], $this->params['country'], $this->params['user_id'], $this->params['source']);
    $dataUserAccessLogUpdate = UserAccessLog::where('ip_address', $this->params['ip_address'])
        ->where('country', $this->params['country'])
        ->where('user_id', $this->params['user_id'])
        ->where('source', $this->params['source'])
        ->first();
    expect($dataUserAccessLog->last_active_at)->not()->toBe($dataUserAccessLogUpdate->last_active_at);
});
