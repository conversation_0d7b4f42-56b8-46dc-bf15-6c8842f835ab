<?php

use App\Models\ChangeLog;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    ['accessToken' => $accessToken, 'warehouse' => $warehouse] = createAccessToken();
    $this->access_token = $accessToken;
    $this->warehouse = $warehouse;
    $this->endpoint = '/api/change-log?';
    $this->withHeaders([
        'Authorization' => 'Bearer ' . $this->access_token,
        'Accept' => 'application/json',
    ]);
    ChangeLog::factory()->create([
        'data' => '12121',
        'source' => 'spa'
    ]);
    $this->params = [
        'source' => 'spa',
    ];
});

// miss name param
test('create change log - missing param', function () {
    unset($this->params['source']);
    $this->endpoint = $this->endpoint . http_build_query($this->params);
    $response = $this->get($this->endpoint);
    $response->assertStatus(422);
    expect(json_decode($response->getContent(), true))->toMatchArray([
        'message' => 'The given data was invalid.',
        'errors' => [
            'source' => [
                'The source field is required.'
            ]
        ]
    ]);
});

test('create change log - success', function () {
    $this->endpoint = $this->endpoint . http_build_query($this->params);
    $response = $this->get($this->endpoint);
    $response->assertStatus(200);
});
