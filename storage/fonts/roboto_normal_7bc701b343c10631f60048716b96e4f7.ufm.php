<?php return array (
  'codeToName' => 
  array (
    0 => 'uni0000',
    2 => 'uni0002',
    13 => 'uni000d',
    32 => 'uni0020',
    33 => 'uni0021',
    34 => 'uni0022',
    35 => 'uni0023',
    36 => 'uni0024',
    37 => 'uni0025',
    38 => 'uni0026',
    39 => 'uni0027',
    40 => 'uni0028',
    41 => 'uni0029',
    42 => 'uni002a',
    43 => 'uni002b',
    44 => 'uni002c',
    45 => 'uni002d',
    46 => 'uni002e',
    47 => 'uni002f',
    48 => 'uni0030',
    49 => 'uni0031',
    50 => 'uni0032',
    51 => 'uni0033',
    52 => 'uni0034',
    53 => 'uni0035',
    54 => 'uni0036',
    55 => 'uni0037',
    56 => 'uni0038',
    57 => 'uni0039',
    58 => 'uni003a',
    59 => 'uni003b',
    60 => 'uni003c',
    61 => 'uni003d',
    62 => 'uni003e',
    63 => 'uni003f',
    64 => 'uni0040',
    65 => 'uni0041',
    66 => 'uni0042',
    67 => 'uni0043',
    68 => 'uni0044',
    69 => 'uni0045',
    70 => 'uni0046',
    71 => 'uni0047',
    72 => 'uni0048',
    73 => 'uni0049',
    74 => 'uni004a',
    75 => 'uni004b',
    76 => 'uni004c',
    77 => 'uni004d',
    78 => 'uni004e',
    79 => 'uni004f',
    80 => 'uni0050',
    81 => 'uni0051',
    82 => 'uni0052',
    83 => 'uni0053',
    84 => 'uni0054',
    85 => 'uni0055',
    86 => 'uni0056',
    87 => 'uni0057',
    88 => 'uni0058',
    89 => 'uni0059',
    90 => 'uni005a',
    91 => 'uni005b',
    92 => 'uni005c',
    93 => 'uni005d',
    94 => 'uni005e',
    95 => 'uni005f',
    96 => 'uni0060',
    97 => 'uni0061',
    98 => 'uni0062',
    99 => 'uni0063',
    100 => 'uni0064',
    101 => 'uni0065',
    102 => 'uni0066',
    103 => 'uni0067',
    104 => 'uni0068',
    105 => 'uni0069',
    106 => 'uni006a',
    107 => 'uni006b',
    108 => 'uni006c',
    109 => 'uni006d',
    110 => 'uni006e',
    111 => 'uni006f',
    112 => 'uni0070',
    113 => 'uni0071',
    114 => 'uni0072',
    115 => 'uni0073',
    116 => 'uni0074',
    117 => 'uni0075',
    118 => 'uni0076',
    119 => 'uni0077',
    120 => 'uni0078',
    121 => 'uni0079',
    122 => 'uni007a',
    123 => 'uni007b',
    124 => 'uni007c',
    125 => 'uni007d',
    126 => 'uni007e',
    160 => 'uni00a0',
    161 => 'uni00a1',
    162 => 'uni00a2',
    163 => 'uni00a3',
    164 => 'uni00a4',
    165 => 'uni00a5',
    166 => 'uni00a6',
    167 => 'uni00a7',
    168 => 'uni00a8',
    169 => 'uni00a9',
    170 => 'uni00aa',
    171 => 'uni00ab',
    172 => 'uni00ac',
    173 => 'uni00ad',
    174 => 'uni00ae',
    175 => 'uni00af',
    176 => 'uni00b0',
    177 => 'uni00b1',
    178 => 'uni00b2',
    179 => 'uni00b3',
    180 => 'uni00b4',
    181 => 'uni00b5',
    182 => 'uni00b6',
    183 => 'uni00b7',
    184 => 'uni00b8',
    185 => 'uni00b9',
    186 => 'uni00ba',
    187 => 'uni00bb',
    188 => 'uni00bc',
    189 => 'uni00bd',
    190 => 'uni00be',
    191 => 'uni00bf',
    192 => 'uni00c0',
    193 => 'uni00c1',
    194 => 'uni00c2',
    195 => 'uni00c3',
    196 => 'uni00c4',
    197 => 'uni00c5',
    198 => 'uni00c6',
    199 => 'uni00c7',
    200 => 'uni00c8',
    201 => 'uni00c9',
    202 => 'uni00ca',
    203 => 'uni00cb',
    204 => 'uni00cc',
    205 => 'uni00cd',
    206 => 'uni00ce',
    207 => 'uni00cf',
    208 => 'uni00d0',
    209 => 'uni00d1',
    210 => 'uni00d2',
    211 => 'uni00d3',
    212 => 'uni00d4',
    213 => 'uni00d5',
    214 => 'uni00d6',
    215 => 'uni00d7',
    216 => 'uni00d8',
    217 => 'uni00d9',
    218 => 'uni00da',
    219 => 'uni00db',
    220 => 'uni00dc',
    221 => 'uni00dd',
    222 => 'uni00de',
    223 => 'uni00df',
    224 => 'uni00e0',
    225 => 'uni00e1',
    226 => 'uni00e2',
    227 => 'uni00e3',
    228 => 'uni00e4',
    229 => 'uni00e5',
    230 => 'uni00e6',
    231 => 'uni00e7',
    232 => 'uni00e8',
    233 => 'uni00e9',
    234 => 'uni00ea',
    235 => 'uni00eb',
    236 => 'uni00ec',
    237 => 'uni00ed',
    238 => 'uni00ee',
    239 => 'uni00ef',
    240 => 'uni00f0',
    241 => 'uni00f1',
    242 => 'uni00f2',
    243 => 'uni00f3',
    244 => 'uni00f4',
    245 => 'uni00f5',
    246 => 'uni00f6',
    247 => 'uni00f7',
    248 => 'uni00f8',
    249 => 'uni00f9',
    250 => 'uni00fa',
    251 => 'uni00fb',
    252 => 'uni00fc',
    253 => 'uni00fd',
    254 => 'uni00fe',
    255 => 'uni00ff',
    256 => 'uni0100',
    257 => 'uni0101',
    258 => 'uni0102',
    259 => 'uni0103',
    260 => 'uni0104',
    261 => 'uni0105',
    262 => 'uni0106',
    263 => 'uni0107',
    264 => 'uni0108',
    265 => 'uni0109',
    266 => 'uni010a',
    267 => 'uni010b',
    268 => 'uni010c',
    269 => 'uni010d',
    270 => 'uni010e',
    271 => 'uni010f',
    272 => 'uni0110',
    273 => 'uni0111',
    274 => 'uni0112',
    275 => 'uni0113',
    276 => 'uni0114',
    277 => 'uni0115',
    278 => 'uni0116',
    279 => 'uni0117',
    280 => 'uni0118',
    281 => 'uni0119',
    282 => 'uni011a',
    283 => 'uni011b',
    284 => 'uni011c',
    285 => 'uni011d',
    286 => 'uni011e',
    287 => 'uni011f',
    288 => 'uni0120',
    289 => 'uni0121',
    290 => 'uni0122',
    291 => 'uni0123',
    292 => 'uni0124',
    293 => 'uni0125',
    294 => 'uni0126',
    295 => 'uni0127',
    296 => 'uni0128',
    297 => 'uni0129',
    298 => 'uni012a',
    299 => 'uni012b',
    300 => 'uni012c',
    301 => 'uni012d',
    302 => 'uni012e',
    303 => 'uni012f',
    304 => 'uni0130',
    305 => 'uni0131',
    306 => 'uni0132',
    307 => 'uni0133',
    308 => 'uni0134',
    309 => 'uni0135',
    310 => 'uni0136',
    311 => 'uni0137',
    312 => 'uni0138',
    313 => 'uni0139',
    314 => 'uni013a',
    315 => 'uni013b',
    316 => 'uni013c',
    317 => 'uni013d',
    318 => 'uni013e',
    319 => 'uni013f',
    320 => 'uni0140',
    321 => 'uni0141',
    322 => 'uni0142',
    323 => 'uni0143',
    324 => 'uni0144',
    325 => 'uni0145',
    326 => 'uni0146',
    327 => 'uni0147',
    328 => 'uni0148',
    329 => 'uni0149',
    330 => 'uni014a',
    331 => 'uni014b',
    332 => 'uni014c',
    333 => 'uni014d',
    334 => 'uni014e',
    335 => 'uni014f',
    336 => 'uni0150',
    337 => 'uni0151',
    338 => 'uni0152',
    339 => 'uni0153',
    340 => 'uni0154',
    341 => 'uni0155',
    342 => 'uni0156',
    343 => 'uni0157',
    344 => 'uni0158',
    345 => 'uni0159',
    346 => 'uni015a',
    347 => 'uni015b',
    348 => 'uni015c',
    349 => 'uni015d',
    350 => 'uni015e',
    351 => 'uni015f',
    352 => 'uni0160',
    353 => 'uni0161',
    354 => 'uni0162',
    355 => 'uni0163',
    356 => 'uni0164',
    357 => 'uni0165',
    358 => 'uni0166',
    359 => 'uni0167',
    360 => 'uni0168',
    361 => 'uni0169',
    362 => 'uni016a',
    363 => 'uni016b',
    364 => 'uni016c',
    365 => 'uni016d',
    366 => 'uni016e',
    367 => 'uni016f',
    368 => 'uni0170',
    369 => 'uni0171',
    370 => 'uni0172',
    371 => 'uni0173',
    372 => 'uni0174',
    373 => 'uni0175',
    374 => 'uni0176',
    375 => 'uni0177',
    376 => 'uni0178',
    377 => 'uni0179',
    378 => 'uni017a',
    379 => 'uni017b',
    380 => 'uni017c',
    381 => 'uni017d',
    382 => 'uni017e',
    383 => 'uni017f',
    399 => 'uni018f',
    402 => 'uni0192',
    416 => 'uni01a0',
    417 => 'uni01a1',
    431 => 'uni01af',
    432 => 'uni01b0',
    496 => 'uni01f0',
    506 => 'uni01fa',
    507 => 'uni01fb',
    508 => 'uni01fc',
    509 => 'uni01fd',
    510 => 'uni01fe',
    511 => 'uni01ff',
    536 => 'uni0218',
    537 => 'uni0219',
    538 => 'uni021a',
    539 => 'uni021b',
    567 => 'uni0237',
    601 => 'uni0259',
    700 => 'uni02bc',
    710 => 'uni02c6',
    711 => 'uni02c7',
    713 => 'uni02c9',
    728 => 'uni02d8',
    729 => 'uni02d9',
    730 => 'uni02da',
    731 => 'uni02db',
    732 => 'uni02dc',
    733 => 'uni02dd',
    755 => 'uni02f3',
    768 => 'uni0300',
    769 => 'uni0301',
    771 => 'uni0303',
    777 => 'uni0309',
    783 => 'uni030f',
    803 => 'uni0323',
    900 => 'uni0384',
    901 => 'uni0385',
    902 => 'uni0386',
    903 => 'uni0387',
    904 => 'uni0388',
    905 => 'uni0389',
    906 => 'uni038a',
    908 => 'uni038c',
    910 => 'uni038e',
    911 => 'uni038f',
    912 => 'uni0390',
    913 => 'uni0391',
    914 => 'uni0392',
    915 => 'uni0393',
    916 => 'uni0394',
    917 => 'uni0395',
    918 => 'uni0396',
    919 => 'uni0397',
    920 => 'uni0398',
    921 => 'uni0399',
    922 => 'uni039a',
    923 => 'uni039b',
    924 => 'uni039c',
    925 => 'uni039d',
    926 => 'uni039e',
    927 => 'uni039f',
    928 => 'uni03a0',
    929 => 'uni03a1',
    931 => 'uni03a3',
    932 => 'uni03a4',
    933 => 'uni03a5',
    934 => 'uni03a6',
    935 => 'uni03a7',
    936 => 'uni03a8',
    937 => 'uni03a9',
    938 => 'uni03aa',
    939 => 'uni03ab',
    940 => 'uni03ac',
    941 => 'uni03ad',
    942 => 'uni03ae',
    943 => 'uni03af',
    944 => 'uni03b0',
    945 => 'uni03b1',
    946 => 'uni03b2',
    947 => 'uni03b3',
    948 => 'uni03b4',
    949 => 'uni03b5',
    950 => 'uni03b6',
    951 => 'uni03b7',
    952 => 'uni03b8',
    953 => 'uni03b9',
    954 => 'uni03ba',
    955 => 'uni03bb',
    956 => 'uni03bc',
    957 => 'uni03bd',
    958 => 'uni03be',
    959 => 'uni03bf',
    960 => 'uni03c0',
    961 => 'uni03c1',
    962 => 'uni03c2',
    963 => 'uni03c3',
    964 => 'uni03c4',
    965 => 'uni03c5',
    966 => 'uni03c6',
    967 => 'uni03c7',
    968 => 'uni03c8',
    969 => 'uni03c9',
    970 => 'uni03ca',
    971 => 'uni03cb',
    972 => 'uni03cc',
    973 => 'uni03cd',
    974 => 'uni03ce',
    977 => 'uni03d1',
    978 => 'uni03d2',
    982 => 'uni03d6',
    1024 => 'uni0400',
    1025 => 'uni0401',
    1026 => 'uni0402',
    1027 => 'uni0403',
    1028 => 'uni0404',
    1029 => 'uni0405',
    1030 => 'uni0406',
    1031 => 'uni0407',
    1032 => 'uni0408',
    1033 => 'uni0409',
    1034 => 'uni040a',
    1035 => 'uni040b',
    1036 => 'uni040c',
    1037 => 'uni040d',
    1038 => 'uni040e',
    1039 => 'uni040f',
    1040 => 'uni0410',
    1041 => 'uni0411',
    1042 => 'uni0412',
    1043 => 'uni0413',
    1044 => 'uni0414',
    1045 => 'uni0415',
    1046 => 'uni0416',
    1047 => 'uni0417',
    1048 => 'uni0418',
    1049 => 'uni0419',
    1050 => 'uni041a',
    1051 => 'uni041b',
    1052 => 'uni041c',
    1053 => 'uni041d',
    1054 => 'uni041e',
    1055 => 'uni041f',
    1056 => 'uni0420',
    1057 => 'uni0421',
    1058 => 'uni0422',
    1059 => 'uni0423',
    1060 => 'uni0424',
    1061 => 'uni0425',
    1062 => 'uni0426',
    1063 => 'uni0427',
    1064 => 'uni0428',
    1065 => 'uni0429',
    1066 => 'uni042a',
    1067 => 'uni042b',
    1068 => 'uni042c',
    1069 => 'uni042d',
    1070 => 'uni042e',
    1071 => 'uni042f',
    1072 => 'uni0430',
    1073 => 'uni0431',
    1074 => 'uni0432',
    1075 => 'uni0433',
    1076 => 'uni0434',
    1077 => 'uni0435',
    1078 => 'uni0436',
    1079 => 'uni0437',
    1080 => 'uni0438',
    1081 => 'uni0439',
    1082 => 'uni043a',
    1083 => 'uni043b',
    1084 => 'uni043c',
    1085 => 'uni043d',
    1086 => 'uni043e',
    1087 => 'uni043f',
    1088 => 'uni0440',
    1089 => 'uni0441',
    1090 => 'uni0442',
    1091 => 'uni0443',
    1092 => 'uni0444',
    1093 => 'uni0445',
    1094 => 'uni0446',
    1095 => 'uni0447',
    1096 => 'uni0448',
    1097 => 'uni0449',
    1098 => 'uni044a',
    1099 => 'uni044b',
    1100 => 'uni044c',
    1101 => 'uni044d',
    1102 => 'uni044e',
    1103 => 'uni044f',
    1104 => 'uni0450',
    1105 => 'uni0451',
    1106 => 'uni0452',
    1107 => 'uni0453',
    1108 => 'uni0454',
    1109 => 'uni0455',
    1110 => 'uni0456',
    1111 => 'uni0457',
    1112 => 'uni0458',
    1113 => 'uni0459',
    1114 => 'uni045a',
    1115 => 'uni045b',
    1116 => 'uni045c',
    1117 => 'uni045d',
    1118 => 'uni045e',
    1119 => 'uni045f',
    1120 => 'uni0460',
    1121 => 'uni0461',
    1122 => 'uni0462',
    1123 => 'uni0463',
    1124 => 'uni0464',
    1125 => 'uni0465',
    1126 => 'uni0466',
    1127 => 'uni0467',
    1128 => 'uni0468',
    1129 => 'uni0469',
    1130 => 'uni046a',
    1131 => 'uni046b',
    1132 => 'uni046c',
    1133 => 'uni046d',
    1134 => 'uni046e',
    1135 => 'uni046f',
    1136 => 'uni0470',
    1137 => 'uni0471',
    1138 => 'uni0472',
    1139 => 'uni0473',
    1140 => 'uni0474',
    1141 => 'uni0475',
    1142 => 'uni0476',
    1143 => 'uni0477',
    1144 => 'uni0478',
    1145 => 'uni0479',
    1146 => 'uni047a',
    1147 => 'uni047b',
    1148 => 'uni047c',
    1149 => 'uni047d',
    1150 => 'uni047e',
    1151 => 'uni047f',
    1152 => 'uni0480',
    1153 => 'uni0481',
    1154 => 'uni0482',
    1155 => 'uni0483',
    1156 => 'uni0484',
    1157 => 'uni0485',
    1158 => 'uni0486',
    1160 => 'uni0488',
    1161 => 'uni0489',
    1162 => 'uni048a',
    1163 => 'uni048b',
    1164 => 'uni048c',
    1165 => 'uni048d',
    1166 => 'uni048e',
    1167 => 'uni048f',
    1168 => 'uni0490',
    1169 => 'uni0491',
    1170 => 'uni0492',
    1171 => 'uni0493',
    1172 => 'uni0494',
    1173 => 'uni0495',
    1174 => 'uni0496',
    1175 => 'uni0497',
    1176 => 'uni0498',
    1177 => 'uni0499',
    1178 => 'uni049a',
    1179 => 'uni049b',
    1180 => 'uni049c',
    1181 => 'uni049d',
    1182 => 'uni049e',
    1183 => 'uni049f',
    1184 => 'uni04a0',
    1185 => 'uni04a1',
    1186 => 'uni04a2',
    1187 => 'uni04a3',
    1188 => 'uni04a4',
    1189 => 'uni04a5',
    1190 => 'uni04a6',
    1191 => 'uni04a7',
    1192 => 'uni04a8',
    1193 => 'uni04a9',
    1194 => 'uni04aa',
    1195 => 'uni04ab',
    1196 => 'uni04ac',
    1197 => 'uni04ad',
    1198 => 'uni04ae',
    1199 => 'uni04af',
    1200 => 'uni04b0',
    1201 => 'uni04b1',
    1202 => 'uni04b2',
    1203 => 'uni04b3',
    1204 => 'uni04b4',
    1205 => 'uni04b5',
    1206 => 'uni04b6',
    1207 => 'uni04b7',
    1208 => 'uni04b8',
    1209 => 'uni04b9',
    1210 => 'uni04ba',
    1211 => 'uni04bb',
    1212 => 'uni04bc',
    1213 => 'uni04bd',
    1214 => 'uni04be',
    1215 => 'uni04bf',
    1216 => 'uni04c0',
    1217 => 'uni04c1',
    1218 => 'uni04c2',
    1219 => 'uni04c3',
    1220 => 'uni04c4',
    1221 => 'uni04c5',
    1222 => 'uni04c6',
    1223 => 'uni04c7',
    1224 => 'uni04c8',
    1225 => 'uni04c9',
    1226 => 'uni04ca',
    1227 => 'uni04cb',
    1228 => 'uni04cc',
    1229 => 'uni04cd',
    1230 => 'uni04ce',
    1231 => 'uni04cf',
    1232 => 'uni04d0',
    1233 => 'uni04d1',
    1234 => 'uni04d2',
    1235 => 'uni04d3',
    1236 => 'uni04d4',
    1237 => 'uni04d5',
    1238 => 'uni04d6',
    1239 => 'uni04d7',
    1240 => 'uni04d8',
    1241 => 'uni04d9',
    1242 => 'uni04da',
    1243 => 'uni04db',
    1244 => 'uni04dc',
    1245 => 'uni04dd',
    1246 => 'uni04de',
    1247 => 'uni04df',
    1248 => 'uni04e0',
    1249 => 'uni04e1',
    1250 => 'uni04e2',
    1251 => 'uni04e3',
    1252 => 'uni04e4',
    1253 => 'uni04e5',
    1254 => 'uni04e6',
    1255 => 'uni04e7',
    1256 => 'uni04e8',
    1257 => 'uni04e9',
    1258 => 'uni04ea',
    1259 => 'uni04eb',
    1260 => 'uni04ec',
    1261 => 'uni04ed',
    1262 => 'uni04ee',
    1263 => 'uni04ef',
    1264 => 'uni04f0',
    1265 => 'uni04f1',
    1266 => 'uni04f2',
    1267 => 'uni04f3',
    1268 => 'uni04f4',
    1269 => 'uni04f5',
    1270 => 'uni04f6',
    1271 => 'uni04f7',
    1272 => 'uni04f8',
    1273 => 'uni04f9',
    1274 => 'uni04fa',
    1275 => 'uni04fb',
    1276 => 'uni04fc',
    1277 => 'uni04fd',
    1278 => 'uni04fe',
    1279 => 'uni04ff',
    1280 => 'uni0500',
    1281 => 'uni0501',
    1282 => 'uni0502',
    1283 => 'uni0503',
    1284 => 'uni0504',
    1285 => 'uni0505',
    1286 => 'uni0506',
    1287 => 'uni0507',
    1288 => 'uni0508',
    1289 => 'uni0509',
    1290 => 'uni050a',
    1291 => 'uni050b',
    1292 => 'uni050c',
    1293 => 'uni050d',
    1294 => 'uni050e',
    1295 => 'uni050f',
    1296 => 'uni0510',
    1297 => 'uni0511',
    1298 => 'uni0512',
    1299 => 'uni0513',
    7680 => 'uni1e00',
    7681 => 'uni1e01',
    7742 => 'uni1e3e',
    7743 => 'uni1e3f',
    7808 => 'uni1e80',
    7809 => 'uni1e81',
    7810 => 'uni1e82',
    7811 => 'uni1e83',
    7812 => 'uni1e84',
    7813 => 'uni1e85',
    7840 => 'uni1ea0',
    7841 => 'uni1ea1',
    7842 => 'uni1ea2',
    7843 => 'uni1ea3',
    7844 => 'uni1ea4',
    7845 => 'uni1ea5',
    7846 => 'uni1ea6',
    7847 => 'uni1ea7',
    7848 => 'uni1ea8',
    7849 => 'uni1ea9',
    7850 => 'uni1eaa',
    7851 => 'uni1eab',
    7852 => 'uni1eac',
    7853 => 'uni1ead',
    7854 => 'uni1eae',
    7855 => 'uni1eaf',
    7856 => 'uni1eb0',
    7857 => 'uni1eb1',
    7858 => 'uni1eb2',
    7859 => 'uni1eb3',
    7860 => 'uni1eb4',
    7861 => 'uni1eb5',
    7862 => 'uni1eb6',
    7863 => 'uni1eb7',
    7864 => 'uni1eb8',
    7865 => 'uni1eb9',
    7866 => 'uni1eba',
    7867 => 'uni1ebb',
    7868 => 'uni1ebc',
    7869 => 'uni1ebd',
    7870 => 'uni1ebe',
    7871 => 'uni1ebf',
    7872 => 'uni1ec0',
    7873 => 'uni1ec1',
    7874 => 'uni1ec2',
    7875 => 'uni1ec3',
    7876 => 'uni1ec4',
    7877 => 'uni1ec5',
    7878 => 'uni1ec6',
    7879 => 'uni1ec7',
    7880 => 'uni1ec8',
    7881 => 'uni1ec9',
    7882 => 'uni1eca',
    7883 => 'uni1ecb',
    7884 => 'uni1ecc',
    7885 => 'uni1ecd',
    7886 => 'uni1ece',
    7887 => 'uni1ecf',
    7888 => 'uni1ed0',
    7889 => 'uni1ed1',
    7890 => 'uni1ed2',
    7891 => 'uni1ed3',
    7892 => 'uni1ed4',
    7893 => 'uni1ed5',
    7894 => 'uni1ed6',
    7895 => 'uni1ed7',
    7896 => 'uni1ed8',
    7897 => 'uni1ed9',
    7898 => 'uni1eda',
    7899 => 'uni1edb',
    7900 => 'uni1edc',
    7901 => 'uni1edd',
    7902 => 'uni1ede',
    7903 => 'uni1edf',
    7904 => 'uni1ee0',
    7905 => 'uni1ee1',
    7906 => 'uni1ee2',
    7907 => 'uni1ee3',
    7908 => 'uni1ee4',
    7909 => 'uni1ee5',
    7910 => 'uni1ee6',
    7911 => 'uni1ee7',
    7912 => 'uni1ee8',
    7913 => 'uni1ee9',
    7914 => 'uni1eea',
    7915 => 'uni1eeb',
    7916 => 'uni1eec',
    7917 => 'uni1eed',
    7918 => 'uni1eee',
    7919 => 'uni1eef',
    7920 => 'uni1ef0',
    7921 => 'uni1ef1',
    7922 => 'uni1ef2',
    7923 => 'uni1ef3',
    7924 => 'uni1ef4',
    7925 => 'uni1ef5',
    7926 => 'uni1ef6',
    7927 => 'uni1ef7',
    7928 => 'uni1ef8',
    7929 => 'uni1ef9',
    8013 => 'uni1f4d',
    8192 => 'uni2000',
    8193 => 'uni2001',
    8194 => 'uni2002',
    8195 => 'uni2003',
    8196 => 'uni2004',
    8197 => 'uni2005',
    8198 => 'uni2006',
    8199 => 'uni2007',
    8200 => 'uni2008',
    8201 => 'uni2009',
    8202 => 'uni200a',
    8203 => 'uni200b',
    8208 => 'uni2010',
    8209 => 'uni2011',
    8211 => 'uni2013',
    8212 => 'uni2014',
    8213 => 'uni2015',
    8215 => 'uni2017',
    8216 => 'uni2018',
    8217 => 'uni2019',
    8218 => 'uni201a',
    8219 => 'uni201b',
    8220 => 'uni201c',
    8221 => 'uni201d',
    8222 => 'uni201e',
    8224 => 'uni2020',
    8225 => 'uni2021',
    8226 => 'uni2022',
    8229 => 'uni2025',
    8230 => 'uni2026',
    8231 => 'uni2027',
    8240 => 'uni2030',
    8242 => 'uni2032',
    8243 => 'uni2033',
    8249 => 'uni2039',
    8250 => 'uni203a',
    8252 => 'uni203c',
    8260 => 'uni2044',
    8308 => 'uni2074',
    8319 => 'uni207f',
    8355 => 'uni20a3',
    8356 => 'uni20a4',
    8358 => 'uni20a6',
    8359 => 'uni20a7',
    8360 => 'uni20a8',
    8361 => 'uni20a9',
    8362 => 'uni20aa',
    8363 => 'uni20ab',
    8364 => 'uni20ac',
    8369 => 'uni20b1',
    8377 => 'uni20b9',
    8378 => 'uni20ba',
    8380 => 'uni20bc',
    8381 => 'uni20bd',
    8453 => 'uni2105',
    8467 => 'uni2113',
    8470 => 'uni2116',
    8482 => 'uni2122',
    8486 => 'uni2126',
    8494 => 'uni212e',
    8539 => 'uni215b',
    8540 => 'uni215c',
    8541 => 'uni215d',
    8542 => 'uni215e',
    8706 => 'uni2202',
    8710 => 'uni2206',
    8719 => 'uni220f',
    8721 => 'uni2211',
    8722 => 'uni2212',
    8730 => 'uni221a',
    8734 => 'uni221e',
    8747 => 'uni222b',
    8776 => 'uni2248',
    8800 => 'uni2260',
    8804 => 'uni2264',
    8805 => 'uni2265',
    9674 => 'uni25ca',
    60929 => 'uniee01',
    60930 => 'uniee02',
    63171 => 'unif6c3',
    64257 => 'unifb01',
    64258 => 'unifb02',
    64259 => 'unifb03',
    64260 => 'unifb04',
    65279 => 'unifeff',
    65532 => 'unifffc',
    65533 => 'unifffd',
  ),
  'isUnicode' => true,
  'EncodingScheme' => 'FontSpecific',
  'FontName' => '刀漀戀漀琀漀',
  'FullName' => '刀漀戀漀琀漀',
  'Version' => '嘀攀爀猀椀漀渀 ㈀⸀㄀㌀㜀㬀 ㈀　㄀㜀',
  'PostScriptName' => '刀漀戀漀琀漀ⴀ刀攀最甀氀愀爀',
  'Weight' => 'Medium',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'UnderlineThickness' => '49',
  'UnderlinePosition' => '-73',
  'FontHeightOffset' => '0',
  'Ascender' => '928',
  'Descender' => '-244',
  'FontBBox' => 
  array (
    0 => '-737',
    1 => '-271',
    2 => '1148',
    3 => '1056',
  ),
  'StartCharMetrics' => '1004',
  'C' => 
  array (
    0 => 0.0,
    2 => 0.0,
    13 => 248.0,
    32 => 248.0,
    33 => 257.0,
    34 => 320.0,
    35 => 616.0,
    36 => 562.0,
    37 => 732.0,
    38 => 622.0,
    39 => 174.0,
    40 => 342.0,
    41 => 348.0,
    42 => 431.0,
    43 => 567.0,
    44 => 196.0,
    45 => 276.0,
    46 => 263.0,
    47 => 412.0,
    48 => 562.0,
    49 => 562.0,
    50 => 562.0,
    51 => 562.0,
    52 => 562.0,
    53 => 562.0,
    54 => 562.0,
    55 => 562.0,
    56 => 562.0,
    57 => 562.0,
    58 => 242.0,
    59 => 211.0,
    60 => 508.0,
    61 => 549.0,
    62 => 522.0,
    63 => 472.0,
    64 => 898.0,
    65 => 652.0,
    66 => 623.0,
    67 => 651.0,
    68 => 656.0,
    69 => 568.0,
    70 => 553.0,
    71 => 681.0,
    72 => 713.0,
    73 => 272.0,
    74 => 552.0,
    75 => 627.0,
    76 => 538.0,
    77 => 873.0,
    78 => 713.0,
    79 => 688.0,
    80 => 631.0,
    81 => 688.0,
    82 => 616.0,
    83 => 593.0,
    84 => 597.0,
    85 => 648.0,
    86 => 636.0,
    87 => 887.0,
    88 => 627.0,
    89 => 601.0,
    90 => 599.0,
    91 => 265.0,
    92 => 410.0,
    93 => 265.0,
    94 => 418.0,
    95 => 451.0,
    96 => 309.0,
    97 => 544.0,
    98 => 561.0,
    99 => 523.0,
    100 => 564.0,
    101 => 530.0,
    102 => 347.0,
    103 => 561.0,
    104 => 551.0,
    105 => 243.0,
    106 => 239.0,
    107 => 507.0,
    108 => 243.0,
    109 => 876.0,
    110 => 552.0,
    111 => 570.0,
    112 => 561.0,
    113 => 568.0,
    114 => 338.0,
    115 => 516.0,
    116 => 327.0,
    117 => 551.0,
    118 => 484.0,
    119 => 751.0,
    120 => 496.0,
    121 => 473.0,
    122 => 496.0,
    123 => 338.0,
    124 => 244.0,
    125 => 338.0,
    126 => 680.0,
    160 => 248.0,
    161 => 244.0,
    162 => 547.0,
    163 => 581.0,
    164 => 713.0,
    165 => 525.0,
    166 => 240.0,
    167 => 613.0,
    168 => 418.0,
    169 => 786.0,
    170 => 447.0,
    171 => 469.0,
    172 => 554.0,
    173 => 276.0,
    174 => 786.0,
    175 => 458.0,
    176 => 374.0,
    177 => 534.0,
    178 => 367.0,
    179 => 367.0,
    180 => 313.0,
    181 => 566.0,
    182 => 489.0,
    183 => 261.0,
    184 => 248.0,
    185 => 367.0,
    186 => 455.0,
    187 => 469.0,
    188 => 732.0,
    189 => 776.0,
    190 => 778.0,
    191 => 473.0,
    192 => 652.0,
    193 => 652.0,
    194 => 652.0,
    195 => 652.0,
    196 => 652.0,
    197 => 652.0,
    198 => 935.0,
    199 => 651.0,
    200 => 568.0,
    201 => 568.0,
    202 => 568.0,
    203 => 568.0,
    204 => 272.0,
    205 => 272.0,
    206 => 272.0,
    207 => 272.0,
    208 => 670.0,
    209 => 713.0,
    210 => 688.0,
    211 => 688.0,
    212 => 688.0,
    213 => 688.0,
    214 => 688.0,
    215 => 533.0,
    216 => 688.0,
    217 => 648.0,
    218 => 648.0,
    219 => 648.0,
    220 => 648.0,
    221 => 601.0,
    222 => 591.0,
    223 => 595.0,
    224 => 544.0,
    225 => 544.0,
    226 => 544.0,
    227 => 544.0,
    228 => 544.0,
    229 => 544.0,
    230 => 844.0,
    231 => 523.0,
    232 => 530.0,
    233 => 530.0,
    234 => 530.0,
    235 => 530.0,
    236 => 247.0,
    237 => 247.0,
    238 => 247.0,
    239 => 247.0,
    240 => 586.0,
    241 => 552.0,
    242 => 570.0,
    243 => 570.0,
    244 => 570.0,
    245 => 570.0,
    246 => 570.0,
    247 => 571.0,
    248 => 566.0,
    249 => 551.0,
    250 => 551.0,
    251 => 551.0,
    252 => 551.0,
    253 => 473.0,
    254 => 576.0,
    255 => 473.0,
    256 => 652.0,
    257 => 544.0,
    258 => 652.0,
    259 => 544.0,
    260 => 652.0,
    261 => 544.0,
    262 => 651.0,
    263 => 523.0,
    264 => 651.0,
    265 => 523.0,
    266 => 651.0,
    267 => 523.0,
    268 => 651.0,
    269 => 523.0,
    270 => 656.0,
    271 => 637.0,
    272 => 670.0,
    273 => 597.0,
    274 => 568.0,
    275 => 530.0,
    276 => 568.0,
    277 => 530.0,
    278 => 568.0,
    279 => 530.0,
    280 => 568.0,
    281 => 530.0,
    282 => 568.0,
    283 => 530.0,
    284 => 681.0,
    285 => 561.0,
    286 => 681.0,
    287 => 561.0,
    288 => 681.0,
    289 => 561.0,
    290 => 681.0,
    291 => 561.0,
    292 => 713.0,
    293 => 551.0,
    294 => 700.0,
    295 => 565.0,
    296 => 272.0,
    297 => 247.0,
    298 => 272.0,
    299 => 247.0,
    300 => 272.0,
    301 => 247.0,
    302 => 272.0,
    303 => 243.0,
    304 => 272.0,
    305 => 247.0,
    306 => 824.0,
    307 => 481.0,
    308 => 552.0,
    309 => 251.0,
    310 => 627.0,
    311 => 507.0,
    312 => 556.0,
    313 => 538.0,
    314 => 243.0,
    315 => 538.0,
    316 => 243.0,
    317 => 538.0,
    318 => 316.0,
    319 => 538.0,
    320 => 350.0,
    321 => 539.0,
    322 => 270.0,
    323 => 713.0,
    324 => 552.0,
    325 => 713.0,
    326 => 552.0,
    327 => 713.0,
    328 => 552.0,
    329 => 552.0,
    330 => 693.0,
    331 => 566.0,
    332 => 688.0,
    333 => 570.0,
    334 => 688.0,
    335 => 570.0,
    336 => 688.0,
    337 => 570.0,
    338 => 954.0,
    339 => 908.0,
    340 => 616.0,
    341 => 338.0,
    342 => 616.0,
    343 => 338.0,
    344 => 616.0,
    345 => 338.0,
    346 => 593.0,
    347 => 516.0,
    348 => 593.0,
    349 => 516.0,
    350 => 593.0,
    351 => 516.0,
    352 => 593.0,
    353 => 516.0,
    354 => 597.0,
    355 => 327.0,
    356 => 597.0,
    357 => 346.0,
    358 => 597.0,
    359 => 327.0,
    360 => 648.0,
    361 => 551.0,
    362 => 648.0,
    363 => 551.0,
    364 => 648.0,
    365 => 551.0,
    366 => 648.0,
    367 => 551.0,
    368 => 648.0,
    369 => 551.0,
    370 => 648.0,
    371 => 551.0,
    372 => 887.0,
    373 => 751.0,
    374 => 601.0,
    375 => 473.0,
    376 => 601.0,
    377 => 599.0,
    378 => 496.0,
    379 => 599.0,
    380 => 496.0,
    381 => 599.0,
    382 => 496.0,
    383 => 248.0,
    399 => 691.0,
    402 => 340.0,
    416 => 687.0,
    417 => 571.0,
    431 => 695.0,
    432 => 619.0,
    496 => 251.0,
    506 => 652.0,
    507 => 544.0,
    508 => 935.0,
    509 => 844.0,
    510 => 688.0,
    511 => 566.0,
    536 => 593.0,
    537 => 516.0,
    538 => 597.0,
    539 => 327.0,
    567 => 251.0,
    601 => 527.0,
    700 => 200.0,
    710 => 471.0,
    711 => 444.0,
    713 => 458.0,
    728 => 427.0,
    729 => 243.0,
    730 => 334.0,
    731 => 271.0,
    732 => 472.0,
    733 => 373.0,
    755 => 294.0,
    768 => 0.0,
    769 => 0.0,
    771 => 0.0,
    777 => 0.0,
    783 => 0.0,
    803 => 0.0,
    900 => 256.0,
    901 => 505.0,
    902 => 652.0,
    903 => 261.0,
    904 => 568.0,
    905 => 713.0,
    906 => 272.0,
    908 => 697.0,
    910 => 649.0,
    911 => 675.0,
    912 => 324.0,
    913 => 652.0,
    914 => 623.0,
    915 => 556.0,
    916 => 705.0,
    917 => 568.0,
    918 => 599.0,
    919 => 713.0,
    920 => 680.0,
    921 => 272.0,
    922 => 627.0,
    923 => 655.0,
    924 => 873.0,
    925 => 713.0,
    926 => 571.0,
    927 => 688.0,
    928 => 713.0,
    929 => 631.0,
    931 => 571.0,
    932 => 597.0,
    933 => 601.0,
    934 => 716.0,
    935 => 627.0,
    936 => 692.0,
    937 => 665.0,
    938 => 272.0,
    939 => 601.0,
    940 => 565.0,
    941 => 539.0,
    942 => 566.0,
    943 => 324.0,
    944 => 545.0,
    945 => 565.0,
    946 => 592.0,
    947 => 501.0,
    948 => 566.0,
    949 => 539.0,
    950 => 518.0,
    951 => 566.0,
    952 => 569.0,
    953 => 324.0,
    954 => 556.0,
    955 => 554.0,
    956 => 566.0,
    957 => 484.0,
    958 => 490.0,
    959 => 570.0,
    960 => 596.0,
    961 => 566.0,
    962 => 538.0,
    963 => 566.0,
    964 => 521.0,
    965 => 545.0,
    966 => 705.0,
    967 => 496.0,
    968 => 700.0,
    969 => 824.0,
    970 => 324.0,
    971 => 545.0,
    972 => 570.0,
    973 => 545.0,
    974 => 824.0,
    977 => 579.0,
    978 => 532.0,
    982 => 785.0,
    1024 => 568.0,
    1025 => 568.0,
    1026 => 750.0,
    1027 => 556.0,
    1028 => 674.0,
    1029 => 593.0,
    1030 => 272.0,
    1031 => 272.0,
    1032 => 552.0,
    1033 => 1071.0,
    1034 => 1080.0,
    1035 => 813.0,
    1036 => 627.0,
    1037 => 713.0,
    1038 => 628.0,
    1039 => 713.0,
    1040 => 652.0,
    1041 => 630.0,
    1042 => 623.0,
    1043 => 556.0,
    1044 => 752.0,
    1045 => 568.0,
    1046 => 908.0,
    1047 => 593.0,
    1048 => 713.0,
    1049 => 713.0,
    1050 => 643.0,
    1051 => 708.0,
    1052 => 873.0,
    1053 => 713.0,
    1054 => 688.0,
    1055 => 713.0,
    1056 => 631.0,
    1057 => 651.0,
    1058 => 597.0,
    1059 => 628.0,
    1060 => 771.0,
    1061 => 627.0,
    1062 => 731.0,
    1063 => 685.0,
    1064 => 941.0,
    1065 => 969.0,
    1066 => 759.0,
    1067 => 865.0,
    1068 => 627.0,
    1069 => 674.0,
    1070 => 894.0,
    1071 => 637.0,
    1072 => 544.0,
    1073 => 553.0,
    1074 => 571.0,
    1075 => 419.0,
    1076 => 604.0,
    1077 => 530.0,
    1078 => 766.0,
    1079 => 508.0,
    1080 => 577.0,
    1081 => 577.0,
    1082 => 540.0,
    1083 => 578.0,
    1084 => 742.0,
    1085 => 577.0,
    1086 => 570.0,
    1087 => 577.0,
    1088 => 561.0,
    1089 => 523.0,
    1090 => 480.0,
    1091 => 473.0,
    1092 => 725.0,
    1093 => 496.0,
    1094 => 592.0,
    1095 => 543.0,
    1096 => 809.0,
    1097 => 827.0,
    1098 => 621.0,
    1099 => 776.0,
    1100 => 543.0,
    1101 => 538.0,
    1102 => 816.0,
    1103 => 549.0,
    1104 => 530.0,
    1105 => 530.0,
    1106 => 551.0,
    1107 => 419.0,
    1108 => 538.0,
    1109 => 516.0,
    1110 => 243.0,
    1111 => 247.0,
    1112 => 239.0,
    1113 => 848.0,
    1114 => 861.0,
    1115 => 567.0,
    1116 => 540.0,
    1117 => 577.0,
    1118 => 473.0,
    1119 => 577.0,
    1120 => 879.0,
    1121 => 771.0,
    1122 => 627.0,
    1123 => 542.0,
    1124 => 896.0,
    1125 => 746.0,
    1126 => 603.0,
    1127 => 534.0,
    1128 => 880.0,
    1129 => 755.0,
    1130 => 852.0,
    1131 => 735.0,
    1132 => 1127.0,
    1133 => 977.0,
    1134 => 517.0,
    1135 => 482.0,
    1136 => 692.0,
    1137 => 700.0,
    1138 => 680.0,
    1139 => 568.0,
    1140 => 630.0,
    1141 => 501.0,
    1142 => 630.0,
    1143 => 501.0,
    1144 => 1161.0,
    1145 => 1043.0,
    1146 => 680.0,
    1147 => 566.0,
    1148 => 875.0,
    1149 => 768.0,
    1150 => 879.0,
    1151 => 771.0,
    1152 => 649.0,
    1153 => 535.0,
    1154 => 624.0,
    1155 => 0.0,
    1156 => 0.0,
    1157 => 0.0,
    1158 => 0.0,
    1160 => 0.0,
    1161 => 0.0,
    1162 => 754.0,
    1163 => 616.0,
    1164 => 627.0,
    1165 => 542.0,
    1166 => 638.0,
    1167 => 567.0,
    1168 => 548.0,
    1169 => 445.0,
    1170 => 556.0,
    1171 => 419.0,
    1172 => 607.0,
    1173 => 502.0,
    1174 => 954.0,
    1175 => 797.0,
    1176 => 593.0,
    1177 => 508.0,
    1178 => 700.0,
    1179 => 590.0,
    1180 => 629.0,
    1181 => 562.0,
    1182 => 652.0,
    1183 => 519.0,
    1184 => 818.0,
    1185 => 689.0,
    1186 => 750.0,
    1187 => 606.0,
    1188 => 976.0,
    1189 => 713.0,
    1190 => 1024.0,
    1191 => 869.0,
    1192 => 741.0,
    1193 => 603.0,
    1194 => 651.0,
    1195 => 523.0,
    1196 => 597.0,
    1197 => 480.0,
    1198 => 601.0,
    1199 => 501.0,
    1200 => 601.0,
    1201 => 501.0,
    1202 => 637.0,
    1203 => 521.0,
    1204 => 896.0,
    1205 => 670.0,
    1206 => 717.0,
    1207 => 573.0,
    1208 => 679.0,
    1209 => 552.0,
    1210 => 679.0,
    1211 => 551.0,
    1212 => 773.0,
    1213 => 592.0,
    1214 => 773.0,
    1215 => 592.0,
    1216 => 272.0,
    1217 => 908.0,
    1218 => 766.0,
    1219 => 629.0,
    1220 => 544.0,
    1221 => 749.0,
    1222 => 617.0,
    1223 => 712.0,
    1224 => 566.0,
    1225 => 759.0,
    1226 => 615.0,
    1227 => 685.0,
    1228 => 543.0,
    1229 => 914.0,
    1230 => 780.0,
    1231 => 272.0,
    1232 => 652.0,
    1233 => 544.0,
    1234 => 652.0,
    1235 => 544.0,
    1236 => 935.0,
    1237 => 844.0,
    1238 => 568.0,
    1239 => 530.0,
    1240 => 691.0,
    1241 => 527.0,
    1242 => 691.0,
    1243 => 527.0,
    1244 => 908.0,
    1245 => 766.0,
    1246 => 593.0,
    1247 => 508.0,
    1248 => 582.0,
    1249 => 582.0,
    1250 => 713.0,
    1251 => 577.0,
    1252 => 713.0,
    1253 => 577.0,
    1254 => 688.0,
    1255 => 570.0,
    1256 => 680.0,
    1257 => 568.0,
    1258 => 680.0,
    1259 => 568.0,
    1260 => 674.0,
    1261 => 538.0,
    1262 => 628.0,
    1263 => 473.0,
    1264 => 628.0,
    1265 => 473.0,
    1266 => 628.0,
    1267 => 473.0,
    1268 => 685.0,
    1269 => 543.0,
    1270 => 556.0,
    1271 => 419.0,
    1272 => 865.0,
    1273 => 776.0,
    1274 => 589.0,
    1275 => 458.0,
    1276 => 647.0,
    1277 => 531.0,
    1278 => 627.0,
    1279 => 496.0,
    1280 => 620.0,
    1281 => 564.0,
    1282 => 822.0,
    1283 => 861.0,
    1284 => 792.0,
    1285 => 646.0,
    1286 => 536.0,
    1287 => 503.0,
    1288 => 969.0,
    1289 => 807.0,
    1290 => 998.0,
    1291 => 829.0,
    1292 => 620.0,
    1293 => 514.0,
    1294 => 709.0,
    1295 => 641.0,
    1296 => 674.0,
    1297 => 539.0,
    1298 => 751.0,
    1299 => 618.0,
    7680 => 652.0,
    7681 => 544.0,
    7742 => 873.0,
    7743 => 876.0,
    7808 => 887.0,
    7809 => 751.0,
    7810 => 887.0,
    7811 => 751.0,
    7812 => 887.0,
    7813 => 751.0,
    7840 => 652.0,
    7841 => 544.0,
    7842 => 652.0,
    7843 => 544.0,
    7844 => 652.0,
    7845 => 544.0,
    7846 => 652.0,
    7847 => 544.0,
    7848 => 652.0,
    7849 => 544.0,
    7850 => 652.0,
    7851 => 544.0,
    7852 => 652.0,
    7853 => 544.0,
    7854 => 652.0,
    7855 => 544.0,
    7856 => 652.0,
    7857 => 544.0,
    7858 => 652.0,
    7859 => 544.0,
    7860 => 652.0,
    7861 => 544.0,
    7862 => 652.0,
    7863 => 544.0,
    7864 => 568.0,
    7865 => 530.0,
    7866 => 568.0,
    7867 => 530.0,
    7868 => 568.0,
    7869 => 530.0,
    7870 => 568.0,
    7871 => 530.0,
    7872 => 568.0,
    7873 => 530.0,
    7874 => 568.0,
    7875 => 530.0,
    7876 => 568.0,
    7877 => 530.0,
    7878 => 568.0,
    7879 => 530.0,
    7880 => 272.0,
    7881 => 247.0,
    7882 => 272.0,
    7883 => 243.0,
    7884 => 688.0,
    7885 => 570.0,
    7886 => 688.0,
    7887 => 570.0,
    7888 => 688.0,
    7889 => 570.0,
    7890 => 688.0,
    7891 => 570.0,
    7892 => 688.0,
    7893 => 570.0,
    7894 => 688.0,
    7895 => 570.0,
    7896 => 688.0,
    7897 => 570.0,
    7898 => 687.0,
    7899 => 571.0,
    7900 => 687.0,
    7901 => 571.0,
    7902 => 687.0,
    7903 => 571.0,
    7904 => 687.0,
    7905 => 571.0,
    7906 => 687.0,
    7907 => 571.0,
    7908 => 648.0,
    7909 => 551.0,
    7910 => 648.0,
    7911 => 551.0,
    7912 => 695.0,
    7913 => 619.0,
    7914 => 695.0,
    7915 => 619.0,
    7916 => 695.0,
    7917 => 619.0,
    7918 => 695.0,
    7919 => 619.0,
    7920 => 695.0,
    7921 => 619.0,
    7922 => 601.0,
    7923 => 473.0,
    7924 => 601.0,
    7925 => 473.0,
    7926 => 601.0,
    7927 => 473.0,
    7928 => 601.0,
    7929 => 473.0,
    8013 => 722.0,
    8192 => 510.0,
    8193 => 1020.0,
    8194 => 510.0,
    8195 => 1020.0,
    8196 => 340.0,
    8197 => 255.0,
    8198 => 170.0,
    8199 => 562.0,
    8200 => 273.0,
    8201 => 204.0,
    8202 => 102.0,
    8203 => 0.0,
    8208 => 275.0,
    8209 => 275.0,
    8211 => 656.0,
    8212 => 781.0,
    8213 => 781.0,
    8215 => 456.0,
    8216 => 200.0,
    8217 => 200.0,
    8218 => 199.0,
    8219 => 200.0,
    8220 => 354.0,
    8221 => 357.0,
    8222 => 344.0,
    8224 => 551.0,
    8225 => 570.0,
    8226 => 337.0,
    8229 => 471.0,
    8230 => 669.0,
    8231 => 187.0,
    8240 => 958.0,
    8242 => 174.0,
    8243 => 320.0,
    8249 => 300.0,
    8250 => 300.0,
    8252 => 515.0,
    8260 => 455.0,
    8308 => 367.0,
    8319 => 422.0,
    8355 => 553.0,
    8356 => 581.0,
    8358 => 792.0,
    8359 => 820.0,
    8360 => 1058.0,
    8361 => 740.0,
    8362 => 771.0,
    8363 => 579.0,
    8364 => 562.0,
    8369 => 731.0,
    8377 => 517.0,
    8378 => 557.0,
    8380 => 660.0,
    8381 => 664.0,
    8453 => 738.0,
    8467 => 476.0,
    8470 => 1028.0,
    8482 => 625.0,
    8486 => 665.0,
    8494 => 636.0,
    8539 => 769.0,
    8540 => 855.0,
    8541 => 851.0,
    8542 => 802.0,
    8706 => 570.0,
    8710 => 705.0,
    8719 => 694.0,
    8721 => 585.0,
    8722 => 571.0,
    8730 => 596.0,
    8734 => 1028.0,
    8747 => 256.0,
    8776 => 563.0,
    8800 => 549.0,
    8804 => 508.0,
    8805 => 523.0,
    9674 => 504.0,
    60929 => 287.0,
    60930 => 320.0,
    63171 => 251.0,
    64257 => 554.0,
    64258 => 568.0,
    64259 => 854.0,
    64260 => 854.0,
    65279 => 0.0,
    65532 => 1025.0,
    65533 => 1026.0,
  ),
  'CIDtoGID_Compressed' => true,
  'CIDtoGID' => '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',
  '_version_' => 6,
);