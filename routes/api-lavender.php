<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\DesignCheckController;
use App\Http\Controllers\LavenderAuthController;
use App\Http\Controllers\LavenderController;
use App\Http\Controllers\OwnershipController;
use App\Http\Controllers\TrademarkController;
use Illuminate\Support\Facades\Route;

Route::prefix('lavender')->group(function () {
    Route::post('login', [LavenderAuthController::class, 'login']);
    Route::post('google/login', [AuthController::class, 'loginWithGoogle']);
    Route::post('logout', [LavenderAuthController::class, 'logout']);
});

Route::middleware(['auth.lavender'])->group(function () {
    Route::get('lavender/user-profile', [LavenderAuthController::class, 'userProfile'])->name('auth_userProfile');
    Route::prefix('design-check')->group(function () {
        Route::get('', [DesignCheckController::class, 'getList']);
        Route::get('get-count-pending', [DesignCheckController::class, 'getCountPending']);
        Route::get('get-count', [DesignCheckController::class, 'getCount']);
        Route::get('get-count-order', [DesignCheckController::class, 'getCountOrder']);
        Route::put('reject-design', [LavenderController::class, 'rejectDesign'])->name('reject-design');
        Route::put('pass-design', [LavenderController::class, 'passDesign'])->name('pass-design');
        Route::put('confirm-reject-design', [LavenderController::class, 'confirmRejectDesign'])->name('confirm-reject-design');
        Route::put('confirm-pass-design', [LavenderController::class, 'confirmPassDesign'])->name('confirm-pass-design');
    });
    Route::prefix('trademark')->group(function () {
        Route::get('', [TrademarkController::class, 'index']);
        Route::get('get-all', [TrademarkController::class, 'getAll']);
        Route::post('', [TrademarkController::class, 'create']);
        Route::put('/{id}', [TrademarkController::class, 'update']);
        Route::delete('/{id}', [TrademarkController::class, 'delete']);
    });
    Route::prefix('ownership')->group(function () {
        Route::post('', [OwnershipController::class, 'createOwnership']);
        Route::get('get-all', [OwnershipController::class, 'getAll']);
    });
});
