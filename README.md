### === Setup 1  ====


### 1. Change config php.int file
```terminal
​
max_input_time=6000
max_execution_time=1200
upload_max_filesize=1G
memory_limit=1G
​​
 ```
### 2. Command install 
```terminal
chmod -R 777 storage/
​
composer install
​
```

### 3. Make environment configuration  
```terminal
cp .env.example .env
​​
```
### 4. Configuration database connection in .env file
```terminal
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=my_db_name
DB_USERNAME=my_db_user
DB_PASSWORD=my_password
```
​
### 5. Migrate database and seeder
​
```terminal
php artisan key:generate

php artisan jwt:secret
​
php artisan reload:cache

```
