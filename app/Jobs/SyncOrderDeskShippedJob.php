<?php

namespace App\Jobs;

use App\Models\FolderOrderDesk;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderHistory;
use App\Models\Shipment;
use App\Services\OrderDeskService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncOrderDeskShippedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * // define the properties of $params
     */
    public function __construct(public $shipmentId)
    {
    }

    public function handle(): void
    {
        Log::channel('order_desk')->info(sprintf('Start sync order desk shipped from order desk api with shipment_id: %s', $this->shipmentId));
        $shipment = Shipment::with(['saleOrder.account'])->where('id', $this->shipmentId)
            ->firstOrFail();

        $saleOrder = $shipment->saleOrder;
        $saleOrderAccount = $saleOrder->account;
        if ($saleOrderAccount->source != SaleOrderAccount::SOURCE_ORDER_DESK
            || $saleOrderAccount->sync_orderdesk != SaleOrderAccount::ACTIVE) {
            return;
        }
        $orderDeskService = new OrderDeskService($saleOrderAccount);
        $folders = FolderOrderDesk::where('sale_order_account_id', $saleOrderAccount->id)
            ->pluck('folder_id', 'name')
            ->toArray();
        $externalId = $saleOrder->external_key;
        $resAddShipment = $orderDeskService->addShipment($externalId, $shipment);
        Log::channel('order_desk')->info(sprintf('$resAddShipment: %s', json_encode($resAddShipment->json())));
        if ($resAddShipment->successful()) {
            saleOrderHistory(
                null,
                null,
                $saleOrder->id,
                SaleOrderHistory::SYNC_ORDER_DESK_SHIPPED,
                config('orderdesk.note.sync_tracking_success'),
            );
            $resMoveFolder = $orderDeskService->moveFolder($folders[FolderOrderDesk::FOLDER_CLOSED], $externalId);
            Log::channel('order_desk')->info(sprintf('$resMoveFolder: %s', json_encode($resMoveFolder->json())));
        } else {
            throw new \Exception('Sync Order Desk Shipped Job failed');
        }
    }
}
