<?php

namespace App\Jobs;

use App\Services\QBBillService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class QBSyncBillStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->queue = 'queue-sync-quick-book';
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(QBBillService $QBBillService)
    {
        try {
            $r = $QBBillService->updatePoStatus($this->id);

            if (!empty($r)) {
                Log::info('QBUpdateBillStatusJob.handle update Bill status success, qb_ref: ' . $this->id);
            } else {
                Log::info('QBUpdateBillStatusJob.handle update Bill status not success, qb_ref: ' . $this->id);
            }
        } catch (\Exception $e) {
            Log::error('QBUpdateBillStatusJob.handle', [$e]);
        }
    }
}
