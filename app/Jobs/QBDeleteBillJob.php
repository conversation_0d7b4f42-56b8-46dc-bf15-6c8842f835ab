<?php

namespace App\Jobs;

use App\Models\PurchaseOrder;
use App\Services\QBBillService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class QBDeleteBillJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $poId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($poId)
    {
        $this->queue = 'queue-sync-quick-book';
        $this->poId = $poId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(QBBillService $QBBillService)
    {
        try {
            $po = PurchaseOrder::where('id', $this->poId)->first();

            if (empty($po)) {
                Log::info('QBDeleteBillJob.handle not found PO ID: ' . $this->poId);

                return;
            }

            if ($po->qb_ref > 0) {
                $r = $QBBillService->delete($po);
            }

            if (!empty($r)) {
                Log::info('QBDeleteBillJob.handle delete Bill success, PO number: ' . $po->po_number);
            } else {
                Log::info('QBDeleteBillJob.handle delete Bill not success, PO number: ' . $po->po_number);
            }
        } catch (\Exception $e) {
            Log::error('QBDeleteBillJob.handle', [$e]);
        }
    }
}
