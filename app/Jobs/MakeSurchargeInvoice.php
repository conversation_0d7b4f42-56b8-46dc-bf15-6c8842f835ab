<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Repositories\InvoiceRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MakeSurchargeInvoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $id;

    public $timeout = 3600; // Set the timeout to 3600 seconds (60 minutes)

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(InvoiceRepository $invoiceRepository)
    {
        try {
            $invoice = Invoice::find($this->id);
            if ($invoice) {
                if (is_null($invoice->processing_surcharge_at)) {
                    echo "Processing surcharge invoice $this->id\n";
                    $invoice->processing_surcharge_at = now();
                    $invoice->save();
                    $invoiceRepository->generateSurchargeInvoice($invoice);
                } else {
                    echo "Invoice $this->id is already being processed for surcharge";
                }
            } else {
                echo "Wrong invoiceId: $this->id when making surcharge invoice";
            }
        } catch (\Throwable $th) {
            if (isset($invoice)) {
                Invoice::where('id', $invoice->id)->update(['processing_surcharge_at' => null]);
            }
            echo 'Error when making surcharge invoice: ' . $th->getMessage();
            throw $th;
        }
    }
}
