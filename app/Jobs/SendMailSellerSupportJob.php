<?php

namespace App\Jobs;
use App\Models\SaleOrderClaimSupport;
use App\Models\SaleOrderItem;
use App\Models\Setting;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendMailSellerSupport;
use Exception;
use Carbon\Carbon;

class SendMailSellerSupportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $order_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->order_id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $data = SaleOrderClaimSupport::where('id', $this->order_id)->with(['images','store','saleOrder','feedback','feedbackFiles'])->firstOrFail();
        try{
            if($data['type'] == SaleOrderClaimSupport::TYPE_ORDER_NEED_SOME_CARE) {
                $arraySaleOrderItem = array_map('trim', explode(",", $data['sale_order_item_id']));
                $saleOrderItem = SaleOrderItem::select('external_id','sku')->whereIn('id',$arraySaleOrderItem)->get();
                $data['saleOrderItem'] = $saleOrderItem;
            }            
            echo "Start sending email from customer " .$data['customer_email']. " with order " .$data['saleOrder']['id']. "\n";
            $to = Setting::where('label', 'to_email_claim_order')->where('warehouse_id', Setting::GLOBAL)->first();
            $arrayTo = array_map('trim', explode(",", $to['value']));
            if(isset($data['feedback'])) {
                $data['cc_to'] = $arrayTo;
                $mail = Mail::to($data['customer_email']);
            } else {
                $data['cc_to'] = [$data['customer_email']];
                $mail = Mail::to($arrayTo);
            }
            $mail->send(new SendMailSellerSupport($data));
            SaleOrderClaimSupport::find($this->order_id)->update(['updated_at' => carbon::now()]);
            echo "Sending email completed for order " .$data['saleOrder']['id']. "\n";
    
        } catch (Exception $e) {
            echo $e->getMessage();
            echo "Sending email failed for order " . $data['saleOrder']['id'] . "\n";
            SaleOrderClaimSupport::find($this->order_id)->update(['error_log' => $e->getMessage()]);
        }

    }
}
