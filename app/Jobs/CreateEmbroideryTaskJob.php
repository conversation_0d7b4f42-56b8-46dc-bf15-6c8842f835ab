<?php

namespace App\Jobs;

use App\Models\EmbroideryTask;
use App\Models\ImageHash;
use App\Models\IntegrateLog;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Setting;
use App\Models\Store;
use App\Repositories\GelatoRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateEmbroideryTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $input;

    /**
     * @var int
     */
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($input)
    {
        $this->input = $input;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $this->createTask();
        } catch (\Exception $exception) {
            Log::channel('create_embroidery_task')->error($exception->getMessage());
            throw new \Exception($exception->getMessage());
        }
    }

    public function createTask()
    {
        try {
            $orderId = $this->input['sale_order_id'] ?? null;
            $orderItemId = $this->input['order_item_id'] ?? null;
            $imageHashId = $this->input['image_hash_id'] ?? null;
            $imageId = $this->input['sale_order_image_id'] ?? null;

            // Fetch order and validate test status
            $order = SaleOrderItem::join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
                ->where('sale_order_item.id', $orderItemId)
                ->select('sale_order.*')
                ->first();

            if (!$order) {
                Log::channel('create_embroidery_task')->error("Order not found for Order Item ID: $orderItemId");

                return;
            }

            if ($order->is_test != SaleOrder::NOT_TEST) {
                echo "is_test order - cancel create task for order $orderId\n";

                return;
            }

            echo "Start create task for order $orderId\n";

            // Fetch bypass settings
            $listStoresByPass = Setting::where('label', 'embroidery_store_exclude_digitization')->value('value');
            $arrayListStoresByPass = $listStoresByPass ? array_map('trim', explode(',', $listStoresByPass)) : [];

            // Check for existing tasks
            $existingTask = EmbroideryTask::where('sale_order_image_id', $imageId)
                ->whereIn('status', [
                    EmbroideryTask::STATUS_PENDING,
                    EmbroideryTask::STATUS_IN_PROGRESS,
                    EmbroideryTask::STATUS_IN_REVIEW,
                    EmbroideryTask::STATUS_COMPLETED
                ])->first();

            if ($existingTask && $existingTask->image_hash_id == $imageHashId) {
                return;
            }

            $this->cancelCurrentTask();

            $input = [
                'order_item_id' => $orderItemId,
                'sale_order_id' => $orderId,
                'sale_order_image_id' => $imageId,
                'print_area' => $this->input['print_area'] ?? null,
                'print_size' => $this->input['print_size'] ?? null,
                'image_hash_id' => $imageHashId,
            ];

            $parentTask = EmbroideryTask::where('image_hash_id', $imageHashId)
                ->whereNotIn('status', [EmbroideryTask::STATUS_REJECT, EmbroideryTask::STATUS_CANCELLED])
                ->whereNull('parent_id')
                ->first();

            $image = SaleOrderItemImage::where('id', $imageId)->first();

            if ($parentTask) {
                $input['parent_id'] = $parentTask->id;
                $input['status'] = $parentTask->status;

                if ($parentTask->status === EmbroideryTask::STATUS_COMPLETED) {
                    $input['link_url'] = $parentTask->link_url;
                    $input['color_url'] = $parentTask->color_url;
                }
            } elseif (in_array($order->store_id, $arrayListStoresByPass)) {
                $hasInvalidExtension = EmbroideryTask::hasValidExtension($image?->image_url, SaleOrderItemImage::NO_BYPASS_EMB_EXTENSIONS)
                    || EmbroideryTask::hasCustomValidExtension($image?->image_url, SaleOrderItemImage::NO_BYPASS_EMB_EXTENSIONS);
                if ($order->store_id == Store::STORE_GELATO && !$hasInvalidExtension) {
                    $this->handleBypassStore($imageId, $input, $orderItemId);
                }
            }

            if ((EmbroideryTask::hasValidExtension($image?->image_url, SaleOrderItemImage::VALID_EMB_EXTENSIONS)
                || EmbroideryTask::hasCustomValidExtension($image?->image_url, SaleOrderItemImage::VALID_EMB_EXTENSIONS))
                && $order->store_id == Store::STORE_GELATO
            ) {
                $this->getStitchColorWorksheetGelato($image, $input, $orderItemId, $orderId, $this->input['print_area']);
            }

            $createdTask = EmbroideryTask::create($input);

            if ($parentTask && $parentTask->status === EmbroideryTask::STATUS_COMPLETED && !in_array($order->store_id, $arrayListStoresByPass)) {
                $this->logTaskApproval($createdTask, $parentTask);
            }

            $this->dispatchColorStatusUpdate($createdTask);

            echo "End create task for order $orderId\n";
        } catch (\Exception $exception) {
            Log::channel('create_embroidery_task')->error($exception->getMessage());
        }
    }

    private function handleBypassStore($imageId, &$input, $orderItemId)
    {
        $image = SaleOrderItemImage::find($imageId);
        if (!$image) {
            echo "Image not found for ID: $imageId\n";

            return false;
        }

        $hash = ImageHash::find($image->image_hash_id);
        if (!$hash) {
            echo "Hash not found for Image Hash ID: $image->image_hash_id\n";

            return false;
        }

        if (EmbroideryTask::hasValidExtension($image->image_url, SaleOrderItemImage::VALID_EMB_EXTENSIONS)
            || EmbroideryTask::hasCustomValidExtension($image->image_url, SaleOrderItemImage::VALID_EMB_EXTENSIONS)) {
            $link_url = $this->validateAndUploadImage($image, $hash, $orderItemId);
            if (!$link_url) {
                return false;
            }
        }

        $input['status'] = EmbroideryTask::STATUS_COMPLETED;
        $input['link_url'] = $link_url;

        SaleOrderHistory::create([
            'order_id' => $this->input['sale_order_id'],
            'type' => SaleOrderHistory::APPROVE_EMBROIDERY_FILE,
            'message' => 'The task was completed automatically because it is from a store that excludes embroidery digitization',
            'created_at' => now()->toDateTimeString(),
        ]);

        return true;
    }

    private function validateAndUploadImage($image, $hash, $orderItemId)
    {
        $imageUrl = $this->addHttp($image->image_url);
        $downloadedData = file_get_contents($imageUrl);

        if (!$downloadedData) {
            echo "Failed to download image from URL: $imageUrl\n";

            return false;
        }

        if (md5($downloadedData) !== $hash->hash_md5) {
            echo "Upload to S3:: hash failed for item ID $orderItemId\n";

            return false;
        }
        $parsedUrl = parse_url($image->image_url);
        $cleanUrl = $parsedUrl['path'];

        $fileExtension = strtolower(pathinfo($cleanUrl, PATHINFO_EXTENSION));
        $filename = $image->image_hash_id . '.' . $fileExtension;

        if (!\Storage::disk('s3')->put("/embroidery/file/$filename", $downloadedData)) {
            echo "Failed to upload file to S3 for item ID: $orderItemId\n";

            return false;
        }

        return $filename;
    }

    private function logTaskApproval($createdTask, $parentTask)
    {
        SaleOrderHistory::create([
            'order_id' => $createdTask->sale_order_id,
            'type' => SaleOrderHistory::APPROVE_EMBROIDERY_FILE,
            'message' => "The TBF/CT0 file for \"$createdTask->print_area\" area on Task ID #$parentTask->id has been approved by a leader/admin.",
            'created_at' => now()->toDateTimeString(),
        ]);
    }

    private function dispatchColorStatusUpdate($createdTask)
    {
        $image = SaleOrderItemImage::find($createdTask->sale_order_image_id);

        if ($image && EmbroideryTask::hasValidExtension($image->image_url, SaleOrderItemImage::VALID_EMB_EXTENSIONS) || $image && EmbroideryTask::hasCustomValidExtension($image->image_url, SaleOrderItemImage::VALID_EMB_EXTENSIONS)) {
            dispatch(new UpdateOrderItemColorStatus($createdTask->order_item_id))
                ->onQueue(QueueJob::QUEUE_UPDATE_ORDER_ITEM_COLOR_STATUS);
        }
    }

    public function cancelCurrentTask()
    {
        try {
            $task = EmbroideryTask::where('sale_order_image_id', $this->input['sale_order_image_id'])
                ->whereIn('status', [EmbroideryTask::STATUS_PENDING, EmbroideryTask::STATUS_IN_PROGRESS, EmbroideryTask::STATUS_IN_REVIEW])
                ->first();
            if ($task) {
                $task->update(['status' => EmbroideryTask::STATUS_CANCELLED]);
                if (!$task->parent_id) {
                    $seconderyTask = EmbroideryTask::where('parent_id', $task->id)
                        ->whereNotIn('status', [EmbroideryTask::STATUS_CANCELLED, EmbroideryTask::STATUS_REJECT])
                        ->orderBy('id', 'desc')
                        ->first();
                    if ($seconderyTask) {
                        $seconderyTask->update(['parent_id' => null]);
                    }
                }
            }
        } catch (\Exception $exception) {
            Log::channel('create_embroidery_task')->error($exception->getMessage());
        }
    }

    protected function addHttp($url)
    {
        // return $url;
        if (empty($url)) {
            return '';
        }

        // Search the pattern
        if (!preg_match('~^(?:f|ht)tps?://~i', $url)) {
            // If not exist then add http
            $url = 'https://' . $url;
        }

        // Return the URL
        return $url;
    }

    public function getStitchColorWorksheetGelato($image, &$input, $orderItemId, $orderId, $printArea)
    {
        $saleOrderItem = SaleOrderItem::where('id', $orderItemId)->first();
        $datalogGelato = IntegrateLog::where('order_id', $orderId)->where('store_id', Store::STORE_GELATO)->first();
        if (!$datalogGelato) {
            echo "Order ID $orderId not found order integrate log";

            return false;
        }

        $dataGelatos = json_decode($datalogGelato->json)->items;
        $gelatoRepo = new GelatoRepository();
        $printSide = $gelatoRepo->mappingEmbPrintSideWithGelato()[strtolower((string) $printArea)];

        $urlWorkSheetGelato = null;
        foreach ($dataGelatos as $dataGelato) {
            if ($dataGelato?->id == $saleOrderItem->external_id) {
                foreach ($dataGelato?->product?->attributes as $attribute) {
                    if ($attribute->name == $printSide . '_stitch-count') {
                        $input['stitch_count'] = $attribute->value;
                    }
                    if ($attribute->name == $printSide . '_threads-colors') {
                        $input['threads_colors'] = $attribute->value;
                    }
                }
                foreach ($dataGelato?->assets as $asset) {
                    if ($asset->type == $printSide . '_worksheet') {
                        $urlWorkSheetGelato = $asset->url;
                    }
                }
                break;
            }
        }

        // upload file worksheetGelato
        if (!empty($urlWorkSheetGelato)) {
            $input['worksheet_url'] = !$this->validateAndUploadPdf($urlWorkSheetGelato, $image->id) ? null : $this->validateAndUploadPdf($urlWorkSheetGelato, $image->id);
        }
        $input['chart_name'] = $gelatoRepo::GELATO;

        return true;
    }

    private function validateAndUploadPdf($urlPdf, $orderItemImageId)
    {
        try {
            $imageUrl = $this->addHttp($urlPdf);
            $downloadedData = file_get_contents($imageUrl);
            $parsedUrl = parse_url($urlPdf);
            $cleanUrl = $parsedUrl['path'];
            $fileExtension = strtolower(pathinfo($cleanUrl, PATHINFO_EXTENSION));
            $filename = $orderItemImageId . '.' . $fileExtension;
            if (!\Storage::disk('s3')->put(EmbroideryTask::PREFIX_FILE_EMBROIDERY_WORKSHEET . $filename, $downloadedData)) {
                echo "Failed to upload file to S3 for item ID: $orderItemImageId\n";

                return false;
            }

            return $filename;
        } catch (\Exception $exception) {
            echo 'Failed :' . $exception->getMessage() . "\n";

            return false;
        }
    }
}
