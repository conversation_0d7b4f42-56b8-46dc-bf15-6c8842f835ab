<?php

namespace App\Jobs;

use App\Models\SaleOrder;
use App\Models\Shipment;
use App\Models\ShipmentTransit;
use Carbon\Carbon;
use EasyPost\Error;
use EasyPost\Shipment as ShipmentEasyPost;
use EasyPost\Tracker;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateShipmentStatusJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $shipmentId;

    protected $easyPostShipmentId;

    protected $easyPostApiKey;

    protected $isOrderLabel;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shipmentId, $easyPostShipmentId, $easyPostApiKey, $isOrderLabel = false)
    {
        $this->shipmentId = $shipmentId;
        $this->easyPostShipmentId = $easyPostShipmentId;
        $this->easyPostApiKey = $easyPostApiKey;
        $this->isOrderLabel = $isOrderLabel;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $item = Shipment::query()->where('id', $this->shipmentId)->first();
        if (!$item) {
            $this->info('shipment not found');

            return false;
        }
        $currentStatus = $item->tracking_status;

        // don label get tracking status by tracker easypost
        if ($this->isOrderLabel) {
            try {
                $tracker = Tracker::create(['tracking_code' => $item->tracking_number], $this->easyPostApiKey);
                if (isset($tracker->status) && $tracker->status != $item->tracking_status) {
                    $update['tracking_status'] = $tracker->status;
                    $update['url_tracking_easypost'] = $tracker->public_url;
                    $item->update($update);
                    $shipmentTransit = ShipmentTransit::firstOrCreate(['shipment_id' => $this->shipmentId], [
                        'unknown_at' => $item->created_at,
                        'order_id' => $item->order_id,
                        'warehouse_id' => $item->warehouse_id,
                    ]);
                    $time = $this->getTimeByStatus($tracker->status, $tracker->tracking_details ?? []);
                    $time = Carbon::parse($time)->toDateTimeString();
                    switch ($tracker->status) {
                        case 'delivered':
                            if (empty($shipmentTransit->delivered_at)) {
                                $shipmentTransit->delivered_at = $time;
                                handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $item->order_id);
                            }
                            break;
                        case 'in_transit':
                            if (empty($shipmentTransit->in_transit_at)) {
                                $shipmentTransit->in_transit_at = $time;
                                handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $item->order_id);
                            }
                            break;
                        case 'return_to_sender':
                            if (empty($shipmentTransit->returned_at)) {
                                $shipmentTransit->returned_at = $time;
                            }
                            break;
                    }
                    $shipmentTransit->save();
                }
                $this->info("$item->id | $item->tracking_number | $item->created_at | $currentStatus -> $tracker->status");

                return true;
            } catch (Error $e) {
                $this->info($e);

                return false;
            }
        }

        $shipment = ShipmentEasyPost::retrieve($this->easyPostShipmentId, $this->easyPostApiKey);
        if (!$shipment) {
            $this->info('shipment easypost not found');

            return false;
        }

        if ($shipment->status && $shipment->status != $item->tracking_status) {
            $update['tracking_status'] = $shipment->status;
            $item->update($update);
        }

        $this->info("$item->id | $item->tracking_number | $item->created_at | $currentStatus -> $shipment->status");

        return true;
    }

    private function info(string $string)
    {
        echo $string . "\n";
    }

    private function getTimeByStatus($status, $input)
    {
        foreach ($input as $item) {
            if ($item->status == $status) {
                return $item->datetime;
            }
        }

        return null;
    }
}
