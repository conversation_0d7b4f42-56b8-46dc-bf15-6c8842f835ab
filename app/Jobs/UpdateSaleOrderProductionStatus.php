<?php

namespace App\Jobs;

use App\Models\SaleOrder;
use App\Models\Shipment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateSaleOrderProductionStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $order_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($order_id)
    {
        $this->order_id = $order_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            echo "Start update production status order $this->order_id. \n";

            $saleOrder = SaleOrder::query()
                ->with(['items' => function ($query) {
                    $query->with(['barcodes' => function ($q) {
                        //  lay ban ghi chua bi xoa
                        $q->where('is_deleted', '=', 0);
                    }])->orderBy('id');
                }])
                ->with(['store', 'account'])
                ->where('id', $this->order_id)
                ->first();
            $shipment = Shipment::where('order_id', $this->order_id)
            ->with('shipmentLabelPrinted')
            ->whereHas('shipmentLabelPrinted')
            ->first();
            $productionStatus = [
                'Folded' => [],
                'Labeled' => [],
                'QC’ed' => [],
                'Pressed' => [],
                'Printed' => [],
                'Pretreated' => [],
                'Pulled' => [],
                'Not Started' => [],
            ];
            if (!empty($saleOrder->items)) {
                foreach ($saleOrder->items as $item) {
                    if (!empty($item->barcodes)) {
                        foreach ($item->barcodes as $saleOrderItemBarcode) {
                            $productionStatus = $this->getProductionStatus($saleOrderItemBarcode, $productionStatus);
                        }
                    }
                }
            }
            if ($shipment) {
                $productionStatus['Labeled'][] = 1;
            } else {
                $productionStatus['Labeled'][] = 0;
            }
            // } else {
            //     $productionStatus[0]['Labeled'] = 'Labeled';

            // }
            $status = $this->filterProductionStatus($productionStatus);
            $saleOrder->update([
                'production_status' => $status
            ]);

            echo "Production status is $status \n";
        } catch (\Exception $exception) {
            Log::channel('job_update_product_status')->error($exception->getMessage());
            throw new \Exception($exception->getMessage());
        }
    }

    public function getProductionStatus($label, $data)
    {
        if (is_null($label['pulled_at']) || is_null($label['employee_pull_id'])) {
            $data['Not Started'][] = 1;
        }
        if (!is_null($label['pulled_at'])) {
            $data['Pulled'][] = 1;
        } else {
            $data['Pulled'][] = 0;
        }
        if (!is_null($label['pretreated_at'])) {
            $data['Pretreated'][] = 1;
        } else {
            $data['Pretreated'][] = 0;
        }
        if (!is_null($label['printed_at']) || !is_null($label['employee_print_id'])) {
            $data['Printed'][] = 1;
        } else {
            $data['Printed'][] = 0;
        }
        if (!is_null($label['qc_at'])) {
            $data['QC’ed'][] = 1;
        } else {
            $data['QC’ed'][] = 0;
        }
        if (!is_null($label['pressed_at'])) {
            $data['Pressed'][] = 1;
        } else {
            $data['Pressed'][] = 0;
        }
        if (!is_null($label['folded_at'])) {
            $data['Folded'][] = 1;
        } else {
            $data['Folded'][] = 0;
        }

        return $data;
    }

    public function filterProductionStatus(array $array): string
    {
        if (in_array(0, $array['Folded']) && !in_array(1, $array['Folded'])) {
        } elseif (in_array(1, $array['Folded']) && !in_array(0, $array['Folded'])) {
            return 'folded';
        }

        if (in_array(0, $array['Labeled']) && !in_array(1, $array['Labeled'])) {
        } elseif (in_array(1, $array['Labeled']) && !in_array(0, $array['Labeled'])) {
            return 'labeled';
        }

        if (in_array(0, $array['QC’ed']) && !in_array(1, $array['QC’ed'])) {
        } elseif (in_array(1, $array['QC’ed']) && !in_array(0, $array['QC’ed'])) {
            return 'qc';
        }

        if (in_array(0, $array['Pressed']) && !in_array(1, $array['Pressed'])) {
        } elseif (in_array(1, $array['Pressed']) && !in_array(0, $array['Pressed'])) {
            return 'pressed';
        }

        if (in_array(0, $array['Printed']) && !in_array(1, $array['Printed'])) {
        } elseif (in_array(1, $array['Printed']) && !in_array(0, $array['Printed'])) {
            return 'printed';
        }

        if (in_array(0, $array['Pretreated']) && !in_array(1, $array['Pretreated'])) {
        } elseif (in_array(1, $array['Pretreated']) && !in_array(0, $array['Pretreated'])) {
            return 'pretreated';
        }

        if (in_array(0, $array['Pulled']) && !in_array(1, $array['Pulled'])) {
        } elseif (in_array(1, $array['Pulled']) && !in_array(0, $array['Pulled'])) {
            return 'pulled';
        }

        return 'not_started';
    }
}
