<?php

namespace App\Jobs;

use App\Models\Product;
use App\Repositories\ProductRepository;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Warehouse;
use Illuminate\Support\Str;
use App\Models\WebhookFacilityStockNotify;

class FacilityNotifyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $params;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

/**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            echo 'Send notify facility for: '. $this->params['store_id'] ." at: " .$this->params['current_time'] . "\n";
            $warehouse = Warehouse::find($this->params['warehouse_id']);
            $product = Product::select(['id', 'sku', 'name', 'style', 'brand_id', 'color', 'size'])
                ->where('id', $this->params['product_id'])
                ->with([
                    'productQuantities' => function ($q) {
                        $q->select('id', 'product_id', DB::raw('SUM(quantity + incoming_stock) as stock'))
                            ->where('warehouse_id', $this->params['warehouse_id'])
                            ->groupBy('product_id');
                    }, 'brand:id,name',
                    'productStyle:id,name,description'
                ])
                ->first();
            if (!$product) {
                echo 'Product not found: '. $this->params['product_id'] . "\n";
                return;
            }
            $stock = "0";
            if (count($product->productQuantities) > 0 && $product->productQuantities->first()->stock > 0 && in_array($this->params['event'], [Product::STATUS_IN_STOCK, Product::STATUS_DISCONTINUED])) {
                $stock = $product->productQuantities->first()->stock >= 250 ? "9999" : $product->productQuantities->first()->stock;
                }
            $dataEvent = [
                'uuid' => Str::uuid(),
                'name' => 'facility_stock_notify',
                'timestamp' => $this->params['current_time']->toISOString(),
            ];
            $dataProduct = [
                'name' => $product->productStyle->description ?? null,
                'sku' => $product->sku,
                'color' => $product->color ?? null,
                'style' => $product->style ?? null,
                'size' => $product->size ?? null,
                'brand' => $product->brand->name ?? null,
                'facility_code' => $warehouse->code,
                'stock_status' => $this->params['event'],
                'quantity' => $stock,
            ];
        
            $payload = json_encode([
                'event' => $dataEvent,
                'data' => $dataProduct,
            ]);

            $log = WebhookFacilityStockNotify::create([
                'product_id' => $product->id,
                'store_id' => $this->params['store_id'],
                'warehouse_id' => $this->params['warehouse_id'],
                'value' => $stock,
                'event' => $this->params['event'],
            ]);
            $log->save();
            $storeCallBack = DB::table('store_callback_url')
            ->select('store_callback_url.url','store.token')
            ->join('store', 'store_callback_url.store_id', '=', 'store.id')
            ->where('store.id', $this->params['store_id'])
            ->where('store_callback_url.event', Product::FACILITY_NOTIFY)
            ->first();
            $signature = hash_hmac('SHA256', $payload, $storeCallBack->token);
            $client = new Client();
            $response = $client->request('POST', $storeCallBack->url, [
                'body' => $payload,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'Swiftpod WebHook Agent 1.0',
                    'X-Swiftpod-Signature' => $signature
                ],
            ]);
            if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
                $log->status = WebhookFacilityStockNotify::STATUS_SUCCESS;
                $log->message = "Response status: " . $response->getStatusCode() . " Body: " . $response->getBody()->getContents();
                $log->save();
            } else {
                $log->message = "Product $product->id in storeId $this->params['store_id'] url: ".$storeCallBack->url." response status:".$response->getStatusCode()." Body:".$response->getBody()->getContents();
                $log->save();
            }
            echo 'Send notify facility stock for: '. $this->params['store_id'] . "\n";
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
        }    
    }
}
