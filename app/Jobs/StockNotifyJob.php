<?php

namespace App\Jobs;

use App\Models\Product;
use App\Models\Store;
use App\Models\WebhookFacilityStockNotify;
use App\Repositories\GelatoRepository;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class StockNotifyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $params;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            echo 'Send notify stock for: ' . $this->params['store_id'] . ' at: ' . $this->params['current_time'];
            $product = Product::select(['id', 'sku', 'name', 'style', 'brand_id', 'color', 'size'])
                ->where('id', $this->params['product_id'])
                ->with([
                    'productQuantities' => function ($q) {
                        $q->select('id', 'product_id', DB::raw('SUM(quantity + incoming_stock) as stock'))
                            ->groupBy('product_id');
                    }, 'brand:id,name',
                    'productStyle:id,name,description'
                ])
                ->first();

            if (!$product) {
                echo 'Product not found: ' . $this->params['product_id'] . "\n";

                return;
            }

            $stock = '0';
            if (count($product->productQuantities) > 0 && $product->productQuantities->first()->stock > 0) {
                $stock = $product->productQuantities->first()->stock >= 250 ? '9999' : $product->productQuantities->first()->stock;
            }
            $printAreas = [];
            if ($product->productStyle?->productPrintAreas) {
                $printAreas = $product->productStyle?->productPrintAreas->pluck('name')->toArray();
                foreach ($printAreas as &$printArea) {
                    $printArea = strtolower(str_replace(' ', '_', $printArea));
                }
            }
            $dataEvent = [
                'uuid' => Str::uuid(),
                'name' => 'stock_notify',
                'timestamp' => $this->params['current_time']->toISOString(),
            ];
            $dataProduct = [
                'id' => $product->id,
                'name' => $product->productStyle->description ?? null,
                'sku' => $product->sku,
                'color' => $product->color ?? null,
                'style' => $product->style ?? null,
                'size' => $product->size ?? null,
                'brand' => $product->brand->name ?? null,
                'print_areas' => $printAreas ?? null,
                'stock' => $stock,
                'status' => $this->params['event'],
            ];

            $payload = json_encode([
                'event' => $dataEvent,
                'data' => $dataProduct,
            ]);

            $log = WebhookFacilityStockNotify::create([
                'product_id' => $product->id,
                'store_id' => $this->params['store_id'],
                'warehouse_id' => null,
                'value' => $stock,
                'event' => $this->params['event'],
            ]);
            $log->save();

            $storeCallBack = DB::table('store_callback_url')
                ->select('store_callback_url.url', 'store.token')
                ->join('store', 'store_callback_url.store_id', '=', 'store.id')
                ->where('store.id', $this->params['store_id'])
                ->where('store_callback_url.event', Product::STOCK_NOTIFY)
                ->first();

            // ban stock notify cho gelato
            if ($this->params['store_id'] == Store::STORE_GELATO) {
                $gelatoRepo = new GelatoRepository();
                $gelatoRepo->stockNotifyGelato($product, $this->params['status'], $stock, $log);

                return;
            }
            $signature = hash_hmac('SHA256', $payload, $storeCallBack->token);
            $client = new Client();
            $response = $client->request('POST', $storeCallBack->url, [
                'body' => $payload,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'Swiftpod WebHook Agent 1.0',
                    'X-Swiftpod-Signature' => $signature
                ],
            ]);
            if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
                $log->status = WebhookFacilityStockNotify::STATUS_SUCCESS;
                $log->message = 'Response status: ' . $response->getStatusCode() . ' Body: ' . $response->getBody()->getContents();
                $log->save();
            } else {
                $log->message = "Product $product->id in storeId $this->params['store_id'] url: " . $storeCallBack->url . ' response status:' . $response->getStatusCode() . ' Body:' . $response->getBody()->getContents();
                $log->save();
            }
            echo 'Send notify stock stock for: ' . $this->params['store_id'];
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
        }
    }
}
