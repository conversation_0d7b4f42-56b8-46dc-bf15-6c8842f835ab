<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;

class InternalRequestNotification implements ShouldBroadcast
{
    use SerializesModels;

    private $data;
    public $broadcastQueue = 'internal-request-broadcast';

    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('internal_request');
    }

    public function broadcastWith()
    {
        return $this->data;
    }
}
