<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class SaleOrderClaimSupport extends Model
{
    use HasFactory;
    protected $table = 'sale_order_claim_support';

    protected $fillable = [
        'order_id',
        'store_id',
        'sale_order_item_id',
        'customer_email',
        'type',
        'issue',
        'solution',
        'additional_details',
        'error_log',
        'created_at',
        'updated_at',
        'ticket_number',
        'assign',
        'resolved_by',
        'resolved_at'
    ];
    //type
    const TYPE_ORDER_LOST_IN_TRANSIT = 1;
    const TYPE_ORDER_NEED_SOME_CARE = 2;
    //issue
    const ISSUE_Coloring_issue = 1;
    const ISSUE_MISSING_PRINT = 2;
    const ISSUE_WRONG_PRINT = 3;
    const ISSUE_WRONG_GARMENT = 4;
    const ISSUE_WRONG_ITEMS_SENT = 5;
    const ISSUE_WASHED_OUT_GARMENT = 6;
    const ISSUE_OTHER = 7;

    const JOB_SEND_MAIL_SELLER_SUPPORT = 'sent-mail-customer-support';
    public static  function getIssueArray()
    {
        return [
            self::ISSUE_Coloring_issue => 'Faded/Coloring issue',
            self::ISSUE_MISSING_PRINT => 'Missing print',
            self::ISSUE_WRONG_PRINT => 'Wrong print',
            self::ISSUE_WRONG_GARMENT => 'Wrong garment',
            self::ISSUE_WRONG_ITEMS_SENT => 'Wrong items sent',
            self::ISSUE_WASHED_OUT_GARMENT => 'Washed-out garment',
            self::ISSUE_OTHER => 'Other',
        ];
    }
        
    public function images(): hasMany
    {
        return $this->hasMany(SaleOrderClaimSupportImage::class, 'sale_order_claim_support_id');
    }
    public function store(): hasOne
    {
        return $this->hasOne(Store::class, 'id','store_id');
    }
    public function saleOrder(): hasOne
    {
        return $this->hasOne(SaleOrder::class, 'id','order_id');
    }
    public function feedback(): hasOne
    {
        return $this->hasOne(SaleOrderClaimSupportFeedback::class, 'sale_order_claim_support_id','id');
    }
    public function feedbackFiles(): hasMany
    {
        return $this->hasMany(SaleOrderClaimSupportFeedbackFile::class, 'sale_order_claim_support_id');
    }
    public function assigne(): hasOne
    {
        return $this->hasOne(Employee::class, 'id','assign');
    }
    public function resolveBy(): hasOne
    {
        return $this->hasOne(Employee::class, 'id','resolved_by');
    }

}
