<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PrintingPresetSku extends Model
{
    use HasFactory;

    protected $table = 'printing_preset_sku';

    protected $fillable = [
        'sku',
        'black_ink',
        'white_ink',
        'purple_ink',
        'mix_ink',
        'black_ink_xqc',
        'white_ink_xqc',
        'purple_ink_xqc',
        'mix_ink_xqc',
        'background_color',
        'front_size',
        'back_size',
        'inner_neck_label_size',
        'outer_neck_label_size',
        'left_sleeve_size',
        'right_sleeve_size',
        'pocket_size',
        'platen_front_size',
        'platen_back_size',
        'platen_left_sleeve_size',
        'platen_right_sleeve_size',
        'platen_inner_neck_label_size',
        'platen_outer_neck_label_size',
        'platen_pocket_size',
        'front_position',
        'back_position',
        'left_sleeve_position',
        'right_sleeve_position',
        'inner_neck_label_position',
        'outer_neck_label_position',
        'pocket_position',
        'user_id',
        'platen_dtf_f_size',
        'platen_dtf_b_size',
        'admin_edit_only'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'sku', 'sku');
    }

    public static function findByProductSku($product_sku)
    {
        return self::where('sku', $product_sku)->first();
    }
}
