<?php

namespace App\Models\SaleOrder;

use Illuminate\Support\Facades\DB;

class SaleOrderModel
{
    const TABLE_NAME = 'sale_order';

    /**
     * @param $rawData
     * @return int
     */
    public function insert($rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->insertGetId($rawData);
    }


    public function updateById($id, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->update($rawData);
    }

    public function getListOrderIdByListExternalId($listExternalId)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('external_id', $listExternalId)
            ->pluck('id');
    }

    public function getOrderById($id)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->first();
    }

    public function findItemByExternalIdAndAccountId($externalId, $accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('account_id', $accountId)
            ->where('external_id', $externalId)
            ->first();
    }

    public function findItemByExternalNumberAndAccountId($externalId, $accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('account_id', $accountId)
            ->where('external_number', $externalId)
            ->first()
            ;
    }

    public function countOrderByDateByAccount($accountShipStationId, $startDate, $endDate)
    {
        return DB::table(self::TABLE_NAME)
            ->where('account_id', $accountShipStationId)
            ->whereDate('order_date', '>=', $startDate)
            ->whereDate('order_date', '<', $endDate)
            ->count();
    }


    public function countTotalQuantitySaleOrder($dateStart, $dateEnd, $accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->leftJoin('sale_order_item', 'sale_order_item.order_id', '=', self::TABLE_NAME . '.id')
            ->whereBetween(self::TABLE_NAME . '.created_at', [$dateStart, $dateEnd])
            ->where('sale_order_item.barcode_item_status', 1)
            ->where(self::TABLE_NAME . '.account_id', $accountId)
            ->sum('sale_order_item.quantity');
    }

    public function countTotalQuantitySaleOrderByWarehouse($dateStart, $dateEnd, $warehouseId)
    {
        return DB::table(self::TABLE_NAME)
            ->whereBetween('created_at', [$dateStart, $dateEnd])
            ->whereIn('order_status', ['new_order', 'in_production', 'shipped', 'on_hold'])
            ->where('warehouse_id', $warehouseId)
            ->sum('order_quantity');
    }

    public function countTotalBarcodeHaveSaleOrder($dateStart, $dateEnd, $accountId)
    {
        return DB::table(self::TABLE_NAME)
            ->leftJoin('sale_order_item_barcode', 'sale_order_item_barcode.order_id', '=', self::TABLE_NAME . '.id')
            ->whereBetween(self::TABLE_NAME . '.created_at', [$dateStart, $dateEnd])
            ->where(self::TABLE_NAME . '.account_id', $accountId)
            ->count();
    }
}
