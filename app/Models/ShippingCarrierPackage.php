<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class ShippingCarrierPackage extends Model
{
    use HasFactory;

    const PACKAGE_DEFAULT = 'Package';

    public $timestamps = true;

    protected $table = 'shipping_carrier_package';

    protected $fillable = [
        'name',
        'predefined_package',
        'dimensions',
        'weight',
        'is_default',
        'carrier_id',
        'created_at',
        'updated_at',
    ];

    const PACKAGE_LETTER_USPS = 'Letter';

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request['name'])) {
            $query->where("$this->table.name", 'LIKE', '%' . $request['name'] . '%');
        }

        if (!empty($request['predefined_package'])) {
            $query->where("$this->table.predefined_package", 'LIKE', '%' . $request['predefined_package'] . '%');
        }

        return $query;
    }

    public function shippingCarrier()
    {
        return $this->belongsTo(ShippingCarrier::class, 'carrier_id');
    }
}
