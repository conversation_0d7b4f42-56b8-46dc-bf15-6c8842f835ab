<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SupplyInventoryDeduction extends Model
{
    protected $table = 'supply_inventory_deduction';

    protected $fillable = [
        'warehouse_id',
        'user_id',
        'employee_id',
        'supply_id',
        'quantity',
        'box_id',
        'is_deleted'
    ];

    const IS_DELETED = 1;

    const IS_NOT_DELETED = 0;

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('warehouse', function ($query) {
            $request = request();
            if ($request->has('warehouse_id')) {
                $query->where('warehouse_id', $request->warehouse_id);
            } else {
                $query->where('warehouse_id', config('jwt.warehouse_id'));
            }
        });
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function supply()
    {
        return $this->belongsTo(Supply::class);
    }

    public function supplyBox()
    {
        return $this->belongsTo(SupplyBox::class, 'box_id')->withTrashed();
    }

    public function scopeSearch($query, $request)
    {
        $query->when(!empty($request->keyword), function ($q) use ($request) {
            $q->whereHas('supply', function ($q) use ($request) {
                $q->where('sku', 'like', '%' . $request->keyword . '%')
                    ->orWhere('name', 'like', '%' . $request->keyword . '%');
            });
        });
        $query->when(!empty($request->box_barcode), function ($q) use ($request) {
            $q->whereHas('supplyBox', function ($q) use ($request) {
                $q->where('barcode', 'like', '%' . $request->box_barcode . '%');
            });
        });
        $query->when(!empty($request->date[0]), function ($q) use ($request) {
            $q->whereDate('created_at', '>=', $request->date[0]);
        });
        $query->when(!empty($request->date[1]), function ($q) use ($request) {
            $q->whereDate('created_at', '<=', $request->date[1]);
        });
        $query->when(!empty($request->export), function ($q) {
            $q->where('is_deleted', SupplyInventoryDeduction::IS_NOT_DELETED);
        });

        return $query;
    }
}
