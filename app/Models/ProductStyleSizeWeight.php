<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductStyleSizeWeight extends Model
{
    use HasFactory;

    protected $table = 'product_style_size_weights';

    const UNIT_OZ = 'oz';

    const UNIT_LB = 'lb';

    const LB_OZ = 15.99;

    const S3_BUCKET_URL = '/products/style-size-weights/';

    protected $fillable = [
        'product_style_id',
        'product_size_id',
        'single',
        'multiple',
        'history_file_id',
        'original_weight_unit'
    ];

    public function productStyle()
    {
        return $this->belongsTo(ProductStyle::class, 'product_style_id');
    }

    public function productSize()
    {
        return $this->belongsTo(ProductSize::class, 'product_size_id');
    }
}
