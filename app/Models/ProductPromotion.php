<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductPromotion extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'product_promotion';

    protected $fillable = [
        'product_id',
        'promotion_id',
        'status',
    ];

    const STATUS_ACTIVE = 1;

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function promotion()
    {
        return $this->belongsTo(Promotion::class, 'promotion_id');
    }
}
