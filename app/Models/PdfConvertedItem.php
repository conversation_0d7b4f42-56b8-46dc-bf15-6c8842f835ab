<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PdfConvertedItem extends Model
{
    protected $table = 'pdf_converted_item';

    protected $fillable = [
        'pdf_converted_id',
        'barcode_id',
        'print_side',
        'print_method',
        'convert_status',
        'label_id'
    ];

    public static function findByPdfConvertedId($pdfConvertedId)
    {
        return self::where('pdf_converted_id', $pdfConvertedId)->get();
    }

    public static function findLabelGenerated($labelId, $side, $printMethod)
    {
        return self::where('label_id', $labelId)
            ->where('convert_status', PdfConverted::ACTIVE)
            ->where('print_side', $side)
            ->where('print_method', $printMethod)
            ->first();
    }
}
