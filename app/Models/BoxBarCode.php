<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BoxBarCode extends Model
{
    use HasFactory;

    const NOT_DELETED = 0;
    const DELETED = 1;

    protected $table = 'box_barcode';

    protected $fillable = [
        'warehouse_id',
        'box_barcode_printed_id',
        'is_deleted',
        'convert_pdf_status',
        'retry_convert',
        'employee_id',
        'box_id',
        'box_barcode'
    ];

}