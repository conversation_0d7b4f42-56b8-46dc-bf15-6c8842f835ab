<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Printer extends Model
{
    use HasFactory;

    protected $table = 'printer';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'warehouse_id',
        'last_printer_active_at',
        'device_id'
    ];

    const PRINTER_TIME_OUT = 6; // seconds

    public function styles()
    {
        return $this->belongsToMany(ProductStyle::class, 'printer_product', 'printer_id', 'style_sku', 'id', 'sku')->withPivot('color_sku');
    }

    public function colors()
    {
        return $this->belongsToMany(ProductColor::class, 'printer_product', 'printer_id', 'color_sku', 'id', 'sku')->withPivot('style_sku');
    }

    public function printerProducts()
    {
        return $this->hasMany(PrinterProduct::class);
    }

    public static function findByNameAndWarehouse($name, $warehouseId)
    {
        return DB::table('printer')
            ->where('name', $name)
            ->where('warehouse_id', $warehouseId)
            ->first();
    }

    public static function findByArrayNameAndWarehouse($arrayName, $warehouseId)
    {
        return DB::table('printer')
            ->whereIn('name', $arrayName)
            ->where('warehouse_id', $warehouseId)
            ->pluck('id')
            ->toArray();
    }

    public static function listPrinter($limit, $warehouseId)
    {
        return self::with('styles:sku,name')
            ->where('warehouse_id', $warehouseId)
            ->paginate($limit)
            ->through(function ($printer) {
                $dataStyleName = [];
                $printer->styles->each(function ($style) use (&$dataStyleName) {
                    $style->pivot->color_name = ProductColor::where('sku', $style->pivot->color_sku)->first()->name;
                    $dataStyleName[] = $style->name;
                    return $style;
                });
                $printer->style_name = array_unique($dataStyleName);
                return $printer;
            });
    }

    public static function findByStyleColorWarehouse($barcodePrinted)
    {
        return self::whereHas('styles', function ($query) use ($barcodePrinted) {
            $query->where('printer_product.style_sku', $barcodePrinted->style_sku)
                ->where('printer_product.color_sku', $barcodePrinted->color_sku);
            })
            ->where('warehouse_id', $barcodePrinted->warehouse_id)
            ->first();
    }
}
