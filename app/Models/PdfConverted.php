<?php

namespace App\Models;

use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class PdfConverted extends Model
{
    use HasFactory;

    protected $table = 'pdf_converted';

    protected $fillable = [
        'quantity',
        'convert_status',
        'convert_percent',
        'convert_at',
        'download_status',
        'employee_convert_id',
        'warehouse_id',
        'download_at',
        'product_id',
        'employee_download_id',
        'quantity_input',
        'print_method',
        'user_id',
        'options',
        'type',
        'batch_number',
        'country_id',
        'position_illustrator',
        'code_wip'
    ];

    protected $casts = [
        'options' => 'array',
    ];

    const METHOD_UV = 'UV';

    const METHOD_DTF = 'DTF';

    const METHOD_NECK = 'NECK';

    const METHOD_FILM = 'FILM';

    const INACTIVE = 0;

    const ACTIVE = 1;

    const FAIL = 2;

    const TYPE_NECK = 'neck';

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function employeeConvert()
    {
        return $this->belongsTo(Employee::class, 'employee_convert_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function barcodes()
    {
        return $this->belongsToMany(SaleOrderItemBarcode::class, 'pdf_converted_item', 'pdf_converted_id', 'barcode_id')
            ->withPivot(['pdf_converted_id', 'barcode_id', 'print_side', 'print_method', 'convert_status'])
            ->withTimestamps();
    }

    public static function findToConvert()
    {
        return self::with('barcodes')
            ->where('convert_status', self::INACTIVE)
            ->where('print_method', self::METHOD_UV)
            ->where('type', ProductType::ORNAMENT)
            ->first();
    }

    public static function findToConvertSticker()
    {
        return self::with('barcodes')
            ->where('convert_status', self::INACTIVE)
            ->where('print_method', self::METHOD_UV)
            ->where('type', ProductType::STICKER)
            ->first();
    }

    public static function findToConvertDtf()
    {
        return self::with('barcodes')
            ->where('convert_status', self::INACTIVE)
            ->where('print_method', self::METHOD_DTF)
            ->where(function ($query) {
                $query->where('type', '!=', self::TYPE_NECK)
                    ->orWhereNull('type');
            })
            ->first();
    }

    public static function findToConvertDtfFilm()
    {
        return self::with('barcodes')
            ->where('convert_status', self::INACTIVE)
            ->where('print_method', self::METHOD_FILM)
            ->first();
    }

    public static function findToConvertDtfNeck()
    {
        return self::with('barcodes')
            ->where('convert_status', self::INACTIVE)
            ->where('print_method', self::METHOD_NECK)
            ->first();
    }

    public static function findLastCreatedByProductId($product_id, $warehouse_id)
    {
        return self::where('product_id', $product_id)
            ->where('warehouse_id', $warehouse_id)
            ->orderByDesc('id')
            ->first();
    }

    public static function listPdfOrnament($warehouse_id, $limit)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,sku'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('download_status', self::INACTIVE)
            ->whereNotNull('employee_convert_id')
            ->where('print_method', self::METHOD_UV)
            ->where('type', ProductType::ORNAMENT)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('convert_status', self::FAIL)
                        ->where('created_at', '>=', Carbon::now()->toDateString());
                });
                $query->orWhere('convert_status', '!=', self::FAIL);
            })
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listHistoryPdfOrnament($warehouse_id, $limit, $label_id)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,sku'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('pdf_converted_item.label_id', 'LIKE', '%' . $label_id . '%')
                        ->where('pdf_converted_item.convert_status', self::ACTIVE);
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('download_status', self::ACTIVE)
            ->whereNotNull('employee_convert_id')
            ->where('print_method', self::METHOD_UV)
            ->where('type', ProductType::ORNAMENT)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listSticker($warehouse_id, $limit)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,sku'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('download_status', self::INACTIVE)
            ->whereNotNull('employee_convert_id')
            ->where('print_method', self::METHOD_UV)
            ->where('type', ProductType::STICKER)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('convert_status', self::FAIL)
                        ->where('created_at', '>=', Carbon::now()->toDateString());
                });
                $query->orWhere('convert_status', '!=', self::FAIL);
            })
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listHistorySticker($warehouse_id, $limit, $label_id)
    {
        return self::with([
            'employeeConvert:id,name',
            'product:id,sku'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('pdf_converted_item.label_id', 'LIKE', '%' . $label_id . '%')
                        ->where('pdf_converted_item.convert_status', self::ACTIVE);
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('download_status', self::ACTIVE)
            ->whereNotNull('employee_convert_id')
            ->where('print_method', self::METHOD_UV)
            ->where('type', ProductType::STICKER)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listPdfDtf($warehouse_id, $limit, $printMethod)
    {
        return self::with([
            'employeeConvert:id,name'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('download_status', self::INACTIVE)
            ->whereNotNull('employee_convert_id')
            ->where('print_method', $printMethod)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('convert_status', self::FAIL)
                        ->where('created_at', '>=', Carbon::now()->toDateString());
                });
                $query->orWhere('convert_status', '!=', self::FAIL);
            })
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listHistoryPdfDtf($warehouse_id, $limit, $label_id, $print_method)
    {
        return self::with([
            'employeeConvert:id,name'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('pdf_converted_item.label_id', 'LIKE', '%' . $label_id . '%')
                        ->where('pdf_converted_item.convert_status', self::ACTIVE);
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('download_status', self::ACTIVE)
            ->whereNotNull('employee_convert_id')
            ->where('print_method', $print_method)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listPdfDtfNeck($warehouse_id, $limit)
    {
        return self::with([
            'employeeConvert:id,name',
            'country:id,name'
        ])
            ->where('warehouse_id', $warehouse_id)
            ->where('download_status', self::INACTIVE)
            ->whereNotNull('employee_convert_id')
            ->where('print_method', PrintMethod::NECK)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('convert_status', self::FAIL)
                        ->where('created_at', '>=', Carbon::now()->toDateString());
                });
                $query->orWhere('convert_status', '!=', self::FAIL);
            })
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function listHistoryPdfDtfNeck($warehouse_id, $limit, $label_id)
    {
        return self::with([
            'employeeConvert:id,name',
            'country:id,name'
        ])
            ->whereHas('barcodes', function ($query) use ($label_id) {
                if (!is_null($label_id)) {
                    $query->where('pdf_converted_item.label_id', 'LIKE', '%' . $label_id . '%')
                        ->where('pdf_converted_item.convert_status', self::ACTIVE);
                }
            })
            ->where('warehouse_id', $warehouse_id)
            ->where('download_status', self::ACTIVE)
            ->whereNotNull('employee_convert_id')
            ->where('print_method', PrintMethod::NECK)
            ->orderByDesc('id')
            ->paginate($limit);
    }

    public static function findToConvertAi($id)
    {
        return self::where('id', $id)
            ->where('convert_status', self::ACTIVE)
            ->where('download_status', self::ACTIVE)
            ->first();
    }
}
