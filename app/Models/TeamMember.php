<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamMember extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'name',
        'team_member_role_id',
        'username',
        'password',
        'store_ids',
        'status',
    ];

    protected $casts = [
        'store_ids' => 'array',
        'status' => 'boolean',
    ];

    protected $hidden = [
        'password',
    ];

    public function role()
    {
        return $this->belongsTo(TeamMemberRole::class, 'team_member_role_id');
    }
}
