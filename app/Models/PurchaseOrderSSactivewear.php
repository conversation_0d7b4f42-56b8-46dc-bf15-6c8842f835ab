<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseOrderSSactivewear extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'purchase_order_ssactivewear';

    const STATUS_FULL_DATA = 1;
    const STATUS_MISS_DATA = 0;
    const STATUS_INSERT_ORDER_SUCCESS = 1;
    const STATUS_INSERT_ORDER_ERROR = 0;

    protected $fillable = [
        'order_number',
        'invoice_number',
        'po_id',
        'log',
        'created_at',
        'updated_at',
    ];
}
