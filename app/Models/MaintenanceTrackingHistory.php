<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaintenanceTrackingHistory extends Model
{
    use HasFactory;

    protected $table = 'maintenance_tracking_history';
    protected $fillable = [
        'maintenance_tracking_id',
        'employee_id',
        'message',
        'type',
    ];

    const COMMENT_TYPE = 'comment';

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

}
