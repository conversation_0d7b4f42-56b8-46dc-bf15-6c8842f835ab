<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;

class ProductSkuShipstationNullModel
{
    public function insert($rawData)
    {
        return DB::table('product_sku_shipstation_null')
            ->insert($rawData)
        ;
    }

    public function findBySku($sku)
    {
        return DB::table('product_sku_shipstation_null')
            ->where('sku', $sku)
            ->count()
        ;
    }

    public function getListProductNotAlert()
    {
        return DB::table('product_sku_shipstation_null')
            ->select('product_sku_shipstation_null.*', 'sale_order.account_id')
            ->leftJoin('product', 'product.sku', '=', 'product_sku_shipstation_null.sku')
            ->join('sale_order_item_image', 'sale_order_item_image.product_sku', '=', 'product_sku_shipstation_null.sku')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_image.order_id')
            ->whereNull('product.sku')
            ->where('product_sku_shipstation_null.is_alert', 0)
            ->get()
        ;
    }

    public function updateByListId($listId)
    {
        return DB::table('product_sku_shipstation_null')
            ->whereIn('id', $listId)
            ->update(['is_alert' => 1])
        ;
    }
}
