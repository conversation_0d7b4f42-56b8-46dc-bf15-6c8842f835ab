<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseOrderBoxItem extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'purchase_order_box_item';

    protected $fillable = [
        'po_id',
        'po_box_id',
        'product_id',
        'sku',
        'external_sku',
        'gtin',
        'quantity',
        'created_at',
        'updated_at',
        'coo_id',
    ];
    public function purchaseOrder()
    {
        return $this->belongsTo(PurchaseOrder::class, 'po_id');
    }
    public function purchaseOrderBox()
    {
        return $this->belongsTo(PurchaseOrderBox::class, 'po_box_id');
    }
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
