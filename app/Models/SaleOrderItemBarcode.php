<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleOrderItemBarcode extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'sale_order_item_barcode';

    protected $fillable = [
        'id',
        'order_id',
        'order_item_id',
        'sku',
        'barcode_number',
        'order_quantity',
        'is_deleted',
        'barcode_status',
        'print_barcode_status',
        'created_at',
        'updated_at',
        'employee_pull_id',
        'pulled_at',
        'employee_pretreat_id',
        'pretreated_at',
        'employee_print_id',
        'printed_at',
        'employee_qc_id',
        'qc_at',
        'employee_ship_id',
        'shipped_at',
        'barcode_printed_id',
        'user_id',
        'print_barcode_at',
        'barcode_id',
        'label_id',
        'staged_at',
        'employee_scan_id',
        'scanned_at',
        'warehouse_id',
        'part_number_id',
        'print_method',
    ];

    const DELETED = 1;

    const LINE_PRODUCTION = [
        'print_barcode_at',
        'pulled_at',
        'pretreated_at',
        'scanned_at',
        'printed_at',
        'qc_at',
        'staged_at',
        'shipped_at',
        'folded_at'
    ];

    public function orderItem()
    {
        return $this->belongsTo(SaleOrderItem::class, 'order_item_id', 'id');
    }

    public function order()
    {
        return $this->belongsTo(SaleOrder::class, 'order_id', 'id');
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id', 'id');
    }

    public function saleOrderItemImage()
    {
        return $this->hasMany(SaleOrderItemImage::class, 'order_item_id', 'order_item_id');
    }

    public function productPrintSide()
    {
        return $this->hasOne(ProductPrintSide::class, 'code', 'print_side');
    }

    public function employeePull()
    {
        return $this->belongsTo(Employee::class, 'employee_pull_id', 'id');
    }

    public function rbtReceived()
    {
        return $this->hasOne(RbtReceived::class, 'order_id', 'order_id');
    }

    public function rbtWipReceived()
    {
        return $this->hasOne(RbtWipReceived::class, 'label_id', 'label_id');
    }
}
