<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EasyPostAdjustment extends Model
{
    use HasFactory;

    protected $table = 'easypost_adjustments';

    protected $fillable = [
        'invoice_date',
        'shipment_id',
        'label_date',
        'carrier_account_id',
        'carrier',
        'tracking_code',
        'carrier_invoice_id',
        'package_dispute_id',
        'status',
        'quoted_currency',
        'initially_paid_amount',
        'quoted_amount',
        'claimed_length',
        'claimed_width',
        'claimed_height',
        'claimed_weight',
        'claimed_package',
        'claimed_service',
        'final_invoice_amount',
        'captured_length',
        'captured_width',
        'captured_height',
        'captured_weight',
        'captured_package',
        'captured_service',
        'captured_currency',
        'adjustment_amount',
        'adjustment_reason',
        'initially_paid_payment_log',
        'refund_payment_log',
        'clawback_payment_log',
        'invoice_payment_log',
        'user_id',
        'user_parent_id',
        'created_at',
        'updated_at'
    ];
}
