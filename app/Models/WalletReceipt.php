<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WalletReceipt extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_id',
        'order_id',
        'transaction_id',
        'billing_address_id',
        'receipt_number',
        'amount',
        'data',
    ];

    public function transaction()
    {
        return $this->belongsTo(WalletTransaction::class, 'transaction_id', 'id');
    }

    public function order()
    {
        return $this->belongsTo(SaleOrder::class, 'order_id', 'id');
    }
}
