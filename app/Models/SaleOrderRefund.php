<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleOrderRefund extends Model
{
    use HasFactory;

    const AUTO_REFUND = 'auto';

    const MANUAL_REFUND = 'manual';

    protected $fillable = [
        'order_id',
        'refund_by',
        'type',
        'reason',
        'amount',
        'total_amount',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'refund_by', 'id');
    }
}
