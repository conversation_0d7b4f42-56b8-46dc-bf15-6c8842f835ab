<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScreenOrderAddress extends Model
{
    use HasFactory;

    protected $table = 'screen_order_address';

    protected $fillable = [
        'screen_order_id',
        'type',
        'company',
        'street1',
        'street2',
        'city',
        'state',
        'zipcode',
        'country',
    ];

    const TO_ADDRESS = 'to_address';

    const RETURN_ADDRESS = 'return_address';
}
