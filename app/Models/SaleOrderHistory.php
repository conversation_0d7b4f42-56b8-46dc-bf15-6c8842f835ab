<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SaleOrderHistory extends Model
{
    use HasFactory;

    const SYNC_ORDER_DESK_SHIPPED = 'sync_order_desk_shipped';

    protected $table = 'sale_order_history';

    const CREATE_COMMENT_TYPE = 'Create Comment';

    const UPDATE_ORDER_STATUS_TYPE = 'Update Order Status';

    const UPDATE_SHIPPING_METHOD_TYPE = 'Update Shipping Method';

    const UPDATE_TAG_TYPE = 'Update Tag';

    const UPDATE_NOTE_TYPE = 'Update Note';

    const UPDATE_INTERNAL_NOTE_TYPE = 'Update Internal Note';

    const UPDATE_ADDRESS_TYPE = 'Update Address';

    const VERIFY_ADDRESS_TYPE = 'Verify Address';

    const CREATE_LABEL = 'Create Label';

    const PRINT_LABEL = 'Print Label';

    const IMPORT_REROUTE = 'Import Reroute';

    const MANUAL_TRACKING_TYPE = 'Manual Tracking';

    const VOID_SHIPMENT_TYPE = 'Void Shipment';

    const UPDATE_ART_FILE_TYPE = 'Update Art File';

    const UPDATE_PRINT_FILE_TYPE = 'Update Print File';

    const UPDATE_IOSS_NUMBER_TYPE = 'Update Tax Indentifier';

    const UPLOAD_EMBROIDERY_FILE = 'Upload TBF File';

    const UPLOAD_EMBROIDERY_COLOR = 'Upload CT0 File';

    const UPLOAD_EMBROIDERY_EVIDENCE = 'Upload EMB File';

    const REJECT_EMBROIDERY_FILE = 'Reject Embroidery File';

    const APPROVE_EMBROIDERY_FILE = 'Aprove Embroidery File';

    const DETECT_SHIPPING_METHOD_FAIL = 'Detect shipping method fail';

    const DETECT_SHIPPING_METHOD_SUCCESS = 'Detect shipping method success';

    const MISSING_PRINT_METHOD = 'Missing Print Method';

    const DOWNLOAD_ERROR = 'Download Error';

    const DETECT_COLOR_ERROR = 'Detect Color Error';

    const UPLOAD_S3_ERROR = 'Missing Artwork S3';

    const DETECT_PRINT_METHOD_SUCCESS = 'detect_print_method_success';

    const DOWNLOAD_SUCCESS = 'download_success';

    const DETECT_COLOR_SUCCESS = 'detect_color_success';

    const UPLOAD_S3_SUCCESS = 'upload_s3_success';

    const CONVERT_DTF_IMAGE_ERROR = 'convert_dtf_image_error';

    const CONVERT_LATEX_IMAGE_ERROR = 'convert_latex_image_error';

    const READ_LABEL_ORDER = 'read_label_order';

    const DTG_TO_DTF = 'DTG_to_DTF';

    const PRINTING_PRINT_AREA = 'printing_print_area';

    const MANUAL_CREATE_ORDER_TYPE = 'Manual order';

    const UPDATE_ORDER_XQC_TYPE = 'Order XQC';

    const UPDATE_ORDER_TEST_TYPE = 'Order test';

    const UPDATE_ORDER_DEDUCTION_TYPE = 'Deduction';

    const UPDATE_ORDER_PRETREAT_TYPE = 'Pretreat';

    const UPDATE_ORDER_PRESS_TYPE = 'Press';

    const UPDATE_ORDER_PRINTED_TYPE = 'Printed';

    const UPDATE_ORDER_FOLDING_TYPE = 'Folding';

    const UPDATE_ORDER_QUALITY_TYPE = 'Quality';

    const UPDATE_ORDER_WIP_TYPE = 'WIP';

    const UPDATE_ORDER_DETECT_COLOR_TYPE = 'Detect Color';

    const UPDATE_ORDER_CONVERT_NECK_TYPE = 'Convert Neck';

    const UPDATE_ORDER_CONVERT_MUGS_TYPE = 'Convert Mugs';

    const BARCODE_ASSIGN = 'barcode_assign';

    const PRINT_LABEL_INSERT = 'Print insert';

    const PRINT_ORNAMENT = 'Print Ornament';

    const PRINT_NECK = 'Print NECK';

    const PRINT_DTF = 'Print DTF';

    public $timestamps = false;

    protected $fillable = [
        'user_id',
        'employee_id',
        'order_id',
        'type',
        'message',
        'created_at'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public static function findByOrderId($orderId)
    {
        return self::where('order_id', $orderId)->first();
    }
}
