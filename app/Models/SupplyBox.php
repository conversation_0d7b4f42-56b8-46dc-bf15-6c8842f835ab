<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupplyBox extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'supply_boxes';

    protected $fillable = [
        'warehouse_id',
        'barcode',
        'location_id',
        'supply_id',
        'quantity',
        'created_at'
    ];

    public function location()
    {
        return $this->belongsTo(SupplyLocation::class, 'location_id');
    }

    public function supply()
    {
        return $this->belongsTo(Supply::class, 'supply_id');
    }
}
