<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupplyInventoryAddition extends Model
{
    protected $table = 'supply_inventory_addition';

    const IS_DELETED = 1;

    const IS_NOT_DELETED = 0;

    protected $fillable = [
        'po_id',
        'sku',
        'quantity',
        'supply_id',
        'location_id',
        'box_id',
        'user_id',
        'warehouse_id',
        'is_deleted',
        'employee_id'
    ];

    protected static function boot()
    {
        parent::boot();
        static::addGlobalScope('warehouse', function ($query) {
            $request = request();
            if ($request->has('warehouse_id')) {
                $query->where('warehouse_id', $request->warehouse_id);
            } else {
                $query->where('warehouse_id', config('jwt.warehouse_id'));
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(SupplyLocation::class, 'location_id');
    }

    public function box(): BelongsTo
    {
        return $this->belongsTo(SupplyBox::class, 'box_id');
    }

    public function supplyPurchaseOrder(): BelongsTo
    {
        return $this->belongsTo(SupplyPurchaseOrder::class, 'po_id');
    }

    public function supply(): BelongsTo
    {
        return $this->belongsTo(Supply::class, 'supply_id');
    }

    public function supplyPurchaseOrderItem()
    {
        return $this->belongsTo(SupplyPurchaseOrderItem::class, 'po_id', 'po_id')
            ->where('supply_id', $this->supply_id);
    }

    public function scopeSearch($query, $request)
    {
        $query->when(!empty($request->po_number), function ($q) use ($request) {
            $q->whereHas('supplyPurchaseOrder', function ($q) use ($request) {
                $q->where('po_number', 'like', '%' . $request->po_number . '%');
            });
        });
        $query->when(!empty($request->invoice_number), function ($q) use ($request) {
            $q->whereHas('supplyPurchaseOrder', function ($q) use ($request) {
                $q->where('invoice_number', 'like', '%' . $request->invoice_number . '%');
            });
        });
        $query->when(!empty($request->supply), function ($q) use ($request) {
            $q->whereHas('supply', function ($q) use ($request) {
                $q->where('sku', 'like', '%' . $request->supply . '%')
                    ->orWhere('name', 'like', '%' . $request->supply . '%');
            });
        });

        $query->when(!empty($request->date[0]), function ($q) use ($request) {
            $q->whereDate('created_at', '>=', $request->date[0]);
        });
        $query->when(!empty($request->date[1]), function ($q) use ($request) {
            $q->whereDate('created_at', '<=', $request->date[1]);
        });
        $query->when(!empty($request->export), function ($q) {
            $q->where('is_deleted', self::IS_NOT_DELETED);
        });
        $query->when(!empty($request->sku), function ($q) use ($request) {
            $q->where('sku', 'like', '%' . $request->sku . '%');
        });

        return $query;
    }
}
