<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    protected $table = 'setting';

    protected $fillable = [
        'warehouse_id',
        'group_id',
        'label',
        'name',
        'value',
        'created_at',
        'updated_at',
    ];

    const EASYPOST_API_KEY = 'easypost_api_key';

    const ALERT_SLACK_EASYPOST = 'alert_slack_easypost';

    const ALERT_GOOGLE_CHAT_EASYPOST = 'alert_google_easypost';

    const MODE_GET_ORDER_FOR_AUTO_SHIPPING = 'mode_auto_shipping';

    const ALERT_EXCEPTION_SLACK = 'alert_inventory_api_exception';

    const ALERT_REJECT_DESIGN_GG_CHAT = 'group_alert_reject_design';

    const ALERT_GOOGLE_CHAT_ORDER_DESK_ERROR_SYNC = 'alert_order_desk_error_sync';

    const GLOBAL = 0;

    const ORDER_PRINTED = 'order_printed';

    const ORDER_PULLED_STAGED = 'order_pulled_staged';

    const BACKLOG_LABEL = 'backlog_ip_allow';

    const NOTIFY_SLACK_TICKET = 'notification_ticket';

    const NOTIFY_GOOGLE_CHAT_TICKET = 'notification_google_chat';

    const ALERT_ORDER_MISS_SHIPSTATION_TO_SLACK = 'alert_miss_order_shipstation';

    const CONDITION_GET_ADDRESS_TO_FOR_CREATE_LABEL = 'condition_get_address_to_for_label_ship_auto';

    const AlERT_CHANGE_STATUS_CREATE_LABEL_IN_STORE = 'alert_store_change_status_create_label';

    const AlERT_UPDATE_STORE = 'alert_store_update';

    const PRODUCT_SKIP_NOT_AUTO_CREATE_SHIPPING = 'product_skip_not_auto_create_shipping';

    const ALERT_DOWNLOAD_INVOICE_GOOGLE_CHAT = 'alert_download_invoice_google_chat_group';

    const TEST_WIP_SKU = 'test_wip_sku';

    const START_ID_SYNC_TRACKING = 'start_id_sync_tracking';

    const END_ID_SYNC_TRACKING = 'end_id_sync_tracking';

    const STORE_HARD_GOODS_SHIP_USPS = 'store_hard_goods_ship_usps';

    const STORE_USE_ACCOUNT_SHIP_SWIFTPOD = 'store_use_account_ship_swiftpod'; // TH store dùng tài khoản ship của swift pod, account của carrier cũng của swift pod

    const PRIORITY_STORE = 'store_seperate_wip';

    const GOOGLE_SPACE_RB_ORDER_FAIL = 'alert_fail_redbubble_create_order';

    const ALERT_UPDATE_PRINT_AREA = 'alert_update_print_area';

    const SUPPORT_EMAIL = 'support_email';

    const STYLE_SKU_ORNAMENT = 'style_sku_ornament';

    const STYLE_SKU_ORNAMENT_CERAMIC = 'style_sku_ornament_ceramic';

    const EMAIL_RECIPIENT_IP_VIOLATION_PRINTIFY = 'email_recipient_ip_violation_printify';

    const EMAIL_CC_IP_VIOLATION_PRINTIFY = 'email_cc_ip_violation_printify';

    const LOCATION_MAX_BOX = 'location_max_box';

    const RBT_MULTIPLE_ORDER = 'rbt_multiple_order';

    const ACCOUNTING_EMAIL = 'accounting_email';

    const INVOICING_EMAIL = 'invoicing_email';

    const QUICK_BOOK_ALERT_ISSUE = 'quick_book_alert_issue';

    const QUICK_BOOK_FEE_SERVICE_ID_REF = 'quick_book_fee_service_id_ref';

    const QUICK_BOOK_DISCOUNT_SERVICE_ID_REF = 'quick_book_discount_service_id_ref';

    const QUICK_BOOK_LOGIN_TOKEN_VERIFY = 'quick_book_login_token_verify';

    const QUICK_BOOK_WEBHOOK_TOKEN_VERIFY = 'quick_book_webhook_token_verify';

    const QUICK_BOOK_ENABLE_SYNC_AUTOMATIC = 'quick_book_enable_sync_automatic';

    const TOPUP_ALERT_GOOGLE_SPACE = 'topup_alert_google_space';

    const SEND_RECYCLED_TRACKING_NUMBER = 'alert_recycled_tracking_number';

    const IP_RESTRICTION = 'ip_restriction';

    const READ_LABEL_SHIPMENT_JSON = 'read_label_shipment_json';

    const PRINT_CONFIGURATION = 'print_configuration';
}
