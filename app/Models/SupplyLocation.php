<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupplyLocation extends Model
{
    use HasFactory, SoftDeletes;

    const MAX_CREATE_PER_REQUEST = 1000;

    protected $table = 'supply_locations';

    protected $fillable = [
        'warehouse_id',
        'barcode',
        'deleted_at',
        'created_at',
        'updated_at',
    ];

    public function locationQuantities()
    {
        return $this->hasMany(SupplyLocationQuantity::class, 'location_id');
    }

    public function boxes()
    {
        return $this->hasMany(SupplyBox::class, 'location_id');
    }
}
