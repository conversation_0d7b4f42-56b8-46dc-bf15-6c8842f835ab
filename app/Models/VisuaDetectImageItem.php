<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VisuaDetectImageItem extends Model
{
    use HasFactory;

    protected $table = 'visua_detect_image_item';

    protected $fillable = [
        'visua_id',
        'visua_detect_image_id',
        'name',
        'url',
        'type',
        'confidence'
    ];

    const CONFIDENCE = 0.9;

    public function visuaDetect()
    {
        return $this->belongsTo(VisuaDetectImage::class, 'visua_detect_image_id', 'id');
    }

}
