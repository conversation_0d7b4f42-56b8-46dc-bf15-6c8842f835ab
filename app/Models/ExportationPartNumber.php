<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ExportationPartNumber extends Model
{
    use HasFactory;

    protected $table = 'exportation_part_number';

    protected $fillable = [
        'exportation_id',
        'part_number_id',
        'quantity',
        'created_at',
        'updated_at',
    ];

    public function partNumber() :hasOne
    {
        return $this->hasOne(PartNumber::class, 'id', 'part_number_id');
    }
}
