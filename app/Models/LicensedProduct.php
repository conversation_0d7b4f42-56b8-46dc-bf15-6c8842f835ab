<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LicensedProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'licensed_design_id',
        'product_sku',
        'style',
        'color',
        'size',
        'created_by',
    ];

    const STATUS_ACTIVE = 1;

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
