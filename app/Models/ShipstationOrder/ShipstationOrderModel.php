<?php

namespace App\Models\ShipstationOrder;

use Illuminate\Support\Facades\DB;

class ShipstationOrderModel
{
    const TABLE_NAME = 'shipstation_order';

    /**
     * @param $rawData
     * @return int
     */
    public function insert($rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->insert($rawData)
            ;
    }

    public function findAccountIdAndOrderNumber($accountId, $orderId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('account_id', $accountId)
            ->where('order_id', $orderId)
            ->first()
            ;
    }

    public function updateById($id, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->update($rawData)
            ;
    }

}
