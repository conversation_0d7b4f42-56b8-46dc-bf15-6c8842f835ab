<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SaleOrderAddress extends Model
{

    use HasFactory;

    protected $table = 'sale_order_address';

    const UNCONFIRMED = 0;
    const SUCCESS = 1;
    const HAS_PROBLEM = 2;
    const ERROR = 3;
    const IS_NOT_RESIDENTIAL = 0;
    const TO_ADDRESS = 'to_address';
    const RETURN_ADDRESS = 'return_address';

    const IN_US_NEED_CUSTOMS = ['APO','FPO','DPO']; // for city https://postcalc.usps.com/MilitaryRestrictions/Index
    const IN_US_NEED_CUSTOMS_FOR_STATE = ['AS', 'MP', 'PR', 'VI', 'AE', 'AP', 'AA', 'GU']; // for state https://pe.usps.com/text/pub28/28c2_010.htm
        //https://en.wikipedia.org/wiki/Territories_of_the_United_States
    protected $fillable = [
        'order_id',
        'type_address',
        'name',
        'email',
        'company',
        'phone',
        'street1',
        'street2',
        'city',
        'state',
        'zip',
        'country',
        'residential',
        'verified_status',
        'verified_message'
    ];

    public function saleOrder(): BelongsTo
    {
        return $this->belongsTo(SaleOrder::class, 'order_id');
    }
}
