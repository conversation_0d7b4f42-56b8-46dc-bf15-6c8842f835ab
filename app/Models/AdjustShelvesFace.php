<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class AdjustShelvesFace extends Model
{
    use HasFactory;

    protected $table = 'adjust_shelves_face';

    protected $fillable = [
        'warehouse_id',
        'product_id',
        'user_id',
        'sku',
        'product_available',
        'product_on_hand',
        'product_adjust',
        'employee_id',
        'country',
        'cost_value_on_hand',
        'cost_value_adjusted',
        'cost_value_available',
        'type'
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function employee(): HasO<PERSON>
    {
        return $this->hasOne(Employee::class, 'code', 'employee_id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country', 'iso2');
    }
}
