<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Models\DocCategory;

class Doc extends Model
{
    public $timestamps = false;
    use HasFactory;
    protected $table = 'doc';

    protected $fillable = [
        'name',
        'link',
        'doc_category_id',
        'language'
    ];

    public function docCategory()
    {
        return $this->belongsTo(DocCategory::class, 'doc_category_id', 'id');
    }
}
