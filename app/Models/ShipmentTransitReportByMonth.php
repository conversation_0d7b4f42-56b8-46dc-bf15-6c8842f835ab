<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShipmentTransitReportByMonth extends Model
{
    use HasFactory;

    protected $table = 'shipment_transit_report_by_month';
    protected $fillable = [
        'warehouse_id',
        'month_year',
        'destination_id',
        'shipment_carrier',
        'shipment_service',
        'total_transit_day',
        'total_fulfillment_day',
        'total_shipments',
    ];
}
