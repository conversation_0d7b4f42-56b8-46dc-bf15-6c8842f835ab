<?php

namespace App\Models\SaleOrderItem;

use Illuminate\Support\Facades\DB;

class SaleOrderItemModel
{
    const TABLE_NAME = 'sale_order_item';

    /**
     * @param $rawData
     * @return int
     */
    public function insert($rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->insertGetId($rawData);
    }

    public static function getIDBeforeDays($days = 2)
    {
        // Find sale order item id before 2 days
        $result = DB::table(self::TABLE_NAME)->select(DB::raw('MIN(id) as id'))
            ->where('created_at', '>', now()->subDays($days))
            ->where('id', '>', env('SKIP_ORDER_ITEM_ID', 6934193))
            ->first();

        if ($result) {
            return $result->id;
        }

        return env('SKIP_ORDER_ITEM_ID', 6934193);

    }


    public function getOrderItemNotSyn()
    {
        $time = date('Y-m-d H:i:s', time() - 60);
        // get order item id before 2 days

        $skipId = $this->getIDBeforeDays(2);


        return DB::table(self::TABLE_NAME)
            ->select('sale_order_item.*')
            ->join('sale_order', 'sale_order.id', 'sale_order_item.order_id')
            ->where('sale_order_item.created_at', '<', $time)
            ->where('sale_order_item.id', '>', $skipId ) // 2022-11-15
            ->where('sale_order_item.barcode_item_status', 0)
            ->where('sale_order_item.quantity', '<>', 0)
            ->where('sale_order.source', 'shipstation')
            ->whereNotNull('sale_order.order_number')
            ->orderBy('sale_order_item.id', 'DESC')
            ->first();
    }

    public function getItemByOrderId($orderId)
    {
        return DB::table(self::TABLE_NAME)
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->select('sale_order_item.*', 'sale_order.order_date', 'sale_order.order_number')
            ->where('sale_order_item.order_id', $orderId)
            ->orderBy('sale_order_item.id', 'ASC')
            ->get();
    }

    public function findItemByOrderItemId($orderItemId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('external_id', $orderItemId)
            ->first();
    }

    public function updateById($id, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->update($rawData);
    }

    public function updateByOrderId($orderId, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->where('order_id', $orderId)
            ->update($rawData);
    }

    public function updateByListId($listId, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('id', $listId)
            ->update($rawData);
    }

    public function updateBySku($sku, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->where('sku', $sku)
            ->update($rawData);
    }

    public function getItemBySku($sku)
    {
        return DB::table(self::TABLE_NAME)
            ->select(self::TABLE_NAME . '.*', 'product.name', 'sale_order_item_image.order_date')
            ->join('product', 'product.id', '=', self::TABLE_NAME . '.product_id')
            ->leftJoin('sale_order_item_image', 'sale_order_item_image.sku', '=', self::TABLE_NAME . '.sku')
            ->where(self::TABLE_NAME . '.sku', $sku)
            ->first();
    }
}
