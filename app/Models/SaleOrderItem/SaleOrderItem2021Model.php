<?php

namespace App\Models\SaleOrderItem;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class SaleOrderItem2021Model extends Model
{
    protected $table = 'sale_order_item_2021';
    const TABLE_NAME = 'sale_order_item_2021';

    /**
     * @param $rawData
     * @return int
     */
    public function insert($rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->insertGetId($rawData);
    }



    public function updateById($id, $rawData)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->update($rawData);
    }


}
