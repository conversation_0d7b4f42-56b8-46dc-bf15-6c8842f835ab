<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EmbroideryHistory extends Model
{
    use HasFactory;
    protected $table = 'embroidery_history';

    protected $fillable = [
        'task_id',
        'action',
        'embroidery_user_id',
        'note'
    ];

    // Define relationships if any
    // For example:
    // public function task()
    // {
    //     return $this->belongsTo(TaskEmbroidery::class, 'task_id');
    // }

    // public function employee()
    // {
    //     return $this->belongsTo(User::class, 'employee_id');
    // }
}