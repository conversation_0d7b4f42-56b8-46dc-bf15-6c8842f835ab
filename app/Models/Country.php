<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'country';

    protected $fillable = [
        'name',
        'iso3',
        'iso2',
        'phonecode',
        'capital',
        'currency',
        'native',
        'region',
        'subregion',
        'emoji',
        'date_created',
        'date_modified',
        'flag',
        'wikiDataId',
        'code',
    ];

    public function pdfConverteds()
    {
        return $this->hasMany(PdfConverted::class, 'country_id', 'id');
    }

    public static function findByName($name)
    {
        return self::where('name', $name)->first();
    }

    public function partNumbers()
    {
        return $this->hasMany(PartNumber::class, 'country', 'iso2');
    }

}
