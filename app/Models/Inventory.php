<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Inventory extends Model
{
    const DIRECTION_INPUT = 0;

    const DIRECTION_OUTPUT = 1;

    const TYPE_INPUT = 'input';

    const TYPE_OUTPUT = 'output';

    const TYPE_ADJUST = 'adjust';

    const OBJECT_ADDITION = 'addition';

    const OBJECT_ADJUST_PULLING_SHELVES = 'adjust pulling shelves';

    const OBJECT_CREATE_BOX = 'create box';

    const OBJECT_DEDUCTION = 'deduction';

    const OBJECT_TEST_COUNT = 'test count';

    const OBJECT_WORK_ORDER = 'work order';

    const OBJECT_STOCK_TRANSFER = 'stock transfer';

    use HasFactory;

    public $timestamps = false;

    protected $table = 'inventory';

    protected $fillable = [
        'direction',
        'type',
        'location_id',
        'product_id',
        'warehouse_id',
        'created_at',
        'is_deleted',
        'box_id',
        'user_id',
        'object_id',
        'object_name',
        'quantity',
        'new_product',
    ];
}
