<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupplyTestCount extends Model
{
    use HasFactory;

    protected $table = 'supply_test_counts';

    protected $fillable = [
        'box_available',
        'box_on_hand',
        'location_id',
        'note',
        'warehouse_id',
        'employee_id'
    ];

    public function supply()
    {
        return $this->belongsTo(Supply::class, 'supply_id', 'id');
    }

    public function supplyTestCountItems()
    {
        return $this->hasMany(SupplyTestCountItem::class, 'test_count_id', 'id');
    }

    public function supplyLocation()
    {
        return $this->belongsTo(SupplyLocation::class, 'location_id', 'id');
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'code');
    }
}
