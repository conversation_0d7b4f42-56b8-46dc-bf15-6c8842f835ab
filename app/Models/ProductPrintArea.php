<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductPrintArea extends Model
{
    use HasFactory;

    protected $table = 'product_print_area';

    protected $fillable = [
        'name',
        'platen_size',
        'print_size',
        'product_style_id',
        'product_image',
        'platen_position',
        'platen_area_size',
        'print_method',
        'rotate'
    ];

    public const PRODUCT_PRINT_AREA_FOLDER = 'ProductPrintArea/';

    public function productStyle(): BelongsTo
    {
        return $this->belongsTo(ProductStyle::class, 'product_style_id');
    }

    public function productPrintSide(): BelongsTo
    {
        return $this->belongsTo(ProductPrintSide::class, 'name', 'name');
    }

    public function storeProduct()
    {
        return $this->hasOne(StoreProduct::class, 'product_print_area_id');
    }

    public static function getPrintAreaByStyleId($styleId)
    {
        return self::where('product_style_id', $styleId)->pluck('name')->toArray();
    }
}
