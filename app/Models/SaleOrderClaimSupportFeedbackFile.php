<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleOrderClaimSupportFeedbackFile extends Model
{
    use HasFactory;

    protected $table = 'sale_order_claim_support_feedback_file'; // If your table name differs

    protected $fillable = [
        'sale_order_claim_support_id',
        'file',
        'created_at',
        'updated_at'
    ];
    const FILE_PATH = 'ClaimSupport';

}
