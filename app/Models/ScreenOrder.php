<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScreenOrder extends Model
{
    use HasFactory;

    protected $table = 'screen_orders';

    protected $fillable = [
        'order_number',
        'warehouse_id',
        'order_status',
        'external_number',
        'client_id',
        'buyer_name',
        'order_type',
        'encode_id',
        'shipped_at',
        'order_quantity',
        'pay_terms',
        'freight_terms',
        'tax_rate',
        'tax',
        'shipping_and_handling_fee',
        'other_fee',
        'client_note',
        'internal_note',
        'ship_via',
        'free_on_board',
        'order_total',
        'user_id',
    ];

    const MAP_KEYS = [
        'order_status' => 'Order Status',
        'external_number' => 'Customer\'s reference number',
        'client_id' => 'Client',
        'buyer_name' => 'Buyer',
        'order_type' => 'Order Type',
        'shipped_at' => 'Shipped by',
        'order_quantity' => 'Quantity',
        'pay_terms' => 'Pay Terms',
        'freight_terms' => 'Freight Terms',
        'tax_rate' => 'Tax Rate',
        'tax' => 'Tax',
        'shipping_and_handling_fee' => 'S&H',
        'other_fee' => 'Other fees',
        'client_note' => 'Client comments',
        'internal_note' => 'Internal comments',
        'ship_via' => 'Ship Via',
        'free_on_board' => 'F.O.B',
        'order_total' => 'Total value of order',

    ];

    const HEADER = [
        'Customer\'s reference number' => true,
        'Client' => true,
        'Buyer' => true,
        'Order Type' => true,
        'Shipped by' => true,
        'SKU' => true,
        'Design ID 1' => false,
        'Print area key 1' => false,
        'Mockup ID 1' => false,
        'Quantity' => true,
        'Unit Cost' => true,
        'Address 1' => true,
        'Address 2' => false,
        'City' => true,
        'State' => false,
        'ZipCode' => true,
        'Country' => true,
        'Client comments' => false,
        'Internal comments' => false,
        'Ship Via' => true,
        'FOB' => true,
        'Pay Terms' => true,
        'Freight Terms' => false,
        'Tax Rate' => false,
        'Tax' => false,
        'S & H' => false,
        'Other fees' => false,
        'Total value of order' => false,
    ];

    const NEW_ORDER_STATUS = 'new_order';

    const IN_PRODUCTION_STATUS = 'in_production';

    const READY_TO_STATUS = 'ready_to_ship';

    const COMPLETED_STATUS = 'completed';

    const CANCELLED_STATUS = 'cancelled';

    const TYPE = 'SCPR';

    const STATUS = [
        self::NEW_ORDER_STATUS => 'New Order',
        self::IN_PRODUCTION_STATUS => 'In Production',
        self::READY_TO_STATUS => 'Ready To Ship',
        self::COMPLETED_STATUS => 'Completed',
        self::CANCELLED_STATUS => 'Cancelled',
    ];

    const ORDER_ECOM_TYPE = 'ecom';

    const ORDER_STORE_TYPE = 'store';

    public function client()
    {
        return $this->belongsTo(ScreenClient::class, 'client_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function items()
    {
        return $this->hasMany(ScreenOrderItem::class, 'screen_order_id');
    }

    public function images()
    {
        return $this->hasMany(ScreenOrderItemImage::class, 'screen_order_id');
    }

    public function shippingAddress()
    {
        return $this->hasMany(ScreenOrderAddress::class, 'screen_order_id')->where('type', ScreenOrderAddress::TO_ADDRESS)->limit(1);
    }

    public function returnAddress()
    {
        return $this->hasMany(ScreenOrderAddress::class, 'screen_order_id')->where('type', ScreenOrderAddress::RETURN_ADDRESS)->limit(1);
    }

    public function history()
    {
        return $this->hasMany(ScreenOrderHistory::class, 'screen_order_id')->orderByDesc('created_at');
    }

    public function packagings()
    {
        return $this->belongsToMany(ScreenPackaging::class, 'screen_order_packaging', 'screen_order_id', 'screen_packaging_id', 'id', 'id');
    }
}
