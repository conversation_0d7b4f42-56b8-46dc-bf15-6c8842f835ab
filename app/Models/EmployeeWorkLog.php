<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeWorkLog extends Model
{
    use HasFactory;

    protected $table = 'employee_work_logs';

    protected $fillable = [
        'employee_id',
        'warehouse_id',
        'department_id',
        'work_date',
        'task_type',
        'total_tasks',
        'single_order_tasks',
        'multiple_order_tasks',
        'total_hours_worked',
        'average_ink_usage',
    ];

    const DRAGON_FRUIT_PRINT_TASK = 'dragon_fruit_print';

    const ALL_IN_ONE_PRINT_TASK = 'all_in_one_print';

    const MUGS_PRINT_TASK = 'mugs_print';

    const NECK_PRINT_TASK = 'neck_print';

    const PRE_TREAT_TASK = 'pre-treat';

    const PULLING_TASK = 'pulling';

    const QC_TASK = 'qc';

    const LABEL_TASK = 'label';

    const FOLDING_TASK = 'folding';
}
