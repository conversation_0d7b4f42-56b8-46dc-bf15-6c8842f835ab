<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ImportOrderCsv extends Model
{
    use HasFactory;

    protected $table = 'import_order_csv';

    public $timestamps = true;

    protected $fillable = [
        'store_id',
        'file_name',
        'order_total',
        'order_imported',
        'order_failed',
        'file_imported',
        'file_failed',
    ];
}
