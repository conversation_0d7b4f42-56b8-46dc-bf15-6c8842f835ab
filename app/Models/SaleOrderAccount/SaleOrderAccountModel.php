<?php

namespace App\Models\SaleOrderAccount;

use Illuminate\Support\Facades\DB;

class SaleOrderAccountModel
{

    const TABLE_NAME = 'sale_order_account';

    public function getAllAccountActive()
    {
        return DB::table(self::TABLE_NAME)
            ->where('is_active', 1)
            ->where('source', 'shipstation')
            ->where('is_editing', '<>', 1)
            ->get()
        ;
    }
    public function getAllAccountActivePluck()
    {
        return DB::table(self::TABLE_NAME)
            ->where('is_active', 1)
            ->where('source', 'shipstation')
            ->where('is_editing', '<>', 1)
            ->pluck('name', 'id')
            ;
    }

    public function getAccountById($id)
    {
        return DB::table(self::TABLE_NAME)
            ->where('id', $id)
            ->first()
            ;
    }

    public function findTagXqc($listId)
    {
        return DB::table(self::TABLE_NAME)
            ->whereIn('id', $listId)
            ->where('name', 'Sample XQC')
            ->count()
        ;
    }
}
