<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LicensedDesign extends Model
{
    use HasFactory;

    protected $fillable = [
        'licensed_design_id',
        'licensed_holder_id',
        'licensed_holder',
    ];

    public function holder()
    {
        return $this->belongsTo(LicensedHolder::class, 'licensed_holder_id');
    }
}
