<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class ProductSkuMatching extends Model
{
    use HasFactory;

    protected $table = 'product_sku_matching';

    protected $fillable = [
        'swiftpod_sku',
        'store_id',
        'attribute'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'swiftpod_sku', 'sku');
    }

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request['swiftpod_sku'])) {
            $query->where("$this->table.swiftpod_sku", 'LIKE', '%' . $request['swiftpod_sku'] . '%');
        }

        if (!empty($request['zazzle_attribute'])) {
            $query->where("$this->table.attribute", 'LIKE', '%' . $request['zazzle_attribute'] . '%');
        }

        if (!empty($request['style'])) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('style', 'LIKE', '%' . $request['style'] . '%');
            });
        }
        if (!empty($request['color'])) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('color', 'LIKE', '%' . $request['color'] . '%');
            });
        }
        if (!empty($request['size'])) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('size', 'LIKE', '%' . $request['size'] . '%');
            });
        }

        return $query;
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
}
