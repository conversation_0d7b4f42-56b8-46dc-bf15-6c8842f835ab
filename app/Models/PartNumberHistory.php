<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class PartNumberHistory extends Model
{
    use HasFactory;

    protected $table = 'part_number_history';
    public $timestamps = false;

    protected $fillable = [
        'part_number_id',
        'action',
        'type',
        'quantity',
        'balance',
        'employee_id',
        'warehouse_id',
        'created_at',
        'order_id'
    ];

    const TYPE_IMPORT = 'import';
    const TYPE_EXPORT = 'export';

    const ACTION_ADDITION_PULLING_SHELVES = 'addition_to_pulling_shelves';
    const ACTION_ADJUST_PULLING_SHELVES = 'adjust_pulling_shelves';
    const ACTION_BOX_MOVING = 'box_moving_rack_to_pulling';
    const ACTION_QC_FAILED = 'qc_failed';
    const ACTION_EXPORTATION_REPORT = 'exportation_report';
    const ACTION_REVERT_BOX_MOVING  = 'revert_box_moving';
    const ACTION_CREATE_LABEL = 'create_label';

    public function scopeSearch($query, $request)
    {
        if (!empty($request['date']) && is_array($request['date'])) {
            $query->whereBetween('created_at', [$request['date'][0], Carbon::parse($request['date'][1])->endOfDay()]);
        }

        if (!empty($request['action'])) {
            $query->where('action', $request['action']);
        }

        if (!empty($request['type'])) {
            $query->where('type', $request['type']);
        }
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id');
    }

    public function partNumber(): BelongsTo
    {
        return $this->belongsTo(PartNumber::class, 'part_number_id', 'id');
    }
}
