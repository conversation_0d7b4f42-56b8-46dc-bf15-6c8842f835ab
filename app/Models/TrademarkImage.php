<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrademarkImage extends Model
{
    use HasFactory;

    protected $table = 'trademark_image';

    protected $fillable = [
        'image_hash_id',
        'trademark_id',
        'user_id'
    ];

    public function trademark()
    {
        return $this->hasOne(Trademark::class, 'trademark_id', 'id');
    }
}
