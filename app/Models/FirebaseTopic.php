<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FirebaseTopic extends Model
{
    use HasFactory;

    protected $table = 'firebase_topic';


    protected $fillable = [
        'name',
    ];

    public function tokens()
    {
        return $this->belongsToMany(FirebaseTokenDevice::class, 'firebase_token_topic', 'topic_id', 'token_id');
    }
}
