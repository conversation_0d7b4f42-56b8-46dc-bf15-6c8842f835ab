<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExportHistory extends Model
{
    use HasFactory;

    public const STATUS_PENDING = 'pending';

    public const STATUS_COMPLETED = 'completed';

    public const STATUS_FAILED = 'failed';

    public const MODULE_TEST_COUNT = 'test_count';

    public const MODULE_ADJUST_PULLING_SHELVES = 'adjust_pulling_shelves';

    public const MODULES = [
        self::MODULE_TEST_COUNT,
        self::MODULE_ADJUST_PULLING_SHELVES,
    ];

    protected $fillable = [
        'module',
        'user_id',
        'warehouse_id',
        'start_date',
        'end_date',
        'status',
        'file_path',
        'exception',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
