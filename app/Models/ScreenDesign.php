<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScreenDesign extends Model
{
    use HasFactory;

    protected $table = 'screen_designs';

    protected $fillable = [
        'client_id',
        'name',
        'design_id',
        'type',
        'design_url',
        'created_by',
    ];

    const CUSTOM_DESIGN_FOLDER = 'screen_designs';

    const DESIGN_TYPE = 'design';

    const MOCKUP_TYPE = 'mockup';

    public function client()
    {
        return $this->belongsTo(ScreenClient::class, 'client_id');
    }

    public function employeeCreated()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
