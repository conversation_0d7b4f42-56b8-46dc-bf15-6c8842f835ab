<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;

class StockTransfer extends Model
{
    use HasFactory;

    protected $table = 'stock_transfer';

    public $timestamps = true;

    const PENDING_STATUS = 'pending';
    const COMPLETED_STATUS = 'completed';
    const CANCELLED_STATUS = 'cancelled';
    const PARTIAL_COMPLETED_STATUS = 'partial_completed';

    protected $fillable = [
        'from_warehouse_id',
        'destination_warehouse_id',
        'request_number',
        'total_box',
        'total_quantity',
        'employee_id',
        'status',
        'total_box_received',
        'total_quantity_received',
        'fulfill_by',
    ];

    public function items(): HasMany
    {
        return $this->hasMany(StockTransferItem::class);
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'employee_id');

    }
    public function employeeFulfill(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'fulfill_by');

    }

    public function warehouseDestination(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'destination_warehouse_id');

    }
    public function fromWarehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'from_warehouse_id');
    }

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        if (!empty($request->warehouse_id)) {
            $query->where('from_warehouse_id', $request->warehouse_id);
        }
        if (!empty($request->employee_id)) {
            $query->where('employee_id', $request->employee_id);
        }
        if (!empty($request->status)) {
            if(is_array($request->status)){
                $query->whereIn('status', $request->status);
            }else{
                $query->where('status', $request->status);
            }
        }
        if (!empty($request->request_number)) {
            $query->where('request_number', $request->request_number);
        }
        if (!empty($request->sku)) {
            $query->whereHas('items', function ($q) use ($request) {
                $q->whereHas('product', function ($q1) use ($request){
                    $q1->where('sku', 'like', '%' . $request->sku . '%');
                });
            });
        }
        if (!empty($request->date[0])) {
            $query->whereDate('created_at', '>=', $request->date[0]);
        }
        if (!empty($request->date[1])) {
            $query->whereDate('created_at', '<=', $request->date[1]);
        }
        return $query;
    }

    public function fulfillLog(): HasMany
    {
        return $this->hasMany(StockTransferBoxLog::class);
    }
}
