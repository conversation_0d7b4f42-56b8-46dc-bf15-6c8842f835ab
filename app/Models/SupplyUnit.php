<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class SupplyUnit extends Model
{
    use HasFactory;

    protected $table = 'supply_unit';
    protected $fillable = ['name'];

    public function scopeSearch(Builder $query, Request $request): Builder
    {
        return $query->when($request->name, function ($query, $name) {
            return $query->where('name', 'like', "%$name%");
        });
    }
}
