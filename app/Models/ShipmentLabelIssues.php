<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShipmentLabelIssues extends Model
{
    use HasFactory;

    protected $fillable = [
        'shipment_id',
        'issue_type',
        'issues',
    ];

    protected $casts = [
        'issues' => 'json',
    ];

    const ISSUE_TYPE_DOWNLOAD = 'download';
    const ISSUE_TYPE_READ = 'read';

}
