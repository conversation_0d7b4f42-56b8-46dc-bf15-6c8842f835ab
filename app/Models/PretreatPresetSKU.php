<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class PretreatPresetSKU extends Model
{
    use HasFactory;

    protected $table = 'pretreat_preset_sku';

    protected $fillable = [
        'id',
        'pretreat_preset_id',
        'style',
        'color',
        'user_id',
    ];

   public function preset()
   {
       return $this->belongsTo(PretreatPreset::class, 'pretreat_preset_id');
   }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function productColor()
    {
        return $this->belongsTo(ProductColor::class, 'color', 'sku');
    }

    public function productStyle()
    {
        return $this->belongsTo(ProductStyle::class, 'style', 'sku');
    }
//
}
