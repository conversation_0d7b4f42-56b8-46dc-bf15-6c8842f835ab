<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Machine extends Model
{
    use HasFactory;

    protected $table = 'machine';
    protected $fillable = [
        'name',
        'series',
        'manufacturer',
        'status',
        'user_id',
        'bought_in',
        'location_id',
        'model'
    ];
    const ACTIVE = 'active';
    const INACTIVE = 'inactive';
    const FIXING = 'fixing';

    public function scopeSearch(Builder $query, $input)
    {
        if (!empty($input['name'])) {
            $query->where('name', 'like', '%' . $input['name'] . '%');
        }
        if (!empty($input['series'])) {
            $query->where('series', 'like', '%' . $input['series'] . '%');
        }
        if (!empty($input['manufacturer'])) {
            $query->where('manufacturer', 'like', '%' . $input['manufacturer'] . '%');
        }
        if (!empty($input['status'])) {
            $query->where('status', $input['status']);
        }
        if (!empty($input['location'])) {
            $query->where('location_id', $input['location']);

        }
        return $query;
    }

    public function location()
    {
        return $this->belongsTo(LocationMachine::class, 'location_id', 'id');
    }

    public function maintenancesTracking()
    {
        return $this->hasMany(MaintenanceTracking::class);
    }

}
