<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UvPresetSku extends Model
{
    use HasFactory;

    public $timestamps = true;

    protected $table = 'uv_preset_sku';

    protected $fillable = [
        'product_sku',
        'page_width',
        'page_height',
        'max_item_on_row',
        'max_row',
        'margin_x',
        'margin_y',
        'is_mirror',
        'is_circle'
    ];
}
