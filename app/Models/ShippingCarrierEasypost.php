<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ShippingCarrierEasypost extends Model
{
    use HasFactory;

    public $timestamps = true;

    protected $table = 'shipping_carrier_easypost';

    protected $fillable = [
        'name',
        'store_id',
        'carrier_id',
        'carrier_account',
        'status',
        'created_at',
        'updated_at',
        'warehouse_id',
        'integration_account_id',
        'api_key_easypost'
    ];

    const ACTIVE = 1;

    const DHL_CARRIER_ID = 12;

//    public function carrierPackage(): HasMany
//    {
//        return $this->hasMany(ShippingCarrierPredefinedPackage::class, 'carrier_id', 'carrier_id');
//    }

    public function carrier(): HasOne
    {
        return $this->hasOne(ShippingCarrier::class, 'id', 'carrier_id');
    }

    public function shippingIntegrationAccount(): BelongsTo
    {
        return $this->belongsTo(ShippingIntegrationAccount::class, 'integration_account_id', 'id');
    }

    public function shippingMethod(): HasMany
    {
        return $this->hasMany(ShippingMethod::class, 'name', 'name');
    }
}
