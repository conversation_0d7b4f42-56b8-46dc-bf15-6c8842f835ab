<?php

namespace App\Http\Middleware;

use App\Models\Store;
use App\Models\TeamMember;
use App\Models\TeamMemberRolePermission;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Log;
use Symfony\Component\HttpKernel\Exception\HttpException;

class AuthenticateSeller
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            if (!($token = $request->bearerToken())) {
                throw new HttpException(401, 'Unauthorized');
            }
            //check có login = support-login hay k
            $tokenDecrypt = decryptSalt($token);
            [$userId, $clientId, $storeId, $memberId, $createdTime] = explode(':', $tokenDecrypt);
            $message = null;

            if (!empty($userId)) {
                //support login
                $hasPermission = $this->checkMemberPermission(false, true, null);
                $message = "for user: {$userId}";
                if (!empty($storeId)) {
                    $store = Store::find($storeId);

                    if (!$store) {
                        throw new HttpException(401, 'Unauthorized');
                    }
                    $store['user_login_id'] = $userId;
                    auth()->setUser($store);
                } else {
                    $user = User::find($userId);

                    if (!$user) {
                        throw new HttpException(401, 'Unauthorized');
                    }
                    //save support user id
                    $user['user_login_id'] = $userId;
                    $user->is_login_support = 1;
                    auth()->setUser($user);
                }
            } else {
                $hasPermission = $this->checkMemberPermission(true, false, null);
                $message = "for client: {$clientId}";
                $condition = [
                    'is_active' => true,
                ];
                //support login has user id, login doesn't
                $condition['client_id'] = $clientId;
                $request->merge(['client_id' => $clientId]);
                $store = Store::whereId($storeId)->where($condition)->first();

                if (!$store) {
                    throw new HttpException(401, 'Unauthorized');
                }

                auth()->setUser($store);
            }

            //team member login
            if (!empty($memberId)) {
                $store->member_id = $memberId;
                $message = "for member: {$memberId}";
                $hasPermission = $this->checkMemberPermission(false, false, $memberId);
            }

            //check permission
            if (!$hasPermission) {
                Log::error("AuthenticateSeller: Forbidden Access {$message}");

                return response()->json([
                    'Forbidden Access'
                ], 403);
            }
        } catch (\Throwable $th) {
            Log::error('AuthenticateSeller: ' . $th->getMessage());

            return response()->json([
                'Unauthorized'
            ], 401);
        }

        return $next($request);
    }

    private function checkMemberPermission($isClient, $isSupport, $memberId)
    {
        $routerName = request()->route()->getName();
        $arrayRouter = explode('.', $routerName);
        $functionName = $arrayRouter[0] ?? '';
        $permissionName = $arrayRouter[1] ?? '';
        $routerAlias = $arrayRouter[2] ?? '';
        $loginSupportExceptionRouter = [
            'cancel_order',
            'submit_order',
            'update_shipping_method',
        ];

        //clientPermission
        if ($isClient) {
            return true;
        }

        if ($isSupport) {
            //support login only has the permission for viewing
            if ($permissionName == 'view' || $functionName == 'no_permission' || in_array($routerAlias, $loginSupportExceptionRouter)) {
                return true;
            }

            return false;
        }

        //check member permission
        $member = TeamMember::with('role.permissions')->where('id', $memberId)->firstOrFail();

        if ($functionName == 'no_permission' || $member->role->name == 'Admin') {
            return true;
        }

        if ($member->role->permissions->isEmpty() || empty($member->status)) {
            return false;
        }

        foreach ($member->role->permissions as $permission) {
            if (in_array($permission->permission, [TeamMemberRolePermission::ALL_PERMISSION, $permissionName]) && $permission->function_name == $functionName) {
                return true;
            }
        }

        return false;
    }
}
