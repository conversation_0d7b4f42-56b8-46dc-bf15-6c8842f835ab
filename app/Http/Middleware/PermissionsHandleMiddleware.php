<?php


namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Dflydev\DotAccessData\Data;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Role;


class PermissionsHandleMiddleware
{


    public function handle(Request $request, Closure $next)
    {
        try {
            $name = Route::currentRouteName();
            $user = auth()->user();
            $role = $user->getRoleNames()->first(); // 1 user only in 1 role

            if($name == 'select_warehouse' || $name == null || $user->is_admin === 1){
                return $next($request);
            }

            if($role !== 'super admin'){
               $permissions = $user->getPermissionsViaRoles()->pluck('name')->toArray();
               $data = in_array($name, $permissions);
                if(!$data){
                    return response()->json([
                        'message' => 'You do not have permission to access'
                    ], 403);
                }
            }

        }catch (\Exception $e) {
            return response()->json([ $e->getMessage()], 401);
        }

        return $next($request);
    }
}
