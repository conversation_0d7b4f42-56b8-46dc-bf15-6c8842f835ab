<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Requests\Web\SupplyInkConsumptionForecastRequest;
use App\Http\Requests\Web\SupplyInkConsumptionRequest;
use App\Http\Requests\Web\SupplyLoginRequest;
use App\Models\SupplyInkReport;
use App\Repositories\SupplyAccountRepository;
use App\Repositories\SupplyReportRepository;
use App\Repositories\WarehouseRepository;
use Illuminate\Support\Facades\Auth;

class SupplyController extends Controller
{
    protected $supplyReportRepository;

    protected $supplyAccountRepository;

    protected $warehouseRepository;

    public function __construct(
        SupplyReportRepository $supplyReportRepository,
        SupplyAccountRepository $supplyAccountRepository,
        WarehouseRepository $warehouseRepository
    ) {
        $this->supplyReportRepository = $supplyReportRepository;
        $this->supplyAccountRepository = $supplyAccountRepository;
        $this->warehouseRepository = $warehouseRepository;
    }

    public function index(SupplyInkConsumptionRequest $request)
    {
        $categories = [1 => 'Color Ink', 2 => 'White Ink'];
        $year = empty($request->query('year')) ? now()->format('Y') : $request->query('year');
        $dataReport = $this->supplyReportRepository->getInkConsumption($year);
        $dataReport = json_encode($dataReport);

        return view('supply.index', compact('categories', 'dataReport'));
    }

    public function forecast(SupplyInkConsumptionForecastRequest $request)
    {
        $assigns['categories'] = SupplyInkReport::INK_COLOR_TYPES;
        $assigns['warehouses'] = $this->warehouseRepository->listAll();
        $assigns = $assigns + $this->supplyReportRepository->getInkConsumptionForecast($request->all());

        return view('supply.forecast', $assigns);
    }

    public function loginForm()
    {
        $status = Auth::guard('supply')->check();

        if ($status) {
            return redirect(route('supply.report'));
        }

        return view('supply.login_form');
    }

    public function login(SupplyLoginRequest $request)
    {
        $status = Auth::guard('supply')->attempt([
            'username' => $request->input('email'),
            'password' => $request->input('password'),
            'status' => 1
        ]);

        if (!$status) {
            return redirect()->back()->withErrors(['login_failed' => __('auth.password')]);
        }

        $this->supplyAccountRepository->updateLoginTime(Auth::guard('supply')->id());

        return redirect(route('supply.report'));
    }

    public function logout()
    {
        Auth::guard('supply')->logout();

        return redirect(route('supply.login_form'));
    }
}
