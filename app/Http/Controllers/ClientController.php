<?php

namespace App\Http\Controllers;

use App\Repositories\ClientRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ClientController extends Controller
{
    protected $clientRepository;

    public function __construct(ClientRepository $clientRepository)
    {
        $this->clientRepository = $clientRepository;
    }

    public function index(Request $request)
    {
        $data = $this->clientRepository->getList($request->all());

        return response()->json($data);
    }

    public function getAll()
    {
        $data = $this->clientRepository->getAll();

        return response()->json($data);
    }

    public function show(Request $request)
    {
        $data = $this->clientRepository->show($request->id);

        return response()->json($data);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:clients,name',
            'username' => 'required|string|max:255|unique:clients,username',
            'password' => 'required|string|min:6',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()->all()], 422);
        }

        $data = $this->clientRepository->create($request->all());

        return response()->json($data);
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:clients,name,' . $request->id,
            'username' => 'required|string|max:255|unique:clients,username,' . $request->id,
            'password' => 'sometimes|nullable|string|min:6',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()->all()], 422);
        }
        $data = $this->clientRepository->edit($request->id, $request->all());

        return response()->json($data);
    }
}
