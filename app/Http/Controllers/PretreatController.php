<?php

namespace App\Http\Controllers;

use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Repositories\PretreatRepository;
use App\Repositories\PrintingRepository;
use App\Repositories\TimeCheckingRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PretreatController extends Controller
{
    private PretreatRepository $pretreatRepository;

    private PrintingRepository $printingRepository;

    public function __construct(PretreatRepository $pretreatRepository, PrintingRepository $printingRepository)
    {
        $this->pretreatRepository = $pretreatRepository;
        $this->printingRepository = $printingRepository;
    }

    public function allInOne(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'label' => 'required',
            'employee_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $label = $this->printingRepository->removeLabelSide($input['label']);
        $itemBarcode = $this->pretreatRepository->getBarcodeLabel($label);
        if (!$itemBarcode) {
            return response()->json(['message' => 'Label not found'], 422);
        }

        $side = $this->printingRepository->getLabelPrintSide($input['label']);
        $side = $side < 0 ? 0 : $side;
        $data = $this->pretreatRepository->getDataAllInOne($itemBarcode, $side, $input['employee_id']);

        if ($data['status']) {
            return response()->json(['data' => $data['data']], 200);
        } else {
            return response()->json(['message' => $data['message']], 422);
        }
    }

    public function getPretreat(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'label' => 'required',
            'employee_id' => 'required',
            'id_time_checking' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $label = $this->printingRepository->removeLabelSide($input['label']);
        $itemBarcode = $this->pretreatRepository->getBarcodeLabel($label);
        if (!$itemBarcode) {
            return response()->json(['message' => 'Label not found'], 422);
        }

        $side = $this->printingRepository->getLabelPrintSide($input['label']);
        $side = $side < 0 ? 0 : $side;
        $data = $this->pretreatRepository->getDataOnlyPretreat($itemBarcode, $side, $input['employee_id'], $input['version'] ?? 1);

        if ($data['status']) {
            return response()->json(['data' => $data['data']], 200);
        } else {
            return response()->json(['message' => $data['message']], 422);
        }
    }

    public function updatePretreat(Request $request)
    {
        $input = $request->all();
        $validator = Validator::make($input, [
            'label' => 'required',
            'employee_id' => 'required',
            'id_time_checking' => 'required',
            'fluid_grams' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        DB::beginTransaction();
        try {
            $label = $this->printingRepository->removeLabelSide($input['label']);
            $itemBarcode = $this->pretreatRepository->getBarcodeLabel($label);
            if (!$itemBarcode) {
                return response()->json(['message' => 'Label not found'], 422);
            }

            $side = $this->printingRepository->getLabelPrintSide($input['label']);
            $side = $side < 0 ? 0 : $side;

            $this->pretreatRepository->updateFluidGrams($itemBarcode, $side, $input['fluid_grams']);

            $this->pretreatRepository->updatePretreat($input['employee_id'], $itemBarcode->id);

            //todo:  update time end for time checking
            $timeCheckingRepository = new TimeCheckingRepository();
            $timeCheckingRepository->updateTimeChecking(['end_time' => date('Y-m-d H:i:s')], $input['id_time_checking']);

            $sideName = ProductPrintSide::findByCode($side)?->name;
            saleOrderHistory(
                null,
                $input['employee_id'],
                $itemBarcode->order_id,
                SaleOrderHistory::UPDATE_ORDER_PRETREAT_TYPE,
                "Label ID $label pretreated $sideName",
            );

            handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $itemBarcode->order_id);

            DB::commit();

            return response()->json(['message' => 'update pretreat success'], 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return response()->json(['message' => $th->getMessage()], 422);
        }
    }
}
