<?php

namespace App\Http\Controllers;

use App\Repositories\MockupRepository;
use Illuminate\Http\Request;

class MockupController extends Controller
{
    private MockupRepository $mockupRepository;

    public function __construct(MockupRepository $mockupRepository)
    {
        $this->mockupRepository = $mockupRepository;
    }

    public function generate(Request $request, $sku, $print_side)
    {
        try {
            return $this->mockupRepository->generate($sku, $print_side);
        } catch (\Throwable $th) {
            if ($request['debug'] == true) {
                return response($th->getMessage(), 422);
            }
            return response('Generate mockup fail.', 422);
        }
        
    }

}
