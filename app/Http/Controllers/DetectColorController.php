<?php

namespace App\Http\Controllers;

use App\Http\Requests\DetectColorRequest;
use App\Repositories\DetectColorRepository;

class DetectColorController extends Controller
{
    private DetectColorRepository $detectColorRepository;

    public function __construct(DetectColorRepository $detectColorRepository)
    {
        $this->detectColorRepository = $detectColorRepository;
    }

    public function create(DetectColorRequest $request)
    {
        $data = $request->validated();
        $data['type'] = 2;
        $data['user_id'] = auth()->user()->id;
        $response = $this->detectColorRepository->create($data);

        return response()->json($response);
    }

    public function update(DetectColorRequest $request, int $id)
    {
        $data = $request->validated();
        $data['type'] = 2;
        $data['user_id'] = auth()->user()->id;
        $response = $this->detectColorRepository->update($id, $data);

        return response()->json($response);
    }
}
