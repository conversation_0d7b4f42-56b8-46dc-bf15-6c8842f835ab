<?php

namespace App\Http\Controllers;

use App\Http\Requests\AddOrRemoveSellbriteSyncRequest;
use App\Http\Requests\UpdateSellbriteSyncRequest;
use App\Repositories\Contracts\SellbriteSyncRepositoryInterface;
use Illuminate\Http\Request;

class SellbriteSyncController extends Controller
{
    protected $sellbriteSyncRepository;

    public function __construct(
        SellbriteSyncRepositoryInterface $sellbriteSyncRepository
    ) {
        $this->sellbriteSyncRepository = $sellbriteSyncRepository;
    }

    public function index(Request $request)
    {
        return response()->json($this->sellbriteSyncRepository->getAll($request->all()));
    }

    public function sync(Request $request)
    {
        return response()->json($this->sellbriteSyncRepository->sync());
    }

    public function update(UpdateSellbriteSyncRequest $request, $id)
    {
        return response()->json($this->sellbriteSyncRepository->update($id, $request->validated()));
    }
}
