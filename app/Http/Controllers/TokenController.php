<?php

namespace App\Http\Controllers;

use App\Repositories\AuthRepository;
use App\Repositories\TokenRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TokenController extends Controller
{

    protected TokenRepository $tokenRepository;
    protected AuthRepository $authRepository;

    public function __construct(TokenRepository $tokenRepository, AuthRepository $authRepository)
    {
        $this->tokenRepository = $tokenRepository;
        $this->authRepository = $authRepository;
    }

    public function addToken(Request $request)
    {
        try {
            $authorizationHeader = $request->header('Authorization');
            $input['token'] = substr($authorizationHeader, 7);
            $input['ip_address'] = $_SERVER["HTTP_CF_CONNECTING_IP"] ?? $_SERVER["REMOTE_ADDR"] ?? '';
            $response = $this->tokenRepository->addToken($input);
            return response()->json([
                'access_token' => $input['token'],
                'token_type' => 'bearer',
                'user' => $this->authRepository->fetchUserWarehouseByUser(auth()->user()),
            ], $response['code']);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }


    }
}
