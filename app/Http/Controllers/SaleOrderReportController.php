<?php

namespace App\Http\Controllers;

use App\Exports\FulfillmentExport;
use App\Repositories\SaleOrderReportRepository;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Validator;

class SaleOrderReportController extends Controller
{
    protected $repository;

    public function __construct(SaleOrderReportRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getFulfillmentReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_month' => 'required',
            'end_month' => 'required',
            'warehouse' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $items = $this->repository->fetchFulfillmentReportByMonth($request);

        return response()->json($items);
    }
    public function getFulfillmentPrintMethodReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_month' => 'required',
            'end_month' => 'required',
            'warehouse' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $items = $this->repository->fetchFulfillmentPrintMethodReportByMonth($request);

        return response()->json($items);
    }
    public function getFulfillmentWarehouse(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_month' => 'required',
            'end_month' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $items = $this->repository->fetchFulfillmentReportByWarehouse($request);

        return response()->json($items);
    }
    public function getFulfillmentReportForSeller(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_month' => 'required',
            'end_month' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $items = $this->repository->fetchFulfillmentReportForSeller($request);

        return response()->json($items);
    }

    public function reportTemp(Request $request)
    {
        return Excel::download(new FulfillmentExport($request), "fulfillment.xlsx");
    }

}
