<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Repositories\GiftMessageRepository;

class GiftMessageController extends Controller
{

    protected $giftMessageRepository;

    public function __construct(GiftMessageRepository $giftMessageRepository)
    {
        $this->giftMessageRepository = $giftMessageRepository;
    }

    public function cardGiftMessage(Request $request)
    {
        $data = $request->all();
        $data['message'] = $request->message;
        $this->giftMessageRepository->createGiftCard($data['message']);
    }
}
