<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreateInternalTicketRequest;
use App\Http\Requests\UpdateInternalTicketRequest;
use App\Repositories\InternalTicketRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class InternalTicketController extends Controller
{
    private InternalTicketRepository $internalTicketRepository;

    public function __construct(InternalTicketRepository $internalTicketRepository)
    {
        $this->internalTicketRepository = $internalTicketRepository;
    }

    public function fetchAll(Request $request)
    {
        $filter = $request->all();
        $filter['warehouse_id'] = $request->warehouse_id;
        $data = $this->internalTicketRepository->fetchAll($filter);

        return response()->json($data);
    }

    public function getCount()
    {
        $data = $this->internalTicketRepository->getCount();

        return response()->json($data);
    }

    public function updateStatus(UpdateInternalTicketRequest $request, $id)
    {
        try {
            $input = $request->only(['note', 'employee_id', 'type']);
            $res = $this->internalTicketRepository->updateStatus($input, $id);
        } catch (\Exception $e) {
            return response()->json($e->getMessage());
        }

        return response()->json($res);
    }

    public function create(CreateInternalTicketRequest $request)
    {
        try {
            $response = $this->internalTicketRepository->create($request);

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function assign(int $id, Request $request)
    {
        try {
            $response = $this->internalTicketRepository->assign($id, $request);

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
