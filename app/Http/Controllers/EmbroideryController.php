<?php

namespace App\Http\Controllers;

use App\Http\Requests\UploadEmbroideryEMBFileRequest;
use App\Http\Requests\UploadEmbroideryFileRequest;
use App\Jobs\UpdateOrderItemColorStatus;
use App\Models\EmbroideryHistory;
use App\Models\EmbroideryTask;
use App\Models\EmbroideryUser;
use App\Models\QueueJob;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Repositories\EmbroideryRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Validator;

class EmbroideryController extends Controller
{
    private $embroideryRepository;

    public function __construct(EmbroideryRepository $embroideryRepository)
    {
        $this->embroideryRepository = $embroideryRepository;
    }

    public function listTask(Request $request)
    {
        $result = $this->embroideryRepository->listTask($request);

        return response()->json($result);
    }

    public function countTask(Request $request)
    {
        $result = $this->embroideryRepository->countTask($request);

        return response()->json($result);
    }

    public function getDetail($id)
    {
        $result = $this->embroideryRepository->getDetail($id);

        return response()->json($result);
    }

    public function getTask(Request $request)
    {
        $user = auth()->user();
        if ($user->role != 'editor') {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        $currTask = EmbroideryTask::wherein('status', [EmbroideryTask::STATUS_IN_PROGRESS, EmbroideryTask::STATUS_REJECT])
        ->where('embroidery_user_id', $user->id)
        ->get();
        $user = auth()->user();
        if ($currTask->count() == 0) {
            $findTask = EmbroideryTask::where('status', EmbroideryTask::STATUS_PENDING)
                ->whereNull('embroidery_user_id')
                ->whereNull('parent_id')
                ->orderBy('created_at', 'ASC')
                ->whereHas('image.imageHash', function ($q) {
                    $q->where('is_ip_violation', false);
                })
                ->first();
            if ($findTask) {
                if ($findTask->status == EmbroideryTask::STATUS_CANCELLED) {
                    return response()->json(['message' => 'You cannot update a task with a canceled status.'], 422);
                }
                // Update the found task with user and team information
                $findTask->update([
                    'status' => EmbroideryTask::STATUS_IN_PROGRESS,
                    'embroidery_user_id' => $user->id,
                    'embroidery_team_id' => $user->team_id,
                ]);
                EmbroideryHistory::create([
                    'task_id' => $findTask->id,
                    'action' => 'assign',
                    'embroidery_user_id' => $user->id,
                    'note' => 'Task ' . '#' . $findTask->id . ' was assigned to ' . $user->username
                ]);
                $order = SaleOrder::find($findTask->order_id);
                if ($order) {
                    if ($order->order_status == SaleOrder::STATUS_NEW_ORDER) {
                        $order->update([
                            'order_status' => SaleOrder::STATUS_IN_PRODUCTION,
                        ]);
                    }
                    SaleOrderHistory::create([
                        'order_id' => $order->id,
                        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                        'message' => "Order status changed from \"$order->order_status\" to " . SaleOrder::STATUS_IN_PRODUCTION,
                        'created_at' => Carbon::now()->toDateTimeString()
                    ]);
                }

                return response()->json(['message' => 'Successfully get a new task.'], 200);
            } else {
                return response()->json(['message' => 'There are currently no new tasks that can be assigned to you.'], 422);
            }
        } else {
            return response()->json(['message' => 'Please finish uploading your current task before taking on a new one.'], 422);
        }
    }

    public function assginTask(Request $request)
    {
        $data = $request->all();
        $user = auth()->user();
        if (!in_array($user->role, ['admin', 'leader'])) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        // chặn leader khác team
        $task = EmbroideryTask::find($data['task_id']);
        if (!empty($task)) {
            if ($task->embroidery_team_id != $user->team_id && $user->role !== 'admin' && $task->status != EmbroideryTask::STATUS_PENDING || $task->status == EmbroideryTask::STATUS_COMPLETED) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
            if ($task->status == EmbroideryTask::STATUS_CANCELLED) {
                return response()->json(['message' => 'You cannot update a task with a canceled status.'], 422);
            }
            $worker = EmbroideryUser::find($data['embroidery_user_id']);
            if ($worker->role == 'leader' || $worker->role == 'admin') {
                return response()->json(['message' => 'You can only assign tasks to employees, not managers."'], 422);
            }
            $team_leader = EmbroideryUser::where('team_id', $user->team_id)->where('role', '=', 'leader')->first();
            if (empty($team_leader) || !$team_leader) {
                return response()->json(['message' => 'You cannot assign tasks to a team that does not have a manager.'], 422);
            }
            $task->update([
                'status' => EmbroideryTask::STATUS_IN_PROGRESS,
                'embroidery_user_id' => $data['embroidery_user_id'],
                'embroidery_team_id' => $worker->team_id,
                'manager_id' => $team_leader->id
            ]);

            EmbroideryHistory::create([
                'task_id' => $task->id,
                'action' => 'assign',
                'embroidery_user_id' => $data['embroidery_user_id'],
                'note' => 'Task ' . '#' . $task->id . ' was assigned to ' . $worker->username
            ]);

            $order = SaleOrder::find($task->order_id);
            if ($order) {
                if ($order->order_status == SaleOrder::STATUS_NEW_ORDER) {
                    $order->update([
                        'order_status' => SaleOrder::STATUS_IN_PRODUCTION,
                    ]);
                }
                SaleOrderHistory::create([
                    'order_id' => $order->id,
                    'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                    'message' => "Order status changed from \"$order->order_status\" to " . SaleOrder::STATUS_IN_PRODUCTION,
                    'created_at' => Carbon::now()->toDateTimeString()
                ]);
            }

            return response()->json(['message' => 'Assignee updated successfully.'], 200);
        } else {
            return response()->json(['message' => 'Error please try agaign later.'], 422);
        }
    }

    public function uploadTask(UploadEmbroideryFileRequest $request)
    {
        $user = auth()->user();
        $data = $request->all();

        $task = EmbroideryTask::find($data['task_id']);
        if ($user->role != 'admin') {
            if (
                ($user->id != $task->embroidery_user_id && $task->embroidery_team_id != $user->team_id)
                || ($user->role == 'leader' && $user->team_id != $task->embroidery_team_id)
            ) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
        }
        if ($task->status == EmbroideryTask::STATUS_CANCELLED || $task->status == EmbroideryTask::STATUS_COMPLETED) {
            return response()->json(['message' => 'You cannot update a task with a canceled or completed status.'], 422);
        }
        $result = $this->embroideryRepository->uploadFile($data);

        return response()->json(['message' => $result['message']], $result['status']);
    }

    public function uploadTaskColor(UploadEmbroideryFileRequest $request)
    {
        $user = auth()->user();
        $data = $request->all();

        $task = EmbroideryTask::find($data['task_id']);
        if ($user->role != 'admin') {
            if (
                ($user->id != $task->embroidery_user_id && $task->embroidery_team_id != $user->team_id)
                || ($user->role == 'leader' && $user->team_id != $task->embroidery_team_id)
            ) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
        }
        if ($task->status == EmbroideryTask::STATUS_CANCELLED || $task->status == EmbroideryTask::STATUS_COMPLETED) {
            return response()->json(['message' => 'You cannot update a task with a canceled or completed status.'], 422);
        }

        $result = $this->embroideryRepository->uploadFileColor($data);

        return response()->json(['message' => $result['message']], $result['status']);
    }

    public function uploadEmbFile(UploadEmbroideryEMBFileRequest $request)
    {
        $user = auth()->user();
        $data = $request->all();

        $task = EmbroideryTask::find($data['task_id']);
        if ($user->role != 'admin') {
            if (
                ($user->id != $task->embroidery_user_id && $task->embroidery_team_id != $user->team_id)
                || ($user->role == 'leader' && $user->team_id != $task->embroidery_team_id)
            ) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
        }
        if ($task->status == EmbroideryTask::STATUS_CANCELLED || $task->status == EmbroideryTask::STATUS_COMPLETED) {
            return response()->json(['message' => 'You cannot update a task with a canceled or completed status.'], 422);
        }

        $result = $this->embroideryRepository->uploadEmbFile($data);

        return response()->json(['message' => $result['message']], $result['status']);
    }

    public function rejectTask(Request $request, $id)
    {
        $data = $request->all();
        $validator = Validator::make($data, [
            'reject_reason' => 'max:255',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $user = auth()->user();
        if (!in_array($user->role, ['admin', 'leader'])) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        $task = EmbroideryTask::find($id);
        if (!$task) {
            return response()->json(['error' => 'No task found'], 422);
        }
        if ($task->status != EmbroideryTask::STATUS_IN_REVIEW) {
            return response()->json(['error' => 'Error, Can only reject if status is review'], 422);
        }
        if ($user->team_id != $task->embroidery_team_id && $user->role != 'admin') {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        $task->update([
            'status' => EmbroideryTask::STATUS_REJECT,
            'manager_id' => $user->id,
            'reject_reason' => $data['reject_reason'],
        ]);
        EmbroideryHistory::create([
            'task_id' => $task->id,
            'action' => 'reject',
            'embroidery_user_id' => $user->id,
            'note' => 'Task ' . '#' . $task->id . ' was reject by Leader/Admin ' . $user->username . ' reason: ' . $data['reject_reason']
        ]);

        SaleOrderHistory::create([
            'order_id' => $task->sale_order_id,
            'type' => SaleOrderHistory::REJECT_EMBROIDERY_FILE,
            'message' => "The TBF/CT0 file for \"$task->print_area\" area on Task ID #" . $task->id . ' has been rejected by a leader/admin.',
            'created_at' => Carbon::now()->toDateTimeString()
        ]);

        return response()->json(['message' => 'Task reject succesful'], 200);
    }

    public function approveTask($id)
    {
        $user = auth()->user();
        if (!in_array($user->role, ['admin', 'leader'])) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        $task = EmbroideryTask::find($id);
        if (!$task) {
            return response()->json(['error' => 'No task found'], 422);
        }
        if (!in_array($task->status, [EmbroideryTask::STATUS_IN_REVIEW, EmbroideryTask::STATUS_IN_REVIEW])) {
            return response()->json(['error' => 'Error, Can only approve if status is review or rejected'], 422);
        }
        if ($user->team_id != $task->embroidery_team_id && $user->role != 'admin') {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        if (!$task->link_url && !$task->color_url) {
            return response()->json(['error' => 'Error, Can only approve if tbf file and ct0 file exist'], 422);
        }

        $task->update([
            'status' => EmbroideryTask::STATUS_COMPLETED,
            'manager_id' => $user->id,
            'reject_reason' => null
        ]);

        EmbroideryHistory::create([
            'task_id' => $task->id,
            'action' => 'reject',
            'embroidery_user_id' => $user->id,
            'note' => 'Task ' . '#' . $task->id . ' was approved by Leader/Admin ' . $user->username
        ]);

        SaleOrderHistory::create([
            'order_id' => $task->sale_order_id,
            'type' => SaleOrderHistory::APPROVE_EMBROIDERY_FILE,
            'message' => "The TBF/CT0 file for \"$task->print_area\" area on Task ID #" . $task->id . ' has been approved by a leader/admin.',
            'created_at' => Carbon::now()->toDateTimeString()
        ]);

        // update secondary task
        $updatedTaskIds = EmbroideryTask::where('parent_id', $task->id)
        ->whereNotIn('status', [EmbroideryTask::STATUS_CANCELLED, EmbroideryTask::STATUS_COMPLETED])
        ->pluck('id');

        EmbroideryTask::where('parent_id', $task->id)
        ->whereNotIn('status', [EmbroideryTask::STATUS_CANCELLED, EmbroideryTask::STATUS_COMPLETED])
        ->update([
            'status' => 'completed',
            'link_url' => $task->link_url,
            'color_url' => $task->color_url
        ]);

        foreach ($updatedTaskIds as $updatedTaskId) {
            $updatedTask = EmbroideryTask::find($updatedTaskId);
            if ($updatedTask) {
                SaleOrderHistory::create([
                    'order_id' => $updatedTask->sale_order_id,
                    'type' => SaleOrderHistory::APPROVE_EMBROIDERY_FILE,
                    'message' => "The TBF/CT0 file for \"$updatedTask->print_area\" area on Task ID #" . $task->id . ' has been approved by a leader/admin.',
                    'created_at' => now()->toDateTimeString()
                ]);
                dispatch(new UpdateOrderItemColorStatus($updatedTask->order_item_id))->onQueue(QueueJob::QUEUE_UPDATE_ORDER_ITEM_COLOR_STATUS);
            }
        }

        dispatch(new UpdateOrderItemColorStatus($task->order_item_id))->onQueue(QueueJob::QUEUE_UPDATE_ORDER_ITEM_COLOR_STATUS);

        return response()->json(['message' => 'Task approve succesful'], 200);
    }

    public function getPrintArea()
    {
        return response()->json($this->embroideryRepository->getPrintArea());
    }

    public function warningDigitized($id)
    {
        $user = auth()->user();

        $task = EmbroideryTask::find($id);
        if ($user->role != 'admin' || $user->role == 'leader' && $task->status != EmbroideryTask::STATUS_PENDING) {
            if (
                ($user->id != $task->embroidery_user_id && $task->embroidery_team_id != $user->team_id)
                || ($user->role == 'leader' && $user->team_id != $task->embroidery_team_id)
            ) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
        }

        if (!in_array($task->status, [EmbroideryTask::STATUS_IN_PROGRESS, EmbroideryTask::STATUS_PENDING])) {
            return response()->json(['message' => 'You cannot update a task is not in progress or pending status.'], 422);
        }

        if ($task->is_not_digitized) {
            return response()->json(['message' => 'The task is already marked as UNABLE TO BE DIGITIZED.'], 422);
        }

        $result = $this->embroideryRepository->updateWarningDigitized($id);

        return response()->json(['message' => $result['message']], $result['status']);
    }

    public function commentDigitized(Request $request)
    {
        $user = auth()->user();
        $data = $request->all();
        $task = EmbroideryTask::find($data['id']);
        $validator = Validator::make($data, [
            'value' => 'max:255|string|required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        if ($user->role != 'admin') {
            if (
                ($user->id != $task->embroidery_user_id && $task->embroidery_team_id != $user->team_id)
                || ($user->role == 'leader' && $user->team_id != $task->embroidery_team_id)
            ) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
        }

        if ($task->status != EmbroideryTask::STATUS_IN_REVIEW) {
            return response()->json(['message' => 'You cannot update a task with current status.'], 422);
        }

        if (!$task->is_not_digitized) {
            return response()->json(['message' => 'The task is not marked as UNABLE TO BE DIGITIZED.'], 422);
        }

        $result = $this->embroideryRepository->commentDigitized($request);

        return response()->json(['message' => $result['message']], $result['status']);
    }

    public function rejectDigitized($id)
    {
        $user = auth()->user();
        $task = EmbroideryTask::find($id);
        if (!$task) {
            return response()->json(['error' => 'Task not found!'], 422);
        }

        if (!in_array($user->role, ['admin', 'leader'])) {
            if (
                ($user->id != $task->embroidery_user_id && $task->embroidery_team_id != $user->team_id)
            ) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
        }

        if ($task->status == EmbroideryTask::STATUS_CANCELLED || $task->status == EmbroideryTask::STATUS_COMPLETED) {
            return response()->json(['message' => 'You cannot update a task with a canceled or completed status.'], 422);
        }

        if (!$task->is_not_digitized) {
            return response()->json(['message' => 'The task is not marked as UNABLE TO BE DIGITIZED.'], 422);
        }

        $result = $this->embroideryRepository->rejectDigitized($id);

        return response()->json(['message' => $result['message']], $result['status']);
    }
}
