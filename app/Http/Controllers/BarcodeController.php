<?php

namespace App\Http\Controllers;

use App\Http\Requests\Barcode\BlankPretreatedBarcodeRequest;
use App\Http\Requests\Barcode\MobileBarcodePrintedRequest;
use App\Http\Requests\Barcode\MobileBarcodeRequest;
use App\Http\Requests\Barcode\MobileBarcodeStatusPrintRequest;
use App\Http\Requests\Barcode\MobilePrintBarcodeRequest;
use App\Http\Requests\Barcode\MobileReprintBarcodeRequest;
use App\Http\Requests\Dtf\ConfirmPrintBarcodeByOrderTypeRequest;
use App\Http\Requests\Dtf\ConfirmPrintBarcodeRequest;
use App\Models\BarcodePrinted;
use App\Models\BarcodePrintedTime;
use App\Models\PrintMethod;
use App\Models\SaleOrder;
use App\Models\Setting;
use App\Repositories\BarcodeDTFRepository;
use App\Repositories\BarcodeRepository;
use App\Repositories\StoreRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Validator;

class BarcodeController extends Controller
{
    protected $storeRepository;

    protected $barcodeRepository;

    protected $barcodeDTFRepository;

    public function __construct(
        StoreRepository $storeRepository,
        BarcodeRepository $barcodeRepository,
        BarcodeDTFRepository $barcodeDTFRepository
    ) {
        $this->storeRepository = $storeRepository;
        $this->barcodeRepository = $barcodeRepository;
        $this->barcodeDTFRepository = $barcodeDTFRepository;
    }

    public function generateBarcode()
    {
        BarcodePrinted::where('id', '>=', 133006)->where('convert_status', '=', 2)->update(['convert_status' => 0]);

        return response()->json(['message' => 'Success']);
    }

    public function countPending(Request $request)
    {
        $startTime = microtime(true);
        $params = $request->all();
        $params['warehouse_id'] = $request->warehouse_id;
        $params['priorityStores'] = $this->storeRepository->getPriorityStores();
        $sqlExecutionTime0 = microtime(true) - $startTime;
        $bulkOrders = $this->barcodeRepository->countPendingBulkOrder($params);
        $sqlExecutionTime1 = microtime(true) - $startTime;
        $styles = $this->barcodeRepository->countPendingStyleAll($params);
        $sqlExecutionTime2 = microtime(true) - $startTime;
        $stores = $this->barcodeRepository->countPendingStore($params);
        $sqlExecutionTime3 = microtime(true) - $startTime;
        $priorityStores = $this->barcodeRepository->countPendingStore($params, true);
        $sqlExecutionTime4 = microtime(true) - $startTime;
        $xqc = $this->barcodeRepository->countPendingStyleXQC($params);
        $sqlExecutionTime5 = microtime(true) - $startTime;
        $eps = $this->barcodeRepository->countPendingStyleEps($params);
        $sqlExecutionTime6 = microtime(true) - $startTime;
        $warehouses = $this->barcodeRepository->countPendingWarehouse($params);
        $sqlExecutionTime7 = microtime(true) - $startTime;
        $accounts = $this->barcodeRepository->countPendingAccount($params);
        $sqlExecutionTime8 = microtime(true) - $startTime;
        $reprint = $this->barcodeRepository->countPendingReprint($params);
        $sqlExecutionTime9 = microtime(true) - $startTime;
        $manualProcess = $this->barcodeRepository->countPendingManualProcess($params);
        $sqlExecutionTime10 = microtime(true) - $startTime;
        $reroute = $this->barcodeRepository->countPendingReroute($params);
        $sqlExecutionTime11 = microtime(true) - $startTime;
        $fba = $this->barcodeRepository->countPendingFba($params);
        $sqlExecutionTime12 = microtime(true) - $startTime;
        $tiktok = $this->barcodeRepository->countPendingTiktok($params);
        $sqlExecutionTime13 = microtime(true) - $startTime;
        $out = [];
        $barcodePrintedTimes = BarcodePrintedTime::where('print_method', BarcodePrinted::METHOD_DTG)->get();
        $bulkOrders->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'bulk_order', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null);
        $phpExecutionTime0 = microtime(true) - $startTime;
        $tiktok->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'tiktok', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null);
        $phpExecutionTime1 = microtime(true) - $startTime;
        $xqc->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'xqc', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null);
        $phpExecutionTime2 = microtime(true) - $startTime;
        $eps->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'eps', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null);
        $phpExecutionTime3 = microtime(true) - $startTime;
        $reprint->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'reprint', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null);
        $phpExecutionTime4 = microtime(true) - $startTime;
        $manualProcess->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'manual', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null);
        $phpExecutionTime5 = microtime(true) - $startTime;
        $reroute->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'reroute', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null);
        $phpExecutionTime6 = microtime(true) - $startTime;
        $fba->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'fba', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null);
        $phpExecutionTime7 = microtime(true) - $startTime;

        foreach ($styles as $style) {
            $style->printed_at = $this->barcodeRepository->getLastPrintedTime($barcodePrintedTimes, 'style', $params['warehouse_id'], $params['store_id'] ?? null, $params['account_id'] ?? null, $style->product_style_sku);
        }

        $phpExecutionTime8 = microtime(true) - $startTime;
        $out['bulk_orders'] = $bulkOrders;
        $out['styles'] = array_values(collect($styles)->sortBy('printed_at')->toArray());
        $out['tiktok'] = $tiktok;
        $out['stores'] = $stores;
        $out['priority_stores'] = $priorityStores;
        $out['xqc'] = $xqc;
        $out['eps'] = $eps;
        $out['warehouse'] = $warehouses;
        $out['warehouse_id'] = $request->warehouse_id;
        $out['accounts'] = $accounts;
        $out['reprint'] = $reprint;
        $out['manual'] = $manualProcess;
        $out['reroute'] = $reroute;
        $out['fba'] = $fba;
        $out['message'] = [
            'sql_execution_time' => [
                'bulk_orders' => $sqlExecutionTime1 - $sqlExecutionTime0,
                'styles' => $sqlExecutionTime2 - $sqlExecutionTime1,
                'stores' => $sqlExecutionTime3 - $sqlExecutionTime2,
                'priority_stores' => $sqlExecutionTime4 - $sqlExecutionTime3,
                'xqc' => $sqlExecutionTime5 - $sqlExecutionTime4,
                'eps' => $sqlExecutionTime6 - $sqlExecutionTime5,
                'warehouse' => $sqlExecutionTime7 - $sqlExecutionTime6,
                'accounts' => $sqlExecutionTime8 - $sqlExecutionTime7,
                'reprint' => $sqlExecutionTime9 - $sqlExecutionTime8,
                'manual' => $sqlExecutionTime10 - $sqlExecutionTime9,
                'reroute' => $sqlExecutionTime11 - $sqlExecutionTime10,
                'fba' => $sqlExecutionTime12 - $sqlExecutionTime11,
                'tiktok' => $sqlExecutionTime13 - $sqlExecutionTime12,
            ],
            'php_execution_time' => [
                'bulk_order' => $phpExecutionTime0,
                'tiktok' => $phpExecutionTime1,
                'xqc' => $phpExecutionTime2,
                'eps' => $phpExecutionTime3,
                'reprint' => $phpExecutionTime4,
                'manual' => $phpExecutionTime5,
                'reroute' => $phpExecutionTime6,
                'fba' => $phpExecutionTime7,
                'styles' => $phpExecutionTime8
            ]
        ];

        return response()->json($out);
    }

    public function fetchPrintedBarcode(Request $request)
    {
        $params = $request->all();
        $params['warehouse_id'] = $request->warehouse_id;
        $items = $this->barcodeRepository->fetchBarcodePrinted($params);

        return response()->json($items);
    }

    public function updatePrintedBarcode(Request $request)
    {
        $id = $request->id;
        $content = $request->getContent();
        $content = json_decode($content, true);
        $this->barcodeRepository->updateBarcodePrinted($id, $content);

        return response()->json(['id' => $id, 'content' => $content]);
    }

    public function printBarcode(Request $request)
    {
        $params = $request->all();
        $params['priorityStores'] = $this->storeRepository->getPriorityStores();
        $params['limit'] = $params['limit'] ?? 10;
        $params['warehouse_id'] = $request->warehouse_id;
        $newBarcodePrinted = [
            'employee_id' => $params['employee_id'] ?? null,
            'warehouse_id' => $params['warehouse_id'],
            'store_id' => $params['store_id'] ?? null,
            'style_sku' => $params['style_sku'] ?? null,
            'account_id' => $params['account_id'] ?? null,
            'quantity_input' => $params['limit'] ?? 0,
            'is_xqc' => $params['is_xqc'] ?? null,
            'is_eps' => $params['is_eps'] ?? null,
            'is_manual' => $params['is_manual'] ?? null,
            'is_reprint' => $params['is_reprint'] ?? null,
            'is_reroute' => $params['is_reroute'] ?? null,
            'is_fba' => $params['is_fba'] ?? null,
            'is_insert' => $params['is_insert'] ?? null,
            'is_tiktok' => $params['is_tiktok'] ?? null,
            'is_bulk_order' => $params['is_bulk_order'] ?? null,
            'user_id' => Auth::id(),
            'created_at' => date('Y-m-d H:i:s'),
            'print_method' => BarcodePrinted::METHOD_DTG
        ];

        DB::beginTransaction();

        try {
            if (
                is_null($newBarcodePrinted['account_id'])
                && is_null($newBarcodePrinted['is_fba'])
                && is_null($newBarcodePrinted['is_insert'])
                && is_null($newBarcodePrinted['is_manual'])
                && is_null($newBarcodePrinted['is_reprint'])
                && is_null($newBarcodePrinted['is_reroute'])
                && is_null($newBarcodePrinted['is_xqc'])
                && is_null($newBarcodePrinted['store_id'])
                && is_null($newBarcodePrinted['is_bulk_order'])
                && is_null($newBarcodePrinted['is_eps'])
            ) {
                $settingTestWip = DB::table('setting')
                    ->where('label', Setting::TEST_WIP_SKU)
                    ->first();

                if ($settingTestWip && !is_null($settingTestWip->value) && $newBarcodePrinted['warehouse_id'] == 1) {
                    $arrayStyle = explode(',', $settingTestWip->value);

                    if (in_array($newBarcodePrinted['style_sku'], $arrayStyle)) {
                        return response()->json(['id' => 0]);
                    }
                }
            }

            $id = $this->barcodeRepository->insertBarcodePrinted($newBarcodePrinted);
            $params['barcode_printed_id'] = $id;
            $this->barcodeRepository->updateBarcodePrintedItem($params);
            $this->barcodeRepository->updateLastBarcodePrintedTime($params);
            DB::commit();

            return response()->json(['id' => $id]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('barcode')->error($e->getMessage());

            return response()->json(['id' => 0]);
        }
    }

    public function printBarcodeTest(Request $request)
    {
        $params = $request->all();
        $params['limit'] = $params['limit'] ?? 10;
        $params['warehouse_id'] = $request->warehouse_id;
        $params['count'] = 1;
        $r = $this->barcodeRepository->updateBarcodePrintedItem($params);

        return response()->json($r);
    }

    public function confirmBarcodePrinted(Request $request)
    {
        $id = $request->id;
        $payload = $request->getContent();
        $payload = json_decode($payload, true);
        $status = false;
        $check = $this->barcodeRepository->getStartEndBarcodePrinted($id);

        if ($check) {
            $labelStart = $this->barcodeRepository->getLabel($payload['start']);

            if ($labelStart) {
                $labelEnd = $this->barcodeRepository->getLabel($payload['end']);

                if ($labelEnd && $labelStart->sku == $check->first_sku && $labelEnd->sku == $check->last_sku) {
                    //log timeline
                    if ($check->print_status != BarcodePrinted::ACTIVE) {
                        $this->barcodeRepository->logTimeLineOrderByBarcodePrintedId($id, $check->employee_id);
                    }
                    $this->barcodeRepository->updateBarcodePrinted($id, ['print_status' => 1]);
                    $status = true;
                    handleJob(BarcodePrinted::JOB_AUTO_DEDUCTION, $id);
                }
            } elseif ($payload['start'] == $check->first_sku && $payload['end'] == $check->last_sku) {
                //log timeline
                if ($check->print_status != BarcodePrinted::ACTIVE) {
                    $this->barcodeRepository->logTimeLineOrderByBarcodePrintedId($id, $check->employee_id);
                }
                $this->barcodeRepository->updateBarcodePrinted($id, ['print_status' => 1]);
                $status = true;
                handleJob(BarcodePrinted::JOB_AUTO_DEDUCTION, $id);
            }
        }

        return response()->json(['id' => $id, 'check' => $check, 'payload' => $payload, 'status' => $status]);
    }

    public function printBarcodePrinted($id)
    {
        try {
            return response()->json($this->barcodeRepository->printBarcode($id));
        } catch (Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function reprintBarcodePrinted($id)
    {
        try {
            return response()->json($this->barcodeRepository->reprintBarcode($id));
        } catch (Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function countPendingPriorityStoreByOrderType(Request $request)
    {
        try {
            $input = $request->all();
            $input['warehouse_id'] = $request->warehouse_id;
            $input['priorityStores'] = $this->storeRepository->getPriorityStores();
            $data = $this->barcodeRepository->countPendingPriorityStoreByOrderType($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function countDTFByOrderType(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'print_method' => 'required',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input['warehouse_id'] = $request->warehouse_id;
            $input['print_method'] = $request->print_method;
            $input['store_id'] = $request->store_id ?? null;
            if ($input['print_method'] == PrintMethod::NECK) {
                $input['priorityStores'] = $this->storeRepository->getPriorityStores();
            }
            $data = $this->barcodeDTFRepository->countDtfByOrderType($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function fetchDTF(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'print_method' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'print_method']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->barcodeRepository->fetchDtf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function historyDtf(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'label_id' => 'nullable|string',
                'print_method' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'label_id', 'print_method']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->barcodeRepository->historyDtf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function historyByOrderType(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_type' => ['required', Rule::in([SaleOrder::ORDER_TYPE_PRETREATED, SaleOrder::ORDER_TYPE_BLANK])],
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'label_id' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'label_id', 'order_type']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->barcodeRepository->historyByOrderType($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function confirmPrintDtf(ConfirmPrintBarcodeRequest $request)
    {
        try {
            $input = $request->validated();
            $data = $this->barcodeDTFRepository->confirmPrintDtf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function countByOrderType(BlankPretreatedBarcodeRequest $request)
    {
        try {
            $input = $request->all();
            $input['warehouse_id'] = config('jwt.warehouse_id');
            $input['priorityStores'] = $this->storeRepository->getPriorityStores();
            $data = $this->barcodeRepository->countByOrderType($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function fetchByOrderType(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_type' => ['required', Rule::in([SaleOrder::ORDER_TYPE_PRETREATED, SaleOrder::ORDER_TYPE_BLANK])],
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'order_type']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->barcodeRepository->fetchByOrderType($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function confirmPrintByOrderType(ConfirmPrintBarcodeByOrderTypeRequest $request)
    {
        try {
            $input = $request->validated();
            $input['priorityStores'] = $this->storeRepository->getPriorityStores();

            $data = $this->barcodeRepository->confirmPrintByOrderType($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobilePrintBarcode(MobilePrintBarcodeRequest $request)
    {
        try {
            $input = $request->only(['style', 'color', 'quantity', 'employee_code']);
            $data = $this->barcodeRepository->confirmMobilePrintBarcode($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobileReprintBarcode(MobileReprintBarcodeRequest $request)
    {
        try {
            $input = $request->only(['leader_code', 'batch_id']);
            $data = $this->barcodeRepository->confirmMobileRePrintBarcode($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobileBarcodeError(MobileBarcodeRequest $request)
    {
        try {
            $input = $request->only(['batch_id']);
            $data = $this->barcodeRepository->printError($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobileBarcode(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'style' => 'required',
                'color' => 'required',
                'employee_code' => 'required|numeric',
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['style', 'color', 'employee_code']);
            $data = $this->barcodeRepository->mobileBarcode($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobileBarcodePrinted(MobileBarcodePrintedRequest $request)
    {
        try {
            $input = $request->only(['batch_id', 'employee_code']);
            $data = $this->barcodeRepository->mobileBarcodePrinted($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobileBarcodeStatusPrint(MobileBarcodeStatusPrintRequest $request, $id)
    {
        try {
            $input = $request->only(['status_print']);
            $this->barcodeRepository->mobileBarcodeStatusPrint($input, $id);

            return $this->responseSuccess(true);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobileListReprintBarcode(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'warehouse_id' => 'required|numeric'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'warehouse_id']);
            $data = $this->barcodeRepository->mobileListReprintBarcode($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobileBarcodeDetail($id)
    {
        try {
            $data = $this->barcodeRepository->mobileBarcodeDetail($id);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function mobileDeductionByBatchId(MobileBarcodePrintedRequest $request)
    {
        try {
            $input = $request->only(['batch_id', 'employee_code']);
            $data = $this->barcodeRepository->checkBarcodePrintedById($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function testPrint(Request $request)
    {
        try {
            $input = $request->only(['printer', 'url', 'file_name', 'device_id', 'id']);
            $data = $this->barcodeRepository->testPrint($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function regenerateBarcode($id)
    {
        try {
            return $this->barcodeRepository->regenerateBarcode($id);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }
}
