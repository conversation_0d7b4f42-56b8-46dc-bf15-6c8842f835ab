<?php

namespace App\Http\Controllers;

use App\Repositories\EmployeeRepository;
use App\Repositories\PerformanceReportRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PerformanceReportController extends Controller
{
    protected PerformanceReportRepository $performanceReportRepository;

    protected EmployeeRepository $employeeRepository;

    public function __construct(PerformanceReportRepository $performanceReportRepository, EmployeeRepository $employeeRepository)
    {
        $this->performanceReportRepository = $performanceReportRepository;
        $this->employeeRepository = $employeeRepository;
    }

     public function fetchPerformanceReportForManager(Request $request)
     {
         setTimezone();
         $validator = Validator::make($request->all(), [
             'start_date' => 'required',
             'end_date' => 'required',
             'department_id' => 'required',
         ]);

         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $responseData = $this->performanceReportRepository->fetchPerformanceReportForManager($request);

         return response()->json($responseData, 200);
     }

     public function getPerformanceReportForManager(Request $request)
     {
         setTimezone();
         $validator = Validator::make($request->all(), [
             'start_date' => 'required',
             'end_date' => 'required',
             'department_id' => 'required',
             'job_type' => 'required',
         ]);

         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $responseData = $this->performanceReportRepository->fetchPerformanceReportForManagerV2($request);

         return response()->json($responseData, 200);
     }

     public function fetchTopPerformanceEmployees(Request $request)
     {
         setTimezone();
         $validator = Validator::make($request->all(), [
             'start_date' => 'required',
             'end_date' => 'required',
             'department_id' => 'required',
         ]);
         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $data = $this->performanceReportRepository->fetchTopPerformanceEmployees($request);

         return response()->json($data, 200);
     }

     public function fetchPerformanceByDepartments(Request $request)
     {
         $input = $request->all();
         $validator = Validator::make($input, [
             'start_date' => 'required',
             'end_date' => 'required'
         ]);

         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $data = $this->performanceReportRepository->fetchPerformanceByDepartments($input);

         return response()->json($data, 200);
     }

     public function summaryPerformanceReportForLeader(Request $request)
     {
         $input = $request->all();
         $input['department_id'] = Auth::user()->department_id;

         $validator = Validator::make($input, [
             'start_date' => 'required',
             'end_date' => 'required'
         ]);

         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $data = $this->performanceReportRepository->summaryPerformanceReportForLeader($input);

         return response()->json($data, 200);
     }

     public function getPerformanceReportDeptForLeader(Request $request)
     {
         $input = $request->all();
         $input['department_id'] = Auth::user()->department_id;
         $validator = Validator::make($input, [
             'start_date' => 'required',
             'end_date' => 'required'
         ]);

         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $data = $this->performanceReportRepository->getPerformanceByDepartment($input);

         return response()->json($data, 200);
     }

     public function fetchReportAllEmployees(Request $request)
     {
         $input = $request->all();
         $input['department_id'] = Auth::user()->department_id;
         $validator = Validator::make($input, [
             'start_date' => 'required',
             'end_date' => 'required'
         ]);
         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $data = $this->performanceReportRepository->fetchReportAllEmployees($input);

         return response()->json($data, 200);
     }

     public function fetchReportAllEmployeeByTime(Request $request)
     {
         $input = $request->all();
         $input['department_id'] = Auth::user()->department_id;
         $validator = Validator::make($input, [
             'start_date' => 'required',
             'end_date' => 'required'
         ]);
         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $data = $this->performanceReportRepository->fetchReportAllEmployeeByTime($input);

         return response()->json($data, 200);
     }

     public function fetchEmployeesByDept(Request $request)
     {
         $input = $request->all();
         $input['department_id'] = Auth::user()->department_id;
         $data = $this->employeeRepository->getEmployeeOfDepartment($input['department_id']);

         return response()->json($data, 200);
     }

     public function fetchDepartments()
     {
         $data = $this->performanceReportRepository->fetchDepartments();

         return response()->json($data, 200);
     }

     public function fetchDepartmentsForReport()
     {
         $dataDepartments = $this->performanceReportRepository->fetchDepartments();

         return response()->json($dataDepartments, 200);
     }

     public function fetchEmployeePerformance(Request $request)
     {
         setTimezone();
         $validator = Validator::make($request->all(), [
             'employee_id' => 'required',
             'department_id' => 'required',
             'time_tracking_id' => 'required|exists:time_tracking,id',
             'warehouse_id' => 'required',
         ]);
         if ($validator->fails()) {
             return response()->json($validator->errors(), 422);
         }
         $responseData = $this->performanceReportRepository->fetchEmployeePerformance($request);

         return $responseData;
     }
}
