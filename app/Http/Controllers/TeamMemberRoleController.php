<?php

namespace App\Http\Controllers;

use App\Http\Requests\TeamMemberRoleRequest;
use App\Models\TeamMember;
use App\Repositories\TeamMemberRoleRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TeamMemberRoleController extends Controller
{
    protected TeamMemberRoleRepository $teamMemberRoleRepository;

    public function __construct(TeamMemberRoleRepository $teamMemberRoleRepository)
    {
        $this->teamMemberRoleRepository = $teamMemberRoleRepository;
    }

    public function getRoles(Request $request)
    {
        $data = $this->teamMemberRoleRepository->getRoles($request->all());

        return response()->json($data);
    }

    public function showRole($id)
    {
        $data = $this->teamMemberRoleRepository->showRole($id);

        return response()->json($data);
    }

    public function addRole(TeamMemberRoleRequest $request)
    {
        $success = $this->teamMemberRoleRepository->create($request->validated());

        if ($success) {
            return response()->json(['message' => 'Create role successfully'], Response::HTTP_OK);
        }

        return response()->json(['message' => 'Create role failed'], Response::HTTP_BAD_REQUEST);
    }

    public function updateRole($id, TeamMemberRoleRequest $request)
    {
        $success = $this->teamMemberRoleRepository->update($id, $request->validated());

        if ($success) {
            return response()->json(['message' => 'Update role successfully'], Response::HTTP_OK);
        }

        return response()->json(['message' => 'Update role failed'], Response::HTTP_BAD_REQUEST);
    }

    public function deleteRole($id)
    {
        $role = $this->teamMemberRoleRepository->showRole($id);

        if ($role->client_id !== auth()->user()->client_id || strtolower($role->name) === 'admin') {
            return response()->json(['message' => 'You do not have permission to delete this role'], Response::HTTP_FORBIDDEN);
        }

        if (!empty(TeamMember::where('team_member_role_id', $id)->first())) {
            return response()->json(['message' => 'This role cannot be deleted because it is already assigned to a member.'], Response::HTTP_FORBIDDEN);
        }

        $success = $this->teamMemberRoleRepository->delete($id);

        if ($success) {
            return response()->json(['message' => 'Delete role successfully'], Response::HTTP_OK);
        }

        return response()->json(['message' => 'Delete role failed'], Response::HTTP_BAD_REQUEST);
    }
}
