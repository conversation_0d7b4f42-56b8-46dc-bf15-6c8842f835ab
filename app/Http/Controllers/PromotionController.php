<?php

namespace App\Http\Controllers;

use App\Http\Requests\PromotionRequest;
use App\Repositories\PromotionRepository;
use Illuminate\Http\Request;

class PromotionController extends Controller
{
    protected PromotionRepository $promotionRepository;

    public function __construct(PromotionRepository $promotionRepository)
    {
        $this->promotionRepository = $promotionRepository;
    }

    public function fetch(Request $request)
    {
        setTimezone();

        return $this->promotionRepository->fetch($request->all());
    }

    public function getDetail($id)
    {
        setTimezone();
        $promotion = $this->promotionRepository->getDetail($id);

        return response()->json($promotion);
    }

    public function create(PromotionRequest $request)
    {
        return $this->promotionRepository->create($request->all());
    }

    public function update(int $id, PromotionRequest $request)
    {
        return $this->promotionRepository->update($id, $request->all());
    }

    public function updateStatus(int $id, Request $request)
    {
        return $this->promotionRepository->updateStatus($id, $request->all());
    }

    public function getPromotionTypes(Request $request)
    {
        $input = $request->all();

        return response()->json($this->promotionRepository->getTypes($input));
    }
}
