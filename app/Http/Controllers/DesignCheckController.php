<?php

namespace App\Http\Controllers;

use App\Repositories\DesignCheckRepository;
use Illuminate\Http\Request;

class DesignCheckController extends Controller
{
    protected $designCheckRepository;

    public function __construct(DesignCheckRepository $designCheckRepository)
    {
        $this->designCheckRepository = $designCheckRepository;
    }

    public function getList(Request $request)
    {
        $input = $request->all();
        $input['tabs'] = ['visua'];
        $response = $this->designCheckRepository->getList($input);

        return response()->json($response);
    }

    public function getCount(Request $request)
    {
        $input = $request->all();
        $input['tabs'] = ['visua', 'review'];
        $response = $this->designCheckRepository->getCount($input);

        return response()->json($response);
    }

    public function getCountOrder(Request $request)
    {
        $input = $request->only('image_hash_ids');
        $response = $this->designCheckRepository->getCountOrder($input);

        return response()->json($response);
    }

    public function getCountPending(Request $request)
    {
        $input = $request->all();
        $response = $this->designCheckRepository->getCountPending($input);

        return response()->json($response);
    }
}
