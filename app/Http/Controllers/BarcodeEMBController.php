<?php

namespace App\Http\Controllers;

use App\Http\Requests\Dtf\ConfirmPrintBarcodeRequest;
use App\Models\BarcodePrinted;
use App\Repositories\BarcodeEMBRepository;
use App\Repositories\BarcodeRepository;
use App\Repositories\StoreRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Validator;

class BarcodeEMBController extends Controller
{
    protected $barcodeRepository;

    protected $storeRepository;

    public function __construct(BarcodeEMBRepository $barcodeRepository, StoreRepository $storeRepository)
    {
        $this->barcodeRepository = $barcodeRepository;
        $this->storeRepository = $storeRepository;
    }

    public function countPendingPriorityStore(Request $request)
    {
        $params = $request->all();
        $params['warehouse_id'] = $request->warehouse_id;
        $priorityStores = $this->barcodeRepository->countPendingStore($params, true);

        return response()->json($priorityStores);
    }

    public function countPending(Request $request)
    {
        $params = $request->all();
        $params['warehouse_id'] = $request->warehouse_id;
        $styles = $this->barcodeRepository->countPendingStyle($params);
        $stores = $this->barcodeRepository->countPendingStore($params);
        $xqc = $this->barcodeRepository->countPendingStyleXQC($params);
        $warehouses = $this->barcodeRepository->countPendingWarehouse($params);
        $eps = $this->barcodeRepository->countPendingStyleEps($params);
        $reprint = $this->barcodeRepository->countPendingReprint($params);
        $manualProcess = $this->barcodeRepository->countPendingManualProcess($params);
        $reroute = $this->barcodeRepository->countPendingReroute($params);
        $fba = $this->barcodeRepository->countPendingFba($params);
        $out = [];

        $out['styles'] = $styles;
        $out['stores'] = $stores;
        $out['xqc'] = $xqc;
        $out['eps'] = $eps;
        $out['warehouse'] = $warehouses;
        $out['reprint'] = $reprint;
        $out['manual'] = $manualProcess;
        $out['reroute'] = $reroute;
        $out['fba'] = $fba;

        return response()->json($out);
    }

    public function fetchPrintedBarcode(Request $request)
    {
        $params = $request->all();
        $params['warehouse_id'] = $request->warehouse_id;
        $items = $this->barcodeRepository->fetchBarcodePrinted($params);

        return response()->json($items);
    }

    public function updatePrintedBarcode(Request $request)
    {
        $id = $request->id;

        $barcodeRepository = new BarcodeRepository();
        $content = $request->getContent();
        $content = json_decode($content, true);
        $barcodeRepository->updateBarcodePrinted($id, $content);

        return response()->json(['id' => $id, 'content' => $content]);
    }

    public function printBarcode(Request $request)
    {
        $params = $request->all();
        $params['limit'] = $params['limit'] ?? 10;
        $params['warehouse_id'] = $request->warehouse_id;

        $newBarcodePrinted = [
            'employee_id' => $params['employee_id'] ?? null,
            'warehouse_id' => $params['warehouse_id'],
            'store_id' => $params['store_id'] ?? null,
            'style_sku' => $params['style_sku'] ?? null,
            'quantity_input' => $params['limit'] ?? 0,
            'is_xqc' => $params['is_xqc'] ?? null,
            'is_eps' => $params['is_eps'] ?? null,
            'is_manual' => $params['is_manual'] ?? null,
            'is_reprint' => $params['is_reprint'] ?? null,
            'is_reroute' => $params['is_reroute'] ?? null,
            'is_fba' => $params['is_fba'] ?? null,
            'user_id' => Auth::id(),
            'created_at' => date('Y-m-d H:i:s'),
            'print_method' => BarcodePrinted::EMB_PRINT_METHOD,
        ];

        DB::beginTransaction();

        try {
            $id = $this->barcodeRepository->insertBarcodePrinted($newBarcodePrinted);
            $params['barcode_printed_id'] = $id;
            $this->barcodeRepository->updateBarcodePrintedItem($params);
            $this->barcodeRepository->updateLastBarcodePrintedTime($params);

            DB::commit();

            return response()->json(['id' => $id]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('barcode')->error($e->getMessage());

            return response()->json(['id' => 0]);
        }
    }

    public function printBarcodeTest(Request $request)
    {
        $params = $request->all();
        $params['limit'] = $params['limit'] ?? 10;
        $params['warehouse_id'] = $request->warehouse_id;
        $params['count'] = 1;
        $barcodeRepository = new BarcodeRepository();

        $r = $barcodeRepository->updateBarcodePrintedItem($params);

        return response()->json($r);
    }

    public function confirmBarcodePrinted(Request $request)
    {
        $id = $request->id;
        $payload = $request->getContent();
        $payload = json_decode($payload, true);
        $barcodeRepository = new BarcodeRepository();
        $status = false;
        $check = $barcodeRepository->getStartEndBarcodePrinted($id);
        if ($check) {
            $labelStart = $barcodeRepository->getLabel($payload['start']);
            if ($labelStart) {
                $labelEnd = $barcodeRepository->getLabel($payload['end']);
                if ($labelEnd) {
                    if ($labelStart->sku == $check->first_sku && $labelEnd->sku == $check->last_sku) {
                        //log timeline
                        if ($check->print_status != BarcodePrinted::ACTIVE) {
                            $barcodeRepository->logTimeLineOrderByBarcodePrintedId($id, $check->employee_id);
                        }
                        $barcodeRepository->updateBarcodePrinted($id, ['print_status' => 1]);
                        $status = true;
                        handleJob(BarcodePrinted::JOB_AUTO_DEDUCTION, $id);
                    }
                }
            } elseif ($payload['start'] == $check->first_sku && $payload['end'] == $check->last_sku) {
                //log timeline
                if ($check->print_status != BarcodePrinted::ACTIVE) {
                    $barcodeRepository->logTimeLineOrderByBarcodePrintedId($id, $check->employee_id);
                }
                $barcodeRepository->updateBarcodePrinted($id, ['print_status' => 1]);
                $status = true;
                handleJob(BarcodePrinted::JOB_AUTO_DEDUCTION, $id);
            }
        }

        return response()->json(['id' => $id, 'check' => $check, 'payload' => $payload, 'status' => $status]);
    }

    public function fetchDTF(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $barcodeRepository = new BarcodeRepository();
            $input = $request->only(['limit', 'page']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $barcodeRepository->fetchDtf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function historyDtf(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'label_id' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $barcodeRepository = new BarcodeRepository();
            $input = $request->only(['limit', 'page', 'label_id']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $barcodeRepository->historyDtf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function confirmPrintDtf(ConfirmPrintBarcodeRequest $request)
    {
        try {
            $barcodeRepository = new BarcodeRepository();
            $input = $request->only(['style_sku', 'quantity', 'employee_id']);
            $data = $barcodeRepository->confirmPrintDtf($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }
}
