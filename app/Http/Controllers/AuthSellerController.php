<?php

namespace App\Http\Controllers;

use App\Repositories\StoreRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AuthSellerController extends Controller
{
    protected $storeRepository;

    public function __construct(
        StoreRepository $storeRepository
    ) {
        $this->storeRepository = $storeRepository;
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), array_merge(
            [
                'password' => 'required|string|min:3',
                'support_login' => 'sometimes|boolean',
                'root_username' => 'sometimes|string|max:255',
            ],
            $request->has('support_login') ? ['email' => 'required|email'] : ['username' => 'required|string|max:255'],
        ));

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->storeRepository->authenticate($validator->validated(), $request);
    }
}
