<?php

namespace App\Http\Controllers;

use App\Repositories\DtfPrintingRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DtfPrintingController extends Controller
{
    protected $dtfPrintingRepository;

    public function __construct(DtfPrintingRepository $dtfPrintingRepository)
    {
        $this->dtfPrintingRepository = $dtfPrintingRepository;
    }

    public function scanLabel(Request $request)
    {
        $label = $request->input('sku');
        $employeeId = $request->input('employee_id');
        $timeTrackingId = $request->input('time_tracking_id');
        $result = $this->dtfPrintingRepository->scanLabel($label, $employeeId, $timeTrackingId);

        return response()->json($result);
    }

    public function printing(Request $request)
    {
        $labelId = $request->input('label_id');
        $side = $request->input('side');
        $result = $this->dtfPrintingRepository->convertImageForPrint($labelId, $side);
        if ($result['status_code'] !== Response::HTTP_OK) {
            return response()->json([
                'error' => $result['message']
            ], $result['status_code']);
        }

        return $result['imageUrl'];
    }
}
