<?php

namespace App\Http\Controllers;

use App\Repositories\Contracts\ProductTypeWeightRepositoryInterface;
use Illuminate\Http\Request;

class ProductTypeWeightController extends Controller
{
    private ProductTypeWeightRepositoryInterface $productTypeWeightRepository;

    public function __construct(ProductTypeWeightRepositoryInterface $productTypeWeightRepository)
    {
        $this->productTypeWeightRepository = $productTypeWeightRepository;
    }

    public function getList(Request $request)
    {
        return $this->productTypeWeightRepository->getList($request);
    }

    public function create(Request $request)
    {
        return $this->productTypeWeightRepository->create($request);
    }

    public function update(int $id, Request $request)
    {
        return $this->productTypeWeightRepository->update($id, $request);
    }
}
