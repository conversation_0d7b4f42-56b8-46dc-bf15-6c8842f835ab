<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateOrderItemTmpRequest;
use App\Repositories\SaleOrderItemTmpRepository;
use Illuminate\Support\Facades\Log;

class SaleOrderItemTmpController extends Controller
{
    private SaleOrderItemTmpRepository $saleOrderItemTmpRepository;

    public function __construct(SaleOrderItemTmpRepository $saleOrderItemTmpRepository)
    {
        $this->saleOrderItemTmpRepository = $saleOrderItemTmpRepository;
    }

    public function updateBulk(UpdateOrderItemTmpRequest $request)
    {
        $res =  $this->saleOrderItemTmpRepository->updateBulk($request);
        return response()->json($res);
    }



}
