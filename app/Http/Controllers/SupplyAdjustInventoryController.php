<?php

namespace App\Http\Controllers;

use App\Exports\SupplyAdjustExport;
use App\Repositories\SupplyAdjustInventoryRepository;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Validator;

class SupplyAdjustInventoryController extends Controller
{
    protected $adjustRepository;

    public function __construct(SupplyAdjustInventoryRepository $repository)
    {
        $this->adjustRepository = $repository;
    }

    public function fetchAll(Request $request)
    {
        $items = $this->adjustRepository->fetchAll($request);

        return response()->json($items);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'supply_id' => 'required|exists:supply,id',
            'quantity' => 'required|numeric',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
            'id_time_checking' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->adjustRepository->create($request);
    }

    public function exportExcel(Request $request)
    {
        $data = $this->adjustRepository->fetchAll($request);

        return Excel::download(new SupplyAdjustExport($data), 'adjust_supply.xlsx');
    }
}
