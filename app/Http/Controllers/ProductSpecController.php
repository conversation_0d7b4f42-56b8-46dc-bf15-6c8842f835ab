<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductSpec;
use App\Repositories\ProductSpecRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class ProductSpecController extends Controller
{
    private ProductSpecRepository $productSpecRepository;

    public function __construct(ProductSpecRepository $productSpecRepository)
    {
        $this->productSpecRepository = $productSpecRepository;
    }

    public function verifyCsvFile(Request $request)
    {
        $validator = Validator::make(
            [
                'file' => $request->file,
                'extension' => strtolower($request->file->getClientOriginalExtension()),
            ],
            [
                'file' => 'required',
                'extension' => 'required|in:csv,xlsx,xls',
            ],
            [
                'required' => 'The file is required',
                'in' => 'The file must be a file of type: csv, xlsx, xls.',
            ],
        );

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return response()->json($this->productSpecRepository->verifyCsvFile($request));
    }

    public function importCsv(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->productSpecRepository->importCsv($request);
    }

    public function getProductSpec(Request $request)
    {
        $sku = $request->get('sku');
        $style = $request->get('style');
        if (!$sku && !$style) {
            return response()->json([
                'data' => 'SKU or Style is required',
            ], Response::HTTP_BAD_REQUEST);
        }
        if ($sku && $style) {
            return response()->json([
                'data' => 'SKU and Style cannot be used together',
            ], Response::HTTP_BAD_REQUEST);
        }
        $productSpect = null;
        if ($sku) {
            $productSpect = $this->getProductSpecBySku($sku);
        }
        if ($style) {
            $productSpect = $this->getProductSpecByStyle($style);
        }

        return response()->json($productSpect);
    }

    private function getProductSpecBySku(mixed $sku)
    {
        $product = Product::where('sku', $sku)->first();
        if (!$product) {
            return response()->json([
                'data' => 'Product not found',
            ], Response::HTTP_NOT_FOUND);
        }
        $productId = $product->id;

        return ProductSpec::where('product_id', $productId)->first();
    }

    private function getProductSpecByStyle(mixed $style)
    {
        $product = Product::where('style', $style)->get();
        if ($product->isEmpty()) {
            return response()->json([
                'data' => 'Product not found',
            ], Response::HTTP_NOT_FOUND);
        }
        $productId = $product->pluck('id')->toArray();

        return ProductSpec::whereIn('product_id', $productId)->get();
    }
}
