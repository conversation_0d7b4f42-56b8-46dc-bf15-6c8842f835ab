<?php

namespace App\Http\Controllers;

use App\Http\Requests\Dtf\DownloadPdfRequest;
use App\Http\Requests\Dtf\GenerateNeckRequest;
use App\Models\PrintMethod;
use App\Repositories\NeckRepository;
use App\Repositories\OrnamentRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Validator;

class NeckController extends Controller
{
    private NeckRepository $neckRepository;

    private OrnamentRepository $ornamentRepository;

    public function __construct(NeckRepository $neckRepository, OrnamentRepository $ornamentRepository)
    {
        $this->neckRepository = $neckRepository;
        $this->ornamentRepository = $ornamentRepository;
    }

    public function scanLabel(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'label_id' => 'required|string',
                'employee_id' => [
                    'required',
                    'numeric',
                    Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
                ],
            ], [
                'employee_id.exists' => 'You are not in this warehouse!'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['label_id', 'employee_id']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->neckRepository->scanLabel($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function generateBatchNumber()
    {
        try {
            $data = $this->neckRepository->generateBatchNumber();

            return $this->responseSuccess($data);
        } catch(Exception $e) {
            return $this->handleException($e);
        }
    }

    public function index(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->neckRepository->listPdf($input);

            return $this->responseSuccess($data);
        } catch(Exception $e) {
            return $this->handleException($e);
        }
    }

    public function history(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|numeric',
                'page' => 'nullable|numeric',
                'label_id' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->handleValidation($validator);
            }

            $input = $request->only(['limit', 'page', 'label_id']);
            $input['warehouse_id'] = $request->warehouse_id;
            $data = $this->neckRepository->history($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function download(DownloadPdfRequest $request)
    {
        try {
            $input = $request->only(['pdf_converted_id', 'id_time_checking']);
            $data = $this->neckRepository->download($input);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }

    public function generatePdf(GenerateNeckRequest $request)
    {
        try {
            $input = $request->only(['options', 'employee_id', 'batch_number', 'country_id']);
            $batchNumber = $input['batch_number'] ?? null;
            $countryId = $input['country_id'] ?? null;

            $data = $this->ornamentRepository->generatePdf($input, PrintMethod::NECK, 'neck', $batchNumber, $countryId);

            return $this->responseSuccess($data);
        } catch (Exception $e) {
            return $this->handleException($e);
        }
    }
}
