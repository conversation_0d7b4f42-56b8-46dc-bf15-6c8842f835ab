<?php


namespace App\Http\Controllers;

use App\Repositories\Contracts\UserRepositoryInterface;
use App\Repositories\DepartmentRepository;
use App\Repositories\DepartmentJobTypeRepository;
use App\Http\Requests\Department\CreateDepartmentRequest;
use App\Http\Requests\Department\UpdateDepartmentRequest;
use App\Models\Department;
use Illuminate\Http\Request;
use Validator;

class DepartmentController extends Controller
{

    private $departmentRepo;
    private $departmentJobTypeRepo;

    public function __construct(
        DepartmentRepository        $departmentRepo,
        DepartmentJobTypeRepository $departmentJobTypeRepo
    )
    {
        $this->departmentRepo = $departmentRepo;
        $this->departmentJobTypeRepo = $departmentJobTypeRepo;
    }

    public function index(Request $request)
    {
        $departments = $this->departmentRepo->fetchAll($request->all());

        return response()->json($departments);
    }

    public function getAllDepartment()
    {
        $departments = $this->departmentRepo->getAllDepartment();

        return response()->json($departments);
    }

    public function store(CreateDepartmentRequest $request)
    {
        $data = $request->except(['job_type']);
        $jobTypes = $request->job_type;
        $department = $this->departmentRepo->create($data);
        $this->departmentJobTypeRepo->create($department, $jobTypes);

        return response()->json($department);
    }

    public function update($id, UpdateDepartmentRequest $request)
    {
        $data = $request->except(['job_type']);
        $jobTypes = $request->job_type;
        $department = $this->departmentRepo->edit($id, $data);
        if (!$department) {
            return response()->json(['message' => 'Department not found'], 404);
        }
        $this->departmentJobTypeRepo->edit($id, $jobTypes);

        return response()->json($department);
    }

    public function destroy($id)
    {
        $this->departmentJobTypeRepo->delete($id);
        $department = $this->departmentRepo->delete($id);

        return response()->json($department);
    }

    public function getListJobType()
    {
        $departmentJobTypes = $this->departmentJobTypeRepo->list();

        return response()->json($departmentJobTypes);
    }

    public function getListAll()
    {
        $departments = $this->departmentRepo->getListAll();

        return response()->json($departments);
    }

    public function getListEmployeeCheckin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'warehouse_id' => 'required',
            'department_id' => 'required|exists:department,id',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $employeeCheckin = $this->departmentRepo->getListEmployeeCheckinByDepartmentId($request['warehouse_id'], $request['department_id']);

        return response()->json($employeeCheckin);
    }

    public function getListEmployeeCheckout(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'warehouse_id' => 'required',
            'department_id' => 'required|exists:department,id',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $employeeCheckin = $this->departmentRepo->getListEmployeeCheckoutByDepartmentId($request['warehouse_id'], $request['department_id']);

        return response()->json($employeeCheckin);
    }

}
