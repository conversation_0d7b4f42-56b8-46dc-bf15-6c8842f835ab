<?php

namespace App\Http\Controllers;

use App\Http\Requests\Web\SupplyInkConsumptionForecastRequest;
use App\Http\Requests\Web\SupplyInkConsumptionRequest;
use App\Repositories\SupplyReportRepository;

class SupplyReportController extends Controller
{
    protected SupplyReportRepository $supplyReportRepository;

    public function __construct(SupplyReportRepository $supplyReportRepository)
    {
        $this->supplyReportRepository = $supplyReportRepository;
    }

    public function getInkConsumption(SupplyInkConsumptionRequest $request)
    {
        $year = empty($request->query('year')) ? now()->format('Y') : $request->query('year');
        $data = $this->supplyReportRepository->getInkConsumption($year);

        return response()->json($data);
    }

    public function getInkConsumptionForecast(SupplyInkConsumptionForecastRequest $request)
    {
        $data = $this->supplyReportRepository->getInkConsumptionForecast($request->all());

        return response()->json($data);
    }
}
