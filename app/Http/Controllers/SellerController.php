<?php

namespace App\Http\Controllers;

use App\Repositories\StoreRepository;

class SellerController extends Controller
{
    protected $storeRepository;

    public function __construct(
        StoreRepository $storeRepository
    ) {
        $this->storeRepository = $storeRepository;
    }

    public function me()
    {
        $me = $this->storeRepository->getMeInfo();

        return response()->json($me);
    }
}
