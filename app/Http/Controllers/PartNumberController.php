<?php

namespace App\Http\Controllers;

use App\Http\Requests\CreatePartNumberRequest;
use App\Http\Requests\UpdatePartNumberRequest;
use App\Repositories\PartNumberRepository;
use Illuminate\Http\Request;
use Validator;

class PartNumberController extends Controller
{

    protected PartNumberRepository $partNumberRepository;

    public function __construct(PartNumberRepository $partNumberRepository)
    {
        $this->partNumberRepository = $partNumberRepository;
    }

    public function getList(Request $request)
    {
        $input = $request;
        $response = $this->partNumberRepository->getList($input, $request->get('get_total', false));
        return response()->json($response);
    }

    public function create(CreatePartNumberRequest $request)
    {
        $input = $request->validated();
        return $this->partNumberRepository->create($input);
    }

    public function update(UpdatePartNumberRequest $request, $id)
    {
        $input = $request->validated();
        $response = $this->partNumberRepository->update($id, $input);
        return response()->json($response);
    }

    public function verifyImport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,xls,xlsx,txt',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        $res = $this->partNumberRepository->verifyFileData($request['file']);
        return response()->json($res);
    }

    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:csv,xls,xlsx,txt',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        return $this->partNumberRepository->import($request['file']);
    }

}
