<?php

namespace App\Http\Controllers;

use App\Models\QueueJob;
use App\Repositories\LatexPrintingRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class LatexPrintingController extends Controller
{
    protected LatexPrintingRepository $latexPrintingRepository;

    public function __construct(LatexPrintingRepository $latexPrintingRepository)
    {
        $this->latexPrintingRepository = $latexPrintingRepository;
    }

    public function scanLabel(Request $request)
    {
        $label = $request->input('sku');
        $employeeId = $request->input('employee_id');
        $timeTrackingId = $request->input('time_tracking_id');
        $result = $this->latexPrintingRepository->scanLabel($label, $employeeId, $timeTrackingId);

        return response()->json($result);
    }

    public function generatePrintFile($labelId, Request $request): JsonResponse
    {
        $barcode = $this->latexPrintingRepository->getBarcodeLabel($labelId);

        if (empty($barcode)) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid Barcode',
            ]);
        }

        $printFilePath = "canvas/{$barcode->soii_order_date}/{$barcode->soib_sku}-{$barcode->pps_code}-{$barcode->soib_barcode_number}.png";

        if (Storage::disk('s3')->exists($printFilePath)) {
            Storage::disk('s3')->delete($printFilePath);
        }

        if (empty($request->input('async'))) {
            $inputQueue = ['label_id' => $barcode->soib_label_id];
            handleJob(QueueJob::CONVERT_IMAGE_LATEX, $inputQueue);

            return response()->json([
                'status' => true,
                'message' => 'Add queue generate print file success',
                'print_file_url' => $printFilePath,
            ]);
        }

        $result = $this->latexPrintingRepository->generatePrintFile($labelId);

        return response()->json($result);
    }
}
