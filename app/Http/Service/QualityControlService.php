<?php

namespace App\Http\Service;

use App\Models\BarcodeStatus;
use App\Models\PurchaseOrder;
use App\Models\QualityControl\QualityControlModel;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem\SaleOrderItemModel;
use App\Models\SaleOrderItemQualityControl;
use App\Repositories\QualityControlRepository;
use App\Repositories\TimeCheckingRepository;
use Carbon\Carbon;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;

class QualityControlService
{
    private $qualityControlModel;

    private $saleOrderItemModel;

    private $saleOrderItemBarcode;

    private $timeCheckingRepository;

    private $qualityControlRepository;

    public function __construct(
        QualityControlModel $qualityControlModel,
        SaleOrderItemModel $saleOrderItemModel,
        SaleOrderItemBarcode $saleOrderItemBarcode,
        TimeCheckingRepository $timeCheckingRepository,
        QualityControlRepository $qualityControlRepository
    ) {
        $this->qualityControlModel = $qualityControlModel;
        $this->saleOrderItemModel = $saleOrderItemModel;
        $this->saleOrderItemBarcode = $saleOrderItemBarcode;
        $this->timeCheckingRepository = $timeCheckingRepository;
        $this->qualityControlRepository = $qualityControlRepository;
    }

    public function getOrderItemBySku($input)
    {
        return $this->saleOrderItemModel->getItemBySku($input['sku']);
    }

    public function insertLogCheckQualityControl($input, $employee_id)
    {
        $dataInsert = [
            'order_id' => $input->order_id,
            'order_item_id' => $input->order_item_id,
            'user_id' => auth()->user()->id ?? null,
            'sku' => $input->sku,
            'label_id' => $input->label_id,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'employee_id' => $employee_id,
            'warehouse_id' => config('jwt.warehouse_id'),
            'label_root_id' => (isset($input->label_root_id) && $input->label_root_id != '0') ? $input->label_root_id : $input->label_id

        ];

        return $this->qualityControlModel->insert($dataInsert);
    }

    public function updateBarcodeQcAt($data, $employee_id)
    {
        $this->saleOrderItemBarcode::query()->where('id', $data->id)
            ->limit(1)->update(['qc_at' => date('Y-m-d H:i:s'), 'employee_qc_id' => $employee_id]);
    }

    public function findQualityControlById($id)
    {
        return $this->qualityControlModel->getItemById($id);
    }

    public function updateData($id, $dataUpdate)
    {
        return $this->qualityControlModel->updateById($id, $dataUpdate);
    }

    public function updateQualityControl($input)
    {
        $dataUpdate = [
            'status' => $input['status'],
            'updated_at' => date('Y-m-d H:i:s'),
        ];
        if (!empty($input['image'])) {
            $dataUpdate['image'] = $input['image'];
        }

        $this->qualityControlModel->updateById($input['id'], $dataUpdate);
    }

    public function getAll($input)
    {
        return $this->qualityControlModel->getALl($input);
    }

    public function getOrderItemByLabel($input)
    {
        return $this->qualityControlRepository->isItemSaleOrderItemBarcode($input);
    }

    public function updateTimeEnd($data)
    {
        $dataUpdate = [
            'end_time' => date('Y-m-d H:i:s')
        ];
        $idTimeChecking = $data['id_time_checking'];

        $this->timeCheckingRepository->updateTimeChecking($dataUpdate, $idTimeChecking);
    }

    public function handleQcWarehouseReport($warehouseId, $startDate, $endDate)
    {
        $results = DB::table('sale_order_item_quality_control AS qc')
            ->select(DB::raw("
        CASE
            WHEN qc.status IN ('defective item', 'wrong style', 'wrong size', 'wrong color') THEN 'Pulling'
            WHEN qc.status IN ('missing pretreat', 'heat press stain') THEN 'Pretreat'
            WHEN qc.status IN ('wrong print', 'wrong location print', 'faded print') THEN 'Printing'
            WHEN qc.status IN ('lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in QC') THEN 'Lost In Production'
            WHEN qc.status IN ('printing machine', 'pretreat machine') THEN 'Maintenance'
            WHEN qc.status IN ('wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print', 'vendor with hole', 'damaged/broken', 'with stain') THEN 'Vendor Defect'
            WHEN qc.status IN ('design placement issue', 'peeling print', 'fibrillation', 'wrong item', 'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print', 'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'
) THEN 'Customer claims'
            ELSE 'Other'
        END AS category,
        COUNT(*) AS count"))
            ->where('qc.warehouse_id', $warehouseId)
            ->where('qc.created_at', '>=', $startDate)
            ->where('qc.created_at', '<=', $endDate)
            ->whereIn('qc.status', [
                'defective item', 'wrong style', 'wrong size', 'wrong color',
                'missing pretreat', 'heat press stain', 'wrong print',
                'wrong location print', 'faded print',
                'lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in qc',
                'printing machine', 'pretreat machine',
                'wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print',
                'vendor with hole', 'damaged/broken', 'with stain', 'design placement issue', 'peeling print', 'fibrillation', 'wrong item',
                'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print',
                'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'

            ])
            ->groupBy('category')
            ->get();

        $totalCount = $results->sum('count');
        $departments = ['Pulling', 'Pretreat', 'Printing', 'Lost In Production', 'Maintenance', 'Vendor Defect', 'Customer claims'];
        $departmentData = [];
        foreach ($departments as $department) {
            $departmentCount = $results->firstWhere('category', $department);
            $count = $departmentCount ? $departmentCount->count : 0;
            $percentage = ($totalCount != 0) ? ($count / $totalCount) * 100 : 0;

            $departmentData[] = [
                'department' => $department,
                'count' => $count,
                'percentage' => round($percentage, 2),
            ];
        }

        return $departmentData;
    }

    public function getSlaQc($data)
    {
        setTimezone();
        $startDate = Carbon::parse($data['start_date'])->startOfDay();
        $endDate = Carbon::parse($data['end_date'])->endOfDay();
        $data['warehouse_id'] = config('jwt.warehouse_id');
        $warehouseId = $data['warehouse_id'];

        // Subtract 7 days from startDate
        $startDateQc = Carbon::parse($data['start_date'])
            ->subDays(14)
            ->startOfDay();

        if ($data['type'] == 'line') {
            $results = DB::table(table: 'sale_order_item_barcode_status as soibs')
                ->join(
                    DB::raw("(SELECT label_root_id, MIN(created_at) AS created_at
                  FROM sale_order_item_quality_control
                  WHERE status != 'pass'
                  AND label_root_id IS NOT NULL
                  AND created_at BETWEEN ? AND ?
                  AND warehouse_id = ?
                  GROUP BY label_root_id) AS sqc"),
                    'soibs.label_id', '=', 'sqc.label_root_id')
                ->addBinding([$startDateQc, $endDate, $warehouseId], 'join')
                ->select(
                    DB::raw('DATE_FORMAT(soibs.last_qc_at, "%Y-%m-%d") AS created_at_format'),
                    DB::raw('CASE WHEN soibs.total_reject >= 3 THEN 3 ELSE soibs.total_reject END AS adjusted_total_reject'),
                    'soibs.status',
                    DB::raw('SUM(TIMESTAMPDIFF(SECOND, sqc.created_at, soibs.last_qc_at)) / 86400 AS total_last_qc_in_days'),
                    DB::raw('COUNT(*) AS total_item_passed'),

                )
                ->where('soibs.status', 'pass')
                ->whereBetween('soibs.last_qc_at', [$startDate, $endDate])
                ->groupBy('created_at_format', 'adjusted_total_reject')
                ->get();

            for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
                $chartData[$date->format('Y-m-d')] = [
                    '1st_failed' => 0,
                    '2nd_failed' => 0,
                    '3rd_failed' => 0,
                ];
            }
            foreach ($results as $item) {
                $key = '1st_failed'; // Default key
                switch ($item->adjusted_total_reject) {
                    case 2:
                        $key = '2nd_failed';
                        break;
                    case 3:
                        $key = '3rd_failed';
                        break;
                }

                if (isset($chartData[$item->created_at_format])) {
                    $chartData[$item->created_at_format][$key] = round($item->total_last_qc_in_days / $item->total_item_passed, 2);
                }
            }

            return $chartData;
        } else {
            $results = DB::table('sale_order_item_barcode_status as soibs')
                ->select(
                    DB::raw('CASE WHEN soibs.total_reject >= 3 THEN 3 ELSE soibs.total_reject END AS adjusted_total_reject'),
                    DB::raw("CASE WHEN soibs.status != 'pass' THEN 'reject' ELSE soibs.status END AS current_status"),
                    DB::raw('COUNT(*) AS total_item'),
                )
                ->join('sale_order_item_barcode as bc', 'bc.label_id', '=', 'soibs.label_id')
                ->where('soibs.total_reject', '>=', 1)
                ->whereBetween('soibs.last_qc_at', [$startDate, $endDate])
                ->where('bc.warehouse_id', '=', $warehouseId)
                ->groupBy('adjusted_total_reject', 'current_status')
                ->get();
            $pieData = [
                '3rd_failed' => [
                    'total_rejected' => 0,
                    'total_passed' => 0
                ],
                '2nd_failed' => [
                    'total_rejected' => 0,
                    'total_passed' => 0
                ],
                '1st_failed' => [
                    'total_rejected' => 0,
                    'total_passed' => 0
                ]
            ];

            foreach ($results as $item) {
                // Determine the key based on qc_issue_count
                switch ($item->adjusted_total_reject) {
                    case 1:
                        $key = '1st_failed';
                        break;
                    case 2:
                        $key = '2nd_failed';
                        break;
                    case 3:
                        $key = '3rd_failed';
                        break;
                    default:
                        $key = '1st_failed';
                        break;
                }
                if (isset($item->current_status) && $item->current_status == 'pass') {
                    $pieData[$key]['total_passed'] = $item->total_item;
                } elseif (isset($item->current_status) && $item->current_status == 'reject') {
                    $pieData[$key]['total_rejected'] = $item->total_item;
                }
            }

            return $pieData;
        }
    }

    public function getListSlaQc($data, $getTotal = null)
    {
        setTimezone();
        $startDate = Carbon::parse($data['start_date'])->startOfDay();
        $endDate = Carbon::parse($data['end_date'])->endOfDay();
        $data['warehouse_id'] = config('jwt.warehouse_id');
        $limit = $data['limit'] ?? 25;
        $page = $data['page'] ?? 1;
        $offset = ($page - 1) * $limit;
        $warehouseId = $data['warehouse_id'];

        $query = BarcodeStatus::with([
            'listQcFailed:id,created_at,label_root_id'
        ])
            ->select([
                'store.code',
                'sale_order_item_barcode_status.status',
                'sale_order_item_barcode_status.last_qc_at',
                'sale_order_item_barcode_status.label_id',
                DB::raw('(qt.incoming_stock + qt.quantity) as stock'),
                'so.order_number',
                'so.order_status',
                'it.sku',
                'so.production_status',
                'so.created_at as order_created_at',
                'sale_order_item_barcode_status.total_reject',
                'sale_order_item_barcode_status.addition_cost'
            ])
            ->join(DB::raw('(SELECT qc.order_id, qc.order_item_id, qc.label_root_id
                         FROM sale_order_item_quality_control qc
                         WHERE qc.created_at BETWEEN ? AND ?
                         AND qc.label_root_id IS NOT NULL
                         GROUP BY qc.label_root_id) as data_qc'), function ($join) {
                $join->on('data_qc.label_root_id', '=', 'sale_order_item_barcode_status.label_id');
            })
            ->addBinding([$startDate, $endDate], 'join')
            ->join('sale_order as so', 'so.id', '=', 'data_qc.order_id')
            ->join('sale_order_item as it', 'data_qc.order_item_id', '=', 'it.id')
            ->leftJoin('product_quantity as qt', function ($join) use ($warehouseId) {
                $join->on('it.product_id', '=', 'qt.product_id')
                    ->where('qt.warehouse_id', '=', $warehouseId);
            })
            ->join('store', 'store.id', '=', 'so.store_id')
            ->where('sale_order_item_barcode_status.total_reject', '>=', 1)
            ->where('so.warehouse_id', $warehouseId)
            ->whereBetween('sale_order_item_barcode_status.last_qc_at', [$startDate, $endDate])

            // Apply filters based on input data
            ->when(!empty($data['store_id']), function ($query) use ($data) {
                $query->where('so.store_id', $data['store_id']);
            })
            ->when(!empty($data['order_status']), function ($query) use ($data) {
                $query->where('so.order_status', $data['order_status']);
            })
            ->when(!empty($data['order_number']), function ($query) use ($data) {
                $query->where('so.order_number', $data['order_number']);
            })
            ->when(!empty($data['prod_status']), function ($query) use ($data) {
                $query->where('so.production_status', $data['prod_status']);
            })
            ->when(!empty($data['label_id']), function ($query) use ($data) {
                $query->where('sale_order_item_barcode_status.label_id', $data['label_id']);
            })
            ->when(!empty($data['qc_status']), function ($query) use ($data) {
                if ($data['qc_status'] === 'pass') {
                    $query->where('sale_order_item_barcode_status.status', 'pass');
                } else {
                    $query->where('sale_order_item_barcode_status.status', '!=', 'pass');
                }
            })
            ->when(!empty($data['keyword']), function ($query) use ($data) {
                $query->where('it.sku', 'like', '%' . $data['keyword'] . '%');
            });

        if (!$getTotal) {
            $query->orderBy('sale_order_item_barcode_status.last_qc_at', 'desc');
        }
        if ($getTotal == true) {
            $totalAdditionCost = $query->sum('sale_order_item_barcode_status.addition_cost');

            return [
                'total_count' => $query->get()->count(),
                'total_addition_cost' => $totalAdditionCost
            ];
        } else {
            $results = $query->offset($offset)->limit($limit + 1)->get();

            $results->transform(callback: function ($saleOrder) {
                $saleOrder->created_at_utc = shiftTimezoneToUTC($saleOrder->order_created_at);
                $saleOrder->last_qc_at = shiftTimezoneToUTC($saleOrder->last_qc_at);

                $saleOrder->first_qc = isset($saleOrder['listQcFailed'][0])
                    ? shiftTimezoneToUTC($saleOrder['listQcFailed'][0]['created_at'])
                    : null;

                $saleOrder->second_qc = isset($saleOrder['listQcFailed'][1])
                    ? shiftTimezoneToUTC($saleOrder['listQcFailed'][1]['created_at'])
                    : null;
                $saleOrder->third_qc = $saleOrder->total_reject >= 3
                    ? shiftTimezoneToUTC($saleOrder['listQcFailed']->last()->created_at)
                    : null;

                return $saleOrder;
            });

            // Determine if there's a next page
            $hasMore = $results->count() > $limit;

            // Slice the results if there's an extra record
            if ($hasMore) {
                $results = $results->slice(0, $limit);
            }

            return new Paginator($results, $limit, $page, ['path' => Paginator::resolveCurrentPath()]);
        }
    }

    public function insertBarcodeStatus($label_id, $status = 'pass')
    {
        $updateData = [
            'status' => $status,
            'last_qc_at' => Carbon::now(),
        ];

        if ($status != 'pass') {
            $updateData['total_reject'] = DB::raw('total_reject + 1');
        }

        return BarcodeStatus::updateOrCreate(['label_id' => $label_id], $updateData);
    }

    public function getReportSlaQc($data)
    {
        settimezone();
        $warehouseId = $data['warehouse_id'];
        $startDate = Carbon::parse($data['current_date_start'])->startOfDay();
        $endDate = Carbon::parse($data['current_date_end'])->endOfDay();
        $previousStartDate = Carbon::parse($data['previous_date_start'])->startOfDay();
        $previousEndDate = Carbon::parse($data['previous_date_end'])->endOfDay();

        $totalQc = SaleOrderItemQualityControl::whereBetween('created_at', [$startDate, $endDate])
            ->when($warehouseId != 'all', function ($query) use ($warehouseId) {
                // Add the warehouse_id filter if $warehouseId is not 'all'
                return $query->where('warehouse_id', $warehouseId);
            })
            ->whereIn('status', [
                'pass',
                'defective item', 'wrong style', 'wrong size', 'wrong color',
                'missing pretreat', 'heat press stain', 'wrong print',
                'wrong location print', 'faded print', 'crooked print',
                'lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in qc',
                'printing machine', 'pretreat machine',
                'wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print',
                'vendor with hole', 'damaged/broken', 'with stain', 'design placement issue', 'peeling print', 'fibrillation', 'wrong item',
                'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print',
                'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'

            ])
            ->whereNotNull('label_root_id')
            ->count();

        $previousTotalQc = SaleOrderItemQualityControl::whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->when($warehouseId != 'all', function ($query) use ($warehouseId) {
                // Add the warehouse_id filter if $warehouseId is not 'all'
                return $query->where('warehouse_id', $warehouseId);
            })
            ->whereIn('status', [
                'pass',
                'defective item', 'wrong style', 'wrong size', 'wrong color',
                'missing pretreat', 'heat press stain', 'wrong print',
                'wrong location print', 'faded print', 'crooked print',
                'lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in qc',
                'printing machine', 'pretreat machine',
                'wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print',
                'vendor with hole', 'damaged/broken', 'with stain', 'design placement issue', 'peeling print', 'fibrillation', 'wrong item',
                'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print',
                'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'

            ])
            ->whereNotNull('label_root_id')
            ->count();

        $results = SaleOrderItemQualityControl::select(DB::raw("
        CASE
            WHEN status IN ('defective item', 'wrong style', 'wrong size', 'wrong color') THEN 'Pulling'
            WHEN status IN ('missing pretreat', 'heat press stain') THEN 'Pretreat'
            WHEN status IN ('wrong print', 'wrong location print', 'faded print', 'crooked print') THEN 'Printing'
            WHEN status IN ('lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in QC') THEN 'Lost In Production'
            WHEN status IN ('printing machine', 'pretreat machine') THEN 'Maintenance'
            WHEN status IN ('wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print', 'vendor with hole', 'damaged/broken', 'with stain') THEN 'Vendor Defect'
            WHEN status IN ('design placement issue', 'peeling print', 'fibrillation', 'wrong item', 'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print', 'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'
) THEN 'Customer claims'
            ELSE 'Other'
        END AS category,
        SUM(addition_cost) as cost,
        COUNT(*) AS count"))
            ->when($warehouseId != 'all', function ($query) use ($warehouseId) {
                // Add the warehouse_id filter if $warehouseId is not 'all'
                return $query->where('warehouse_id', $warehouseId);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('status', [
                'defective item', 'wrong style', 'wrong size', 'wrong color',
                'missing pretreat', 'heat press stain', 'wrong print',
                'wrong location print', 'faded print', 'crooked print',
                'lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in qc',
                'printing machine', 'pretreat machine',
                'wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print',
                'vendor with hole', 'damaged/broken', 'with stain', 'design placement issue', 'peeling print', 'fibrillation', 'wrong item',
                'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print',
                'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'

            ])
            ->whereNotNull('label_root_id')
            ->groupBy('category')
            ->get();

        $previousResults = SaleOrderItemQualityControl::select(DB::raw("
            CASE
                WHEN status IN ('defective item', 'wrong style', 'wrong size', 'wrong color') THEN 'Pulling'
                WHEN status IN ('missing pretreat', 'heat press stain') THEN 'Pretreat'
                WHEN status IN ('wrong print', 'wrong location print', 'faded print', 'crooked print') THEN 'Printing'
                WHEN status IN ('lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in QC') THEN 'Lost In Production'
                WHEN status IN ('printing machine', 'pretreat machine') THEN 'Maintenance'
                WHEN status IN ('wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print', 'vendor with hole', 'damaged/broken', 'with stain') THEN 'Vendor Defect'
                WHEN status IN ('design placement issue', 'peeling print', 'fibrillation', 'wrong item', 'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print', 'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'
    ) THEN 'Customer claims'
                ELSE 'Other'
            END AS category,
            SUM(addition_cost) as cost,
            COUNT(*) AS count"))
            ->when($warehouseId != 'all', function ($query) use ($warehouseId) {
                // Add the warehouse_id filter if $warehouseId is not 'all'
                return $query->where('warehouse_id', $warehouseId);
            })
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->whereIn('status', [
                'defective item', 'wrong style', 'wrong size', 'wrong color',
                'missing pretreat', 'heat press stain', 'wrong print',
                'wrong location print', 'faded print', 'crooked print',
                'lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in qc',
                'printing machine', 'pretreat machine',
                'wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print',
                'vendor with hole', 'damaged/broken', 'with stain', 'design placement issue', 'peeling print', 'fibrillation', 'wrong item',
                'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print',
                'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'

            ])
            ->whereNotNull('label_root_id')
            ->groupBy('category')
            ->get();

        $totalCount = $results->sum('count');
        $previousTotalCount = $previousResults->sum('count');
        $departments = ['Pulling', 'Pretreat', 'Printing', 'Lost In Production', 'Maintenance', 'Vendor Defect', 'Customer claims'];
        $departmentData = [];
        foreach ($departments as $department) {
            $departmentCount = $results->firstWhere('category', $department);
            $count = $departmentCount ? $departmentCount->count : 0;
            $percentage = ($totalCount != 0) ? ($count / $totalCount) * 100 : 0.00;
            $rate = ($count > 0) ? ($count / $totalQc) * 100 : 0.00;

            $previousDepartmentCount = $previousResults->firstWhere('category', $department);
            $previouscount = $previousDepartmentCount ? $previousDepartmentCount->count : 0;
            $previousPercentage = ($previousTotalCount != 0) ? ($previouscount / $previousTotalCount) * 100 : 0.00;
            $previousrate = ($previouscount > 0) ? ($previouscount / $previousTotalQc) * 100 : 0.00;

            $departmentData[] = [
                'department' => $department,
                'count' => $count,
                'percentage' => number_format($percentage, 2),
                'rate' => number_format($rate, 2),
                'cost' => isset($departmentCount->cost) ? number_format($departmentCount->cost, 2) : number_format(0.00, 2),
                'previous_count' => $previouscount,
                'previous_percentage' => number_format($previousPercentage, 2),
                'previous_rate' => number_format($previousrate, 2),
                'previous_cost' => isset($previousDepartmentCount->cost) ? number_format($previousDepartmentCount->cost, 2) : number_format(0.00, 2),
            ];
        }

        return ['data' => $departmentData,
            'total' => [
                'current_total' => $totalQc,
                'previous_total' => $previousTotalQc
            ]
        ];
    }

    public function getWeeklySlaQc($warehouseId, $startDate, $endDate)
    {
        settimezone();
        $startDate = Carbon::parse($startDate)->startOfDay();
        $endDate = Carbon::parse($endDate)->endOfDay();
        $dateRange = [];
        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $dateRange[] = $currentDate->format('Y-m-d');
            $currentDate->addDay();
        }

        $results = SaleOrderItemQualityControl::select(DB::raw("
        DATE_FORMAT(created_at, '%Y-%m-%d') AS formatted_date,
        SUM(addition_cost) AS total_cost"))
            ->when($warehouseId != 'all', function ($query) use ($warehouseId) {
                // Add the warehouse_id filter if $warehouseId is not 'all'
                return $query->where('warehouse_id', $warehouseId);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('status', [
                'defective item', 'wrong style', 'wrong size', 'wrong color',
                'missing pretreat', 'heat press stain', 'wrong print',
                'wrong location print', 'faded print', 'crooked print',
                'lost in pulling', 'lost in pretreat', 'lost in printing', 'lost in qc',
                'printing machine', 'pretreat machine',
                'wrong garment size', 'out of tolerance', 'threads issue', 'torn garment', 'stained garment', 'misaligned print',
                'vendor with hole', 'damaged/broken', 'with stain', 'design placement issue', 'peeling print', 'fibrillation', 'wrong item',
                'defective item - faded print', 'pilling', 'defective item - wrong size', 'broken', 'stains', 'holes', 'stitching', 'coloring issue', 'sizing issue', 'smell', 'defective item - wrong print',
                'missing item', 'heat press', 'missing print', 'underbase issue', 'blurry print', 'torn', 'mechanical', 'packaging issue', 'lines'
            ])
            ->whereNotNull('label_root_id')
            ->groupBy('formatted_date') // group by formatted date
            ->get()
            ->keyBy('formatted_date');

        $finalResults = [];

        foreach ($dateRange as $date) {
            $finalResults[] = [
                'formatted_date' => $date,
                'cost' => isset($results[$date]) ? $results[$date]->total_cost : number_format(0.00, 2),
            ];
        }

        return $finalResults;
    }

    public function insertBarcodeAdditionCost($product_id, $label_id)
    {
        $latestOrder = PurchaseOrder::select('purchase_order_item.*', 'purchase_order.id as po_id')
            ->join('purchase_order_item', 'purchase_order_item.po_id', '=', 'purchase_order.id')
            ->whereNotNull('purchase_order_item.price')
            ->where('purchase_order_item.product_id', $product_id)
            // ->where('purchase_order.order_status', 'completed')
            ->orderBy('purchase_order.id', 'desc')
            ->first();

        $productValue = $latestOrder ? ($latestOrder->price ?? 0.00) : 0.00;

        $barcode = BarcodeStatus::where('label_id', $label_id)->first();

        if (!$barcode) {
            return false;
        }

        $barcode->update([
            'addition_cost' => $barcode->addition_cost + $productValue
        ]);

        return true;
    }
}
