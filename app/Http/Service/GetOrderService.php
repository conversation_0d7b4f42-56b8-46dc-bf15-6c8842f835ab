<?php

namespace App\Http\Service;

use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductSkuShipstationNullModel;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderAddress;
use App\Models\SaleOrderAutomationRule;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderOnHold;
use App\Models\ShipstationOrder;
use App\Models\Store;
use App\Models\StoreOnHoldHistory;
use App\Models\Tag;
use App\Models\Warehouse;
use Carbon\Carbon;
use Hashids\Hashids;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GetOrderService
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function getAllAccountShipStation()
    {
        return SaleOrderAccount::where('is_active', 1)
            ->where('source', 'shipstation')
            ->where('is_editing', '!=', 1)->get();
    }

    public function getOrderDeskNewest($startDate, $endDate)
    {
        $onlyGetNewOrder = 1;
        // get only account orderdesk
        $account = SaleOrderAccount::where('is_active', 1)
            ->where('id', 1)->first();

        if (!$account->api_key || !$account->api_secret) {
            return false;
        }

        $shipStationService = new ShipstationService($account->api_key, $account->api_secret, $account->partner_id);

        $page = 1;
        while (true) {
            jobEcho("page: $page");

            $data = $shipStationService->getOrderByCreatedDate($startDate, $endDate, $page, 'ASC');

            if (isset($data->orders) && count($data->orders) > 0) {
                jobEcho("total: $data->total");
                $this->insertOrders($data->orders, $account->id);
            }

            if (empty($data) || $page >= $data->pages) {
                break;
            }

            $page++;
        }
    }

    public function getOrderShipStation($backDate = -3, $onlyGetNewOrder = 0, $getOrderNumber = null)
    {
        $listAccountActive = $this->getAllAccountShipStation();
        foreach ($listAccountActive as $account) {
            if (!$account->api_key || !$account->api_secret) {
                continue;
            }
            $shipStationService = new ShipstationService($account->api_key, $account->api_secret, $account->partner_id);
            $page = 1;
            while (true) {
                jobEcho("page: $page");
                if ($getOrderNumber) {
                    $data = $shipStationService->getDataByOrderNumber($page, $getOrderNumber);
                } else {
                    $data = $shipStationService->getDataByDate($page, $backDate);
                }

                if (isset($data->orders) && count($data->orders) > 0) {
                    jobEcho("total: $data->total");
                    $this->insertDataToListTable($data->orders, $account->id, $onlyGetNewOrder);
                }

                if (empty($data) || $page >= $data->pages) {
                    break;
                }
                $page++;
                sleep(2);
            }
            sleep(10);
        }
    }

    public function getOrderShipStationByRangeDate($start, $end)
    {
        $listAccountActive = $this->getAllAccountShipStation();

        foreach ($listAccountActive as $account) {
            $tagTexas = Tag::where('name', 'TEXAS')->first();
            if (!$account->api_key || !$account->api_secret) {
                continue;
            }
            $shipStationService = new ShipstationService($account->api_key, $account->api_secret, $account->partner_id);
            $page = 1;
            while (true) {
                jobEcho("page: $page");

                $list = $shipStationService->getDataByRangeDate($start, $end, $page);

                if (isset($list->orders) && count($list->orders) > 0) {
                    $this->insertDataToListTableRangeDate($list->orders, $account->id, $tagTexas);
                } else {
                    break;
                }
                $page++;
                //   sleep(2);
            }
            sleep(2);
        }
    }

    public function logOrderShipStationByRangeDate($start, $end)
    {
        $listAccountActive = $this->getAllAccountShipStation();

        foreach ($listAccountActive as $account) {
            if (!$account->api_key || !$account->api_secret) {
                continue;
            }
            $shipStationService = new ShipstationService($account->api_key, $account->api_secret, $account->partner_id);
            $page = 1;
            while (true) {
                jobEcho("page: $page");

                $list = $shipStationService->getDataByRangeDate($start, $end, $page);

                if (isset($list->orders) && count($list->orders) > 0) {
                    foreach ($list->orders as $item) {
                        $check = DB::table('log_shipstation_order')->where('external_id', $item->orderId)
                            ->where('account_id', $account->id)->first();

                        if (!$check) {
                            jobEcho("$item->orderNumber $item->orderDate not exist");
                            DB::table('log_shipstation_order')->insert([
                                'external_id' => $item->orderId,
                                'account_id' => $account->id,
                                'external_number' => $item->orderNumber,
                                'order_date' => $item->orderDate
                            ]);
                        }

                        $order = SaleOrder::where('external_id', $item->orderId)
                            ->where('account_id', $account->id)
                            ->first();

                        if ($order && in_array($item->orderStatus, [SaleOrder::STATUS_CANCELLED, SaleOrder::STATUS_SHIPPED, SaleOrder::STATUS_REJECT])) {
                            if (!in_array($order->order_status, [SaleOrder::STATUS_CANCELLED, SaleOrder::STATUS_SHIPPED, SaleOrder::STATUS_REJECT])) {
                                SaleOrder::where('external_id', $item->orderId)
                                    ->where('account_id', $account->id)
                                    ->update(['order_status' => $item->orderStatus]);
                                jobEcho("$item->orderId ($item->orderDate) => shipped");

                                SaleOrderHistory::create([
                                    'order_id' => $order->id,
                                    'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                                    'message' => "Order status changed from \"$order->order_status\" to \"$item->orderStatus\"",
                                    'created_at' => Carbon::now()->toDateTimeString()
                                ]);
                            }
                        }
                    }
                } else {
                    break;
                }
                $page++;
            }
            //   sleep(2);
        }
    }

    public function handleTag($listTag, $tag)
    {
        $tagValue = [];
        if (is_array($tag)) {
            foreach ($tag as $item) {
                if (isset($listTag[$item])) {
                    //                    echo "isset co tag $item";
                    $tagValue[$listTag[$item]] = $listTag[$item];
                }
            }
        }

        return [
            'array' => $tagValue,
            'string' => implode(',', array_values($tagValue))
        ];
    }

    public function convertDate($date)
    {
        // return date("Y-m-d H:i:s", strtotime($date));
        return Carbon::parse($date)->shiftTimezone('America/Los_Angeles')->setTimezone('UTC');
    }

    public function checkPrintSideOption($option)
    {
        $printSide = -1;
        $allowSide = ['printfiles.front', 'printfiles.back', 'printfiles.pocket'];
        foreach ($option as $item) {
            if (!property_exists($item, 'name') || !property_exists($item, 'value') || !in_array(strtolower($item->name), $allowSide) || empty($item->value)) {
                continue;
            }

            $listSide = explode('.', $item->name);
            // if (isset($listSide[0]) && $listSide[0] == 'PrintFiles' && $item->value) {
            if (strtolower($listSide[1]) == 'front' || strtolower($listSide[1]) == 'pocket') {
                $printSide = $printSide <= 0 ? 0 : 2;
            } else {
                $printSide = $printSide < 0 || $printSide == 1 ? 1 : 2;
            }
        }
        //dat gia tri mac dinh
        $printSide = $printSide >= 0 ? $printSide : 0;

        return $printSide;
    }

    public function checkPrintSidesOption($options)
    {
        $printSideNames = [];
        foreach ($options as $option) {
            if (!property_exists($option, 'name') || !property_exists($option, 'value') || empty($option->value)) {
                continue;
            }
            $frontDesign = explode('.', $option->name);
            if ($frontDesign[0] == 'PrintFiles') {
                // $codeName = strtolower($frontDesign[1]);
                if (ctype_upper($frontDesign[1])) {
                    $printSideNames[] = strtolower($frontDesign[1]);
                } else {
                    $replace = strtolower(str_replace(' ', '', $frontDesign[1]));
                    if (ctype_lower($replace)) {
                        $printSideNames[] = $replace;
                    } else {
                        // $codeName = strtolower(trim(implode("_", preg_split('/(?=[A-Z])/', $replace)), "_"));
                        $printSideNames[] = str_replace('_', '', $replace);
                    }
                }
            }
        }
        $printSides = ProductPrintSide::whereIn(DB::raw("REPLACE(code_name, '_', '')"), $printSideNames)->orderBy('order', 'ASC')->get()->pluck('code_wip');

        return implode('', $printSides->toArray());
    }

    public function deleteItemBarcodeByOrderId($orderId)
    {
        return DB::table('sale_order_item_barcode')
            ->where('order_id', $orderId)
            ->update(['is_deleted' => 1]);
    }

    public function deleteItemBarcodeByOrderIdAndSku($orderId, $sku)
    {
        return DB::table('sale_order_item_barcode')
            ->where('order_id', $orderId)
            ->where('sku', $sku)
            ->update(['is_deleted' => 1]);
    }

    public function updateShippingAddress($item, $orderId)
    {
        $address = SaleOrderAddress::query()->where('order_id', $orderId)
            ->where('type_address', 'to_address')->first();

        $data = [
            'type_address' => 'to_address',
            'order_id' => $orderId,
            'name' => $item->shipTo->name,
            'email' => $item->customerEmail,
            'company' => $item->shipTo->company,
            'phone' => $item->shipTo->phone ?? '',
            'street1' => $item->shipTo->street1 ?? '',
            'street2' => $item->shipTo->street2 ?? '',
            'city' => $item->shipTo->city ?? '',
            'state' => $item->shipTo->state ?? '',
            'zip' => $item->shipTo->postalCode ?? '',
            'country' => $item->shipTo->country ?? '',
            'residential' => $item->shipTo->residential ?? true,
            'verified_status' => $item->shipTo->addressVerified == 'Address validated successfully' ? 1 : 2,
            'verified_message' => $item->shipTo->addressVerified,
        ];

        if ($address) {
            $data['updated_at'] = date('Y-m-d H:i:s');
            SaleOrderAddress::query()->where('id', $address->id)->limit(1)->update($data);
            jobEcho("update address: $orderId");
        } else {
            jobEcho("add address: $orderId");
            $data['created_at'] = date('Y-m-d H:i:s');
            SaleOrderAddress::query()->insert($data);
        }
    }

    public function insertDataToListTableRangeDate($listItem, $accountId, $tagTexas = false)
    {
        foreach ($listItem as $key => $item) {
            // begin insert
            //            DB::beginTransaction();
            $orderDateCreate = $item->orderDate;
            // tìm order
            $isSaleOrderExist = SaleOrder::where('account_id', $accountId)
                ->where('external_id', $item->orderId)
                ->first();
            if ($isSaleOrderExist) {
                continue;
            }
            $isSaleOrderExist = SaleOrder::where('account_id', $accountId)
                ->where('external_number', $item->orderNumber)
                ->first();
            if ($isSaleOrderExist) {
                continue;
            }

            $listTag = Tag::where('account_id', $accountId)
                ->where('source', 'sale_order')
                ->pluck('id', 'external_id');
            $rawDataInsertSaleOrder = $this->buildRawDataInsertTableSaleOrder($item, $accountId);

            $storeStandbox = 264520;

            if ($rawDataInsertSaleOrder['store_id'] == $storeStandbox && $rawDataInsertSaleOrder['account_id'] == 3) {
                continue;
            }

            $storeIdCheck = $item->advancedOptions->storeId ?? '';

            $listTagData = $this->handleTag($listTag, $item->tagIds);
            $rawDataInsertSaleOrder['tag'] = $listTagData['string'];

            $listFormatLabel = false;
            if (is_array($item->items) && count($item->items)) {
                $listFormatLabel = $this->findSlOrMul($item->items);
            }

            $zipCode = $item->shipTo->postalCode;
            $country = $item->shipTo->country ?? '';

            if ($isSaleOrderExist) {
                if ($isSaleOrderExist->order_status != SaleOrder::NEW_ORDER && $rawDataInsertSaleOrder['order_status'] == SaleOrder::NEW_ORDER) {
                    //giữ nguyên status order khi order trong db không phải new order còn order status của shipstation là new_order
                    $rawDataInsertSaleOrder['order_status'] = $isSaleOrderExist->order_status;
                }
                $rawDataInsertSaleOrder['is_xqc'] = $this->checkSampleXQC($isSaleOrderExist->id);
                $rawDataInsertSaleOrder['is_eps'] = $this->checkIsEps($isSaleOrderExist->id);
                $rawDataInsertSaleOrder['shipping_method'] = $rawDataInsertSaleOrder['is_eps'] == 1 ? SaleOrder::SHIPPING_METHOD_EXPRESS : $rawDataInsertSaleOrder['shipping_method'];
                $rawDataInsertSaleOrder['order_quantity'] = array_reduce($item->items, function ($result, $item) {
                    return $result + $item->quantity;
                });
                unset($rawDataInsertSaleOrder['created_at']);
                SaleOrder::where('id', $isSaleOrderExist->id)->update($rawDataInsertSaleOrder);
                $orderId = $isSaleOrderExist->id;

                $this->updateShippingAddress($item, $orderId);
            } else {
                $warehouseIdCheck = 1;
                $rawDataInsertSaleOrder['warehouse_id'] = $warehouseIdCheck;

                $rawDataInsertSaleOrder['order_quantity'] = array_reduce($item->items, function ($result, $item) {
                    return $result + $item->quantity;
                });

                // doan nay de bo qua don bi trung index unique
                try {
                    $orderId = SaleOrder::insertGetId($rawDataInsertSaleOrder);
                } catch (\Exception $e) {
                    echo $e->getMessage();

                    continue;
                }

                // insert shipping address

                $this->updateShippingAddress($item, $orderId);
                $check_is_eps = $this->checkIsEps($orderId);

                $rawDataUpdateAfterXqc = [
                    'is_xqc' => $this->checkSampleXQC($orderId),
                    'is_eps' => $check_is_eps,
                    'shipping_method' => $check_is_eps == 1 ? SaleOrder::SHIPPING_METHOD_EXPRESS : $rawDataInsertSaleOrder['shipping_method'],
                ];

                SaleOrder::where('id', $orderId)->update($rawDataUpdateAfterXqc);
            }

            if ($rawDataInsertSaleOrder['order_status'] == 'cancelled' && $orderId) {
                $this->deleteItemBarcodeByOrderId($orderId);
                jobEcho('====== Order cancel ****** V1');
            }

            if ($this->checkDoNotPrintWip($orderId) == 1) {
                $this->deleteItemBarcodeByOrderId($orderId);
                jobEcho("====== do not print wip $orderId ******");
            }

            $rawDataLog = $this->buildRawDataInsertShipstationOrder($item, $accountId, $orderId);
            $isShipStationOrder = ShipstationOrder::where('account_id', $accountId)
                ->where('order_id', $rawDataLog['order_id'])
                ->first();
            if (!$isShipStationOrder) {
                ShipstationOrder::insert($rawDataLog);
            } else {
                unset($rawDataLog['created_at']);
                ShipstationOrder::where('id', $isShipStationOrder->id)->update($rawDataLog);
            }
            $listProductSku = [];
            if (is_array($item->items) && count($item->items) && $orderId) {
                foreach ($item->items as $data) {
                    $rawDataInsertSaleOrderItem = $this->buildRawDataInsertTableSaleOrderItem($data, $orderId);
                    if ($rawDataInsertSaleOrderItem['quantity'] == 0 && $rawDataInsertSaleOrderItem['sku']) {
                        $this->deleteItemBarcodeByOrderIdAndSku($orderId, $rawDataInsertSaleOrderItem['sku']);
                        jobEcho('====== Order cancel ******  V2 ');
                    }
                    if (isset($rawDataInsertSaleOrderItem['product_sku']) && $rawDataInsertSaleOrderItem['product_sku']) {
                        $product = Product::where('sku', $rawDataInsertSaleOrderItem['product_sku'])->first();
                        if ($product) {
                            $rawDataInsertSaleOrderItem['product_id'] = $product->id;
                        } else {
                            $rawDataInsertSaleOrderItem['product_id'] = null;
                            $productShipstationNullModel = new ProductSkuShipstationNullModel();
                            $isProductShipstationNull = $productShipstationNullModel->findBySku($rawDataInsertSaleOrderItem['product_sku']);
                            if (!$isProductShipstationNull) {
                                $productShipstationNullModel->insert([
                                    'sku' => $rawDataInsertSaleOrderItem['product_sku'],
                                    'created_at' => date('Y-m-d H:i:s')
                                ]);
                            }
                        }
                    }
                    if (count($data->options)) {
                        $printSide = $this->checkPrintSideOption($data->options);
                        $printSides = $this->checkPrintSidesOption($data->options);
                        jobEcho("Xu ly print side ######## $printSide : orderId $orderId");
                        $rawDataInsertSaleOrderItem['print_side'] = $printSide;
                        $rawDataInsertSaleOrderItem['print_sides'] = $printSides;
                    }

                    $rawDataInsertSaleOrderItem['store_id'] = $rawDataInsertSaleOrder['store_id'];
                    $rawDataInsertSaleOrderItem['account_id'] = $rawDataInsertSaleOrder['account_id'];
                    $rawDataInsertSaleOrderItem['warehouse_id'] = !$isSaleOrderExist ? $rawDataInsertSaleOrder['warehouse_id'] : $isSaleOrderExist->warehouse_id;

                    $isExist = SaleOrderItem::where('external_id', $data->orderItemId)->first();
                    if ($isExist) {
                        unset($rawDataInsertSaleOrderItem['created_at']);
                        SaleOrderItem::where('id', $isExist->id)->update($rawDataInsertSaleOrderItem);
                        $orderItemId = $isExist->id;
                    } else {
                        $orderItemId = SaleOrderItem::insertGetId($rawDataInsertSaleOrderItem);
                    }
                    if (!is_array($data->options) || !count($data->options)) {
                        continue;
                    }
                    $isDoubleSide = 0;
                    $i = 0;
                    foreach ($data->options as $item) {
                        $listSide = explode('.', $item->name);
                        if (isset($listSide[0]) && $listSide[0] == 'PrintFiles' && $item->value) {
                            $i++;
                        }
                    }

                    if ($i == 2) {
                        $isDoubleSide = 1;
                    }
                    //                    $isDoubleSide = count($data->options) > 1 ? 1 : 0;
                    foreach ($data->options as $side) {
                        if (!$side->value) {
                            continue;
                        }
                        $listSide = explode('.', $side->name);
                        if (isset($listSide[0]) && $listSide[0] == 'PrintFiles') {
                            $dataInsertItem = $this->buildRawDataSaleOrderItemImage($data, $orderId, $orderItemId, $isDoubleSide, $side, $orderDateCreate, $accountId);

                            if (!$dataInsertItem) {
                                continue;
                            }

                            if ($dataInsertItem['product_sku']) {
                                $listProductSku[] = $dataInsertItem['product_sku'];
                            }
                            $dataInsertItem['store_id'] = $storeIdCheck;
                            // Nho check link da ton tai chua
                            if (is_array($dataInsertItem) && count($dataInsertItem)) {
                                $isItemImage = SaleOrderItemImage::where('order_id', $orderId)
                                    ->where('sku', $dataInsertItem['sku'])
                                    ->where('print_side', $dataInsertItem['print_side'])
                                    ->first();
                                //  DB::beginTransaction();
                                try {
                                    if ($isItemImage) {
                                        if ($isItemImage->is_tool_update == 1) {
                                            unset($dataInsertItem['warehouse_id']);
                                        }
                                        unset($dataInsertItem['created_at']);
                                        SaleOrderItemImage::where('id', $isItemImage->id)->update($dataInsertItem);
                                        //                                    echo "update image !  $isItemImage->id \n";
                                    } else {
                                        $dataInsertItem['warehouse_id'] = 1;
                                        $insertImage = SaleOrderItemImage::insertGetId($dataInsertItem);
                                        //                                    echo "insert image $insertImage";
                                    }
                                    //  DB::commit();
                                } catch (\Exception $exception) {
                                    // DB::rollBack();
                                    echo $exception->getMessage();
                                    Log::error($exception->getMessage());
                                    // sleep(3);
                                }
                            }
                        }
                    }
                    jobEcho("order item id: $orderItemId");
                }
            }
            if (!$isSaleOrderExist) {
                $rawDataInsertSaleOrder['is_xqc'] = $rawDataUpdateAfterXqc['is_xqc'];
                $rawDataInsertSaleOrder['is_eps'] = $rawDataUpdateAfterXqc['is_eps'];
                $this->moveWarehouse($orderId, $listProductSku, $zipCode, $country, $rawDataInsertSaleOrder);

                jobEcho('create new !!!');

                var_dump($listFormatLabel);

                if ($listFormatLabel) {
                    $labelFormatSaleOrder = $this->getLabelFormat($listFormatLabel, $orderId);

                    var_dump($labelFormatSaleOrder);

                    if ($labelFormatSaleOrder) {
                        jobEcho("Update order number $labelFormatSaleOrder");
                        SaleOrder::where('id', $orderId)->update(['order_number' => $labelFormatSaleOrder]);
                    }
                }
            } else {
                var_dump($isSaleOrderExist->order_number);
                if ($isSaleOrderExist->order_number === null) {
                    if ($listFormatLabel) {
                        jobEcho('fix order number null !!! ');
                        $labelFormatSaleOrder = $this->getLabelFormat($listFormatLabel, $orderId);
                        if ($labelFormatSaleOrder) {
                            jobEcho("Update order number $labelFormatSaleOrder");
                            SaleOrder::where('id', $orderId)->update(['order_number' => $labelFormatSaleOrder]);
                        }
                    }
                }
            }
            jobEcho("$orderId order id ---- ");
            // end insert
            //            DB::commit();
            sleep(0.2);
        }
    }

    public function updateOrderCreateTime($listItem, $accountId)
    {
        foreach ($listItem as $item) {
            $isSaleOrderExist = SaleOrder::where('account_id', $accountId)
                ->where('external_id', $item->orderId)
                ->first();
            if ($isSaleOrderExist) {
                $up = ['external_created_at' => $this->convertDate($item->createDate)];
                SaleOrder::where('external_id', $item->orderId)->update($up);
                jobEcho("update date $item->orderId :" . $this->convertDate($item->createDate));
            }
        }
    }

    public function insertDataToListTable($listItem, $accountId, $onlyGetNewOrder = 0)
    {
        foreach ($listItem as $key => $item) {
            $time = now();
            // begin insert
            //            DB: :beginTransaction();
            $orderDateCreate = $item->orderDate;

            $storeIdCheck = $item->advancedOptions->storeId ?? '';

            // check neu bo qua ko dong bo shipstation

            $store = Store::find($storeIdCheck);
            // echo "stop sync shipstation: ".$store->stop_sync_shipstation_orders_at." / ".date('Y-m-d H:i:s');
            if ($store && !empty($store->stop_sync_shipstation_orders_at)) {
                if (strtotime($item->orderDate) > strtotime('2022-09-25T21:10:44.1530000') && $item->orderNumber != '7750-***********') {
                    jobEcho("$store->name => skip khong dong bo don shipstation");

                    continue;
                }
            }

            // tìm order
            $isSaleOrderExist = SaleOrder::where('account_id', $accountId)
                ->where('external_id', $item->orderId)
                ->first();

            if ($onlyGetNewOrder == 1 && $isSaleOrderExist) {
                // neu khong phai order moi thi khong xu ly
                jobEcho("skip update: $item->orderId");

                continue;
            }
            if ($onlyGetNewOrder == 0 && !$isSaleOrderExist) {
                jobEcho("skip new order: $item->orderId");

                continue;
            }

            $listTag = Tag::where('account_id', $accountId)
                ->where('source', 'sale_order')
                ->pluck('id', 'external_id');
            $rawDataInsertSaleOrder = $this->buildRawDataInsertTableSaleOrder($item, $accountId);

            $storeStandbox = 264520;

            if ($rawDataInsertSaleOrder['store_id'] == $storeStandbox && $rawDataInsertSaleOrder['account_id'] == 3) {
                continue;
            }

            $listTagData = $this->handleTag($listTag, $item->tagIds);
            $rawDataInsertSaleOrder['tag'] = $listTagData['string'];

            $listFormatLabel = false;
            if (is_array($item->items) && count($item->items)) {
                $listFormatLabel = $this->findSlOrMul($item->items);
            }

            $zipCode = $item->shipTo->postalCode;
            $country = $item->shipTo->country ?? '';

            if ($isSaleOrderExist) {
                if ($isSaleOrderExist->order_status != SaleOrder::NEW_ORDER && $rawDataInsertSaleOrder['order_status'] == SaleOrder::NEW_ORDER) {
                    //giữ nguyên status order khi order trong db không phải new order còn order status của shipstation là new_order
                    $rawDataInsertSaleOrder['order_status'] = $isSaleOrderExist->order_status;
                }
                $rawDataInsertSaleOrder['is_xqc'] = $this->checkSampleXQC($isSaleOrderExist->id);
                $rawDataInsertSaleOrder['is_eps'] = $this->checkIsEps($isSaleOrderExist->id);
                $rawDataInsertSaleOrder['shipping_method'] = $rawDataInsertSaleOrder['is_eps'] == 1 ? SaleOrder::SHIPPING_METHOD_EXPRESS : $rawDataInsertSaleOrder['shipping_method'];
                $rawDataInsertSaleOrder['order_quantity'] = array_reduce($item->items, function ($result, $item) {
                    return $result + $item->quantity;
                });
                $checkOnHoldStore = $this->checkOnHoldStore($rawDataInsertSaleOrder['store_id']);
                if ($checkOnHoldStore && $rawDataInsertSaleOrder['order_status'] == SaleOrder::NEW_ORDER) {
                    $rawDataInsertSaleOrder['order_status'] = SaleOrder::STATUS_ON_HOLD;
                    $storeOnHoldHistory = StoreOnHoldHistory::getInfo($rawDataInsertSaleOrder['store_id']);
                    SaleOrderOnHold::create([
                        'order_id' => $isSaleOrderExist->id,
                        'user_id' => $storeOnHoldHistory->user_id ?? '',
                        'store_on_hold' => 1,
                    ]);

                    SaleOrderHistory::create([
                        'order_id' => $isSaleOrderExist->id,
                        'user_id' => $storeOnHoldHistory->user_id ?? null,
                        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                        'message' => 'Order status changed from New Order to On hold due to unpaid invoice.',
                        'created_at' => date('Y-m-d H:i:s'),
                    ]);
                }
                unset($rawDataInsertSaleOrder['created_at']);
                SaleOrder::where('id', $isSaleOrderExist->id)->update($rawDataInsertSaleOrder);
                $orderId = $isSaleOrderExist->id;

                $this->updateShippingAddress($item, $orderId);
            } else {
                $warehouseIdCheck = 1;
                $rawDataInsertSaleOrder['warehouse_id'] = $warehouseIdCheck;

                $rawDataInsertSaleOrder['order_quantity'] = array_reduce($item->items, function ($result, $item) {
                    return $result + $item->quantity;
                });
                $checkOnHoldStore = $this->checkOnHoldStore($rawDataInsertSaleOrder['store_id']);
                $currentStatus = $rawDataInsertSaleOrder['order_status'];
                if ($checkOnHoldStore) {
                    $rawDataInsertSaleOrder['order_status'] = SaleOrder::STATUS_ON_HOLD;
                }

                // doan nay de bo qua don bi trung index unique
                try {
                    $orderId = SaleOrder::insertGetId($rawDataInsertSaleOrder);
                    if ($checkOnHoldStore) {
                        $storeOnHoldHistory = StoreOnHoldHistory::getInfo($rawDataInsertSaleOrder['store_id']);
                        SaleOrderOnHold::create([
                            'order_id' => $orderId,
                            'user_id' => $storeOnHoldHistory->user_id ?? '',
                            'store_on_hold' => 1,
                        ]);
                        SaleOrderHistory::create([
                            'order_id' => $orderId,
                            'user_id' => $storeOnHoldHistory->user_id ?? null,
                            'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                            'message' => 'Order status changed from New Order to On hold due to unpaid invoice.',
                            'created_at' => date('Y-m-d H:i:s'),
                        ]);
                    }
                } catch (\Exception $e) {
                    echo $e->getMessage();

                    continue;
                }

                // insert shipping address

                $this->updateShippingAddress($item, $orderId);
                $check_is_eps = $this->checkIsEps($orderId);

                $rawDataUpdateAfterXqc = [
                    'is_xqc' => $this->checkSampleXQC($orderId),
                    'is_eps' => $check_is_eps,
                    'shipping_method' => $check_is_eps == 1 ? SaleOrder::SHIPPING_METHOD_EXPRESS : $rawDataInsertSaleOrder['shipping_method'],
                ];

                SaleOrder::where('id', $orderId)->update($rawDataUpdateAfterXqc);
            }

            if ($rawDataInsertSaleOrder['order_status'] == 'cancelled' && $orderId) {
                $this->deleteItemBarcodeByOrderId($orderId);
                jobEcho('====== Order cancel ****** V1');
            }

            if ($this->checkDoNotPrintWip($orderId) == 1) {
                $this->deleteItemBarcodeByOrderId($orderId);
                jobEcho("====== do not print wip $orderId ******");
            }

            $rawDataLog = $this->buildRawDataInsertShipstationOrder($item, $accountId, $orderId);
            $isShipStationOrder = ShipstationOrder::where('account_id', $accountId)
                ->where('order_id', $rawDataLog['order_id'])
                ->first();
            if (!$isShipStationOrder) {
                ShipstationOrder::insert($rawDataLog);
            } else {
                unset($rawDataLog['created_at']);
                ShipstationOrder::where('id', $isShipStationOrder->id)->update($rawDataLog);
            }
            $listProductSku = [];
            if (is_array($item->items) && count($item->items) && $orderId) {
                foreach ($item->items as $data) {
                    $rawDataInsertSaleOrderItem = $this->buildRawDataInsertTableSaleOrderItem($data, $orderId);
                    if ($rawDataInsertSaleOrderItem['quantity'] == 0 && $rawDataInsertSaleOrderItem['sku']) {
                        $this->deleteItemBarcodeByOrderIdAndSku($orderId, $rawDataInsertSaleOrderItem['sku']);
                        jobEcho('====== Order cancel ******  V2 ');
                    }
                    if (isset($rawDataInsertSaleOrderItem['product_sku']) && $rawDataInsertSaleOrderItem['product_sku']) {
                        $product = Product::where('sku', $rawDataInsertSaleOrderItem['product_sku'])->first();
                        if ($product) {
                            $rawDataInsertSaleOrderItem['product_id'] = $product->id;
                        } else {
                            $rawDataInsertSaleOrderItem['product_id'] = null;
                            $productShipstationNullModel = new ProductSkuShipstationNullModel();
                            $isProductShipstationNull = $productShipstationNullModel->findBySku($rawDataInsertSaleOrderItem['product_sku']);
                            if (!$isProductShipstationNull) {
                                $productShipstationNullModel->insert([
                                    'sku' => $rawDataInsertSaleOrderItem['product_sku'],
                                    'created_at' => date('Y-m-d H:i:s')
                                ]);
                            }
                        }
                    }
                    if (count($data->options)) {
                        $printSide = $this->checkPrintSideOption($data->options);
                        $printSides = $this->checkPrintSidesOption($data->options);
                        //echo " Xu ly print side ######## $printSide : orderId $orderId \n";
                        $rawDataInsertSaleOrderItem['print_side'] = $printSide;
                        $rawDataInsertSaleOrderItem['print_sides'] = $printSides;
                    }

                    $rawDataInsertSaleOrderItem['store_id'] = $rawDataInsertSaleOrder['store_id'];
                    $rawDataInsertSaleOrderItem['account_id'] = $rawDataInsertSaleOrder['account_id'];
                    $rawDataInsertSaleOrderItem['warehouse_id'] = !$isSaleOrderExist ? $rawDataInsertSaleOrder['warehouse_id'] : $isSaleOrderExist->warehouse_id;

                    $isExist = SaleOrderItem::where('external_id', $data->orderItemId)->first();
                    if ($isExist) {
                        unset($rawDataInsertSaleOrderItem['created_at']);
                        SaleOrderItem::where('id', $isExist->id)->update($rawDataInsertSaleOrderItem);
                        $orderItemId = $isExist->id;
                    } else {
                        $orderItemId = SaleOrderItem::insertGetId($rawDataInsertSaleOrderItem);
                    }
                    if (!is_array($data->options) || !count($data->options)) {
                        continue;
                    }
                    $isDoubleSide = 0;
                    $i = 0;
                    foreach ($data->options as $item) {
                        $listSide = explode('.', $item->name);
                        if (isset($listSide[0]) && $listSide[0] == 'PrintFiles' && $item->value) {
                            $i++;
                        }
                    }

                    if ($i == 2) {
                        $isDoubleSide = 1;
                    }
                    //                    $isDoubleSide = count($data->options) > 1 ? 1 : 0;
                    foreach ($data->options as $side) {
                        if (!$side->value) {
                            continue;
                        }
                        $listSide = explode('.', $side->name);
                        if (isset($listSide[0]) && $listSide[0] == 'PrintFiles') {
                            $dataInsertItem = $this->buildRawDataSaleOrderItemImage($data, $orderId, $orderItemId, $isDoubleSide, $side, $orderDateCreate, $accountId);

                            if (!$dataInsertItem) {
                                continue;
                            }

                            if ($dataInsertItem['product_sku']) {
                                $listProductSku[] = $dataInsertItem['product_sku'];
                            }
                            $dataInsertItem['store_id'] = $storeIdCheck;
                            // Nho check link da ton tai chua
                            if (is_array($dataInsertItem) && count($dataInsertItem)) {
                                $isItemImage = SaleOrderItemImage::where('order_id', $orderId)
                                    ->where('sku', $dataInsertItem['sku'])
                                    ->where('print_side', $dataInsertItem['print_side'])
                                    ->first();
                                //  DB::beginTransaction();
                                try {
                                    if ($isItemImage) {
                                        if ($isItemImage->is_tool_update == 1) {
                                            unset($dataInsertItem['warehouse_id']);
                                        }
                                        unset($dataInsertItem['created_at']);
                                        SaleOrderItemImage::where('id', $isItemImage->id)->update($dataInsertItem);
                                        //                                    echo "update image !  $isItemImage->id \n";
                                    } else {
                                        $dataInsertItem['warehouse_id'] = 1;
                                        $insertImage = SaleOrderItemImage::insertGetId($dataInsertItem);
                                        //                                    echo "insert image $insertImage";
                                    }
                                    //  DB::commit();
                                } catch (\Exception $exception) {
                                    // DB::rollBack();
                                    echo $exception->getMessage();
                                    Log::error($exception->getMessage());
                                    // sleep(3);
                                }
                            }
                        }
                    }
                    jobEcho("order item id: $orderItemId");
                }
            }
            if (!$isSaleOrderExist) {
                $rawDataInsertSaleOrder['is_xqc'] = $rawDataUpdateAfterXqc['is_xqc'];
                $rawDataInsertSaleOrder['is_eps'] = $rawDataUpdateAfterXqc['is_eps'];
                $this->moveWarehouse($orderId, $listProductSku, $zipCode, $country, $rawDataInsertSaleOrder);

                jobEcho('create new !!!');
                if ($listFormatLabel) {
                    $labelFormatSaleOrder = $this->getLabelFormat($listFormatLabel, $orderId);
                    if ($labelFormatSaleOrder) {
                        jobEcho("update order number: $labelFormatSaleOrder");
                        SaleOrder::where('id', $orderId)->update(['order_number' => $labelFormatSaleOrder]);
                    }
                }
            } else {
                if ($isSaleOrderExist->order_number === null) {
                    if ($listFormatLabel) {
                        $labelFormatSaleOrder = $this->getLabelFormat($listFormatLabel, $orderId);
                        if ($labelFormatSaleOrder) {
                            jobEcho("update order number: $labelFormatSaleOrder");
                            SaleOrder::where('id', $orderId)->update(['order_number' => $labelFormatSaleOrder]);
                        }
                    }
                }
            }
            jobEcho("order id: $orderId");
            jobEcho('time usage: ' . $time->diffInSeconds(now()));

            // end insert
            //            DB::commit();
            sleep(0.1);
        }
    }

    protected function checkOnHoldStore($storeId)
    {
        $store = Store::find($storeId);
        if (!empty($store) && $store->is_on_hold == Store::IS_ON_HOLD) {
            return true;
        }

        return false;
    }

    public function getLabelFormat($listFormatLabel, $orderId)
    {
        $saleOrderAfterUpdate = SaleOrder::find($orderId);

        $dateAfterFormat = $this->createFormatDate($saleOrderAfterUpdate->created_at);
        $warehouse = Warehouse::find($saleOrderAfterUpdate->warehouse_id);

        $warehouseFormat = empty($warehouse->code) ? 'SJ' : $warehouse->code;

        $dataFormatLabel = DB::select("CALL create_order_number('$dateAfterFormat', '$warehouseFormat', '$listFormatLabel')");
        if (!isset($dataFormatLabel[0]->number)) {
            return null;
        }

        return $dataFormatLabel[0]->number;
    }

    private function createFormatDate($date)
    {
        $date = Carbon::parse($date, 'UTC')->setTimezone('America/Los_Angeles')->format('Y-m-d');

        $listDate = explode('-', $date);
        if (!isset($listDate[2])) {
            return '';
        }

        return $listDate['1'] . $listDate['2'] . substr($listDate[0], -2);
    }

    public function findSlOrMul($listItems)
    {
        $quanlity = 0;
        foreach ($listItems as $item) {
            $quanlity = $quanlity + $item->quantity;
        }
        $value = 'M';
        if ($quanlity == 1) {
            $value = 'S';
        }

        return $value;
    }

    public function checkTimeCutOff($beginTime, $endTime, $timeZone = 'America/Chicago')
    {
        $timeZone = empty($timeZone) ? 'America/Chicago' : $timeZone;
        $date = new \DateTime('now', new \DateTimeZone($timeZone));
        $timeNow = $date->format('w H:i');

        $timeNow = $this->convertTimeString($timeNow);
        $beginTime = $this->convertTimeString($beginTime);
        $endTime = $this->convertTimeString($endTime);
        if ($beginTime == $endTime) {
            return true;
        } elseif ($beginTime < $endTime) {
            return $timeNow >= $beginTime && $timeNow <= $endTime;
        } else {
            return !($timeNow >= $endTime && $timeNow <= $beginTime);
        }
    }

    public function convertTimeString($timeString)
    {
        $listTime = explode(' ', $timeString);
        $day = $listTime[0];
        if ($day == 0) {
            $day = 7;
        }
        $hours = explode(':', $listTime[1])[0];
        $p = explode(':', $listTime[1])[1];

        return $day * 24 * 3600 + $hours * 3600 + $p * 60;
    }

    public function moveWarehouse($orderId, $listSku, $zipCode, $country, $order)
    {
        jobEcho('check reroute warehouse start');

        $automationRuleList = SaleOrderAutomationRule::where('is_active', 1)
            ->where('type', 1)
            ->orderBy('priority', 'DESC')
            ->get();
        if (!$automationRuleList) {
            jobEcho('automation rule not found');

            return;
        }
        $order['imageItems'] = SaleOrderItemImage::with('printSide')->where('order_id', $orderId)->get();
        foreach ($automationRuleList as $automationRule) {
            jobEcho('check reroute warehouse: ' . $automationRule->warehouse_id);

            $date = date('Y-m-d', strtotime($order['order_date']));

            $automationRule->count_sale_order
                = $dailyOrderQuantity = DB::table('sale_order_warehouse_quantity')
                ->where('order_date', $date)
                ->where('warehouse_id', $automationRule->warehouse_id)
                ->first();

            $automationRule->count_sale_order = empty($dailyOrderQuantity) ? 0 : $dailyOrderQuantity->quantity;
            jobEcho('start check move warehouse: ' . $automationRule->warehouse_id);

            $isCheck = $this->checkMoveWarehouse($listSku, (object) $order, $zipCode, $country, $automationRule);
            jobEcho('check: ' . $isCheck);

            if (!$isCheck) {
                continue;
            }
            jobEcho('Warehouse: ' . $automationRule->warehouse_id);

            SaleOrder::where('id', $orderId)->update(['warehouse_id' => $automationRule->warehouse_id]);
            SaleOrderItemImage::where('order_id', $orderId)->update(['warehouse_id' => $automationRule->warehouse_id]);
            SaleOrderItem::where('order_id', $orderId)->update(['warehouse_id' => $automationRule->warehouse_id]);
            SaleOrderItemBarcode::where('order_id', $orderId)->update(['warehouse_id' => $automationRule->warehouse_id]);
            if (empty($dailyOrderQuantity)) {
                DB::table('sale_order_warehouse_quantity')->insert([
                    'warehouse_id' => $automationRule->warehouse_id,
                    'order_date' => $date,
                    'quantity' => $order['order_quantity']
                ]);
            } else {
                DB::table('sale_order_warehouse_quantity')->where('order_date', $date)
                    ->where('warehouse_id', $automationRule->warehouse_id)
                    ->update(['quantity' => $automationRule->count_sale_order + $order['order_quantity']]);
            }
            jobEcho(' => reroute warehouse done');
            break;
        }
    }

    public function checkMoveWarehouse($listSku, $order, $zipCode, $country, $automationRule)
    {
        jobEcho('check move warehouse: ' . $automationRule->warehouse_id);
        if ($automationRule->limit_daily < $automationRule->count_sale_order + $order->order_quantity) {
            return false;
        }

        $rule = json_decode($automationRule->rule);
        if (empty($rule->international) && strtoupper($country) != 'US') {
            return false;
        }
        jobEcho('start checkstartDate');
        if (isset($rule->startDate) && isset($rule->endDate)) {
            jobEcho('check startDate');
            $rule->startHours = empty($rule->startHours) ? '00:00' : $rule->startHours;
            $rule->endHours = empty($rule->endHours) ? '23:59' : $rule->endHours;
            $beginTime = $rule->startDate . ' ' . $rule->startHours;
            $endTime = $rule->endDate . ' ' . $rule->endHours;
            $isTimeCutOff = $this->checkTimeCutOff($beginTime, $endTime, $automationRule?->warehouse?->time_zone);
            if (!$isTimeCutOff) {
                return false;
            }
        }
        jobEcho('check listOrderType');

        // check eps & xqc
        if (isset($rule->listOrderType) && count($rule->listOrderType) > 0) {
            $listOrderTypeRule = $this->refactorListRule($rule->listOrderType);
            if (!empty($order->is_manual) && empty($listOrderTypeRule[strtoupper('manual_process')])) {
                return false;
            }
            if (!empty($order->is_eps) && empty($listOrderTypeRule[strtoupper('eps')])) {
                return false;
            }
            // tach 2 dieu kien vi 2 cai check khac
            if (!empty($order->is_xqc) && empty($listOrderTypeRule[strtoupper('xqc')])) {
                return false;
            }

            // check fba order
            if (!empty($order->is_fba_order) && empty($listOrderTypeRule[strtoupper('fba')])) {
                return false;
            }

            // check single & multiple
            if ($order->order_quantity > 1 && !empty($listOrderTypeRule[strtoupper('single_order')])) {
                return false;
            }
        }
        jobEcho('check print area');

        // check print area
        if (isset($rule->printAreas) && count($rule->printAreas) > 0) {
            $printAreas = $this->refactorListRule($rule->printAreas);
            $isCheck = true;
            foreach ($order->imageItems as $item) {
                $value = strtoupper(str_replace(' ', '_', $item->printSide->name));
                if (isset($printAreas[strtoupper($value)]) && !$printAreas[strtoupper($value)]) {
                    $isCheck = false;
                    break;
                }
            }
            if (!$isCheck) {
                return false;
            }
        }
        jobEcho('check SKU');

        // check SKU
        if (!isset($rule->listSku) || count($rule->listSku) == 0) {
            return false;
        }
        $listSkuRule = $this->refactorListRule($rule->listSku);
        $isCheck = true;
        foreach ($listSku as $item) {
            if (!isset($listSkuRule[strtoupper($item)]) || !$listSkuRule[strtoupper($item)]) {
                $isCheck = false;
                break;
            }
        }
        if (!$isCheck) {
            return false;
        }
        jobEcho('check Merchant (store)');

        // check Merchant (store)
        if (isset($rule->listMerchant) && count($rule->listMerchant) > 0) {
            $listMerchantRule = $this->refactorListRule($rule->listMerchant);
            if (!isset($listMerchantRule[strtoupper($order->store_id)]) || !$listMerchantRule[strtoupper($order->store_id)]) {
                return false;
            }
        }
        jobEcho('check zip code');

        // check zip code
        if (isset($rule->listZipcode) && count($rule->listZipcode) > 0) {
            $zipCode = substr($zipCode, 0, 3);
            $listZipcodeRule = $this->refactorListRule($rule->listZipcode);
            if (!isset($listZipcodeRule[strtoupper($zipCode)]) || !$listZipcodeRule[strtoupper($zipCode)]) {
                return false;
            }
        }
        jobEcho('Done check');

        return true;
    }

    public function refactorListRule($rawData)
    {
        $rawDataNew = [];
        foreach ($rawData as $item) {
            $rawDataNew[strtoupper($item->value)] = $item->status;
        }

        return $rawDataNew;
    }

    public function buildRawDataInsertShipstationOrder($item, $accountId, $orderId)
    {
        return [
            'account_id' => $accountId,
            'order_id' => $orderId,
            'order_number' => $item->orderNumber,
            'data' => json_encode($item),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];
    }

    public function buildRawDataInsertTableSaleOrder($item, $accountId)
    {
        $status = [
            'awaiting_payment' => 'new_order',
            'awaiting_shipment' => 'new_order',
            'shipped' => 'shipped',
            'on_hold' => 'on_hold',
            'cancelled' => 'cancelled',
            'processing' => 'new_order'
        ];

        $rawData = [
            //            'order_number' => bcrypt(env('APP_KEY')),
            'account_id' => $accountId,
            'external_id' => $item->orderId,
            'external_number' => $item->orderNumber,
            //            'warehouse_id' => $item->advancedOptions->warehouseId ?? 0,
            'external_key' => $item->orderKey,
            'order_status' => $status[$item->orderStatus] ?? 'new_order',
            /*    'customer_id' => $item->customerId,*/
            'customer_email' => $item->customerEmail,
            'order_total' => $item->orderTotal,
            'amount_paid' => $item->amountPaid,
            'tax_amount' => $item->taxAmount,
            'shipping_amount' => $item->shippingAmount,
            'customer_note' => $item->customerNotes,
            'internal_note' => $item->internalNotes,
            'is_gift' => $item->gift,
            'gift_message' => $item->giftMessage,
            'payment_method' => $item->paymentMethod,
            'shipping_method' => $item->requestedShippingService == 'Priority Mail (PM)' ? SaleOrder::SHIPPING_METHOD_PRIORITY : SaleOrder::SHIPPING_METHOD_STANDARD,
            /*  'shipping_service' => $item->requestedShippingService,*/
            'source' => 'shipstation',
            'store_id' => $item->advancedOptions->storeId ?? '',
            /* 'shipping_name' => $item->shipTo->name ?? '',
             'shipping_state' => $item->shipTo->state ?? '',
             'shipping_country' => $item->shipTo->country ?? '',
             'shipping_zip' => $item->shipTo->postalCode ?? '',
             'shipping_phone' => $item->shipTo->phone ?? '',
             'shipping_company' => $item->shipTo->company ?? '',
             'shipping_street1' => $item->shipTo->street1 ?? '',
             'shipping_street2' => $item->shipTo->street2 ?? '',
             'shipping_city' => $item->shipTo->city ?? '',*/
            /* 'billing_name' => $item->billTo->name ?? '',
             'billing_phone' => $item->billTo->phone ?? '',
             'billing_company' => $item->billTo->company ?? '',
             'billing_address1' => $item->billTo->street1 ?? '',
             'billing_address2' => $item->billTo->street2 ?? '',
             'billing_city' => $item->billTo->city ?? '',
             'billing_state' => $item->billTo->state ?? '',
             'billing_country' => $item->billTo->country ?? '',
             'billing_zip' => $item->billTo->postalCode ?? '',
             'billing_email' => $item->customerEmail ?? '',*/
            'created_at' => $this->convertDate($item->orderDate),
            'updated_at' => date('Y-m-d H:i:s'),
            /*   'custom_field1' => $item->advancedOptions->customField1 ?? '',
               'custom_field2' => $item->advancedOptions->customField2 ?? '',
               'custom_field3' => $item->advancedOptions->customField3 ?? '',*/
            /* 'carrier_code' => $item->carrierCode,
             'service_code' => $item->serviceCode,
             'package_code' => $item->packageCode,*/
            /*  'confirmation' => $item->confirmation,*/
            'order_date' => $item->orderDate,
            'order_time' => $this->convertDate($item->orderDate),
            //            'ship_date' => $item->shipDate,
            //            'hold_until_date' => $item->holdUntilDate,
            /*  'weight_value' => $item->weight->value ?? '',
              'weight_unit' => $item->weight->units ?? '',*/
            //            'dimension_length' => $item->name,
            //            'dimension_width' => $item->name,
            //            'dimension_height' => $item->name,
            //            'dimension_unit' => $item->name,
            //            'tag' => $item->name,
        ];
        jobEcho("Convert $item->orderNumber | $item->orderDate ==> " . $this->convertDate($item->orderDate));

        return $rawData;
    }

    public function buildRawDataInsertTableSaleOrderItem($item, $orderId)
    {
        $rawData = [
            'order_id' => $orderId,
            'external_id' => $item->orderItemId,
            'external_line_item_key' => $item->lineItemKey,
            'sku' => $item->sku,
            'name' => $item->name,
            'image_url' => $item->options->value ?? '',
            'weight' => $item->weight,
            'quantity' => $item->quantity,
            'unit_price' => $item->unitPrice,
            'tax_amount' => $item->taxAmount,
            'shipping_amount' => $item->shippingAmount,
            'options' => json_encode($item->options),
            // 'product_id' => $item->productId,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            // 'created_at' => $item->createDate,
            //            'updated_at' => $item->modifyDate,
            //       'fulfillment_sku' => $item->fulfillmentSku,
            //        'upc' => $item->upc,
        ];
        if ($item->sku) {
            $rawData['product_sku'] = substr($item->sku, -9);
            $rawData['product_style_sku'] = substr($item->sku, -9, 4);
            $rawData['product_color_sku'] = substr($item->sku, -5, 2);
            $rawData['product_size_sku'] = substr($item->sku, -3);
            $rawData['product_style_color_sku'] = substr($item->sku, -9, 6);
        }

        return $rawData;
    }

    public function checkIsEps($orderId)
    {
        $order = SaleOrder::find($orderId);
        $listTag = $order->tag;
        if (!$listTag) {
            return 0;
        }
        $lisTag = explode(',', $listTag);

        $isXqc = Tag::whereIn('id', $lisTag)
            ->where('name', 'UPGRADE TO EXPRESS SHIPPING')
            ->count();

        if ($isXqc) {
            return 1;
        }

        return 0;
    }

    public function checkSampleXQC($orderId)
    {
        $order = SaleOrder::find($orderId);
        $listTag = $order->tag;
        if (!$listTag) {
            return 0;
        }
        $lisTag = explode(',', $listTag);

        $isXqc = Tag::whereIn('id', $lisTag)
            ->where('name', 'Sample XQC')
            ->count();

        if ($isXqc) {
            return 1;
        }

        return 0;
    }

    public function checkDoNotPrintWip($orderId)
    {
        $order = SaleOrder::find($orderId);
        $listTag = $order->tag;
        if (!$listTag) {
            return 0;
        }
        $lisTag = explode(',', $listTag);

        $isXqc = Tag::whereIn('id', $lisTag)
            ->where('name', 'DO NOT PRINT WIP')
            ->count();
        if ($isXqc) {
            return 1;
        }

        return 0;
    }

    public function buildRawDataSaleOrderItemImage($item, $orderId, $orderItemId, $isDoubleSide, $side, $orderDateCreate, $accountId)
    {
        if ($side->value) {
            $code = $this->checkPrintSideImage($side);

            if (is_null($code)) {
                return false;
            }
            $mime = 0;
            $rawData = [
                'order_id' => $orderId,
                'order_item_id' => $orderItemId,
                /*'name' => $item->name,*/
                'image_url' => $side->value,
                'image_ext' => $mime,
                //                'image_width' => $widthImage,
                //                'image_height' => $heightImage,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'sku' => $item->sku,
                'order_date' => $orderDateCreate,
                'order_time' => $this->convertDate($orderDateCreate),
                'print_side' => $code,
                'is_double_side' => $isDoubleSide,
                'account_id' => $accountId,
                /* 'is_xqc' => $this->checkSampleXQC($orderId),*/
            ];
            if ($item->sku) {
                $rawData['product_sku'] = substr($item->sku, -9);
                /* $rawData['product_style_sku'] = substr($item->sku, -9, 4);
                 $rawData['product_color_sku'] = substr($item->sku, -6, 2);
                 $rawData['product_size_sku'] = substr($item->sku, -3);*/
            }

            return $rawData;
        }
    }

    public function checkPrintSideImage($side)
    {
        if (!property_exists($side, 'name') || !property_exists($side, 'value') || empty($side->value)) {
            return null;
        }

        $codeName = null;
        $frontDesign = explode('.', $side->name);
        if ($frontDesign[0] == 'PrintFiles') {
            if (ctype_upper($frontDesign[1])) {
                $codeName = strtolower($frontDesign[1]);
            } else {
                $replace = strtolower(str_replace(' ', '', $frontDesign[1]));
                if (ctype_lower($replace)) {
                    $codeName = $replace;
                } else {
                    $codeName = str_replace('_', '', $replace);
                }
            }
        }

        $code = null;

        if (!is_null($codeName)) {
            $printSide = ProductPrintSide::where(DB::raw("REPLACE(code_name, '_', '')"), $codeName)->first();
            $code = $printSide ? $printSide->code : null;
        }

        return $code;
    }

    public function insertOrders($listItem, $accountId)
    {
        foreach ($listItem as $key => $item) {
            $time = now();
            // begin insert
            //            DB: :beginTransaction();
            $orderDateCreate = $item->orderDate;

            $storeIdCheck = $item->advancedOptions->storeId ?? '';

            // check neu bo qua ko dong bo shipstation

            $store = Store::find($storeIdCheck);
            // echo "stop sync shipstation: ".$store->stop_sync_shipstation_orders_at." / ".date('Y-m-d H:i:s');
            if ($store && !empty($store->stop_sync_shipstation_orders_at)) {
                if (strtotime($item->orderDate) > strtotime('2022-09-25T21:10:44.1530000') && $item->orderNumber != '7750-***********') {
                    jobEcho("$store->name => skip khong dong bo don shipstation");

                    continue;
                }
            }

            // tìm order
            $isSaleOrderExist = SaleOrder::where('account_id', $accountId)
                ->where('external_id', $item->orderId)
                ->first();

            if ($isSaleOrderExist) {
                // neu khong phai order moi thi khong xu ly
                jobEcho("skip update: $item->orderNumber / $item->orderId");

                continue;
            }

            $listTag = Tag::where('account_id', $accountId)
                ->where('source', 'sale_order')
                ->pluck('id', 'external_id');
            $rawDataInsertSaleOrder = $this->buildRawDataInsertTableSaleOrder($item, $accountId);
            $checkOnHoldStore = $this->checkOnHoldStore($rawDataInsertSaleOrder['store_id']);
            $currentStatus = $rawDataInsertSaleOrder['order_status'];
            if ($checkOnHoldStore) {
                $rawDataInsertSaleOrder['order_status'] = SaleOrder::STATUS_ON_HOLD;
            }

            $storeStandbox = 264520;

            if ($rawDataInsertSaleOrder['store_id'] == $storeStandbox && $rawDataInsertSaleOrder['account_id'] == 3) {
                continue;
            }

            $listTagData = $this->handleTag($listTag, $item->tagIds);
            $rawDataInsertSaleOrder['tag'] = $listTagData['string'];

            $listFormatLabel = false;
            if (is_array($item->items) && count($item->items)) {
                $listFormatLabel = $this->findSlOrMul($item->items);
            }

            $zipCode = $item->shipTo->postalCode;
            $country = $item->shipTo->country ?? '';

            $warehouseIdCheck = 1;
            $rawDataInsertSaleOrder['warehouse_id'] = $warehouseIdCheck;

            $rawDataInsertSaleOrder['order_quantity'] = array_reduce($item->items, function ($result, $item) {
                return $result + $item->quantity;
            });

            // doan nay de bo qua don bi trung index unique
            try {
                $orderId = SaleOrder::insertGetId($rawDataInsertSaleOrder);
                if ($checkOnHoldStore) {
                    $storeOnHoldHistory = StoreOnHoldHistory::getInfo($rawDataInsertSaleOrder['store_id']);
                    SaleOrderOnHold::create([
                        'order_id' => $orderId,
                        'user_id' => $storeOnHoldHistory->user_id ?? '',
                        'store_on_hold' => 1,
                    ]);
                    SaleOrderHistory::create([
                        'order_id' => $orderId,
                        'user_id' => $storeOnHoldHistory->user_id ?? null,
                        'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                        'message' => 'Order status changed from New Order to On hold due to unpaid invoice.',
                        'created_at' => date('Y-m-d H:i:s'),
                    ]);
                }
            } catch (\Exception $e) {
                echo $e->getMessage();

                continue;
            }

            // insert shipping address

            $this->updateShippingAddress($item, $orderId);
            $check_is_eps = $this->checkIsEps($orderId);

            // insert encode id
            $saltKey = env('APP_KEY');
            $minLength = env('HASH_ID_MIN_LENGTH', 8);
            $alphabet = env('HASH_ID_ALPHABET', 'abcdefghijklmnopqrstuvwxyz1234567890');
            $hashIds = new Hashids($saltKey, $minLength, $alphabet);
            $encodeId = $hashIds->encode($orderId);

            $rawDataUpdateAfterXqc = [
                'is_xqc' => $this->checkSampleXQC($orderId),
                'is_eps' => $check_is_eps,
                'shipping_method' => $check_is_eps == 1 ? SaleOrder::SHIPPING_METHOD_EXPRESS : $rawDataInsertSaleOrder['shipping_method'],
                'encode_id' => $encodeId
            ];

            SaleOrder::where('id', $orderId)->update($rawDataUpdateAfterXqc);

            if ($rawDataInsertSaleOrder['order_status'] == 'cancelled' && $orderId) {
                $this->deleteItemBarcodeByOrderId($orderId);
                jobEcho('====== Order cancel ****** V1 ');
            }

            if ($this->checkDoNotPrintWip($orderId) == 1) {
                $this->deleteItemBarcodeByOrderId($orderId);
                jobEcho("====== do not print wip $orderId ******  ");
            }

            $listProductSku = [];
            if (is_array($item->items) && count($item->items) && $orderId) {
                foreach ($item->items as $data) {
                    $rawDataInsertSaleOrderItem = $this->buildRawDataInsertTableSaleOrderItem($data, $orderId);

                    if (isset($rawDataInsertSaleOrderItem['product_sku']) && $rawDataInsertSaleOrderItem['product_sku']) {
                        $product = Product::where('sku', $rawDataInsertSaleOrderItem['product_sku'])->first();
                        if ($product) {
                            $rawDataInsertSaleOrderItem['product_id'] = $product->id;
                        } else {
                            $rawDataInsertSaleOrderItem['product_id'] = null;
                            $productShipstationNullModel = new ProductSkuShipstationNullModel();
                            $isProductShipstationNull = $productShipstationNullModel->findBySku($rawDataInsertSaleOrderItem['product_sku']);
                            if (!$isProductShipstationNull) {
                                $productShipstationNullModel->insert([
                                    'sku' => $rawDataInsertSaleOrderItem['product_sku'],
                                    'created_at' => date('Y-m-d H:i:s')
                                ]);
                            }
                        }
                    }

                    if (count($data->options)) {
                        $printSide = $this->checkPrintSideOption($data->options);
                        //echo " Xu ly print side ######## $printSide : orderId $orderId \n";
                        $printSides = $this->checkPrintSidesOption($data->options);
                        $rawDataInsertSaleOrderItem['print_side'] = $printSide;
                        $rawDataInsertSaleOrderItem['print_sides'] = $printSides;
                    }

                    $rawDataInsertSaleOrderItem['store_id'] = $rawDataInsertSaleOrder['store_id'];
                    $rawDataInsertSaleOrderItem['account_id'] = $rawDataInsertSaleOrder['account_id'];
                    $rawDataInsertSaleOrderItem['warehouse_id'] = !$isSaleOrderExist ? $rawDataInsertSaleOrder['warehouse_id'] : $isSaleOrderExist->warehouse_id;

                    $orderItemId = SaleOrderItem::insertGetId($rawDataInsertSaleOrderItem);

                    if (!is_array($data->options) || !count($data->options)) {
                        continue;
                    }
                    $isDoubleSide = 0;
                    $i = 0;
                    foreach ($data->options as $item) {
                        $listSide = explode('.', $item->name);
                        if (isset($listSide[0]) && $listSide[0] == 'PrintFiles' && $item->value) {
                            $i++;
                        }
                    }

                    if ($i == 2) {
                        $isDoubleSide = 1;
                    }
                    //                    $isDoubleSide = count($data->options) > 1 ? 1 : 0;
                    foreach ($data->options as $side) {
                        if (!$side->value) {
                            continue;
                        }
                        $listSide = explode('.', $side->name);
                        if (isset($listSide[0]) && $listSide[0] == 'PrintFiles') {
                            $dataInsertItem = $this->buildRawDataSaleOrderItemImage($data, $orderId, $orderItemId, $isDoubleSide, $side, $orderDateCreate, $accountId);

                            if (!$dataInsertItem) {
                                continue;
                            }

                            if ($dataInsertItem['product_sku']) {
                                $listProductSku[] = $dataInsertItem['product_sku'];
                            }
                            $dataInsertItem['store_id'] = $storeIdCheck;
                            // Nho check link da ton tai chua
                            if (is_array($dataInsertItem) && count($dataInsertItem)) {
                                $dataInsertItem['warehouse_id'] = 1;
                                $insertImage = SaleOrderItemImage::insertGetId($dataInsertItem);
                            }
                        }
                    }
                    jobEcho("order item id: $orderItemId");
                }
            }

            $rawDataInsertSaleOrder['is_xqc'] = $rawDataUpdateAfterXqc['is_xqc'];
            $rawDataInsertSaleOrder['is_eps'] = $rawDataUpdateAfterXqc['is_eps'];
            $this->moveWarehouse($orderId, $listProductSku, $zipCode, $country, $rawDataInsertSaleOrder);

            if ($listFormatLabel) {
                $labelFormatSaleOrder = $this->getLabelFormat($listFormatLabel, $orderId);
                if ($labelFormatSaleOrder) {
                    jobEcho("update order number: $labelFormatSaleOrder");
                    SaleOrder::where('id', $orderId)->update(['order_number' => $labelFormatSaleOrder]);
                }
            }

            jobEcho("order id: $orderId");
            jobEcho('time usage: ' . $time->diffInSeconds(now()));

            // end insert
            //            DB::commit();
            sleep(0.1);
        }
    }
}
