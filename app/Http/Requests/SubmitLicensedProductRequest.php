<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SubmitLicensedProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'products' => 'required|array', // Validate products must be an array
            'products.*.product_sku' => [
                'required',
                'string',
                Rule::exists('product', 'sku')->where(function ($query) {
                    $query->where('is_deleted', 0);
                }),
            ], // Validate each product's SKU
            'products.*.licensed_design_id' => 'required|string', // Validate each licensed design ID
            'products.*.style' => 'required|string|exists:product_style,name', // Validate each product's style
            'products.*.color' => 'required|string|exists:product_color,name', // Validate each product's color
            'products.*.size' => 'required|string|exists:product_size,name', // Validate each product's size
        ];
    }

    public function message()
    {
        return [
            'products.required' => 'The products field is required.',
            'products.array' => 'The products field must be an array.',
            'products.*.product_sku.required' => 'Each product must have a SKU.',
            'products.*.product_sku.string' => 'The SKU must be a string.',
            'products.*.product_sku.exists' => 'The :input product SKU is invalid or does not exist in the database.',
            'products.*.licensed_design_id.required' => 'Each product must have a licensed design ID.',
            'products.*.licensed_design_id.string' => 'The licensed design ID must be a string.',
            'products.*.style.required' => 'Each product must have a style.',
            'products.*.style.string' => 'The style must be a string.',
            'products.*.style.exists' => 'The :input style is invalid or does not exist in the database.',
            'products.*.color.required' => 'Each product must have a color.',
            'products.*.color.string' => 'The color must be a string.',
            'products.*.color.exists' => 'The :input color is invalid or does not exist in the database.',
            'products.*.size.required' => 'Each product must have a size.',
            'products.*.size.string' => 'The size must be a string.',
            'products.*.size.exists' => 'The :input size is invalid or does not exist in the database.',
        ];
    }
}
