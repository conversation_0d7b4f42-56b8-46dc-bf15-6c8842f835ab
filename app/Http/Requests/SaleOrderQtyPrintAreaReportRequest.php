<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SaleOrderQtyPrintAreaReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'warehouse_ids' => [
                'nullable',
            ],
            'start_date' => [
                'nullable',
                'date',
                'date_format:Y-m',
            ],
            'end_date' => [
                'nullable',
                'date',
                'date_format:Y-m',
                'after_or_equal:start_date'
            ],
        ];
    }
}
