<?php

namespace App\Http\Requests;

use App\Rules\UniqueProductGtin;
use Illuminate\Foundation\Http\FormRequest;

class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        if ($this->method() === 'POST') {
            return [
                'gtin' => ['max:100', new UniqueProductGtin()],
                'gtin_case' => 'required:gtin|integer|gt:0',
                'style' => 'required|max:255',
                'color' => 'required',
                'size' => 'required',
                'brand_id' => 'required'
            ];
        }
        return [
            'gtin' => ['max:100', new UniqueProductGtin($this->id)],
            'gtin_case' => 'required:gtin|integer|gt:0'
        ];
    }
}
