<?php

namespace App\Http\Requests\ChangeLog;

use App\Models\ChangeLog;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ChangeLogSourceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'source' => ['required', 'string', Rule::in(ChangeLog::SOURCE_CHANGE_LOG)],
        ];
    }
}
