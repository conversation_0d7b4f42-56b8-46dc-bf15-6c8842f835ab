<?php

namespace App\Http\Requests;

use App\Models\Trademark;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateTrademarkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|unique:trademarks,name',
            'is_active' => ['required', Rule::in([0, 1])],
            'type' => ['required', Rule::in([Trademark::BRAND_TYPE, Trademark::CHARACTER_TYPE])],
            'ownership_id' => ['required', 'exists:ownerships,id']
        ];
    }

    public function attributes()
    {
        return [
            'ownership_id' => 'ownership',
        ];
    }

    public function messages()
    {
        return [
            'name.unique' => 'Error! Duplicate Trademark.'
        ];
    }
}
