<?php

namespace App\Http\Requests;

use App\Rules\CheckUniquePretreatPresetSKU;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class CreatePretreatPresetSKURequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request): array
    {
        return [
            'pretreat_preset_id' => ['required', 'exists:pretreat_preset,id'],
            'style' =>['required', 'exists:product_style,sku', new CheckUniquePretreatPresetSKU($request)],
            'color' =>['required','exists:product_color,sku', new CheckUniquePretreatPresetSKU($request)],
        ];
    }
}
