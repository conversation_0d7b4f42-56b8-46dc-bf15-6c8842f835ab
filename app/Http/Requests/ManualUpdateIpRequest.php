<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ManualUpdateIpRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Set to true if any user can access this request
    }

    public function rules()
    {
        return [
            'id' => 'required|exists:app_ips,id',
            'ip_address' => 'required',
            'is_active' => 'required|boolean',
            'note' => 'nullable|string',
            'expired_at' => 'nullable|string',
        ];
    }

    public function messages()
    {
        return [
            'id.required' => 'The ID field is required.',
            'ip_address.required' => 'The IP address field is required.',
            'ip_address.unique' => 'This IP address already exists.',
            'is_active.required' => 'The status field is required.',
            'is_active.boolean' => 'The status must be true or false.',
        ];
    }
}
