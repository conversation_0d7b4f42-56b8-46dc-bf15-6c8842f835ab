<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RbtKittingRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Set to false if authorization logic is required
    }

    public function rules(): array
    {
        return [
            'order_id' => ['nullable', 'string', 'exists:sale_order,id', 'required_without:wip_id'],
            'wip_id' => ['nullable', 'string', 'exists:sale_order_item_barcode,label_id', 'required_without:order_id'],
        ];
    }

    public function messages(): array
    {
        return [
            'order_id.exists' => "The provided 'order_id' does not exist.",
            'wip_id.exists' => "The provided 'wip_id' does not exist.",
            'order_id.required_without' => "Either 'order_id' or 'wip_id' must be provided.",
            'wip_id.required_without' => "Either 'order_id' or 'wip_id' must be provided.",
        ];
    }
}
