<?php

namespace App\Http\Requests;

use App\Rules\CheckFileExtension;
use Illuminate\Foundation\Http\FormRequest;

class UploadEmbroideryFileRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Change to appropriate authorization logic
    }

    public function rules()
    {
        return [
            'file' => ['required', 'file', new CheckFileExtension(['tbf', 'ct0', 'dst', 'dgt'])],
            'task_id' => 'required|exists:embroidery_task,id',
        ];
    }
}
