<?php

namespace App\Http\Requests;

use App\Models\InternalRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateInternalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'product_id' => ['required', 'exists:product,id'],
            'employee_id' => ['required', 'exists:employee,id'],
            'box_quantity' => ['required', 'numeric', 'min:1', 'max:5'],
            'priority' => ['required', Rule::in([InternalRequest::LOW_PRIORITY, InternalRequest::HIGH_PRIORITY, InternalRequest::TIKTOK_PRIORITY])],
        ];
    }

    public function messages()
    {
        return [
            'product_id.exists' => 'The selected product not found.',
            'employee_id.exists' => 'The selected employee not found.',
            'box_quantity.min' => 'The box quantity must be at least :min.',
            'box_quantity.max' => 'The box quantity must not exceed :max.',
        ];
    }
}
