<?php

namespace App\Http\Requests\ProductPrintSide;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|unique:product_print_side,name|string|max:255',
            'code' => 'required|unique:product_print_side,code|numeric',
            'description' => 'nullable|string|max:255',
            'code_wip' => 'required|string|max:1'
        ];
    }
}
