<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddBypassUsersRequest extends FormRequest
{
    public function authorize()
    {
        return true; // Set to true if any user can access this request
    }

    public function rules()
    {
        return [
            'id' => 'required|array',
            'id.*' => 'required|exists:user,id', // Validate each item in the array
        ];
    }

    public function messages()
    {
        return [
            'id.required' => 'The user ID field is required.',
            'id.array' => 'The user ID must be an array.',
            'id.*.exists' => 'One or more selected users do not exist.',
        ];
    }
}
