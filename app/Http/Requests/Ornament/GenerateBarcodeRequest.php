<?php

namespace App\Http\Requests\Ornament;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GenerateBarcodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'quantity' => 'required|numeric',
            'product_id' => 'required_without_all:is_tiktok,is_bulk_order,is_reprint',
            'is_tiktok' => 'required_without_all:product_id,is_bulk_order,is_reprint',
            'is_bulk_order' => 'required_without_all:product_id,is_tiktok,is_reprint',
            'is_reprint' => 'required_without_all:product_id,is_bulk_order,is_tiktok',
            'store_id' => 'nullable|numeric|exists:store,id',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
        ];
    }

    public function messages()
    {
        return [
            'employee_id.exists' => 'You are not in this warehouse!',
        ];
    }
}
