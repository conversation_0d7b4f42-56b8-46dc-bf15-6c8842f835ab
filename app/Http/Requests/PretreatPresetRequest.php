<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PretreatPresetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $id = $this['id'];
        return [
            'preset_name' => "required|string|max:255|unique:pretreat_preset,preset_name,$id",
            'density' => 'required|numeric|min:0',
            'cure_temperature' => 'required|numeric|min:0',
            'cure_time' => 'required|numeric|min:0',
            'press_time' => 'required|numeric|min:0',
            'pressure' => 'required|numeric|min:0',
            'press_temperature' => 'required|numeric|min:0',
            'print_cure_temperature' => 'required|numeric|min:0',
            'print_cure_time' => 'required|numeric|min:0',
            // 'print_time' => 'required|numeric|min:0',
        ];
    }

    public function messages()
    {
        return [
            'preset_name.required' => 'Preset Name field is required',
            'preset_name.unique' => 'Preset Name already exist',
            'density.required' => 'Density field is required',
            'cure_temperature.required' => 'Cure Temperature field is required',
            'press_temperature.required' => 'Press Temperature field is required',
            'cure_time.required' => 'Cure time field is required',
            'press_time.required' => 'Press time field is required',
            'pressure.required' => 'Pressure field is required',
            'print_cure_temperature.required' => 'Print Cure Temperature field is required',
            'print_cure_time.required' => 'Print Cure Time field is required',
            // 'print_time.required' => 'Print Time field is required',
        ];
    }
}
