<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $id = $this['id'];

        return [
            'username' => "required|max:100|unique:user,username,$id,id",
            'password' => $this->method() === 'POST' ? 'nullable' : 'min:3',
            'seller_password' => $this->method() === 'POST' ? 'nullable' : 'min:3',
            'email' => "required|email|max:100|unique:user,email,$id,id",
            'warehouse_ids' => 'required_if:is_admin,0',
            'role_id' => 'required_if:is_admin,0|exists:roles,id',
            'is_admin' => 'required|in:1,0',
            'is_all_store' => 'nullable|in:1,0'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'role_id.required_if' => 'The role field is required.',
            'warehouse_ids.required_if' => 'The warehouse field is required if the role is not an administrator.'
        ];
    }
}
