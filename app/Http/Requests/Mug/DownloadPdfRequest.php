<?php

namespace App\Http\Requests\Mug;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DownloadPdfRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'is_history' => ['required', Rule::in([true, false])],
            'employee_id' => [
                'nullable',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
            'barcode_printed_id' => 'required'
        ];
    }
}
