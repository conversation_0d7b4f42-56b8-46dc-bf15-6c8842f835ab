<?php

namespace App\Http\Requests\Mug;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ConvertPdfRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'quantity' => 'required|numeric|min:1|max:300',
            'product_id' => 'nullable|numeric',
            'store_id' => 'nullable|numeric|exists:store,id',
            'employee_id' => [
                'required',
                Rule::exists('employee', 'id')->where('warehouse_id', config('jwt.warehouse_id'))
            ],
            'is_fba' => ['nullable', Rule::in([true, false])],
            'is_tiktok' => ['nullable', Rule::in([true, false])],
            'is_reroute' => ['nullable', Rule::in([true, false])],
            'is_manual' => ['nullable', Rule::in([true, false])],
            'is_reprint' => ['nullable', Rule::in([true, false])],
            'is_xqc' => ['nullable', Rule::in([0, 1])],
            'is_eps' => ['nullable', Rule::in([true, false])],
            'is_all' => ['nullable', Rule::in([true, false])],
            'is_style_sku' => ['nullable', Rule::in([true, false])],
            'is_bulk_order' => ['nullable', Rule::in([true, false])],
        ];
    }

    public function messages()
    {
        return [
            'employee_id.exists' => 'You are not in this warehouse!',
        ];
    }
}
