<?php

namespace App\Http\Requests\ProductPrintArea;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateProductPrintAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $request = $this->request;
        return [
            'name' => [
                'required',
                Rule::unique('product_print_area', 'name')
                    ->where('product_style_id', $request->get('product_style_id'))
            ],
            // 'platen_size' => 'required|regex:/(^\d+x\d+$)/',
            // 'print_size' => 'required|regex:/^[\d]+[.\d]{0,10}x[\d]+[.\d]{0,10}$/',
            'platen_area_size' => 'required|regex:/^[\d]+[.\d]{0,10}x[\d]+[.\d]{0,10}$/',
            'product_style_id' => 'required|exists:product_style,id',
            'top' => 'required|numeric',
            'left' => 'required|numeric',
            'image' => ['required', 'mimes:jpeg,png', 'mimetypes:image/jpeg,image/png', 'max:10240'],
            'rotate' => 'required|numeric|in:0,1',
        ];
    }
}
