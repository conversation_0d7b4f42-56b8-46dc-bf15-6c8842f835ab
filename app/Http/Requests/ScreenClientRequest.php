<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ScreenClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rule = [
            'company' => ['required', 'string'],
            'email' => ['nullable', 'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]+$/', 'sometimes'],
            'phone' => ['nullable', 'string'],
            'contact_name' => ['nullable', 'string'],
            'contact_email' => ['nullable', 'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]+$/'],
            'contact_phone' => ['nullable', 'string'],
            'street1' => ['required', 'string'],
            'street2' => ['nullable'],
            'city' => ['required', 'string'],
            'state' => ['required_if:country,US'],
            'zipcode' => ['required', 'string'],
            'country' => ['required', 'string'],
            'is_has_billing_address' => ['nullable', Rule::in([0, 1])],
            'billing_email' => ['nullable', 'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]+$/'],
            'billing_phone' => ['nullable', 'string'],
            'billing_street1' => ['nullable', 'string'],
            'billing_street2' => ['nullable'],
            'billing_city' => ['nullable', 'string'],
            'billing_state' => ['required_if:billing_country,US'],
            'billing_zipcode' => ['nullable', 'string'],
            'billing_country' => ['nullable', 'string'],
        ];
        if (!empty($this['id'])) {
            $rule['name'] = ['required', 'string', 'unique:screen_clients,name,' . $this['id']];
        } else {
            $rule['name'] = ['required', 'string', 'max:50', 'unique:screen_clients,name'];
        }

        return $rule;
    }

    public function messages()
    {
        return [
            'phone.regex' => 'The phone number must be number.',
            'contact_phone.regex' => 'The contact phone number must be number.',
            'email.regex' => 'The email must be a valid email address.',
            'contact_email.regex' => 'The email must be a valid email address.',
            'billing_email.regex' => 'The email must be a valid email address.',
        ];
    }
}
