<?php

namespace App\Http\Requests\CostReport;

use Illuminate\Foundation\Http\FormRequest;

class DetailLaborRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'pay_period_begin' => ['required_with:pay_period_end', 'date'],
            'pay_period_end' => ['required_with:pay_period_begin', 'date'],
            'employee_id' => 'sometimes|exists:employee,id',
            'year' => 'sometimes|date_format:Y',
        ];
    }
}
