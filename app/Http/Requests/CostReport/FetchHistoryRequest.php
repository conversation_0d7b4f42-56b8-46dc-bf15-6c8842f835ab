<?php

namespace App\Http\Requests\CostReport;

use App\Models\CostReport;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class FetchHistoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'employee_id' => 'sometimes|exists:employee,id',
            'cost_type' => ['sometimes', 'array', Rule::in(CostReport::allTypeCost())],
            'affected_at' => 'date_format:Y-m',
            'limit' => 'sometimes|numeric',
            'page' => 'sometimes|numeric',
        ];
    }
}
