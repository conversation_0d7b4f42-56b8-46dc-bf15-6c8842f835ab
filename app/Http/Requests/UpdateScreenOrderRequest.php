<?php

namespace App\Http\Requests;

use App\Models\ScreenOrder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateScreenOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'shipped_at' => 'nullable|date_format:Y-m-d',
            'client_note' => 'nullable|string',
            'internal_note' => 'nullable|string',
            'buyer_name' => 'nullable|string',
            'ship_via' => 'nullable|string',
            'free_on_board' => 'nullable|string',
            'pay_terms' => 'nullable|string',
            'freight_terms' => 'nullable|string',
            'tax_rate' => 'nullable|numeric',
            'tax' => 'nullable|numeric',
            'shipping_and_handling_fee' => 'nullable|numeric',
            'other_fee' => 'nullable|numeric',
            'order_total' => 'nullable|numeric',
            'order_status' => ['string', 'nullable', Rule::in([ScreenOrder::NEW_ORDER_STATUS, ScreenOrder::IN_PRODUCTION_STATUS, ScreenOrder::READY_TO_STATUS, ScreenOrder::CANCELLED_STATUS, ScreenOrder::COMPLETED_STATUS])],
        ];
    }
}
