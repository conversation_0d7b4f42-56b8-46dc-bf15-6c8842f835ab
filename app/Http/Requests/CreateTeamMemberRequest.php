<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateTeamMemberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $clientId = auth()->user()->client_id ?? null;
        $teamMemberId = $this->route('id');

        return [
            'name' => 'required|string|max:255',
            'status' => 'sometimes|boolean',
            'team_member_role_id' => [
                'required',
                Rule::exists('team_member_roles', 'id')->where(function ($query) use ($clientId) {
                    return $query->where('client_id', $clientId);
                }),
            ],
            'password' => [
                Rule::requiredIf(!$teamMemberId),
                'string',
                'min:6',
                'max:30',
                'regex:/^[a-zA-Z0-9!@#$%^&*()_+\\-=<>?]+$/'
            ],
            'store_ids' => 'required|array',
            'store_ids.*' => 'required|exists:store,id',
            'username' => [
                'required',
                'string',
                'max:30',
                Rule::unique('team_members')->where(function ($query) use ($clientId, $teamMemberId) {
                    return $query->where('client_id', $clientId)
                                 ->when($teamMemberId, function ($query) use ($teamMemberId) {
                                     return $query->where('id', '<>', $teamMemberId);
                                 });
                }),
                'regex:/^[a-zA-Z0-9!@#$%^&*()_+\\-=<>?]+$/'
            ],
        ];
    }
}
