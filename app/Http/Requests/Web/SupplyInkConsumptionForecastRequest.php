<?php

namespace App\Http\Requests\Web;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\RedirectResponse;

class SupplyInkConsumptionForecastRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'year' => 'nullable|date_format:Y',
            'warehouse_id' => 'nullable|string',
            'color_type' => 'nullable|string',
            'unit_type' => 'nullable|string',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        if ($this->isJson() || $this->expectsJson()) {
            $response = response()->json([
                'errors' => $validator->errors(),
            ], 422);

            throw new HttpResponseException($response);
        }

        $response = new RedirectResponse(route('supply.forecast'));

        $response->setSession($this->session());
        $response->withInput($this->input())->withErrors($validator->errors());

        throw new HttpResponseException($response);
    }
}
