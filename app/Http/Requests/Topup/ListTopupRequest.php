<?php

namespace App\Http\Requests\Topup;

use App\Models\WalletTopup;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ListTopupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'keyword' => 'nullable|string',
            'payment_method' => ['nullable', Rule::in([WalletTopup::CARD_METHOD, WalletTopup::BANK_TRANSFER_METHOD])],
            'status' => ['nullable', Rule::in([WalletTopup::STATUS_PENDING, WalletTopup::STATUS_APPROVED, WalletTopup::STATUS_FAILED, WalletTopup::STATUS_PROCESSING])],
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d',
            'updated_start_date' => 'nullable|date_format:Y-m-d',
            'updated_end_date' => 'nullable|date_format:Y-m-d',
            'store_id' => 'nullable|numeric',
            'fetch_all' => 'nullable',
        ];
    }
}
