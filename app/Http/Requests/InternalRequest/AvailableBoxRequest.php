<?php

namespace App\Http\Requests\InternalRequest;

use App\Models\InternalRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AvailableBoxRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'product_id' => ['required', 'exists:product,id'],
            'warehouse_id' => ['required', 'exists:warehouse,id'],
        ];
    }

    public function messages()
    {
        return [];
    }
}
