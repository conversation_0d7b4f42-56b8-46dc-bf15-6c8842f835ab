<?php

namespace App\Http\Requests\Barcode;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class MobilePrintBarcodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'quantity' => 'required|numeric',
            'style' => 'required',
            'color' => 'required',
            'employee_code' => 'required|numeric'
        ];
    }
}
