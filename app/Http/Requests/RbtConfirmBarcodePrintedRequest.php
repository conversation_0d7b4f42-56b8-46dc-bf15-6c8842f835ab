<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RbtConfirmBarcodePrintedRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'time_tracking_id' => 'nullable|exists:time_tracking,id',
            'employee_id' => 'required|exists:employee,code',
            'last_wip_id' => 'required|exists:sale_order_item_barcode,label_id',
            'location' => 'required|string',
            'destroy' => 'sometimes|boolean',
        ];
    }

    public function messages()
    {
        return [
            'employee_id.exists' => 'The provided employee does not exist.',
            'last_wip_id.exists' => 'The provided WIP ID does not exist.',
        ];
    }
}
