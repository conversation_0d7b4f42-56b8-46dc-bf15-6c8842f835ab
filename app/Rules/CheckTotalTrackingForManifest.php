<?php

namespace App\Rules;

use App\Models\ShipmentManifestTracking;
use Illuminate\Contracts\Validation\Rule;

class CheckTotalTrackingForManifest implements Rule
{
    protected $id;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->id = $id;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $trackingNumber = ShipmentManifestTracking::where('manifest_id', $value)->count();
        return $trackingNumber > 0;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Do not generate manifest because there is no tracking number';
    }
}
