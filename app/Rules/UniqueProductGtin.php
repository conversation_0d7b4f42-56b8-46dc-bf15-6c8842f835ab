<?php

namespace App\Rules;

use App\Models\Product;
use App\Models\SaleOrderItem;
use Illuminate\Contracts\Validation\Rule;

class UniqueProductGtin implements Rule
{
    private $id;

    /**
     * Create a new rule instance.
     *
     * @param null $id
     */
    public function __construct($id = null)
    {
        $this->id = $id;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        if (!$value) return true;
        $product = Product::gtin($value, $this->id)->first();
        return !$product;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'Invalid GTIN';
    }
}
