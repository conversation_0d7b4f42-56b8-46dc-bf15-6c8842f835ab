<?php

namespace App\Rules;

use App\Models\ProductMatch;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Log;

class CheckSwiftPodSKUCreateMatching implements Rule
{
    protected $id;
    protected $storeId;
    protected $shipstationSku;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($storeId, $shipstationSku)
    {
        $this->storeId = $storeId;
        $this->shipstationSku = $shipstationSku;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $sku = ProductMatch::where('store_id', $this->storeId)
            ->where('shipstation_sku', $this->shipstationSku)
            ->where('swiftpod_sku', $value)
            ->get();
        return count($sku) == 0;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The Swiftpod SKU has been matched for the shipstation SKU.';
    }
}
