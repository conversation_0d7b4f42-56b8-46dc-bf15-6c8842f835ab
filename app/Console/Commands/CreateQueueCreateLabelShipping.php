<?php

namespace App\Console\Commands;

use App\Models\QueueJob;
use App\Services\QueueShippingLabelService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreateQueueCreateLabelShipping extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:create-queue-shipping-label';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create queue shipping label';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(QueueShippingLabelService $queueShippingLabelService)
    {
        while (true) {
            try {
                $count = $queueShippingLabelService->addQueueCreateShippingLabel();
                $this->info(date('Y-m-d H:i:s') . ' | ' . QueueJob::QUEUE_CREATE_SHIPPING_LABEL . ': ' . $count);
            } catch (\Throwable $error) {
                Log::channel('queue_create_shipping_label')->error($error->getMessage());
                $this->error($error->getMessage());
            }
            DB::disconnect();
            sleep(5);
        }
    }
}
