<?php

namespace App\Console\Commands;

use App\Http\Service\GetOrderService;
use App\Repositories\SanMarServiceRepository;
use Carbon\CarbonPeriod;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class SanMarSyncOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:sanMarSyncOrder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $sanMarRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(SanMarServiceRepository $sanMarRepository)
    {
        $this->sanMarRepository = $sanMarRepository;
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $startDate = '2022-02-01';
        $endDate = '2022-03-16';
        $dataInvoices = $this->sanMarRepository->getInvoicesHeaderByInvoiceDateRange($startDate, $endDate);

        $dataInvoiceOrder = [];
        if (isset($dataInvoices->InvoiceHeader)) {
            foreach ($dataInvoices->InvoiceHeader as $itemInvoice) {

                ///Todo : group  Invoice theo PurchaseOrderNo
                $dataInvoiceOrder[$itemInvoice->PurchaseOrderNo][] = $itemInvoice;
            }
        }


        try {
            foreach ($dataInvoiceOrder as $purchaseNo => $invoice) {

                $shippingOrder = $this->sanMarRepository->OrderShipmentNotificationServiceBinding($purchaseNo);
                $shipmentLocation = $shippingOrder->OrderShipmentNotificationArray->OrderShipmentNotification
                    ->SalesOrderArray->SalesOrder->ShipmentLocationArray->ShipmentLocation;
                
              ///Todo : xu ly tung Ivoice
                foreach ($invoice as $numberInvoice => $itemInvoice) {

                    ///Todo : lay thong tin cua tung Invoice
                    $invoiceDetail = $this->sanMarRepository->getInvoiceByInvoiceNo($itemInvoice->InvoiceNo);

                    ////Todo : xu ly tung Line item ( item trong moi purchase order)
                    if (is_array($invoiceDetail->LineItem)) {
                        foreach ($invoiceDetail->LineItem as &$line) {
                            $productId = $line->StyleNo;
                            $partId = $line->UniqueKey;
                            $product = $this->sanMarRepository->ProductDataServiceBindingV2($productId, $partId);
                            $line->gtin = $product->Product->ProductPartArray->ProductPart->gtin;
                        }

                    } else {
                        $productId = $invoiceDetail->LineItem->StyleNo;
                        $partId = $invoiceDetail->LineItem->UniqueKey;
                        $product = $this->sanMarRepository->ProductDataServiceBindingV2($productId, $partId);
                        $invoiceDetail->LineItem->gtin = $product->Product->ProductPartArray->ProductPart->gtin;
                    }

                    $shipingInfo = is_array($shipmentLocation) ? $shipmentLocation[$numberInvoice] : $shipmentLocation;
                    $box = $shipingInfo->PackageArray->Package;

                    /////Todo : kiem tra trang thai Invoice get ve co all box da full tracking_number chua ?
                    $status = 1;
                    if(is_array($box)){
                        $purchaseOrderSanmar['tracking_number'] = $box[0]->trackingNumber;
                        foreach ($box as $item){
                            if(empty($item->trackingNumber)){
                                $status = 0;
                            }
                        }

                    }else{
                        $purchaseOrderSanmar['tracking_number'] = $box->trackingNumber;
                        if(empty($box->trackingNumber)){
                            $status = 0;
                        }
                    }
                    $purchaseOrderSanmar['order_number'] = $invoiceDetail->Header->SalesOrderNumber;
                    $purchaseOrderSanmar['purchase_number'] = $invoiceDetail->Header->PurchaseOrderNo;
                    $purchaseOrderSanmar['invoice_number'] = $invoiceDetail->Header->InvoiceNo;
                    $purchaseOrderSanmar['status'] = $status;
                    $purchaseOrderSanmar['data'] = json_encode([
                        'invoice' => $invoiceDetail,
                        'shipping' => $shipingInfo,
                    ]);
                    $purchaseOrderSanmar['created_at'] = date('Y-m-d H:i:s');
                    print_r($purchaseOrderSanmar);

                    $isExisted = DB::table('purchase_order_sanmar')
                        ->where('invoice_number', $invoiceDetail->Header->InvoiceNo)->count();

                    if(!$isExisted){
                        DB::table('purchase_order_sanmar')->insert($purchaseOrderSanmar);
                    }

                }






            }
        } catch (\Exception $exception) {


            dd($exception->getMessage());
        }


        dd('done');


    }







}


function tmp (){

    $box = [];

    $invoiceDetail = $this->sanMarRepository->getInvoiceByInvoiceNo($itemInvoice->InvoiceNo);

    dd($invoiceDetail);

    if (isset($invoiceDetail->Header->PurchaseOrderNo)) {
        $orderNumber = $invoiceDetail->Header->PurchaseOrderNo;

        dd($orderNumber);
        $shippingOrder = $this->sanMarRepository->OrderShipmentNotificationServiceBinding($orderNumber);

        $shipmentLocation = $shippingOrder->OrderShipmentNotificationArray->OrderShipmentNotification
            ->SalesOrderArray->SalesOrder->ShipmentLocationArray->ShipmentLocation;

        //dd($shipmentLocation);

        foreach ($shipmentLocation as $shipment) {
            if (is_array($shipment->PackageArray->Package)) {
                foreach ($shipment->PackageArray->Package as $itemPackage){
                    $box[] = $itemPackage;
                }
            } else {
                $box[] = $shipment->PackageArray->Package;
            }
        }

        if (isset($invoiceDetail->LineItem)) {
            if (is_array($invoiceDetail->LineItem)) {
                foreach ($invoiceDetail->LineItem as $line) {
                    $productId = $line->StyleNo;
                    $partId = $line->UniqueKey;
//                                $product = $this->sanMarRepository->ProductDataServiceBindingV2($productId, $partId);
//                                if (isset($product->Product->ProductPartArray->ProductPart)) {
//                                    $gtin = $product->Product->ProductPartArray->ProductPart->gtin;
//                                }
                }
            } else {
                $productId = $invoiceDetail->LineItem->StyleNo;
                $partId = $invoiceDetail->LineItem->UniqueKey;
//                            $product = $this->sanMarRepository->ProductDataServiceBindingV2($productId, $partId);
//                            // dd($product);
//                            if (isset($product->Product->ProductPartArray->ProductPart)) {
//                                $gtin = $product->Product->ProductPartArray->ProductPart->gtin;
//                            }
            }
        }
    }

    dd($invoiceDetail,$box);
}
