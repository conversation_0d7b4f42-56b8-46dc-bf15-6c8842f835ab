<?php

namespace App\Console\Commands;

use App\Models\SaleOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RetryTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'retry:tracking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->shipNotify();
    }

    public function orderNotify()
    {
        $callbackLog = DB::table('callback_log')->where('event', 'order_notify')
            ->where('message', "Order status in_production to in_production")
            ->where('created_at', '>=', '2023-05-15')
            ->get();

        foreach ($callbackLog as $item) {
            echo "Order id: " . $item->order_id . "\n";
            handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $item->order_id);
        }
    }

    public function shipNotify()
    {
        $callbackLog = DB::table('callback_log')
            ->where('event', 'shipment_notify')
            ->where('message', "Order don't have tracking")
            ->where('created_at', '>=', '2023-05-15')
            ->get();

        foreach ($callbackLog as $item) {
            echo "Order id: " . $item->order_id . "\n";
            $check = DB::table('callback_log')
                ->where('order_id', $item->order_id)
                ->where('event', 'shipment_notify')
                ->whereNotNull('value')
                ->first();
            if (!$check) {
                echo "\nOrder id: " . $item->order_id . "\n";
                handleJob(SaleOrder::JOB_NOTIFY_TRACKING_SHIPMENT, $item->order_id);
            }
        }
    }
}
