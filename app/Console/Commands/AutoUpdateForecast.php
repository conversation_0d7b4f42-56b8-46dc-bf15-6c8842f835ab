<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class AutoUpdateForecast extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:auto-update-forecast';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto update forecast daily';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->info('Start update forecast');
        $sql = "REPLACE INTO
    forecast_sale_order(
        product_id,
        warehouse_id,
        order_date,
        quantity
    ) SELECT
        sale_order_item.product_id AS product_id,
        sale_order.warehouse_id AS warehouse_id,
        sale_order.order_date AS order_date,
        SUM(sale_order_item.quantity) AS quantity
    FROM
        sale_order
    JOIN sale_order_item ON sale_order.id = sale_order_item.order_id
    WHERE
    	sale_order.is_test = 0 AND
        sale_order_item.product_id IS NOT NULL AND order_date = DATE(SUBDATE(NOW(), INTERVAL 1 DAY))
    GROUP BY
        sale_order_item.product_id,
        sale_order.warehouse_id,
        sale_order.order_date";
        DB::statement($sql);
        $this->info('End update forecast');
    }
}
