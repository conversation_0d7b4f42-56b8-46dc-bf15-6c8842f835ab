<?php

namespace App\Console\Commands;

use App\Events\CountPendingNotification;
use App\Models\BarcodePrinted;
use App\Models\ProductColor;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CountWipPending extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'count:wip-pending';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Count Wip Pending';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        while (true) {
            $startTime = microtime(true);
            $result = $this->countPending();
            $endTime = microtime(true);
            echo Carbon::now()->format('Y-m-d H:i:s') . ' | Time: ' . ($endTime - $startTime) . " seconds\n";
            if (empty($result)) {
                echo Carbon::now()->format('Y-m-d H:i:s') . " | Empty wip \n";
                sleep(10);

                continue;
            }

            echo Carbon::now()->format('Y-m-d H:i:s') . ' | Count wip = ' . count($result) . "\n";
            broadcast(new CountPendingNotification($result));
            sleep(10);
        }
    }

    public function countPending()
    {
        $result = [];
        $data = DB::table(DB::raw('sale_order_item_barcode FORCE INDEX (count_idx)'))
            ->select(DB::raw('product_style.sku as style_sku, product_color.sku as color_sku, product_style.name as style_name, product_color.name as color_name, COUNT(DISTINCT sale_order_item_barcode.id) total, barcode_printed_time.printed_at as printed_at, sale_order_item.warehouse_id'))
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->join('product_color', 'product_color.sku', 'sale_order_item.product_color_sku')
            ->leftJoin('barcode_printed_time', function ($join) {
                $join->on('barcode_printed_time.style_sku', 'sale_order_item.product_style_sku');
                $join->on('barcode_printed_time.color_sku', 'sale_order_item.product_color_sku');
                $join->on('barcode_printed_time.warehouse_id', 'sale_order_item.warehouse_id');
            })
            ->where('sale_order.is_test', SaleOrder::INACTIVE)
            ->where('sale_order.is_xqc', SaleOrder::INACTIVE)
            ->where('sale_order.is_eps', SaleOrder::INACTIVE)
            ->where('sale_order_item.ink_color_status', SaleOrderItem::DETECT_INK_COLOR_DONE)
            ->where('sale_order_item_barcode.barcode_printed_id', BarcodePrinted::BARCODE_PRINTED_ID_DEFAULT)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_DTG)
            ->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL)
            ->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::ACTIVE)
            ->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058))
            ->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565))
            ->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED)
            ->whereNull('sale_order_item_barcode.label_root_id')
            ->whereNull('sale_order_item_barcode.employee_reroute_id')
            ->where('sale_order.is_fba_order', SaleOrder::INACTIVE)
            ->where('product_style.type', '!=', ProductStyle::TYPE_INSERT)
            ->groupBy('sale_order_item.product_style_sku')
            ->groupBy('sale_order_item.product_color_sku')
            ->groupBy('sale_order_item.warehouse_id')
            ->get();

        $barcodePending = DB::table('barcode_printed')
            ->select(DB::raw('barcode_printed.style_sku as style_sku, barcode_printed.color_sku as color_sku, barcode_printed_time.printed_at as printed_at, barcode_printed.warehouse_id, barcode_printed.quantity'))
            ->leftJoin('barcode_printed_time', function ($join) {
                $join->on('barcode_printed_time.style_sku', 'barcode_printed.style_sku');
                $join->on('barcode_printed_time.color_sku', 'barcode_printed.color_sku');
                $join->on('barcode_printed_time.warehouse_id', 'barcode_printed.warehouse_id');
            })
            ->whereNotNull('barcode_printed.color_sku')
            ->where('barcode_printed.print_status', BarcodePrinted::INACTIVE)
            ->get();
        $pendings = [];
        $pendingsZeroQuantity = [];
        if (!empty($barcodePending)) {
            foreach ($barcodePending as $pending) {
                $pendings["{$pending->style_sku}_{$pending->color_sku}_{$pending->warehouse_id}"] = true;

                // Handle case pending = 0 & print status = 0
                $check = $data->first(function ($item) use ($pending) {
                    return $item->style_sku != $pending->style_sku && $item->color_sku != $pending->color_sku && $item->warehouse_id != $pending->warehouse_id;
                });
                if ($check) {
                    $pendingsZeroQuantity[] = [
                        'style_sku' => $pending->style_sku,
                        'color_sku' => $pending->color_sku,
                        'warehouse_id' => $pending->warehouse_id,
                        'printed_at' => $pending->printed_at,
                        'quantity' => $pending->quantity
                    ];
                }
            }
        }

        foreach ($data as $item) {
            $key = "{$item->style_sku}_{$item->color_sku}_{$item->warehouse_id}";
            $result["{$item->style_name}_{$item->color_name}_{$item->warehouse_id}"] = [
                'total' => $item->total,
                'printed_at' => $item->printed_at,
                'status' => array_key_exists($key, $pendings) ? BarcodePrinted::WIP_STATUS_PENDING_PRINT : BarcodePrinted::WIP_STATUS_PENDING_CONVERT
            ];
        }

        if (!empty($pendingsZeroQuantity)) {
            foreach ($pendingsZeroQuantity as $value) {
                $style = ProductStyle::findByStyleSku($value['style_sku']);
                $color = ProductColor::findBySku($value['color_sku']);
                $result["{$style?->name}_{$color?->name}_{$value['warehouse_id']}"] = [
                    'total' => $value['quantity'],
                    'printed_at' => $value['printed_at'],
                    'status' => BarcodePrinted::WIP_STATUS_PENDING_PRINT
                ];
            }
        }

        return $result;
    }
}
