<?php

namespace App\Console\Commands;

use App\Models\PricingHistory;
use App\Models\Store;
use App\Models\StorePricingHistory;
use App\Repositories\StoreProductRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Rap2hpoutre\FastExcel\FastExcel;

class ImportStoreProductPricing extends Command
{
    protected StoreProductRepository $storeProductPricingRepo;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:store-product-pricing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'import store product pricing';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(StoreProductRepository $storeProductPricingRepo)
    {
        parent::__construct();

        $this->storeProductPricingRepo = $storeProductPricingRepo;
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        while (true) {
            $this->processClonePricingPending();
            $this->processUploadPricingPending();
            $this->info('Waiting...');
            sleep(10 * 60);
        }
    }

    private function processClonePricingPending()
    {
        $clonePricingPending = StorePricingHistory::whereNotNull('effective_from')
            ->where('status', StorePricingHistory::PENDING_STATUS)
            ->where('effective_from', '<=', Carbon::now())
            ->get();

        if ($clonePricingPending->isEmpty()) {
            return false;
        }
        foreach ($clonePricingPending as $pending) {
            echo "clone pricing store {$pending->store_id}" . PHP_EOL;
            $result = $this->storeProductPricingRepo->clonePricingPending($pending);
            $pending->status = $result ? StorePricingHistory::APPLIED_STATUS : StorePricingHistory::ERROR_STATUS;
            $pending->save();
        }

        return true;
    }

    private function processUploadPricingPending()
    {
        $uploadPricingPending = PricingHistory::whereNotNull('effective_from')
            ->where('status', PricingHistory::PENDING_STATUS)
            ->where('effective_from', '<=', Carbon::now())
            ->get();
        if ($uploadPricingPending->isEmpty()) {
            return false;
        }

        foreach ($uploadPricingPending as $csvPending) {
            try {
                echo "import pricing store {$csvPending->store_id}" . PHP_EOL;
                $this->importCsvFile($csvPending);
                $csvPending->status = PricingHistory::APPLIED_STATUS;
                $csvPending->save();
                echo 'Success';
            } catch (\Throwable $exception) {
                echo 'Failed';
                $csvPending->status = PricingHistory::ERROR_STATUS;
                $csvPending->save();
                Log::error('Error in processUploadPricingPending: ' . $exception->getMessage(), [
                    'csvPending_id' => $csvPending->id ?? null,
                    'exception' => $exception
                ]);
            }
        }

        return true;
    }

    private function importCsvFile($csvPending)
    {
        try {
            $fileContents = file_get_contents($csvPending->url);
            if ($fileContents === false) {
                throw new \Exception("Failed to get file contents from URL: {$csvPending->url}");
            }
            $tempFilePath = $this->getTempFilePath($csvPending->id);
            file_put_contents($tempFilePath, $fileContents);
            $uploadedFile = new \Illuminate\Http\UploadedFile(
                $tempFilePath,
                'import_file.xlsx',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                null,
                true,
            );

            $sheets = (new FastExcel)->withSheetsNames()->importSheets($uploadedFile);
            $arraySheet = $sheets->toArray();
            $store = Store::find($csvPending->store_id);
            if (!$store) {
                throw new \Exception("Store not found for store_id: {$csvPending->store_id}");
            }
            $result = $this->storeProductPricingRepo->importStorePricing(
                $store,
                $arraySheet,
                $uploadedFile,
                true,
                null,
                false,
            );
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }

            if (!$result) {
                throw new \Exception("Failed to import store pricing for store_id: {$csvPending->store_id}");
            }

            return true;
        } catch (\Throwable $exception) {
            Log::error('Error in importCsvFile: ' . $exception->getMessage(), [
                'csvPending_id' => $csvPending->id ?? null,
                'exception' => $exception
            ]);
            throw $exception;
        }
    }

    private function getTempFilePath($id)
    {
        $tempFolderPath = storage_path('app/public/pricing/');
        if (!is_dir($tempFolderPath)) {
            mkdir($tempFolderPath, 0777, true);
        }

        return $tempFolderPath . 'temp_import_file_' . $id . '.xlsx';
    }
}
