<?php

namespace App\Console\Commands;

use App\Http\Service\ConvertService;
use App\Models\BarcodePrinted;
use App\Models\Warehouse;
use Illuminate\Console\Command;

class ConvertPoster extends Command
{
    protected ConvertService $convertService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'convert:poster';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Poster to PDF';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(ConvertService $convertService)
    {
        parent::__construct();
        $this->convertService = $convertService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        while (true) {
            $job = BarcodePrinted::findToPoster();
            if (!$job) {
                sleep(10);

                continue;
            }

            sleep(15);
            if ($job->warehouse_id == Warehouse::WAREHOUSE_SANJOSE_ID) {
                $this->convertService->convertPosterWithLayerCut($job);
            } else {
                $this->convertService->convertPoster($job);
            }
        }
    }
}
