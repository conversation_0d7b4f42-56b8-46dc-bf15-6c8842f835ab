<?php

namespace App\Console\Commands;

use App\Exports\WeeklyLicensedSaleReportFor80sTeeStoreExport;
use App\Models\SaleOrder;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class WeeklyLicensedSaleReportFor80sTeeStore extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'export:WeeklyLicensedSalesFor80sTee';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Weekly licensed sales report for 80s tee store';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        setTimezone();

        $today = Carbon::now();
        $startOfLastWeek = $today->clone()->subWeek()->startOfWeek();
        $endOfLastWeek = $today->clone()->subWeek()->endOfWeek();

        echo "Exporting Weekly licensed sales report for 80s tee store from {$startOfLastWeek->copy()->toDateTimeString()} to {$endOfLastWeek->copy()->toDateTimeString()}...\n";

        $dataReport = collect();
        SaleOrder::where('sale_order.store_id', Store::STORE_80S_TEE)
            ->where('is_test', SaleOrder::NOT_TEST)
            ->whereNotIn('order_status', SaleOrder::ARRAY_STATUS_INACTIVE)
            ->whereBetween('sale_order.created_at', [$startOfLastWeek->copy()->toDateTimeString(), $endOfLastWeek->copy()->toDateTimeString()])
            ->join('sale_order_item as item', 'item.order_id', '=', 'sale_order.id')
            ->join('licensed_designs as ld', 'ld.licensed_design_id', '=', 'item.licensed_design_id')
            ->leftJoin('licensed_holders as lh', 'lh.id', '=', 'ld.licensed_holder_id')
            ->select(
                'lh.licensed_holder',
                'item.licensed_design_id',
                DB::raw('SUM(item.quantity) as weekly_unit_sold'),
            )
            ->groupBy('item.licensed_design_id')
            ->orderBy('sale_order.id', 'asc')
            ->chunk(500, function ($orders) use ($dataReport) {
                $orders->each(function ($order) use ($dataReport) {
                    $dataReport->push([
                        'license_holder' => $order->licensed_holder,
                        'licensed_design_id' => $order->licensed_design_id,
                        'weekly_unit_sold' => $order->weekly_unit_sold
                    ]);
                });
            });
        $fileName = "licensed/licensed_sale_report_{$startOfLastWeek->copy()->format('mdY')}-{$endOfLastWeek->copy()->format('mdY')}.xlsx";
        Excel::store(new WeeklyLicensedSaleReportFor80sTeeStoreExport($dataReport), $fileName, 's3');
        $url = Storage::disk('s3')->url($fileName);
        echo $url;
        // Reset time zone
        setTimezoneDefault();

        return $url;
    }
}
