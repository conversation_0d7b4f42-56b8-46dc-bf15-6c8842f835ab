<?php

namespace App\Console\Commands\QuickBook;

use App\Jobs\QBDeleteBillJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class QBDeleteBillCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qb:delete-bill {--ids=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Bill and sync to Quick Book';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $query = DB::table('purchase_order')
                ->where('qb_ref', '>', 0);

            if (!empty($this->option('ids'))) {
                $ids = explode(',', $this->option('ids'));
                $query->whereIn('id', $ids);
            }

            $total = $query->clone()->count('id');
            $success = [];
            $index = 1;

            $query->chunkById(5000, function ($data) use (&$index, $total, &$success) {
                foreach ($data as $item) {
                    $this->info("Process {$index}/{$total}");
                    dispatch(new QBDeleteBillJob($item->id));
                    $index++;
                    $success[] = $item->id;
                }
            });

            $this->info('Success: ' . implode(',', $success));
            Log::info('QBSyncBillCommand.handle Dispatch job delete bill success', $success);

            return 1;
        } catch (\Exception $e) {
            $this->error($e->getMessage());
            Log::error('QBSyncBillCommand.handle', [$e]);

            return 0;
        }
    }
}
