<?php

namespace App\Console\Commands;

use App\Models\Setting;
use App\Models\Shipment;
use Carbon\Carbon;
use EasyPost\EasyPost;
use EasyPost\Shipment as ShipmentEasyPost;
use Illuminate\Console\Command;


class RefundShipmentUnSubmitted extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:refund-shipment-un-submit';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $shipmentUnSubmits = Shipment::with('shipmentEasypost')->where('refund_status', 'un_submitted')
                ->get();
            if ($shipmentUnSubmits->count() == 0) {
                $this->info('done..');
                exit;
            }
            $easypostApiKey = Setting::where('name', Setting::EASYPOST_API_KEY)->first()->value;
            foreach ($shipmentUnSubmits as $key => $shipmentUnSubmit) {
                $this->info("order $shipmentUnSubmit->id");
                EasyPost::setApiKey($easypostApiKey);
                $shipment_id = $shipmentUnSubmit->shipmentEasypost->easypost_id;
                $shipment = ShipmentEasyPost::retrieve($shipment_id);
                $refund = $shipment->refund();
                $shipmentUnSubmit->refund_status = $refund->refund_status;
                $shipmentUnSubmit->save();
                sleep(5);
            }
            $this->info("end foreach \n");
        } catch (\Exception $exception) {
            $this->error($exception->getMessage());
        }
    }
}
