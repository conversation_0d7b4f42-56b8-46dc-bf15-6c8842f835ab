<?php

namespace App\Console\Commands;

use App\Repositories\ProductRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class CoverProductBlankCostYearCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cover:product-blank-cost-year';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Cover data to product_blank_cost_year table.";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle(ProductRepository $productRepository): void
    {
        $this->info("============ Start ============");

        $yearNow = Carbon::now('America/Los_Angeles')->year;
        $year = 2022;

        while ($year < $yearNow) {
            $this->info("============ $year ============");

            setTimezone();
            $productRepository->coverProductBlankCostByYear($year);
            $year++;
        }
        
        $this->info("============ End ============");
    }
}
