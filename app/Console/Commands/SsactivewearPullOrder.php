<?php

namespace App\Console\Commands;

use App\Http\Service\AlertService;
use App\Models\Setting;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class SsactivewearPullOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ssactivewear:pull-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'pull order from ssactivewear';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */

    public function handle()
    {
        while (true){
            $this->pullOrder();
            sleep(60 * 60 );
        }
    }
    public function pullOrder()
    {
        $dataSettingAccount = Setting::where('label', 'ssactivewear_account')->first();
        if (empty($dataSettingAccount)) {
            $this->error("Account connect to api.ssactivewear.com NOT FOUND");
            $alertService = new AlertService();
            return $alertService->alertPullPurchaseOrder("Account connect to api.ssactivewear.com NOT FOUND");
        }
        $settingAccount = explode("_USER_PASS_", $dataSettingAccount->value);
        $user = $settingAccount[0];
        $password = $settingAccount[1];
        $response = Http::withBasicAuth($user, $password)->get('https://api.ssactivewear.com/v2/orders/?All=True&lines=true&Boxes=true&mediatype=json');
        if ($response->getStatusCode() <> 200) {
            $this->error("Cannot connect to api.ssactivewear.com");
            $alertService = new AlertService();
            return $alertService->alertPullPurchaseOrder("Ssactivewear : Cannot connect to api.ssactivewear.com");
        }
        $data = json_decode($response->getBody()->getContents());
        if (empty($data)) {
            $this->error("Data from api.ssactivewear.com is empty");
            $alertService = new AlertService();
            return $alertService->alertPullPurchaseOrder("Ssactivewear : Data from api.ssactivewear.com is empty");
        }
        $invoiceNumberIds = array_column($data, "invoiceNumber");
        $dataChunkInvoiceNumberIds = array_chunk($invoiceNumberIds, 500);
        $ordersExited = array();
        foreach ($dataChunkInvoiceNumberIds as $itemChunk) {
            $dataOrderInvoiceIds = DB::table('purchase_order_ssactivewear')
                ->whereIn('invoice_number', $itemChunk)
                ->where('vendor_id', 3)
                ->pluck('invoice_number')
                ->toArray();
            if (!empty($dataOrderInvoiceIds)) {
                foreach ($dataOrderInvoiceIds as $invoiceId) {
                    array_push($ordersExited, $invoiceId);
                }
            }
        }
        $i = 0;
        foreach ($data as $item) {
            if (empty($item->invoiceNumber)) continue;
            if (!in_array($item->invoiceNumber, $ordersExited)) {
                $this->info("insert order number: $item->orderNumber, invoice number : $item->invoiceNumber order status is $item->orderStatus");
                $status = 1;
                if (count($item->boxes) > 1) {
                    foreach ($item->boxes as $itemBox) {
                        if (empty($itemBox->trackingNumber)) $status = 0;
                    }
                } else {
                    foreach ($item->boxes as $itemBox) {
                        if (empty($itemBox->trackingNumber) && empty($item->trackingNumber)) $status = 0;
                    }
                }
                DB::table('purchase_order_ssactivewear')->insert([
                    "order_number" => $item->orderNumber,
                    "invoice_number" => $item->invoiceNumber,
                    "data" => json_encode($item),
                    "status" => $status,
                    'created_at' => date("Y-m-d H:i:s"),
                ]);
                $i++;
            }
             sleep(2);
        }
        $this->info("Pull $i orders done");
        $this->info("-------------Begin insert Orders---------------");
        $this->call('ssactivewear:insert-order');
    }
}
