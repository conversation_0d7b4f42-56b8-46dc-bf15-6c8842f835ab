<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SaleOrderItemImage;
use App\Models\ImageHash;
use Exception;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Carbon;

class CreateThumbArtworkManual extends Command
{
    protected $path_art;
    protected $path_thumb;
    protected $path_thumb_proof;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:thumb-artwork-manual';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->path_art = storage_path('app/public/750/');
        $this->part_thumb = storage_path('app/public/250/');
        $this->path_thumb_proof = storage_path('app/public/proof/');
    }

    public function grabImage($url, $saveto)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $config['useragent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';

        curl_setopt($ch, CURLOPT_USERAGENT, $config['useragent']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $raw = curl_exec($ch);
        if ($url == 'https://d1ud88wu9m1k4s.cloudfront.net/design/2021/06/09/A19420_SEKDjadYGsKX53ZlX7JQwmuz0_1623214010469.png') {
            echo $raw;
            exit;
        }
        curl_close($ch);
        if (file_exists($saveto)) {
            unlink($saveto);
        }
        $fp = fopen($saveto, 'x');
        fwrite($fp, $raw);
        fclose($fp);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        while (true) {
            $data = SaleOrderItemImage::where('id', 1526877)->get();
            if (count($data) == 0) {
                echo "done\n";
                sleep(3);
                continue;
            }

            foreach ($data as $item) {
                try {
                    echo $item->image_url . "\n";
                    $side = $item->print_side;
                    $dir = $this->path_art . $item->order_date;
                    $dir_thumb = $this->part_thumb . $item->order_date;
                    $dir_thumb_proof = $this->path_thumb_proof . $item->order_date;
                    if (!is_dir($dir)) {
                        mkdir($dir, 0777, true);
                    }
                    if (!is_dir($dir_thumb)) {
                        mkdir($dir_thumb, 0777, true);
                    }
                    if (!is_dir($dir_thumb_proof)) {
                        mkdir($dir_thumb_proof, 0777, true);
                    }
                    $file = $dir . '/' . $item->sku . '-' . $side . '.png';
                    $thumb = $dir_thumb . '/' . $item->sku . '-' . $side . '.png';
                    $thumb_s3 = $item->order_date . '/' . $item->sku . '-' . $side . '.png';
                    $thumb_proof = $dir_thumb_proof . '/' . $item->sku . '-' . $side . '.png';
                    $thumb_proof_s3 = $item->order_date . '/' . $item->sku . '-' . $side . '.png';

                    //  echo $file."\n";

                    file_put_contents($file, file_get_contents($item->image_url));

                  //  $this->grabImage($item->image_url, $file);

                    list($width, $height, $type, $attr) = getimagesize($file);

                    $this->createThumbNoTrim($file, $thumb, $thumb_s3, $thumb_proof, $thumb_proof_s3);

                    $this->proofThumb($file, 500, $thumb_proof, $thumb_proof_s3);

                    $this->createThumb($file, $file);



                    if (!file_exists($file)) {
                        throw new Exception('thumb 750 not found');
                    }

                    // hash md5 file
                    $hash_md5 = md5_file($item->image_url);
                    $hash = ImageHash::findByMd5($hash_md5);
                    if (!$hash) {
                        $hash = ImageHash::create(['hash_md5' => $hash_md5]);
                    }
                    $item->image_width = $width;
                    $item->image_height = $height;
                    $item->thumb_750 = 1;
                    $item->thumb_250 = 1;
                    $item->image_hash_id = $hash->id;
                    $item->save();
                    echo " $width $height--> done\n";
                } catch (\Exception $e) {
                    echo $e->getMessage();
                    $item->thumb_750 = 2;
                    $item->thumb_250 = 2;
                    $item->save();
                }
            }
        }
    }

    public function createThumbNoTrim($url, $filename, $thumb_s3, $thumb_proof, $thumb_proof_s3, $width = 250, $height = true)
    {
        $img = new \Imagick($url);
        $img->thumbnailImage($width, 0, false);
        $img->setImageFormat('png');
        $img->writeImage($filename);

        if (!file_exists($filename)) {
            throw new Exception('thumb 250 not found');
        }

        echo "250 ---> $thumb_s3\n";
        Storage::disk('s3')->put("/thumb/250/$thumb_s3", $img->getImageBlob());
        // $this->proofThumb($filename, $width, $thumb_proof, $thumb_proof_s3);
        unlink($filename);
        $img = null;
    }


    public function createThumb($url, $filename, $width = 750, $height = true)
    {
        $img = new \Imagick($url);
        $img->trimImage(0);
        $img->thumbnailImage($width, 0, false);
        $img->setImageFormat('png');
        $img->writeImage($filename);
        $img = null;
    }

    public function proofThumb($url, $width, $thumb_proof, $thumb_proof_s3)
    {
        $image = new \Imagick($url);


        $image->thumbnailImage($width, 0, false);

        $text = "SWIFTPOD - " . Carbon::now()->format('Y/m/d H:i:s');

        $draw = new \ImagickDraw();

        $draw->setFont('tahoma.ttf');

        $draw->setFontSize(40);
        $draw->setFillColor('white');
        $draw->setGravity(\Imagick::GRAVITY_SOUTHEAST);
        $draw->setFillOpacity(0.2);
        for ($i = 1; $i <= 7; $i += 2) {
            $image->annotateImage($draw, 17, 120 * $i - 1, -45, $text);
        }

        $draw->setFillColor('grey');
        $draw->setFillOpacity(0.6);
        for ($i = 1; $i <= 7; $i += 2) {
            $image->annotateImage($draw, 20, 120 * $i - 1, -45, $text);
        }

        /* Give image a format */
        $image->setImageFormat('png');
        $image->writeImage($thumb_proof);

        if (!file_exists($thumb_proof)) {
            throw new Exception('thumb proof not found');
        }

        echo "proof ---> $thumb_proof_s3\n";
        Storage::disk('s3')->put("/thumb/proof/$thumb_proof_s3", $image->getImageBlob());
        $image = null;
        unlink($thumb_proof);
    }
}
