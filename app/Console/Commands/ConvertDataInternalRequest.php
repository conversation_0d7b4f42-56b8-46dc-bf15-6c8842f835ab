<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ConvertDataInternalRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'convert-data:internal-request';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $internalRequests = DB::table('internal_request_old')
            ->where('box_quantity', '>', 1)
            ->whereIn('status', ['uncheck', 'checked'])
            ->get();
            jobEcho("total: " . count($internalRequests));
            foreach ($internalRequests as $request) {
                jobEcho("start old id: {$request->id}");
                jobEcho("status: {$request->status}");
                jobEcho("total box: {$request->box_quantity}");

                $boxes = DB::table('box_internal_request')->where('internal_request_id', $request->id)->get();
                $internalRequestNews = DB::table('internal_request')->where('old_id', $request->id)->get();

                foreach($internalRequestNews as $requestNew) {
                    foreach ($boxes as $box) {
                        if (empty($box->selected)) {
                            $internalRequestUpdate['box_id'] = $box->box_id;
                            $box->selected = true;
                            jobEcho("box {$box->box_id} duoc chon");
                            break;
                        }
                    }
                    DB::table('internal_request')->where('id', $requestNew->id)->update($internalRequestUpdate);
                    jobEcho("update internal request: {$requestNew->id}");
                }
            }
        } catch (\Throwable $th) {
            dd($th->getMessage());
        }
    }
}
