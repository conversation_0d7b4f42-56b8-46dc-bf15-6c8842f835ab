<?php

namespace App\Console\Commands;

use App\Models\ProductRankHistory;
use App\Models\SaleOrder;
use App\Repositories\SupplyInventoryRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ReportProductRankHistories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inventory:report-product-rank {--quarter=} {--year=}';

    protected $supplyInventoryRepository;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Report Product Rank';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(SupplyInventoryRepository $supplyInventoryRepository)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $quarter = !empty($this->option('quarter')) ? $this->option('quarter') : now()->subQuarter()->quarter;
        $year = !empty($this->option('year')) ? $this->option('year') : now()->subQuarter()->year;

        if ($quarter < 1 || $quarter > 4) {
            $this->error('Invalid quarter value');

            return 0;
        }

        if (strlen($year) != 4) {
            $this->error('Invalid year value');

            return 0;
        }

        $quarterLabel = "Q{$quarter}/{$year}";
        $startOfQuarter = Carbon::create($year, ($quarter - 1) * 3 + 1, 1)->startOfDay()->format('Y-m-d H:i:s');
        $endOfQuarter = Carbon::create($year, $quarter * 3, 1)->endOfMonth()->endOfDay()->format('Y-m-d H:i:s');
        $this->info("Process time {$quarterLabel} ({$startOfQuarter} ~ {$endOfQuarter})");
        setTimezone();
        $data = SaleOrder::query()
            ->selectRaw('
                sale_order_item.product_id,
                SUM(sale_order_item.quantity) as total_unit_sold,
                SUM(sale_order_item.amount_paid) as total_revenue
            ')
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->whereNotIn('sale_order.order_status', [
                SaleOrder::ON_HOLD,
                SaleOrder::CANCELLED,
                SaleOrder::REJECTED,
                SaleOrder::DRAFT,
                SaleOrder::STATUS_IN_PRODUCTION_CANCELLED
            ])
            ->where('sale_order.is_test', 0)
            ->where('sale_order.created_at', '>=', $startOfQuarter)
            ->where('sale_order.created_at', '<=', $endOfQuarter)
            ->whereNotNull('sale_order_item.product_id')
            ->groupBy('sale_order_item.product_id')
            ->orderByDesc('total_unit_sold')
            ->orderByDesc('total_revenue')
            ->get();
        $total = $data->count();
        $dataRank = $data->chunk(round($total * 20 / 100));
        $ranks = ['A', 'B', 'C', 'D', 'E'];
        $dataLog = [
            'uuid' => Str::uuid(),
            'year' => $year,
            'quarter' => $quarter,
        ];
        Log::info('ReportProductRankHistories.handle begin', $dataLog);

        foreach ($dataRank as $item) {
            $dataUpsert = [];
            $rank = array_shift($ranks) ?? 'E';

            foreach ($item as $productInfo) {
                $productItem = $productInfo->toArray();
                $productItem['year'] = $year;
                $productItem['quarter'] = $quarter;
                $productItem['rank'] = $rank;
                $dataUpsert[] = $productItem;
            }

            if (!empty($dataUpsert)) {
                ProductRankHistory::query()
                    ->upsert(
                        $dataUpsert,
                        ['product_id', 'year', 'quarter'],
                        ['product_id', 'year', 'quarter', 'rank', 'total_unit_sold', 'total_revenue'],
                    );
            }

            $this->info("Rank {$rank}, total: " . count($dataUpsert));
            Log::info('ReportProductRankHistories.handle rank', $dataLog + ['rank' => $rank, 'total' => count($dataUpsert)]);
        }

        $this->info('Report done, total: ' . $total);
        Log::info('ReportProductRankHistories.handle done', $dataLog + ['total' => $total]);

        return 1;
    }
}
