<?php

namespace App\Console\Commands;

use App\Models\Box;
use App\Models\InventoryAddition;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Warehouse;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CalculateBoxCostValue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'box:calculate-cost-value';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate all boxes that do not have cost value';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $warehouses = Warehouse::select(['id', 'name', 'code'])->get();
        foreach ($warehouses as $warehouse) {
            $this->info("------------Calculate Box's cost value for warehouse $warehouse->name------------");
            Box::where([
                'cost_value' => 0,
                'is_deleted' => 0,
                'warehouse_id' => $warehouse->id
            ])->chunkById(100, function ($boxList) use ($warehouse) {
                // Get box ID list
                $boxIds = $boxList->pluck('id');
                $productIds = $boxList->pluck('product_id');
                $additionInfo = InventoryAddition::where('warehouse_id', $warehouse->id)
                    ->whereIn('box_id', $boxIds)
                    ->whereNotNull('po_id')
                    ->get();

                $poIdList = $additionInfo->pluck('po_id');
                $additionPoItem = PurchaseOrderItem::whereIn('po_id', $poIdList)->get();

                $latestPOItems = PurchaseOrderItem::select([
                    'po_id', 'product_id', 'price',
                    DB::raw('MAX(purchase_order.order_date) AS max_po_order_date')
                ])
                    ->whereIn('product_id', $productIds)
                    ->whereNotNull('price')
                    ->join('purchase_order', 'purchase_order.id', '=', 'purchase_order_item.po_id')
                    ->where('purchase_order.warehouse_id', '=', $warehouse->id)
                    ->where('purchase_order.order_status', '!=', PurchaseOrder::CANCELLED_STATUS)
                    ->orderBy('purchase_order.order_date', 'DESC')
                    ->groupBy('product_id')
                    ->get();
                foreach ($boxList as $key => $box) {
                    $this->info("------------Calculating Box's cost value: $box->barcode------------");
                    $boxAdditionPo = $additionInfo->where('box_id', $box->id)->first();
                    $poPrice = 0;

                    if (!empty($boxAdditionPo)) {
                        // Box is created from Addition
                        $poPrice = $additionPoItem->where('po_id', $boxAdditionPo->po_id)->where('product_id', $box->product_id)->first()?->price ?? 0;
                    } else {
                        // Box is created from test count
                        $poPrice = $latestPOItems->where('product_id', $box->product_id)->first()?->price ?? 0;
                    }
                    $costValue = round($poPrice * $box->quantity, 2);
                    $box->cost_value = $costValue;
                    $box->save();
                }
            });
            $this->info("------------Finish calculating Box's cost value for warehouse $warehouse->name------------");
        }
    }
}
