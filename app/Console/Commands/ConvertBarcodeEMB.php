<?php

namespace App\Console\Commands;

use App\Http\Service\BarcodeService;
use App\Repositories\BarcodeEMBRepository;
use App\Repositories\BarcodeRepository;

use Illuminate\Console\Command;

class ConvertBarcodeEMB extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:convert-barcode-emb';

    /**
     * <PERSON>hi người dùng tạo danh sách barcode sản xuất thì tool này sẽ tạo thành file pdf để in
     *
     * @var string
     */
    protected $description = 'convert barcode EMB to pdf version 3';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function convertBarcode()
    {
        $barcodeModel = new BarcodeEMBRepository();

        $job = $barcodeModel->fetchPendingBarcodeEMBPrinted();
        if (!$job) {
            $barcodeModel = null;
            return false;
        }
        sleep(20);
        $barcodeService = new BarcodeService();

        $barcodeService->convertBarcodeV5($job->id);

        $barcodeService = null;

    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        echo $this->signature . "\n";
        while (true) {
            $this->convertBarcode();
            // echo "done \n";
            sleep(10);
        }
    }
}
