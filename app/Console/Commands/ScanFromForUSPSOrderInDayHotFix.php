<?php

namespace App\Console\Commands;

use App\Http\Service\ShipmentService;
use App\Models\Shipment;
use App\Models\ShipmentManifest;
use App\Models\ShipmentManifestTracking;
use App\Models\ShippingCarrier;
use App\Models\Store;
use App\Models\Tag;
use App\Models\User;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ScanFromForUSPSOrderInDayHotfix extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:scan-form-USPS-order-in-a-day-hot-fix {start} {end}';

    /**`
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate scan form for USPS order(exclude Tiktok order) in a day';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $start = $this->argument('start');
            $end = $this->argument('end');
            // <PERSON>y<PERSON><PERSON> đổi sang đối tượng Carbon
            $start = Carbon::parse($start);
            $end = Carbon::parse($end);
            jobEcho($start);
            jobEcho($end);
            $warehouses = $this->groupWarehouseByTimezone();
            foreach ($warehouses as $timezone => $warehouseList) {
                jobEcho('Start scan form for USPS order in day');
                foreach ($warehouseList as $warehouseId) {
                    setTimezoneDefault($timezone);
                    $skipShipmentId = Shipment::GetIdShipmentBeforeDays(30);
                    $shipments = Shipment::with('shippingCarrierService', 'shipmentEasypost', 'shippingIntegrationAccount.shippingIntegration', 'store:id,name', 'saleOrder:id,order_type,tag')
                        ->whereHas('saleOrder', function ($q) {
                            $q->whereIn('order_type', [SaleOrder::ORDER_TYPE_NORMAL, SaleOrder::ORDER_TYPE_PRETREATED, SaleOrder::ORDER_TYPE_BLANK, SaleOrder::ORDER_TYPE_LICENSE_ORDER]);
                        })
                        ->whereHas('shipmentLabelPrinted', function ($q) {
                            $q->havingRaw('min(printed_date) between "' . now()::yesterday()->setHour(12)->setMinute(45)->setSecond(0) . '" and "' . now()->setHour(12)->setMinute(44)->setSecond(59) . '"');
                        })
                        ->whereDoesntHave('shipmentManifestTracking', function ($q) {
                            $q->where('created_at', '>=', now()->subDays(30)->startOfDay()->toDateTimeString());
                        })
                        ->where('id', '>', $skipShipmentId)
                        ->where('created_at', '>=', $end->copy()->subDays(2)->startOfDay()->toDateTimeString())
                        ->whereNull('refund_status')
                        ->where('carrier_code', ShippingCarrier::USPS_ECOMMERCE_CODE)
                        ->where('provider', Shipment::PROVIDER_EASYPOST)
                        ->whereNotNull('account_shipping_easypost')
                        ->where('warehouse_id', $warehouseId)
                        ->get();
                    $tracking = [];
                    $manifestId = [];
                    $apikeyEasypost = [];
                    foreach ($shipments as $key => $shipment) {
                        $tag = explode(',', $shipment->saleOrder->tag);
                        if ($shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_STORE && $shipment->store_id == Store::PRINTIFY_API_ID && !in_array(Tag::LABEL_TAG_ID, $tag ?? [])) {
                            if (!isset($manifestId[$shipment->account_shipping_easypost])) {
                                $dataShipmentManifest = [
                                    'warehouse_id' => $shipment->warehouse_id,
                                    'integration_account_id' => $shipment->shippingIntegrationAccount?->id,
                                    'employee_create_id' => User::SYSTEM,
                                    'barcode_box' => str_replace(' ', '', str_replace('API', '', strtoupper($shipment->store?->name))) . '-ALL' . $end->copy()->format('mdY')
                                ];
                                $shipmentManifest = ShipmentManifest::create($dataShipmentManifest)->id;
                                $manifestId[$shipment->account_shipping_easypost] = $shipmentManifest;
                                $apikeyEasypost[$shipment->account_shipping_easypost] = $shipment->shippingIntegrationAccount?->shippingIntegration?->api_key;
                            }
                            $tracking[$shipment->account_shipping_easypost][] = [
                                'tracking_number' => $shipment->tracking_number,
                                'ship_id_partner' => $shipment->shipmentEasypost?->easypost_id,
                                'service_code_id' => $shipment->shippingCarrierService?->id,
                                'manifest_id' => $manifestId[$shipment->account_shipping_easypost]
                            ];
                        }
                        if ($shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD && !in_array(Tag::LABEL_TAG_ID, $tag ?? [])) {
                            if (!isset($tracking[$shipment->account_shipping_easypost])) {
                                $dataShipmentManifest = [
                                    'warehouse_id' => $shipment->warehouse_id,
                                    'integration_account_id' => $shipment->shippingIntegrationAccount?->id,
                                    'employee_create_id' => User::SYSTEM,
                                    'barcode_box' => strtoupper(Shipment::SCANFORM_SWIFTPOD_NAME) . $end->copy()->format('mdY'),
                                ];
                                $shipmentManifest = ShipmentManifest::create($dataShipmentManifest)->id;
                                $manifestId[$shipment->account_shipping_easypost] = $shipmentManifest;
                                $apikeyEasypost[$shipment->account_shipping_easypost] = $shipment->shippingIntegrationAccount?->shippingIntegration?->api_key;
                            }
                            $tracking[$shipment->account_shipping_easypost][] = [
                                'tracking_number' => $shipment->tracking_number,
                                'ship_id_partner' => $shipment->shipmentEasypost?->easypost_id,
                                'service_code_id' => $shipment->shippingCarrierService?->id,
                                'manifest_id' => $manifestId[$shipment->account_shipping_easypost]
                            ];
                        }
                    }
                    foreach ($tracking as $keyAccount => $trackingNumber) {
                        $dataShippingEasypost = [];
                        $dataShipmentManifestTracking = [];
                        $apikey = $apikeyEasypost[$keyAccount];

                        foreach ($trackingNumber as $item) {
                            $dataShippingEasypost[] = [
                                'id' => $item['ship_id_partner']
                            ];
                            $dataShipmentManifestTracking[] = [
                                'service_code_id' => $item['service_code_id'],
                                'employee_scan_id' => User::SYSTEM,
                                'tracking_number' => $item['tracking_number'],
                                'ship_id_partner' => $item['ship_id_partner'],
                                'manifest_id' => $item['manifest_id'],
                                'created_at' => date('Y-m-d H:i:s'),
                                'updated_at' => date('Y-m-d H:i:s')
                            ];
                        }
                        $shipmentService = app()->make(ShipmentService::class);
                        $dataBatch = $shipmentService->makeScanForm($dataShippingEasypost, $apikey);
                        $dataUpdateManifest = [
                            'batch_id_easypost' => $dataBatch->id,
                            'status' => ShipmentManifest::STATUS_GENERATING,
                            'state_batch' => $dataBatch?->state,
                        ];
                        ShipmentManifest::where('id', $manifestId[$keyAccount])
                            ->update($dataUpdateManifest);
                        ShipmentManifestTracking::insert($dataShipmentManifestTracking);
                    }
                }
            }
            jobEcho('End scan form for USPS order in day');
        } catch (\Exception $exception) {
            jobEcho('Error: ' . $exception->getMessage());
        }
    }

    protected function groupWarehouseByTimezone()
    {
        $warehouses = Warehouse::query()->select('id', 'time_zone')->get();
        $data = [];
        foreach ($warehouses as $warehouse) {
            $data[$warehouse->time_zone][] = $warehouse->id;
        }

        return $data;
    }
}
