<?php

namespace App\Console\Commands;

use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AutoUpdateOrderPrinted extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:auto-update-order-printed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto update order printed';



    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $id = SaleOrder::getIDBeforeDays(15);
        $this->info('[START] Auto update order printed');

        $ordersToUpdate =  DB::table('sale_order_item_barcode as b')
            ->join('sale_order as o', function ($join) use ($id) {
                $join->on('o.id', '=', 'b.order_id')
                    ->where('o.barcode_printed_status', '=', 0)
                    ->where('o.id', '>', $id);
            })
            ->groupBy('b.order_id')
            ->havingRaw('SUM(b.barcode_printed_id > 0) = b.order_quantity')
            ->select('b.order_id', 'b.order_quantity', 'o.order_date')
            ->get();

        // Thực hiện cập nhật từng dòng
        foreach ($ordersToUpdate as $order) {
            DB::table('sale_order')
                ->where('id', $order->order_id)
                ->update(['barcode_printed_status' => 1]);

            $this->info('Updated order: ' . $order->order_id);
        }

        $this->info('[END] Auto update order printed');
    }
}
