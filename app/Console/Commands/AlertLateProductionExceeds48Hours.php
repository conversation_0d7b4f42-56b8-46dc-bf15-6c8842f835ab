<?php

namespace App\Console\Commands;

use App\Http\Service\AlertService;
use App\Models\SaleOrder;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AlertLateProductionExceeds48Hours extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'alert:late-production-exceeds-48-hours';

    const SLEEP = 3600;

    const TIME_ZONE_PST = 'America/Los_Angeles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Alert new order exceeds 48 hours';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Start alert late production exceeds 48 hours');
        $currentHour = Carbon::now(self::TIME_ZONE_PST)->hour;
        if ($currentHour != 0) {
            $this->info('not send notify') . PHP_EOL;
            DB::disconnect();
            sleep(self::SLEEP);

            return false;
        }
        SaleOrder::with(['store:id,code', 'items.product.productStyle', 'wareHouse'])
            ->select('id', 'order_number', 'store_id', 'created_at', 'warehouse_id')
            ->where('order_status', SaleOrder::NEW_ORDER)
            ->whereRaw('TIMESTAMPDIFF(HOUR, sale_order.created_at, NOW()) > 48')
            ->where('is_test', SaleOrder::NOT_TEST)
            ->orderBy('created_at', 'ASC')
            ->chunk(15, function ($saleOrders) {
                $orderTexts = '';
                foreach ($saleOrders as $saleOrder) {
                    $storeCode = $saleOrder->store->code;
                    $orderNumber = $saleOrder->order_number;
                    $warehouseName = $saleOrder?->wareHouse?->name;
                    // Reset $orderTexts for each sale order
                    $orderTexts .= "*Warehouse*: $warehouseName - *Store*: $storeCode - *Order number*: $orderNumber - *SKU*: ";
                    $textItem = '';
                    foreach ($saleOrder->items as $item) {
                        $productSKU = $item->product_sku;
                        $productType = optional($item->product)->productStyle->type ?? 'Unknown'; // If product or productStyle is null, use 'Unknown'
                        $textItem .= "$productSKU ($productType) / ";
                    }
                    // Remove the trailing slash and space from $textItem
                    $textItem = Str::replaceLast(' / ', "\n", rtrim($textItem, ' / '));
                    $orderTexts .= $textItem . "\n";
                }
                if ($orderTexts) {
                    $alertService = new AlertService();
                    $alertService->alertCheckSaleOrderExceedHours($orderTexts);
                    sleep(2);
                }
            });
        DB::disconnect();
        sleep(self::SLEEP);

        return true;
    }
}
