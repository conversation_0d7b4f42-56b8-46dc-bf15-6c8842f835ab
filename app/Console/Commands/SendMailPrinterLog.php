<?php

namespace App\Console\Commands;

use App\Repositories\PrintingRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;


class SendMailPrinterLog extends Command
{
    const TIME_ZONE_PST = "America/Los_Angeles";

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sendMail:PrinterLog';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send mail printer logs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        while (true) {
            $this->sendMailPrinter();
            $this->info('Waiting...');
            sleep(3600);
        }
    }


    private function sendMailPrinter()
    {
        $timeZonePST = self::TIME_ZONE_PST;
        $current = Carbon::now($timeZonePST);
        $isSunday = $current->isSunday();
        if (!$isSunday || $current->hour <> 21) {
            $this->info("current is {$current->toDateTimeString()}") . PHP_EOL;
            return 0;
        }
        $this->info("Begin make send mail") . PHP_EOL;
        $printingRepo = new PrintingRepository();
        $printingRepo->sendMailLogPrinter();
        $this->info("End");
    }

}
