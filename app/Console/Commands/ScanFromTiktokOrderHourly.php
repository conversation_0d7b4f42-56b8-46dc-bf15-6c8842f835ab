<?php

namespace App\Console\Commands;

use App\Http\Service\ShipmentService;
use App\Models\SaleOrder;
use App\Models\Shipment;
use App\Models\ShipmentManifest;
use App\Models\ShipmentManifestTracking;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierService;
use App\Models\User;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ScanFromTiktokOrderHourly extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:scan-form-tiktok-order-hourly';

    /**`
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $dateTime = Carbon::now()->format('mdY-H');
            $warehouses = Warehouse::all();
            foreach ($warehouses as $warehouse) {
                $shipments = Shipment::with('shippingCarrierService', 'shipmentEasypost', 'shippingIntegrationAccount.shippingIntegration', 'store:id,name')
                    ->whereHas('saleOrder', function ($q) {
                        $q->where('order_type', SaleOrder::ORDER_TYPE_TIKTOK_ORDER);
                    })
                    ->whereDoesntHave('shipmentManifestTracking', function ($q) {
                        $q->where('created_at', '>=', now()->subDays(30)->startOfDay()->toDateTimeString());
                    })
                    ->where('created_at', '>=', now()->subDays(2)->startOfDay()->toDateTimeString())
                    ->whereNull('refund_status')
                    ->where('carrier_code', ShippingCarrier::USPS_ECOMMERCE_CODE)
                    ->where('provider', Shipment::PROVIDER_EASYPOST)
                    ->whereNotIn('service_code', [ShippingCarrierService::SERVICE_FIRST_CLASS, ShippingCarrierService::SERVICE_FIRST_CLASS_INTERNATIONAL])
                    ->whereNotNull('account_shipping_easypost')
                    ->where('warehouse_id', $warehouse->id)
                    ->get();
                $tracking = [];
                $manifestId = [];
                $apikeyEasypost = [];
                foreach ($shipments as $key => $shipment) {
                    if ($shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD_STORE || $shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_STORE) {
                        if (!isset($manifestId[$shipment->account_shipping_easypost])) {
                            $dataShipmentManifest = [
                                'warehouse_id' => $shipment->warehouse_id,
                                'integration_account_id' => $shipment->shippingIntegrationAccount?->id,
                                'employee_create_id' => User::SYSTEM,
                                'barcode_box' => str_replace(' ', '', str_replace('API', '', strtoupper($shipment->store?->name))) . '-TIKTOK-' . $dateTime
                            ];
                            $shipmentManifest = ShipmentManifest::create($dataShipmentManifest)->id;
                            $manifestId[$shipment->account_shipping_easypost] = $shipmentManifest;
                            $apikeyEasypost[$shipment->account_shipping_easypost] = $shipment->shippingIntegrationAccount?->shippingIntegration?->api_key;
                        }
                        $tracking[$shipment->account_shipping_easypost][] = [
                            'tracking_number' => $shipment->tracking_number,
                            'ship_id_partner' => $shipment->shipmentEasypost?->easypost_id,
                            'service_code_id' => $shipment->shippingCarrierService?->id,
                            'manifest_id' => $manifestId[$shipment->account_shipping_easypost]
                        ];
                    } if ($shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD) {
                        if (!isset($tracking[$shipment->account_shipping_easypost])) {
                            $dataShipmentManifest = [
                                'warehouse_id' => $shipment->warehouse_id,
                                'integration_account_id' => $shipment->shippingIntegrationAccount?->id,
                                'employee_create_id' => User::SYSTEM,
                                'barcode_box' => Shipment::SCANFORM_TIKTOK_NAME . '-' . $dateTime,
                            ];
                            $shipmentManifest = ShipmentManifest::create($dataShipmentManifest)->id;
                            $manifestId[$shipment->account_shipping_easypost] = $shipmentManifest;
                            $apikeyEasypost[$shipment->account_shipping_easypost] = $shipment->shippingIntegrationAccount?->shippingIntegration?->api_key;
                        }
                        $tracking[$shipment->account_shipping_easypost][] = [
                            'tracking_number' => $shipment->tracking_number,
                            'ship_id_partner' => $shipment->shipmentEasypost?->easypost_id,
                            'service_code_id' => $shipment->shippingCarrierService?->id,
                            'manifest_id' => $manifestId[$shipment->account_shipping_easypost]
                        ];
                    }
                }
                foreach ($tracking as $keyAccount => $trackingNumber) {
                    $dataShippingEasypost = [];
                    $dataShipmentManifestTracking = [];
                    $apikey = $apikeyEasypost[$keyAccount];

                    foreach ($trackingNumber as $item) {
                        $dataShippingEasypost[] = [
                            'id' => $item['ship_id_partner']
                        ];
                        $dataShipmentManifestTracking[] = [
                            'service_code_id' => $item['service_code_id'],
                            'employee_scan_id' => User::SYSTEM,
                            'tracking_number' => $item['tracking_number'],
                            'ship_id_partner' => $item['ship_id_partner'],
                            'manifest_id' => $item['manifest_id'],
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                    }
                    $shipmentService = app()->make(ShipmentService::class);
                    $dataBatch = $shipmentService->makeScanForm($dataShippingEasypost, $apikey);
                    $dataUpdateManifest = [
                        'batch_id_easypost' => $dataBatch->id,
                        'status' => ShipmentManifest::STATUS_GENERATING,
                        'state_batch' => $dataBatch?->state,
                    ];
                    ShipmentManifest::where('id', $manifestId[$keyAccount])
                        ->update($dataUpdateManifest);
                    ShipmentManifestTracking::insert($dataShipmentManifestTracking);
                }
            }
            jobEcho('End scan form for USPS order in day');
        } catch (\Exception $exception) {
            Log::channel('scan_form_tiktok_order')->error($exception->getMessage());
        }
    }
}
