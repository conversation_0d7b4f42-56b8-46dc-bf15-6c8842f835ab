<?php

namespace App\Console\Commands;

use App\Models\SaleOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateOrderPrintedStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update-order-printed-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';



    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $id = SaleOrder::getIDBeforeDays(15);
        $ordersToUpdate = \DB::table('sale_order as o')
            ->where('o.order_printed_status', 0)
            ->where('o.id', '>', $id)
            ->whereExists(function ($query) {
                $query->select(\DB::raw(1))
                    ->from('sale_order_item_barcode as b')
                    ->whereColumn('b.order_id', 'o.id')
                    ->where('b.is_deleted', 0)
                    ->groupBy('b.order_id')
                    ->havingRaw('SUM(b.printed_at IS NOT NULL) = o.order_quantity');
            })
            ->select('o.id as order_id', 'o.order_number', 'o.order_quantity', 'o.order_date')
            ->get();

        // Thực hiện cập nhật từng dòng
        foreach ($ordersToUpdate as $order) {
            DB::table('sale_order')
                ->where('id', $order->order_id)
                ->update(['order_printed_status' => 1]);

            $this->info('Updated order: ' . $order->order_id);
        }

        $this->info('[END] Auto update order printed');
    }
}
