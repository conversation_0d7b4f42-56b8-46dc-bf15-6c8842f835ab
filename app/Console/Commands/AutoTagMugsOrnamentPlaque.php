<?php

namespace App\Console\Commands;

use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\Store;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AutoTagMugsOrnamentPlaque extends Command
{
    protected $signature = 'command:auto-tag-mugs-ornament-plaque';

    protected $description = 'Auto tag mugs and ornament plaque';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('[START] Auto tag mugs and ornament plaque');
        $this->tagMugs();
        sleep(1);
        $this->tagOrnament();
        sleep(1);
        $this->tagPlaque();
        sleep(1);
        $this->tagSticker();
        sleep(1);
        $this->tagForStore([Store::STORE_GOOD_MVMT, Store::STORE_REDBUBBLE,
            Store::STORE_BONF, Store::STORE_BNFR_API, Store::STORE_DLS]);
        sleep(1);
        $this->tagPoster();
        sleep(1);
        $this->tagTumbler();
        //  $this->tagPrivatePackageForDLSMugOrder(Store::STORE_DLS);
        $this->info('[END] Auto tag mugs and ornament plaque');
    }

    public function tagOrnament()
    {
        //get all style sku plaque
        $listOrnamentSku = ProductStyle::where('type', ProductType::ORNAMENT)->pluck('sku')->toArray();
        if (empty($listOrnamentSku)) {
            return true;
        }
        $listOrnamentSkuStr = "'" . implode("','", $listOrnamentSku) . "'";
        $sql = "UPDATE sale_order_item AS soi
        INNER JOIN sale_order AS so ON so.id = soi.order_id
        SET so.tag = IFNULL(CONCAT_WS(',', so.tag, 163), '163')
        WHERE soi.product_style_sku IN ($listOrnamentSkuStr)
        AND (so.tag IS NULL OR NOT FIND_IN_SET(163, so.tag))
        AND so.created_at > NOW() - INTERVAL 1 HOUR AND so.created_at < NOW() - INTERVAL 5 MINUTE";
        DB::statement($sql);
    }

    public function tagPlaque()
    {
        //get all style sku plaque
        $listPlaqueSku = ProductStyle::where('type', ProductType::PLAQUE)->pluck('sku')->toArray();
        if (empty($listPlaqueSku)) {
            return true;
        }
        $listPlaqueSkuStr = "'" . implode("','", $listPlaqueSku) . "'";
        $sql = "UPDATE sale_order_item AS soi
        INNER JOIN sale_order AS so ON so.id = soi.order_id
        SET so.tag = IFNULL(CONCAT_WS(',', so.tag, 167), '167')
        WHERE soi.product_style_sku IN ($listPlaqueSkuStr)
        AND (so.tag IS NULL OR NOT FIND_IN_SET(167, so.tag))
        AND so.created_at > NOW() - INTERVAL 1 HOUR AND so.created_at < NOW() - INTERVAL 5 MINUTE";

        // Execute the query
        DB::statement($sql);
    }

    public function tagMugs()
    {
        //get all style sku mugs
        $listMugsSku = ProductStyle::where('type', ProductType::MUGS)->pluck('sku')->toArray();
        if (empty($listMugsSku)) {
            return true;
        }
        $listMugsSkuStr = "'" . implode("','", $listMugsSku) . "'";
        $sql = "UPDATE sale_order_item AS soi
        INNER JOIN sale_order AS so ON so.id = soi.order_id
        SET so.tag = IFNULL(CONCAT_WS(',', so.tag, 162), '162')
        WHERE soi.product_style_sku IN ($listMugsSkuStr)
        AND (so.tag IS NULL OR NOT FIND_IN_SET(162, so.tag))
        AND so.created_at > NOW() - INTERVAL 1 HOUR AND so.created_at < NOW() - INTERVAL 5 MINUTE";
        // Execute the query
        DB::statement($sql);
    }

    public function tagSticker()
    {
        //get all style sku Sticker
        $listStickerSku = ProductStyle::where('type', ProductType::STICKER)->pluck('sku')->toArray();
        if (empty($listStickerSku)) {
            return true;
        }
        $lisStickerSkuStr = "'" . implode("','", $listStickerSku) . "'";

        $sql = "UPDATE sale_order_item AS soi
        INNER JOIN sale_order AS so ON so.id = soi.order_id
        SET so.tag = IFNULL(CONCAT_WS(',', so.tag, 178), '178')
        WHERE soi.product_style_sku IN ($lisStickerSkuStr)
        AND (so.tag IS NULL OR NOT FIND_IN_SET(178, so.tag))
        AND so.created_at > NOW() - INTERVAL 1 HOUR AND so.created_at < NOW() - INTERVAL 5 MINUTE";
        // Execute the query
        DB::statement($sql);
    }

    public function tagForStore($storeIds)
    {
        $storeIdsString = implode(',', $storeIds);
        $sql = "UPDATE sale_order AS so
        SET so.tag = IFNULL(CONCAT_WS(',', so.tag, 190), '190')
        WHERE so.store_id IN ($storeIdsString)
        AND (so.tag IS NULL OR NOT FIND_IN_SET(190, so.tag))
        AND so.created_at > NOW() - INTERVAL 1 HOUR AND so.created_at < NOW() - INTERVAL 5 MINUTE";
        // Execute the query
        DB::statement($sql);
    }

    public function tagPoster()
    {
        //get all style sku Sticker
        $listPosterSku = ProductStyle::where('type', ProductType::POSTER)->pluck('sku')->toArray();
        if (empty($listPosterSku)) {
            return true;
        }
        $lisPosterSkuStr = "'" . implode("','", $listPosterSku) . "'";

        $sql = "UPDATE sale_order_item AS soi
        INNER JOIN sale_order AS so ON so.id = soi.order_id
        SET so.tag = IFNULL(CONCAT_WS(',', so.tag, 205), '205')
        WHERE soi.product_style_sku IN ($lisPosterSkuStr)
        AND (so.tag IS NULL OR NOT FIND_IN_SET(205, so.tag))
        AND so.created_at > NOW() - INTERVAL 1 HOUR AND so.created_at < NOW() - INTERVAL 5 MINUTE";
        // Execute the query
        DB::statement($sql);
    }

    public function tagTumbler()
    {
        //get all style sku Tumbler
        $listTumblerSku = ProductStyle::where('type', ProductType::TUMBLER)->pluck('sku')->toArray();
        if (empty($listTumblerSku)) {
            return true;
        }
        $listTumblerSkuStr = "'" . implode("','", $listTumblerSku) . "'";
        $sql = "UPDATE sale_order_item AS soi
        INNER JOIN sale_order AS so ON so.id = soi.order_id
        SET so.tag = IFNULL(CONCAT_WS(',', so.tag, 147), '147')
        WHERE soi.product_style_sku IN ($listTumblerSkuStr)
        AND (so.tag IS NULL OR NOT FIND_IN_SET(147, so.tag))
        AND so.created_at > NOW() - INTERVAL 1 HOUR AND so.created_at < NOW() - INTERVAL 5 MINUTE";
        // Execute the query
        DB::statement($sql);
    }

    /*
      câu này giống bên trên
      public function tagPrivatePackageForDLSMugOrder($storeId)
        {
            $sql = "UPDATE sale_order AS so
                INNER JOIN (
                    SELECT DISTINCT so.id
                    FROM sale_order AS so
                    INNER JOIN sale_order_item AS soi ON so.id = soi.order_id
                    INNER JOIN product AS p ON soi.product_id = p.id
                    INNER JOIN product_style AS ps ON p.style = ps.name
                    INNER JOIN product_type AS pt ON ps.type = pt.name
                    WHERE so.store_id = $storeId
                      AND pt.name = 'MUGS'
                      AND so.order_date > DATE_SUB(NOW(), INTERVAL 2 DAY) LIMIT 300
                ) AS distinct_orders ON so.id = distinct_orders.id
                SET so.tag = IFNULL(CONCAT_WS(',', so.tag, 190), '190')
            WHERE (so.tag IS NULL OR NOT FIND_IN_SET(190, so.tag))";

            DB::statement($sql);
        }*/
}
