<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use App\Models\InvoiceSaleOrder;
use App\Models\InvoiceSaleOrderError;
use App\Models\SaleOrder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RestoreInvoiceError extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'restore:invoice-error';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Restore invoice error';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('[START] Restore invoice error');

        $invoiceSaleOrderErrors = InvoiceSaleOrderError::select('invoice_id')->groupBy('invoice_id')->get()->toArray();

        foreach ($invoiceSaleOrderErrors as $item) {
            $invoiceId = $item['invoice_id'];
            //  print_r($invoiceId);
            try {
                $saleOrderIds = InvoiceSaleOrderError::where('invoice_id', $invoiceId)->select('sale_order_id')->get()->pluck('sale_order_id')->toArray();

                DB::beginTransaction();
                $saleOrderIdsCalculated = SaleOrder::whereIn('id', $saleOrderIds)
                    ->whereNotNull('calculated_at')->select('id')->get()->pluck('id')->toArray();

                //print_r( $saleOrderIdsCalculated);

                if (count($saleOrderIdsCalculated) >= 2000) {

                    $saleOrderInserted = InvoiceSaleOrder::where('invoice_id', $invoiceId)->whereIn('sale_order_id', $saleOrderIds)->select('sale_order_id')->get();
                    $saleOrderInserted = $saleOrderInserted->pluck('sale_order_id')->toArray();

                    $saleOrderIdsCalculatedNotInserted = array_diff($saleOrderIdsCalculated, $saleOrderInserted);

                    //  print_r($saleOrderIdsCalculatedNotInserted);

                    if (count($saleOrderIdsCalculatedNotInserted) > 0) {
                        $invoice = Invoice::findOrFail($invoiceId);
                        //   print_r($invoiceId);

                        $chunkSize = 1000; // Define the chunk size limit
                        $saleOrderChunks = array_chunk($saleOrderIdsCalculatedNotInserted, $chunkSize);

                        foreach ($saleOrderChunks as $chunk) {
                        //    print_r($chunk);
                            $invoice->saleOrders()->attach($chunk);
                            InvoiceSaleOrderError::where('invoice_id', $invoiceId)->whereIn('sale_order_id', $chunk)->delete();
                        }

                       // $invoice->saleOrders()->attach($saleOrderIdsCalculatedNotInserted);
                    }
                   // print_r($saleOrderIdsCalculatedNotInserted);
                    print_r(collect($saleOrderIdsCalculatedNotInserted)->count());
                    echo ' --- ';
                    echo $invoiceId . "\n";
                   // DB::rollBack();
                   // break;
                //  break;
                } else {
                    echo "No sale order calculated\n";
                }
                DB::commit();

                if (count($saleOrderIdsCalculated) > 0) {
                //    break;
                }
            } catch (\Throwable $th) {
                DB::rollBack();
                echo $th->getMessage();
                throw $th;
            }
        }

        $this->info('[END] Restore invoice error successfully');
    }
}
