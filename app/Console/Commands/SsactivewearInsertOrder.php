<?php

namespace App\Console\Commands;

use App\Http\Service\AlertService;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderSSactivewear;
use App\Repositories\ProductQuantityRepository;
use App\Repositories\ProductRepository;
use App\Repositories\WarehouseRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class SsactivewearInsertOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ssactivewear:insert-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $dayUpdateService = '2022-12-27';
        $data = DB::table('purchase_order_ssactivewear')
            ->whereNull('status_insert_order')
            ->whereDate('created_at', '>=', $dayUpdateService)
            //->where('status', 0)
            // ->limit(10)
            ->get()
            ->toArray();
        if (empty($data)) {
            $this->info('data order is empty');
            exit;
        }
        $warehouseZip = WarehouseRepository::getListWarehouseWithField('zip');
        foreach ($data as $item) {
            $ssacOrder = json_decode($item->data);
            $result = $this->handleOrder($ssacOrder, $warehouseZip);
            if (!$result['status']) {
                $this->updateWriteLog($ssacOrder->invoiceNumber, PurchaseOrderSSactivewear::STATUS_INSERT_ORDER_ERROR, $result['message']);
                $alertService = new AlertService();
                $alertService->alertPullPurchaseOrder($result['message']);

                continue;
            }
            if (!$item->status) {
                //Todo : gui notify canh bao
                $message = "INSERT PURCHASE ORDER SUCCESSFULLY !!! but tracking number MISSING, please check in swiftpod app \n vendor : Ssactivewear  | po number: $ssacOrder->poNumber | invoice number : $ssacOrder->invoiceNumber | order number : $ssacOrder->orderNumber";
                $alertService = new AlertService();
                $alertService->alertPullPurchaseOrder($message);
            }
            $this->updateWriteLog($ssacOrder->invoiceNumber, PurchaseOrderSSactivewear::STATUS_INSERT_ORDER_SUCCESS);
            $this->info("Insert invoice number :  $ssacOrder->invoiceNumber  done");
            sleep(2);
        }
        $this->info('Insert all orders done');
    }

    private function handleOrder($ssacOrder, $warehouseZip)
    {
        try {
            DB::beginTransaction();
            if ($ssacOrder->orderStatus == 'Credit') {
                //Todo : gui notify canh bao
                $message = "INSERT PURCHASE ORDER FAILED !!! because Order status is CREDIT \n vendor : Ssactivewear \npo number: $ssacOrder->poNumber | invoice number : $ssacOrder->invoiceNumber | order number : $ssacOrder->orderNumber";
                $this->info('INSERT PURCHASE ORDER FAILED !!! because Order status is CREDIT');
                DB::commit();

                return [
                    'status' => false,
                    'message' => $message,
                ];
            }
            if (strtolower($ssacOrder->orderStatus) == PurchaseOrder::CANCELLED_STATUS) {
                $orderExisted = DB::table('purchase_order')
                    ->where('invoice_number', $ssacOrder->invoiceNumber)
                    ->first();
                if (!empty($orderExisted)) {
                    if (!in_array($orderExisted->order_status, [PurchaseOrder::CANCELLED_STATUS, PurchaseOrder::COMPLETED_STATUS])) {
                        PurchaseOrder::query()
                            ->where('id', $orderExisted->id)
                            ->update([
                                'order_status' => PurchaseOrder::CANCELLED_STATUS,
                                'status_updated_at' => date('Y-m-d H:i:s'),
                            ]);
                        ProductQuantityRepository::updateIncomingByPurchaseOrderInWarehouse($orderExisted->id, $orderExisted->warehouse_id);
                        $message = "UPDATE PO FROM $orderExisted->order_status to Cancelled !!! \n vendor : Ssactivewear \npo number: $ssacOrder->poNumber | invoice number : $ssacOrder->invoiceNumber | order number : $ssacOrder->orderNumber";
                        $this->info('UPDATE order status to Cancelled');
                        DB::commit();

                        return [
                            'status' => false,
                            'message' => $message,
                        ];
                    }
                }
                $message = "INSERT PURCHASE ORDER FAILED !!! because Order status is Cancelled \n vendor : Ssactivewear \npo number: $ssacOrder->poNumber | invoice number : $ssacOrder->invoiceNumber | order number : $ssacOrder->orderNumber";
                $this->info('INSERT PURCHASE ORDER FAILED !!! because Order status is Cancelled');
                DB::commit();

                return [
                    'status' => false,
                    'message' => $message,
                ];
            }
            if (empty($ssacOrder->lines)) {
                $message = "INSERT PURCHASE ORDER FAILED !!! because Order items is EMPTY \n vendor : Ssactivewear | po number: $ssacOrder->poNumber | invoice number : $ssacOrder->invoiceNumber | order number : $ssacOrder->orderNumber";
                $this->info('INSERT PURCHASE ORDER FAILED !!! because Order items is EMPTY');
                DB::commit();

                return [
                    'status' => false,
                    'message' => $message,
                ];
            }
            if (empty($ssacOrder->boxes)) {
                $message = "INSERT PURCHASE ORDER FAILED !!! because Order boxes is EMPTY \n vendor : Ssactivewear | po number: $ssacOrder->poNumber | invoice number : $ssacOrder->invoiceNumber | order number : $ssacOrder->orderNumber";
                $this->info('INSERT PURCHASE ORDER FAILED !!! because Order boxes is EMPTY');
                DB::commit();

                return [
                    'status' => false,
                    'message' => $message,
                ];
            }
            $arrGtin = array_unique(array_column($ssacOrder->lines, 'gtin'));
            $productsActive = ProductRepository::fetchProductsGroupByKey($arrGtin, 'gtin');
            $gtinMissStr = '';
            if (empty($productsActive)) {
                $gtinMissStr = implode('  ', $arrGtin);
                foreach ($arrGtin as $gtinMiss) {
                    DB::table('import_order_gtin_miss_log')->insert([
                        'gtin' => $gtinMiss,
                        'order_number' => $ssacOrder->orderNumber,
                        'po_number' => $ssacOrder->poNumber,
                        'vendor_id' => 3,
                        'invoice_number' => $ssacOrder->invoiceNumber,
                        'created_at' => date('Y-m-d H:i:s'),
                    ]);
                }
            } else {
                foreach ($arrGtin as $gtin) {
                    if (!isset($productsActive[$gtin])) {
                        $gtinMissStr .= ' ' . $gtin;
                        DB::table('import_order_gtin_miss_log')->insert([
                            'gtin' => $gtin,
                            'order_number' => $ssacOrder->orderNumber,
                            'po_number' => $ssacOrder->poNumber,
                            'vendor_id' => 3,
                            'invoice_number' => $ssacOrder->invoiceNumber,
                            'created_at' => date('Y-m-d H:i:s'),
                        ]);
                    }
                }
            }
            if ($gtinMissStr) {
                $message = "INSERT PURCHASE ORDER FAILED !!! because PRODUCT NOT FOUND \n
                vendor : Ssactivewear | po number: $ssacOrder->poNumber | invoice number : $ssacOrder->invoiceNumber | order number : $ssacOrder->orderNumber |
                GTIN not found : $gtinMissStr";
                $this->info('INSERT PURCHASE ORDER FAILED !!! because PRODUCT NOT FOUND');
                DB::commit();

                return [
                    'status' => false,
                    'message' => $message,
                ];
            }
            $zip = $ssacOrder->shippingAddress->zip;
            if ($zip == 22151) {
                //Virginia update them zip = 22151
                $warehouse_id = 19;
            } elseif ($zip == 75041) {
                //TX update them zip = 75041
                $warehouse_id = 2;
            } else {
                $warehouse_id = in_array($zip, $warehouseZip) ? array_search($zip, $warehouseZip) : 1; /// Fix cung San Joe la 1;
            }
            $orderStatus = strtolower($ssacOrder->orderStatus) == 'in progress' ? PurchaseOrder::NEW_ORDER_STATUS : $ssacOrder->orderStatus;
            $trackingNumber = !empty($ssacOrder->trackingNumber) ? $ssacOrder->trackingNumber : null;
            $orderId = DB::table('purchase_order')->insertGetId(
                [
                    'order_number' => $ssacOrder->orderNumber,
                    'po_number' => $ssacOrder->poNumber,
                    'invoice_number' => $ssacOrder->invoiceNumber,
                    'order_date' => Carbon::parse($ssacOrder->orderDate)->format('Y-m-d H:i:s'),
                    'delivery_date' => !empty($ssacOrder->shipDate) ? Carbon::parse($ssacOrder->shipDate)->format('Y-m-d H:i:s') : null,
                    'order_status' => $orderStatus,
                    'tracking_number' => $trackingNumber,
                    'warehouse_id' => $warehouse_id,
                    'vendor_id' => 3, /// SSActivewear có id = 3
                    'user_id' => 22, // username : API
                ]);

            foreach ($ssacOrder->lines as $line) {
                $product = $productsActive[$line->gtin];
                $quantity = $line->qtyOrdered > 0 ? $line->qtyOrdered : 0;
                DB::table('purchase_order_item')->insert([
                    'po_id' => $orderId,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'price' => $line->price,
                    'total' => ($line->price) * $quantity,
                ]);
                ProductQuantityRepository::updateInComeQuantity($warehouse_id, $product->id, $quantity);
            }
            ///Todo : insert boxes, boxes item
            foreach ($ssacOrder->boxes as $box) {
                $trackingBoxNumber = !empty($box->trackingNumber) ? $box->trackingNumber : null;
                $trackingBoxNumber = count($ssacOrder->boxes) == 1 && empty($trackingBoxNumber) ? $trackingNumber : $trackingBoxNumber;
                $orderBoxId = DB::table('purchase_order_box')->insertGetId([
                    'po_id' => $orderId,
                    'box_number' => $box->boxNumber,
                    'invoice_number' => $ssacOrder->invoiceNumber,
                    'tracking_number' => $trackingBoxNumber,
                    'created_at' => date('Y-m-d H:i:s'),
                ]);
                foreach ($box->lines as $itemLine) {
                    $quantity = $itemLine->qtyOrdered > 0 ? $itemLine->qtyOrdered : 0;
                    $product = $productsActive[$itemLine->gtin];
                    DB::table('purchase_order_box_item')->insert([
                        'po_id' => $orderId,
                        'po_box_id' => $orderBoxId,
                        'sku' => !empty($product->sku) ? $product->sku : null,
                        'product_id' => !empty($product->id) ? $product->id : null,
                        'external_sku' => $itemLine->sku,
                        'gtin' => $itemLine->gtin,
                        'quantity' => $quantity,
                        'quantity_api' => $itemLine->qtyOrdered,
                        'created_at' => date('Y-m-d H:i:s'),
                    ]);
                }
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return [
                'status' => false,
                'message' => $exception->getMessage(),
            ];
        }

        return [
            'status' => true,
            'message' => 'success',
        ];
    }

    public function updateWriteLog($invoiceNumber, $status, $log = '')
    {
        return PurchaseOrderSSactivewear::where('invoice_number', $invoiceNumber)
            ->update([
                'status_insert_order' => $status,
                'log' => $log,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
    }
}
