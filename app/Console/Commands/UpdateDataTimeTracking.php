<?php

namespace App\Console\Commands;

use App\Models\TimeTracking;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateDataTimeTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update-data-time-traking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update data time tracking';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $startTime = '2024-08-01 00:00:00';
            $endTime = '2024-08-12 23:59:59';
            $timeTrackings = TimeTracking::where('start_time', '>=', $startTime)
                ->where('end_time', '<=', $endTime)
                ->where('job_type', 'quality_control')
                ->select('id', 'employee_id', 'job_type', 'start_time', 'end_time', 'quantity', DB::raw('TIMESTAMPDIFF(SECOND, start_time, end_time) AS time_difference_seconds'))
                ->whereColumn('start_time', '=', 'end_time')
                ->orderBy('start_time')
                ->get()->groupBy(['employee_id', function ($item) {
                    return Carbon::parse($item['start_time'])->format('Y-m-d');
                }]);
            foreach ($timeTrackings as $employeeId => $items) {
                jobEcho('Update employee: ' . $employeeId);
                foreach ($items as $date => $trackings) {
                    $firstItem = $trackings->first();
                    $startTime = $firstItem->start_time;
                    $endTime = $firstItem->end_time;
                    $id = $firstItem->id;
                    jobEcho('Update date: ' . $date);

                    foreach ($trackings as $item) {
//                        if ($endTime >= $item->start_time && $endTime < $item->end_time) {
//                            $endTime = $item->end_time;
//                        } elseif ($endTime < $item->start_time && $endTime < $item->end_time) {
//                            $endTime = Carbon::parse($endTime)->addSeconds($item->time_difference_seconds)->toDateTimeString();
//                        }
                        if ($endTime < $item->end_time) {
                            $endTime = $item->end_time;
                        }
                    }
                    $count = DB::table('sale_order_item_quality_control')->where('employee_id', $employeeId)->whereDate('created_at', $date)->count();

                    TimeTracking::find($id)->update([
                        'quantity' => $count,
                        'start_time' => $startTime,
                        'end_time' => $endTime
                    ]);
                    jobEcho('Update tracking: ' . $id);
                    TimeTracking::where('employee_id', $employeeId)->whereDate('start_time', $date)->where('id', '!=', $id)->delete();
                }
            }

            return 'Success';
        } catch (\Exception $exception) {
            jobEcho($exception->getMessage());
        }
    }
}
