<?php


namespace App\Console\Commands;


use App\Http\Service\AlertService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateVisuaLateResponse extends Command
{

    protected $signature = 'update:visua-late-response';


    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        try {
            DB::table('visua_detect_image')
                ->where('is_received_response', 0)
                ->where('created_at', '<', DB::raw('NOW() - INTERVAL 10 MINUTE'))
                ->update(['is_received_response' => 1]);

            $this->info('Updated visua_detect_image records successfully.');
        } catch (\Exception $e) {
            $this->error('Failed to update visua_detect_image records: ' . $e->getMessage());
        }

        return 0;
    }


}
