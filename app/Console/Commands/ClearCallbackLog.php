<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearCallbackLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:callback-log';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear old callback log from the callback_log table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('[START] Delete old callback log');
        DB::table('callback_log')->where('created_at', '<', Carbon::now()->subDays(45))->delete();
        $this->info('[END] Old callback log cleared successfully');
    }
}
