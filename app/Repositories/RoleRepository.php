<?php


namespace App\Repositories;


use App\Models\PurchaseOrderBoxItem;
use App\Models\User;
use App\Repositories\Contracts\RoleRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleRepository implements RoleRepositoryInterface
{
    const LIMIT = 10;

    public function create($input): JsonResponse
    {
        $dataInsert = [
            'name' => $input['name'],
            'data' => json_encode($input['data'])
        ];
        Role::create($dataInsert);
        return response()->json(['message' => 'success']);
    }

    public function edit($input, $id)
    {
        $role = Role::where('id', $id)->first();

        if (!$role) {
            return response()->json(['message' => ["not found"]], 422);
        }

        $role->update($input);
        return response()->json(['message' => 'success']);
    }

    public function fetchAll()
    {
        $data = Role::get();

        foreach ($data as $item ){
            $item->data = json_decode($item->data);
        }
        return $data;
    }

    public function delete($id)
    {
        $role = Role::where('id', $id)->first();
        if (!$role) {
            return response()->json(['message' => ["Role not found"]], 422);
        }
        // delete in roles, role_has_permissions, model_has_role
        $role->delete();

        return response()->json(['message' => 'success']);
    }

    public function creatUserRole($input)
    {
       $user = User::where('id', $input['user_id'])
                ->first();

       $role = Role::where('id', $input['roles_id'])
           ->first();

       $user->assignRole($role->name);

       return response()->json(['message' => 'success']);


    }

    public function creatRolePermission($input)
    {
        $role = Role::where('id', $input['roles_id'])
            ->first();
        $permissions = Permission::WhereIn('id',$input['permission_id'])->pluck('name')->toArray();

        $role->syncPermissions($permissions);

        return response()->json(['message' => 'success']);
    }

}
