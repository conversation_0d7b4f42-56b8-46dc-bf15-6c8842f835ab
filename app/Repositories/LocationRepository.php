<?php

namespace App\Repositories;

use App\Models\Box;
use App\Models\Location;
use App\Repositories\Contracts\LocationRepositoryInterface;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Validator;

class LocationRepository extends CommonRepository implements LocationRepositoryInterface
{
    const LIMIT = 10;

    public function fetchAll($input)
    {
        $query = Location::where('warehouse_id', $input['warehouse_id']);

        if (!empty($input['barcode'])) {
            $barcode = $input['barcode'];
            $query->where('barcode', 'LIKE', '%' . $barcode . '%');
        }

        if (!empty($input['type'])) {
            $query->where('type', $input['type']);
        }

        $query->where('is_deleted', 0);

        if (!empty($input['without_pagination'])) {
            return $query->orderBy('id', 'desc')->get();
        }
//        return $query->orderBy('id', 'desc')->paginate($limit);
        return $this->getLocationHasPaginate($input);
    }

    public function getLocationHasPaginate($input)
    {
        $query = 'SELECT * FROM ((';
        $query .= 'SELECT location.id, location.barcode, COUNT(box.id) total_box, location.created_at as location_created_at, location.updated_at as location_updated_at, location.warehouse_id, location.type FROM location LEFT JOIN box ON box.location_id = location.id AND box.is_deleted = 0 ';
        $query .= 'where location.is_deleted = 0';
        $bindings = [];

        if (!empty($input['warehouse_id'])) {
            $bindings[] = $input['warehouse_id'];
            $query .= ' and location.warehouse_id = ? ';
        }

        if (!empty($input['barcode'])) {
            $bindings[] = $input['barcode'];
            $query .= ' and location.barcode = ?';
        }

        $query .= ' and location.type = 0 GROUP BY location.id ORDER BY location.id ASC) AS location_box LEFT JOIN ('; // get only rack
        $query .= 'SELECT SUM(location_product.quantity) total_quantity, location_product.location_id FROM location_product GROUP BY location_product.location_id ) AS location_product_quantity ON location_product_quantity.location_id = location_box.id )';

        $count = count(DB::select($query, $bindings));

        $size = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $page = ($input['page'] - 1) * $size;
        $query .= 'LIMIT ? OFFSET ?';
        $data = DB::select($query, array_merge($bindings, [$size, $page]));
        $dataOut = [
            'total' => $count,
            'data' => $data
        ];

        return $dataOut;
    }

    public function create($input)
    {
        $res = [];
        for ($i = 1; $i <= $input['num']; $i++) {
            $barcode = DB::select('CALL create_serial("LO")');
            if (!empty($barcode)) {
                $box = Location::create([
                    'barcode' => $barcode[0]->number,
                    'warehouse_id' => $input['warehouse_id'],
                    'type' => $input['type']
                ])->fresh();

                array_push($res, $box);
            }
        }

        return $res;
    }

    public function fetch($id)
    {
        return Location::find($id);
    }

    public function findByBarcode($barcode)
    {
        return Location::where('barcode', $barcode)->first();
    }

    public function getDetail($id)
    {
        return Location::query()
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->where('is_deleted', 0)
            ->where('id', $id)
            ->first();
    }

    public function update($id, $dataUpdate)
    {
        return Location::where('id', $id)->update($dataUpdate);
    }

    public function delete($id)
    {
        $totalBox = Box::where('location_id', $id)->count();
        if ($totalBox) {
            return $this->errorResponse("Can't delete this location", Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        Location::where('id', $id)->update(['is_deleted' => 1]);

        return $this->successResponse('delete location successful ');
    }

    public function bulkInsert($input)
    {
        $items = explode("\n", $input['items']);
        $totalInsertOk = 0;

        foreach ($items as $item) {
            $data = [
                'warehouse_id' => $input['warehouse_id'],
                'barcode' => trim($item),
                'type' => 0, // 0: rack
            ];

            $validator = Validator::make($data, [
                'barcode' => ['required',
                    Rule::unique('location')->where(function ($query) use ($data) {
                        return $query->where('warehouse_id', $data['warehouse_id'])
                            ->where('barcode', $data['barcode']);
                    })]
            ]);
            if (!$validator->fails()) {
                Location::create($data);
                $totalInsertOk++;
            }
        }

        return response()->json(['totalInsertSuccess' => $totalInsertOk], 201);
    }

    public function getLocationByParams($input)
    {
        return Location::where('warehouse_id', $input['warehouse_id'])
            ->where('is_deleted', 0)
            ->where('barcode', 'LIKE', '%' . $input['barcode'] . '%')
            ->get();
    }

    public function getPullingShelves($warehouse_id)
    {
        return Location::where('warehouse_id', $warehouse_id)
            ->where('is_deleted', 0)
            ->where('type', Location::PULLING_SHELVES)->first();
    }
}
