<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemBarcode;
use App\Models\Shipment;
use App\Models\ShipmentCreateErrorLog;
use App\Models\ShippingCarrierService;
use App\Models\Warehouse;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ShipmentRepository extends CommonRepository
{
    public function getList($request, $getTotal = false)
    {
        setTimezone();
        $request['sort_column'] = 'ship_date';

        if (!empty($request['carrier_service'])) {
            $carrierService = ShippingCarrierService::with('shippingCarrier:id,name,code')
                ->whereHas('shippingCarrier', function ($q) {
                    $q->where('is_deleted', '<>', 1);
                })
                ->get();

            foreach ($carrierService as $item) {
                if ($request['carrier_service'] == $item->shippingCarrier->name . ' - ' . $item->display_name) {
                    $request['shipment_service'] = $item->name;
                    $request['shipping_carrier'] = $item->shippingCarrier->code;
                    break;
                }
            }
        }

        $query = Shipment::query()->search($request);

        if (!empty($request['type_test']) && $request['type_test'] != 'all') {
            $query->whereHas('saleOrder', function ($query) use ($request) {
                if ($request['type_test'] == 'is_test') {
                    $query->where('sale_order.is_test', 1);
                } else {
                    $query->where('sale_order.is_test', 0);
                }
            });
        }

        if ($getTotal) {
            return ['total' => $query->selectRaw('count(shipment.id) as total')->first()->total ?? 0];
        }

        $query->select('shipment.*')
            ->with([
                'saleOrder',
                'shipmentLabelLastPrinted',
                'saleOrder.store:id,name,code',
                'shippingCarrierService:name,display_name',
                'shippingCarrier:name,code,tracking_url',
                'employeeCreate:id,name',
                'employeePrinted:id,name'
            ])
            ->withSum('saleOrderItem', 'quantity')
            ->withMax('shipmentLabelLastPrinted', 'printed_date');

        $columnSort = [
            'printed_date' => 'shipment_label_last_printed_max_printed_date',
            'ship_date' => 'ship_date',
        ];
        $shipment = $query->orderBy($this->sortColumn != 'id' ? $columnSort[$this->sortColumn] : $columnSort['ship_date'], $this->sortBy ?? 'desc')
            ->simplePaginate($this->limit);
        $orderIds = $shipment->pluck('order_id');
        $dataLastAction = [];

        if (!empty($orderIds)) {
            $saleOrderItemBarcodeRepository = app()->make(SaleOrderItemBarcodeRepository::class);
            $barcodeArr = $saleOrderItemBarcodeRepository->getBarcodeForLineProductionByOrderIds($orderIds);
            $groupedBarcodeByOrderId = $this->groupBarcodeByOrderId($barcodeArr);

            foreach ($groupedBarcodeByOrderId as $key => $barcodeOfOrder) {
                $lastActionData = $this->getLastActionForItemBarcode($barcodeOfOrder);

                if (!empty($lastActionData)) {
                    $dataLastAction[$key] = $lastActionData;
                }
            }
        }

        foreach ($shipment as $item) {
            if (!$item->url_tracking_easypost && $item->shippingCarrier) {
                $item->url_tracking_easypost = str_replace('{tracking_code}', $item->tracking_number, $item->shippingCarrier->tracking_url);
            }

            if (isset($dataLastAction[$item->order_id])) {
                $itemLastAction = $dataLastAction[$item->order_id];
                $item->last_action = $itemLastAction['last_action'];
                $item->last_action_at = $itemLastAction['last_action_at'];
            }

            if (!empty($item->created_at)) {
                $item->created_at_pst = shiftTimezoneToPST($item->created_at)->format('Y-m-d H:i:s');
            }

            if (!empty($item->saleOrder->created_at)) {
                $item->saleOrder->created_at_pst = shiftTimezoneToPST($item->saleOrder->created_at)->format('Y-m-d H:i:s');
            }
        }

        return $shipment;
    }

    private function groupBarcodeByOrderId($barcodeArr): array
    {
        return array_reduce($barcodeArr, function (array $accumulator, array $element) {
            $accumulator[$element['order_id']][] = $element;

            return $accumulator;
        }, []);
    }

    private function getLastActionForItemBarcode($barcodeOfOrder): array
    {
        $lineProduction = SaleOrderItemBarcode::LINE_PRODUCTION;
        $barcodeOrderTmp = [];

        foreach ($barcodeOfOrder as $barcodeItem) {
            for ($i = count($lineProduction) - 1; $i >= 0; $i--) {
                $line = $lineProduction[$i];

                if (!empty($barcodeItem[$line])) {
                    if (empty($barcodeOrderTmp[$i])
                        || ($barcodeOrderTmp[$i]['last_action_at'] < $barcodeItem[$line])) {
                        $barcodeOrderTmp[$i] = [
                            'last_action' => trim(str_replace(['_at', '_'], ' ', $line)),
                            'last_action_at' => $barcodeItem[$line],
                        ];
                    }

                    break;
                }
            }
        }

        return !empty($barcodeOrderTmp) ? $barcodeOrderTmp[min(array_keys($barcodeOrderTmp))] : [];
    }

    public function GetDataExportReportForShipment($shipDate, $storeId, $info)
    {
        $data = [];
        $getAllWarehouse = Warehouse::where('is_auto_create_shipping', Warehouse::AUTO_CREATE_SHIPPING)->pluck('name', 'id')->toArray();
        $getAllShipment = Shipment::with('saleOrder:id,order_number,order_quantity')
            ->where('store_id', $storeId)
            ->where('ship_date', $shipDate)
            ->whereIn('warehouse_id', array_keys($getAllWarehouse))
            ->where('provider', Shipment::PROVIDER_EASYPOST)
            ->get();

        foreach ($getAllWarehouse as $key => $item) {
            $data[$item] = $getAllShipment->where('warehouse_id', $key);
        }

        if ($info) {
            $total = $getAllShipment->count();
            $totalAuto = $getAllShipment->where('is_auto_created', 1)->count();
            $totalManual = $getAllShipment->where('is_auto_created', 0)->count();
            $totalFirst = $getAllShipment->where('service_code', 'First')->count();
            $totalFirstAuto = $getAllShipment->where('service_code', 'First')->where('is_auto_created', 1)->count();
            $totalFirstManual = $getAllShipment->where('service_code', 'First')->where('is_auto_created', 0)->count();
            $totalPriority = $getAllShipment->where('service_code', 'Priority')->count();
            $totalPriorityAuto = $getAllShipment->where('service_code', 'Priority')->where('is_auto_created', 1)->count();
            $totalPriorityManual = $getAllShipment->where('service_code', 'Priority')->where('is_auto_created', 0)->count();
            $totalPriorityCubic = $getAllShipment->where('service_code', 'Priority')->whereNotNull('dimension_length')->count();
            $totalInterNational = $getAllShipment->where('carrier_code', 'AsendiaUsa')->count();
            $totalInterNationalAuto = $getAllShipment->where('carrier_code', 'AsendiaUsa')->where('is_auto_created', 1)->count();
            $totalInterNationalManual = $getAllShipment->where('carrier_code', 'AsendiaUsa')->where('is_auto_created', 0)->count();

            return [
                'total' => $total,
                'totalAuto' => $totalAuto,
                'totalManual' => $totalManual,
                'totalFirst' => $totalFirst,
                'totalFirstAuto' => $totalFirstAuto,
                'totalFirstManual' => $totalFirstManual,
                'totalPriority' => $totalPriority,
                'totalPriorityAuto' => $totalPriorityAuto,
                'totalPriorityManual' => $totalPriorityManual,
                'totalPriorityCubic' => $totalPriorityCubic,
                'totalInterNational' => $totalInterNational,
                'totalInterNationalAuto' => $totalInterNationalAuto,
                'totalInterNationalManual' => $totalInterNationalManual,
            ];
        }

        return $data;
    }

    public function updateManualTracking($inputs, $id)
    {
        $shipment = Shipment::findShipmentManualTracking($id);

        if (!$shipment) {
            throw new Exception('Shipment not found!', Response::HTTP_NOT_FOUND);
        }

        $carrierCode = $inputs['carrier_code'] ?? $shipment->carrier_code;
        $trackingNumber = $inputs['tracking_number'] ?? $shipment->tracking_number;
        $shipDate = $inputs['ship_date'] ?? $shipment->ship_date;
        $price = $inputs['price'] ?? $shipment->shipment_cost;
        $isUpdate = false;

        if (
            $carrierCode != $shipment->carrier_code
            || $trackingNumber != $shipment->tracking_number
            || $shipDate != $shipment->ship_date
            || $price != $shipment->shipment_cost
        ) {
            $isUpdate = true;
        }

        if ($isUpdate) {
            try {
                DB::beginTransaction();

                if (
                    (
                        $carrierCode != $shipment->carrier_code
                        && $trackingNumber != $shipment->tracking_number
                        && $shipDate != $shipment->ship_date
                        && $price != $shipment->shipment_cost
                    )
                    || $trackingNumber != $shipment->tracking_number
                ) {
                    $message = "Updated tracking number $trackingNumber of $carrierCode carrier with ship date on " . Carbon::parse($shipment->ship_date)->format('Y-m-d') . " for $$price";
                } else {
                    $mesCarrier = $carrierCode != $shipment->carrier_code ? "$shipment->carrier_code to $carrierCode," : null;
                    $mesShipDate = $shipDate != $shipment->ship_date ? "$shipment->ship_date to " . Carbon::parse($shipDate)->format('Y-m-d') . ',' : null;
                    $mesCost = $price != $shipment->shipment_cost ? "$$shipment->shipment_cost to $$price," : null;
                    $mes = substr(trim("$mesCarrier $mesShipDate $mesCost"), 0, strlen(trim("$mesCarrier $mesShipDate $mesCost")) - 1);
                    $message = "Updated $mes of tracking number $trackingNumber";
                }

                $shipment->carrier_code = $carrierCode;
                $shipment->tracking_number = $trackingNumber;
                $shipment->ship_date = $shipDate;
                $shipment->shipment_cost = $price;
                $shipment->save();

                SaleOrderHistory::create([
                    'user_id' => auth()->user()['id'],
                    'order_id' => $shipment->order_id,
                    'type' => SaleOrderHistory::MANUAL_TRACKING_TYPE,
                    'message' => $message,
                    'created_at' => now()
                ]);
                DB::commit();

                return [];
            } catch (Exception $e) {
                DB::rollBack();
                throw new Exception($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        }

        return [];
    }

    public function getCount($request)
    {
        $query = Shipment::whereHas('saleOrder', function ($query) {
            $query->where('sale_order.is_test', SaleOrder::NOT_TEST);
        });

        if ($request['is_all_warehouse'] == 0 && is_numeric($request['warehouse_id'])) {
            $query->where('shipment.warehouse_id', $request['warehouse_id']);
        }

        $storeIds = $request['store_ids'] ?? '';

        if (!empty($storeIds)) {
            $query->whereHas('saleOrder', function ($q) use ($storeIds) {
                $q->whereIn('sale_order.store_id', $storeIds);
            });
        }

        return $query->where('id', '>', env('ID_SHIPMENT_COUNT_VALID', 4649901))
            ->whereIn('tracking_status', [Shipment::IN_TRANSIT, Shipment::FAILURE])->groupBy('tracking_status')
            ->select(DB::raw('tracking_status, count(id) as total'))
            ->pluck('total', 'tracking_status');
    }

    public function fixShipDateShipment($id)
    {
        $query = ShipmentCreateErrorLog::whereNull('fix')
            ->where('note', 'like', '%Invalid datetime format: 1292%')
            ->where('id', '>=', $id)
            ->take(50)
            ->get();
        foreach ($query as $key => $value) {
            try {
                DB::beginTransaction();
                $data = $value->note;
                $pattern = "/\(([^)]+)\)/";
                preg_match_all($pattern, $data, $matches);
                $valuesString = $matches[1][1];
                $valuesString = str_replace('?', 'null', $valuesString);
                $valuesArray = explode(', ', $valuesString);

                foreach ($valuesArray as $keyValue => $valueArray) {
                    $valuesArray[$keyValue] = ($valueArray === 'null') ? null : $valueArray;

                    if ($keyValue == 4) {
                        $valuesArray[$keyValue] = Carbon::parse($valueArray)->toDateTimeString();
                    }
                }

                $columnNames = ['order_id', 'account_id', 'store_id', 'warehouse_id', 'ship_date', 'shipment_cost', 'insurance_cost', 'carrier_code', 'tracking_number', 'tracking_status', 'service_code', 'package_code', 'weight_value', 'weight_unit', 'dimension_length', 'dimension_width', 'dimension_height', 'dimension_unit', 'label_url', 'url_tracking_easypost', 'shipment_quantity', 'provider', 'is_auto_created', 'created_at', 'updated_at', 'employee_create_id', 'shipment_account', 'label_zpl_url'];
                $dataInsert = array_combine($columnNames, $valuesArray);
                $saleOrder = SaleOrder::where('id', $value->order_id)->first();

                if ($saleOrder->shipment_id) {
                    continue;
                }

                $shipmentId = DB::table('shipment')
                    ->insertGetId($dataInsert);
                $saleOrder->shipment_id = $shipmentId;
                $saleOrder->order_status = SaleOrder::SHIPPED;
                $saleOrder->save();
                $value->fix = 1;
                $value->save();
                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        }

        return 'done';
    }
}
