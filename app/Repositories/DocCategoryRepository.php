<?php

namespace App\Repositories;
use App\Models\DocCategory;
use App\Models\Doc;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Validator;
use App\Repositories\Contracts\DocCategoryRepositoryInterface;

class DocCategoryRepository implements DocCategoryRepositoryInterface
{
    public function fetchAll($input)
    {
        return DocCategory::get();
    }

    public function create($input)
    {
        return DocCategory::create($input);
    }

    public function fetch($id)
    {
        return DocCategory::find($id);
    }

    public function update($id, $dataUpdate)
    {
        $dataUpdate['updated_at'] = now();
        return DocCategory::where('id', $id)->update($dataUpdate);
    }

    public function delete($id)
    {
        DB::beginTransaction();
        try {
            $docCategory = DocCategory::where('id', $id)->delete();
            Doc::where('doc_category_id', $id)->delete();
            DB::commit();
            return $docCategory;
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
