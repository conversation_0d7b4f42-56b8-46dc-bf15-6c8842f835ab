<?php

namespace App\Repositories;

use App\Models\Product;
use App\Models\ProductSkuMatching;
use App\Models\Store;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class ProductSkuMatchingRepository extends CommonRepository
{
    public function fetch($request)
    {
        $query = ProductSkuMatching::search($request)->with(['product', 'store:name,id']);

        return $query->latest('id')->paginate($request['limit'] ?? self::LIMIT);
    }

    public function delete($id)
    {
        $deleted = ProductSkuMatching::where('id', $id)->delete();

        return $this->successResponse('Delete Product Sku Matching Successfully!', $deleted);
    }

    public function verifyCsvFile($request)
    {
        $data = Excel::toCollection(new Collection(), $request->file('file'))[0];

        $invalidData = [];
        $validData = [];

        $product = Product::all();
        $store = Store::all();
        foreach ($data as $key => $row) {
            if ($key === 0) continue;

            $sku = trim($row[3]);
            $storeId = trim($row[4]);
            if (empty($row[0])) {
                $invalidData[] = [
                    'row' => $row,
                    'reason' => 'Style is missing.',
                ];
                continue;
            }

            if (empty($sku)) {
                $invalidData[] = [
                    'row' => $row,
                    'reason' => 'Swiftpod SKU is missing.',
                ];
                continue;
            }

            if (empty($storeId)) {
                $invalidData[] = [
                    'row' => $row,
                    'reason' => 'Store id is missing.',
                ];
                continue;
            }

            $skuCheck = $product->filter(function ($item) use ($sku) {
                return $item->sku === $sku;
            });
            if (count($skuCheck) < 1) {
                $invalidData[] = [
                    'row' => $row,
                    'reason' => 'Swiftpod SKU not found.',
                ];
                continue;
            }
            $storeCheck = $store->filter(function ($item) use ($storeId) {
                return $item->id == $storeId;
            });
            if (count($storeCheck) < 1) {
                $invalidData[] = [
                    'row' => $row,
                    'reason' => 'Store not found.',
                ];
                continue;
            }

            if (!isset($validData[$sku])) {
                $validData[$sku] = $this->tranformData($row);
            } else {
                $invalidData[] = [
                    'row' => $row,
                    'reason' => 'Duplicate data.',
                ];
                continue;
            }
        }

        return [
            'valid' => $validData,
            'invalid' => $invalidData,
            'countValid' => count($validData),
            'countInvalid' => count($invalidData),
        ];
    }

    public function importCsv($input)
    {
        $res = $this->verifyCsvFile($input);
        if ($res['countValid'] < 1) {
            return response(['message' => 'No data import.'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        try {
            DB::beginTransaction();

//            ProductSkuMatching::upsert(array_values($res['valid']),  ['swiftpod_sku',  'store_id'], ['attribute', 'swiftpod_sku',  'store_id']);
            ProductSkuMatching::upsert($res['valid'], ['swiftpod_sku',  'store_id', 'attribute'], ['attribute']);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return response(['message' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response(['message' => 'Import product sku matching successfully!']);
    }

    public function tranformData($data)
    {
        return [
            'swiftpod_sku' => trim($data[3]),
            'store_id' => trim($data[4]),
            'attribute' => "style=". (trim($data[0]) ?? '') . "&color=" . (trim($data[1]) ?? '') . "&size=". (trim($data[2]) ?? ''),
        ];
    }
}
