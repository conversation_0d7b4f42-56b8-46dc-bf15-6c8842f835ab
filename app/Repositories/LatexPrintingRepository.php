<?php

namespace App\Repositories;

use App\Models\Employee;
use App\Models\PrintingPreset;
use App\Models\PrintMethod;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Models\TimeTracking;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class LatexPrintingRepository
{
    const DPI = 300;

    const T_75 = '0.75';

    const T_125 = '1.25';

    const T_150 = '1.50';

    const THICKNESS1 = 0.042;

    const THICKNESS2 = 0.083;

    /**
     * Crop sizes for different templates (in inches).
     *
     * @const array<string, float> CROP_SIZES
     * Key: Template type (T_75, T_125, T_150)
     * Value: Crop size in inches
     */
    const CROP_SIZES = [
        self::T_75 => 2.65, // ((0.75 - 0.3) + 2 * 1.1)
        self::T_125 => 3.35, // ((1.25 - 0.1) + (2 * 1.1))
        self::T_150 => 3.4, // ((1.50 - 0.3) + (2 * 1.1))
    ];

    /**
     * Dimensions for side alignment helper.
     * Each item in the array represents a dimension in centimeters (cm).
     */
    const SIDE_ALIGNMENT_HELPER_DIMENSION = [
        self::T_75 => 3.35,
        self::T_125 => 3,
        self::T_150 => 3.38,
    ];

    public function removeLabelSide($label)
    {
        $label = trim($label);
        $i = explode('-', $label);
        if (count($i) == 6) {
            return substr($label, 0, -2);
        }

        return $label;
    }

    public function getBarcodeLabel($label)
    {
        return SaleOrderItemBarcode::from('sale_order_item_barcode as soib')
            ->selectRaw('
                pps2.*,
                soi.id as order_item_id,
                soi.ink_color_status as soi_ink_color_status,
                soi.options as soi_options,
                soi.product_sku as soi_product_sku,
                soii.id as soii_id,
                soii.custom_platen as soii_custom_platen,
                soii.pretreat_info as soii_pretreat_info,
                soii.order_date as soii_order_date,
                soii.image_width as soii_image_width,
                soii.image_height as soii_image_height,
                soii.image_url as soii_image_url,
                soii.upload_s3_status as soii_upload_s3_status,
                pps.code as pps_code,
                pps.code_name as pps_code_name,
                pps.name as pps_name,
                pps.description as pps_description,
                ps.sku as ps_sku,
                ps.name as ps_name,
                pc.name as pc_name,
                p.description as p_description,
                ps2.name as ps2_name,
                soib.id as soib_id,
                soib.sku as soib_sku,
                soib.is_deleted as soib_is_deleted,
                soib.label_id as soib_label_id,
                soib.barcode_number as soib_barcode_number,
                so.order_status,
                so.warehouse_id,
                so.store_id
            ')
            ->join('sale_order as so', 'so.id', '=', 'soib.order_id')
            ->join('sale_order_item as soi', 'soi.id', '=', 'soib.order_item_id')
            ->join('sale_order_item_image as soii', 'soi.id', '=', 'soii.order_item_id')
            ->join('product_style as ps', 'ps.sku', '=', 'soi.product_style_sku')
            ->join('product_print_side as pps', 'pps.code', '=', 'soii.print_side')
            ->join('product_print_area as ppa', function ($join) {
                $join->on('ppa.name', '=', 'pps.name');
                $join->on('ppa.product_style_id', '=', 'ps.id');
            })
            ->leftJoin('printing_preset_sku as pps2', 'pps2.sku', '=', 'soi.product_sku')
            ->join('product_color as pc', 'pc.sku', '=', 'soi.product_color_sku')
            ->join('product_size as ps2', 'ps2.sku', '=', 'soi.product_size_sku')
            ->join('product as p', 'p.id', '=', 'soi.product_id')
            ->where('ppa.print_method', PrintMethod::LATEX)
            ->where('soib.label_id', $label)
            ->first();
    }

    public function scanLabel($label, $employeeId = 0, $timeTrackingId = null): array
    {
        $label = $this->removeLabelSide($label);
        $barcode = $this->getBarcodeLabel($label);

        if (!empty($timeTrackingId)) {
            TimeTracking::where('id', $timeTrackingId)->update(['end_time' => now()]);
        }

        if (empty($barcode)) {
            return [
                'status' => false,
                'message' => 'Invalid Barcode',
            ];
        }

        if ($barcode->soi_ink_color_status != SaleOrderItem::ACTIVE) {
            return [
                'status' => false,
                'message' => 'Image not found!',
            ];
        }

        if (empty($barcode->id)) {
            return [
                'status' => false,
                'message' => 'No Printing Preset SKU detected.',
            ];
        }

        if ($barcode->soib_is_deleted == 1) {
            return [
                'status' => false,
                'message' => 'This order has been cancelled',
            ];
        }

        if (in_array($barcode->order_status, SaleOrder::ARRAY_STATUS_INACTIVE)) {
            return [
                'status' => false,
                'message' => 'Sale order is ' . str_replace('_', ' ', $barcode->order_status),
            ];
        }

        $employee = Employee::query()->where('id', $employeeId)->first();

        if (empty($employee)) {
            return [
                'status' => false,
                'message' => 'Employee not found',
            ];
        }

        if ($employee->warehouse_id != $barcode->warehouse_id) {
            return [
                'status' => false,
                'message' => 'This label belongs to another warehouse',
            ];
        }

        $store = Store::where('id', $barcode->store_id)->first();

        if (empty($store)) {
            return [
                'status' => false,
                'message' => 'Store not found',
            ];
        }

        $sku = $barcode->soib_sku;
        $platenSize = 'platen_' . $barcode->pps_code_name . '_size';
        $printSize = $barcode->pps_code_name . '_size';
        $position = $barcode->pps_code_name . '_position';

        if (empty($barcode->{$platenSize})) {
            return [
                'status' => false,
                'message' => 'Please check platen size information.',
            ];
        }

        if (empty($barcode->{$printSize})) {
            return [
                'status' => false,
                'message' => 'Please check print size information.',
            ];
        }

        if (empty($barcode->{$position})) {
            return [
                'status' => false,
                'message' => 'Please check position information.',
            ];
        }

        $platenSizeArr = explode('x', ($barcode->{$platenSize} ?? ''));
        $printSizeArr = explode('x', ($barcode->{$printSize} ?? ''));
        $thicknessKey = $this->thicknessByDimension($platenSizeArr[0], $printSizeArr[0]);

        if ($thicknessKey === false) {
            return [
                'status' => false,
                'message' => 'Invalid thickness',
            ];
        }

        $preview_by_side = null;
        $dataOptions = json_decode($barcode->soi_options);

        foreach ($dataOptions as $item) {
            if (str_contains($item->name, 'PreviewFiles.' . str_replace(' ', '', ucwords(str_replace('_', ' ', $barcode->pps_code_name))))) {
                $preview_by_side = $item->value;
            }
        }

        $imageName = "$sku-{$barcode->pps_code}.png";
        $printFileName = "{$barcode->soib_sku}-{$barcode->pps_code}-{$barcode->soib_barcode_number}.png";
        $pathThumb250 = "thumb/250/{$barcode->soii_order_date}/{$imageName}";
        $pathArtworkS3 = "artwork/{$barcode->soii_order_date}/{$imageName}";
        $printFilePath = "canvas/{$barcode->soii_order_date}/$printFileName";

        if (!empty($preview_by_side)) {
            $preview = $preview_by_side;
        } elseif (Storage::disk('s3')->exists($pathThumb250)) {
            $preview = Storage::disk('s3')->url($pathThumb250);
        } elseif (Storage::disk('s3')->exists($pathArtworkS3)) {
            $preview = Storage::disk('s3')->url($pathArtworkS3);
        }

        SaleOrderItemBarcode::query()
            ->where('id', $barcode->soib_id)
            ->update([
                'employee_scan_id' => $employeeId,
                'scanned_at' => date('Y-m-d H:i:s')
            ]);

        return [
            'status' => true,
            'data' => [
                'is_label' => true,
                'label_id' => $barcode->soib_label_id,
                'order_date' => $barcode->soii_order_date,
                'sku' => $barcode->soib_sku,
                'preview' => $preview ?? '',
                'print_file_name' => $printFileName,
                'print_file' => Storage::disk('s3')->exists($printFilePath) ? Storage::disk('s3')->url($printFilePath) : null,
                'style' => $barcode->ps_name,
                'platen' => $barcode->{$platenSize},
                'color' => $barcode->pc_name,
                'size' => $barcode->{$platenSize} . 'x' . $thicknessKey,
            ],
        ];
    }

    public function generatePrintFile($labelId)
    {
        $barcode = $this->getBarcodeLabel($labelId);

        if (empty($barcode)) {
            return [
                'status' => false,
                'message' => 'Order not found',
            ];
        }

        $platenSize = 'platen_' . $barcode->pps_code_name . '_size';
        $printSize = $barcode->pps_code_name . '_size';
        $position = $barcode->pps_code_name . '_position';
        $platenSizeArr = explode('x', ($barcode->{$platenSize} ?? ''));
        $printSizeArr = explode('x', ($barcode->{$printSize} ?? ''));
        $positionArr = explode('x', ($barcode->{$position} ?? ''));

        if (count($platenSizeArr) != 2) {
            return [
                'status' => false,
                'message' => 'Please check platen size information.',
                'detail' => $barcode,
            ];
        }

        if (count($printSizeArr) != 2) {
            return [
                'status' => false,
                'message' => 'Please check print size information.',
                'detail' => $barcode,
            ];
        }

        if (count($positionArr) != 2) {
            return [
                'status' => false,
                'message' => 'Please check position information.',
                'detail' => $barcode,
            ];
        }

        $imageName = "{$barcode->soib_sku}-{$barcode->pps_code}.png";
        $artwork_s3 = $barcode->soii_upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS ? env('AWS_S3_URL') . '/artwork/' . $barcode->soii_order_date . '/' . $imageName : null;

        $imgW = $barcode->soii_image_width;
        $imgH = $barcode->soii_image_height;

        $platenSizeW = $platenSizeArr[0] * self::DPI;
        $platenSizeH = $platenSizeArr[1] * self::DPI;

        $printSizeW = $printSizeArr[0] * self::DPI;
        $printSizeH = $printSizeArr[1] * self::DPI;

        $resizeW = $barcode->pps_code_name == 'white_border' ? $platenSizeW : $printSizeW;
        $resizeH = $barcode->pps_code_name == 'white_border' ? $platenSizeH : $printSizeH;

        $positionW = $positionArr[0] * self::DPI;
        $positionH = $positionArr[1] * self::DPI;

        $paddingBottom = round(($positionH - $printSizeH) / 2, 2);
        $qrSize = round(min(($positionH - $printSizeH - 0.2) / 2, 0.6 * self::DPI), 2);
        $thicknessKey = $this->thicknessByDimension($platenSizeArr[0], $printSizeArr[0]);
        $cropSize = round(self::CROP_SIZES[$thicknessKey] * self::DPI, 2);
        $padding = 0.7 * self::DPI;

        if ($thicknessKey === self::T_125) {
            $padding = (0.7 + 0.2) * self::DPI;
        }

        $preset = PrintingPreset::query()->where('name', 'LATEX_PRESET')->first();

        if (!empty($preset?->data)) {
            $xmlObject = simplexml_load_string($preset->data);
            $settings = json_decode(json_encode($xmlObject), true);

            if (isset($settings['corner_crop_size'][$thicknessKey]) and is_numeric($settings['corner_crop_size'][$thicknessKey])) {
                $cropSize = round($settings['corner_crop_size'][$thicknessKey] * self::DPI, 2);
            }
        }

        if ($thicknessKey === false) {
            return [
                'status' => false,
                'message' => 'Invalid thickness',
            ];
        }

        $canvas = new \Imagick();
        $canvas->newImage($positionW, $positionH, new \ImagickPixel('white'));
        $canvas->setImageFormat('png');
        $canvas->setImageResolution(300, 300);
        $canvas->setResolution(300, 300);
        $canvas->setImageUnits(\Imagick::RESOLUTION_PIXELSPERINCH);

        $image = new \Imagick();
        $image->readImageBlob(file_get_contents(!is_null($artwork_s3) ? $artwork_s3 : $barcode->soii_image_url));
        $image->resizeImage(min($resizeW, $imgW), min($resizeH, $imgH), \Imagick::FILTER_LANCZOS, 1, true);
        $imageX = round(($positionW - $image->getImageWidth()) / 2, 2);
        $imageY = round(($positionH - $image->getImageHeight()) / 2, 2);
        $canvas->compositeImage($image, \Imagick::COMPOSITE_OVER, $imageX, $imageY);
        $image->clear();
        $image->destroy();

        if ($platenSizeArr[0] > 40 || $platenSizeArr[0] < 8 || $platenSizeArr[1] > 40 || $platenSizeArr[1] < 8) {
            $alignmentPadding = 1.1;
            $alignmentGuideBorderW = round($positionW - ($alignmentPadding - self::THICKNESS2 / 2) * self::DPI * 2, 2);
            $alignmentGuideBorderH = round($positionH - ($alignmentPadding - self::THICKNESS2 / 2) * self::DPI * 2, 2);
            $alignmentGuidBorder = $this->drawBorder(self::THICKNESS2 * self::DPI, $alignmentGuideBorderW, $alignmentGuideBorderH);
            $canvas->compositeImage($alignmentGuidBorder, \Imagick::COMPOSITE_ATOP, round(($positionW - $alignmentGuideBorderW) / 2, 2), round(($positionH - $alignmentGuideBorderH) / 2, 2));
            $alignmentGuidBorder->clear();
            $alignmentGuidBorder->destroy();

            $lineX = 0;
            $lineY = round(self::SIDE_ALIGNMENT_HELPER_DIMENSION[$thicknessKey] * self::DPI, 2);
            $lineThickness = 0.05 * self::DPI;
            $lineWidth = 1.1 * self::DPI;
            $startX = $lineX * self::DPI;
            $startY = $lineY;
            $startBottomY = $positionH - $lineY;
            $endX = $startX + $lineWidth;
            $endY = $startY;
            $endBottomY = $startBottomY;

            $drawTop = new \ImagickDraw();
            $drawTop->setStrokeColor(new \ImagickPixel('black'));
            $drawTop->setStrokeWidth($lineThickness);
            $drawTop->line($startX, $startY, $endX, $endY);
            $canvas->drawImage($drawTop);
            $drawTop->clear();
            $drawTop->destroy();

            $drawBottom = new \ImagickDraw();
            $drawBottom->setStrokeColor(new \ImagickPixel('black'));
            $drawBottom->setStrokeWidth($lineThickness);
            $drawBottom->line($startX, $startBottomY, $endX, $endBottomY);
            $canvas->drawImage($drawBottom);
            $drawBottom->clear();
            $drawBottom->destroy();

            $cropSizeLineBorder = max($cropSize, $padding);
            $this->drawLineBorder($canvas, $cropSizeLineBorder, $padding, $positionW - $cropSizeLineBorder, $padding);
            $this->drawLineBorder($canvas, $cropSizeLineBorder, $positionH - $padding, $positionW - $cropSizeLineBorder, $positionH - $padding);
            $this->drawLineBorder($canvas, $padding, $cropSizeLineBorder, $padding, $positionH - $cropSizeLineBorder);
            $this->drawLineBorder($canvas, $positionW - $padding, $cropSizeLineBorder, $positionW - $padding, $positionH - $cropSizeLineBorder);
        }

        $draw = new \ImagickDraw();
        $draw->setFillColor(new \ImagickPixel('white'));
        $draw->setStrokeColor(new \ImagickPixel('black'));
        $draw->setStrokeWidth(self::THICKNESS1 / 3 * self::DPI);
        $draw->polygon([['x' => $cropSize, 'y' => 0], ['x' => 0, 'y' => 0], ['x' => 0, 'y' => $cropSize]]);
        $draw->polygon([['x' => $positionW, 'y' => 0], ['x' => $positionW - $cropSize, 'y' => 0], ['x' => $positionW, 'y' => $cropSize]]);
        $draw->polygon([['x' => $positionW, 'y' => $positionH], ['x' => $positionW - $cropSize, 'y' => $positionH], ['x' => $positionW, 'y' => $positionH - $cropSize]]);
        $draw->polygon([['x' => $cropSize, 'y' => $positionH], ['x' => 0, 'y' => $positionH], ['x' => 0, 'y' => $positionH - $cropSize]]);
        $canvas->drawImage($draw);
        $draw->clear();
        $draw->destroy();

        $qrBinary = QrCode::size(100)->format('png')->generate($barcode->soib_label_id);

        $qr = new \Imagick();
        $qr->readImageBlob($qrBinary);
        $qr->setImageFormat('png');
        $qr->resizeImage($qrSize, $qrSize, \Imagick::FILTER_LANCZOS, 1);
        $qrX = round(($positionW - $qrSize * 2) / 2, 2);
        $qrY = round($printSizeH + $paddingBottom + (($paddingBottom - $qrSize) / 2), 2);
        $canvas->compositeImage($qr, \Imagick::COMPOSITE_OVER, $qrX, $qrY);
        $qr->clear();
        $qr->destroy();

        $textDraw = new \ImagickDraw();
        $textDraw->setFontSize(36);
        $textDraw->setFillColor('black');
        $textX = $qrX + $qrSize + 20;
        $textY = $qrY + 30;
        $canvas->annotateImage($textDraw, $textX, $textY, 0, $barcode->soib_label_id);
        $canvas->annotateImage($textDraw, $textX, $textY + 50, 0, $barcode->soi_product_sku);
        $canvas->annotateImage($textDraw, $textX, $textY + 100, 0, $barcode->{$platenSize} . 'x' . $thicknessKey);
        !empty($barcode->p_description) && $canvas->annotateImage($textDraw, $textX, $textY + 150, 0, $barcode->p_description);
        $textDraw->clear();
        $textDraw->destroy();

        $printFilePath = "canvas/{$barcode->soii_order_date}/{$barcode->soib_sku}-{$barcode->pps_code}-{$barcode->soib_barcode_number}.png";
        $result = Storage::disk('s3')->put($printFilePath, $canvas->getImageBlob());

        $canvas->clear();
        $canvas->destroy();

        return [
            'status' => $result,
            'print_file_url' => $result ? Storage::disk('s3')->url($printFilePath) : null,
            'detail' => $barcode,
        ];
    }

    public function thicknessByDimension($platenW, $printSizeW)
    {
        $thickness = round($printSizeW - $platenW - 0.8, 1);

        if ($thickness == 1.5) {
            return self::T_75;
        }

        if ($thickness == 3.0) {
            return self::T_150;
        }

        if ($thickness == 2.5) {
            return self::T_125;
        }

        return false;
    }

    public function drawLineBorder(\Imagick &$canvas, float $x1, float $y1, float $x2, float $y2): void
    {
        $dashLength = 0.5 * self::DPI;
        $gapLength = 2 * self::DPI;
        $borderWidth = self::THICKNESS1 * self::DPI;
        $offset = 0.1 * self::DPI;

        $totalCycle = $dashLength + $gapLength;
        $lineLength = hypot($x1 - $x2, $y1 - $y2);
        $dashQty = ceil($lineLength / $totalCycle);
        $gapLength = ($lineLength - $dashQty * $dashLength) / ($dashQty - 1);

        $drawBlack = new \ImagickDraw();
        $drawBlack->setStrokeColor(new \ImagickPixel('black'));
        $drawBlack->setStrokeWidth($borderWidth);
        $drawBlack->setStrokeDashArray([$dashLength, $gapLength]);
        $drawBlack->setFillOpacity(0);
        $drawBlack->line($x1, $y1, $x2, $y2);

        $canvas->drawImage($drawBlack);
        $drawBlack->clear();
        $drawBlack->destroy();

        $drawWhite = new \ImagickDraw();
        $drawWhite->setStrokeColor(new \ImagickPixel('white'));
        $drawWhite->setStrokeWidth($borderWidth - $borderWidth * 80 / 100);
        $angle = atan2($y2 - $y1, $x2 - $x1);
        $shiftX = cos($angle) * $offset / 2;
        $shiftY = sin($angle) * $offset / 2;
        $drawWhite->translate($shiftX, $shiftY);
        $drawWhite->setStrokeDashArray([$dashLength - $offset, $gapLength + $offset]);
        $drawWhite->setFillOpacity(0);
        $drawWhite->line($x1, $y1, $x2, $y2);

        $canvas->drawImage($drawWhite);
        $drawWhite->clear();
        $drawWhite->destroy();
    }

    /**
     * Creates an image with a solid or dashed border.
     *
     * @param  float  $borderWidth The border width in pixels.
     * @param  float  $width The width of the image in pixels.
     * @param  float  $height The height of the image in pixels.
     * @param  bool  $isStrokeDash Whether to use a dashed border (true) or solid border (false).
     * @return Imagick The border image.
     */
    public function drawBorder(float $borderWidth, float $width, float $height, bool $isStrokeDash = false): \Imagick
    {
        $borderImage = new \Imagick();
        $borderImage->newImage($width, $height, new \ImagickPixel('transparent'));
        $borderImage->setImageFormat('png');

        $drawBlack = new \ImagickDraw();
        $drawBlack->setStrokeColor(new \ImagickPixel('black'));
        $drawBlack->setStrokeWidth($borderWidth);
        $drawBlack->setFillOpacity(0);

        $drawWhite = new \ImagickDraw();
        $drawWhite->setStrokeColor(new \ImagickPixel('white'));
        $drawWhite->setStrokeWidth($borderWidth - $borderWidth * 80 / 100);
        $drawWhite->setFillOpacity(0);

        if ($isStrokeDash) {
            $dashLength = 0.5 * self::DPI;
            $gapLength = 2 * self::DPI;
            $offset = 0.1 * self::DPI;
            $drawBlack->setStrokeDashArray([$dashLength, $gapLength]);
            $drawBlack->setStrokeDashOffset($offset / 2);
            $drawWhite->setStrokeDashArray([$dashLength - $offset, $gapLength + $offset]);
        }

        $x1 = $borderWidth / 2;
        $y1 = $borderWidth / 2;
        $x2 = round($width - $borderWidth / 2, 2);
        $y2 = round($height - $borderWidth / 2, 2);

        $drawBlack->rectangle($x1, $y1, $x2, $y2);
        $drawWhite->rectangle($x1, $y1, $x2, $y2);

        $borderImage->drawImage($drawBlack);
        $borderImage->drawImage($drawWhite);

        $drawBlack->clear();
        $drawBlack->destroy();

        $drawWhite->clear();
        $drawWhite->destroy();

        return $borderImage;
    }
}
