<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\Shipment;
use App\Models\ShipmentManifest;
use App\Models\ShipmentManifestPrinted;
use App\Models\ShipmentManifestTracking;
use App\Models\ShippingCarrier;
use App\Models\ShippingCarrierEasypost;
use EasyPost\Batch;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ShipmentManifestRepository extends CommonRepository
{
    public function scanTrackingNumber($input)
    {
        $shipment = Shipment::with('shippingCarrier', 'shippingCarrierService', 'easypostLog', 'shipmentEasypost')
            ->where('tracking_number', $input['tracking_number'])
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->where('is_deleted', false)
            ->whereNull('refund_status')
            ->first();

//        $dataNow = Carbon::now();
//        // check xem tracking number cua DHL da tao lau chua, neu tao lau roi thi bat phai tao lai
//        if ($shipment && $shipment->carrier_code == ShippingCarrier::DHL_ECOMMERCE_CODE && $dataNow->diffInDays($shipment->created_at) >= 1 ) {
//            return $this->handleFail('Shipments must be manifested within 1 days of creation');
//        }

        if ($shipment && empty($shipment->shippingCarrier)) {
            return $this->handleFail('Tracking number is not associated with any carrier in my system');
        }

        if ($shipment->shipment_account == null) {
            return $this->handleFail("Tracking number wasn't created by my system");
        }

        if (empty($shipment->shippingCarrierService)) {
            return $this->handleFail('Tracking number is not associated with any service in system');
        }

        if (empty($shipment->easypostLog) && empty($shipment->shipmentEasypost)) {
            return $this->handleFail('Tracking number is not associated with any my shipping carrier in Partner');
        }

        $dataLogFromPartner = empty($shipment->shipmentEasypost) ? json_decode($shipment->easypostLog?->data)?->id : str_replace('"', '', $shipment->shipmentEasypost?->easypost_id);

        if (empty($dataLogFromPartner)) {
            return $this->handleFail('Tracking number is not associated with any shipping id of Partner');
        }

        // check xem shipment co map voi account truyen vao khong
        // neu store_id ma dung tai khoan swiftpod thi where theo store 1 con khong thi where theo store khach
        $shippingCarrierEasyPost = ShippingCarrierEasypost::with('shippingIntegrationAccount')
            ->where('carrier_id', $shipment->shippingCarrier->id)
            ->where('store_id', $shipment->shipment_account == Shipment::SHIPMENT_ACCOUNT_SWIFTPOD ? LabelRepository::DEFAULT_STORE : $shipment->store_id)
            ->where('warehouse_id', $shipment->warehouse_id)
            ->where('integration_account_id', $input['shipping_integration_account_id'])
            ->first();

        if (!$shippingCarrierEasyPost || ($shippingCarrierEasyPost && empty($shippingCarrierEasyPost->shippingIntegrationAccount))) {
            return $this->handleFail('Wrong service tracking number');
        }

        $dataShipmentManifestTracking = [
            'service_code_id' => $shipment->shippingCarrierService->id,
            'employee_scan_id' => $input['employee_id'],
            'tracking_number' => $input['tracking_number'],
            'ship_id_partner' => $dataLogFromPartner,
        ];

        try {
            DB::beginTransaction();
            if (empty($input['shipment_manifest_id'])) {
                $dataShipmentManifest = [
                    'warehouse_id' => config('jwt.warehouse_id'),
                    'integration_account_id' => $input['shipping_integration_account_id'],
                    'employee_create_id' => $input['employee_id'],
                    'barcode_box' => $input['barcode_box']
                ];
                $shipmentManifest = ShipmentManifest::create($dataShipmentManifest);
                $dataShipmentManifestTracking['manifest_id'] = $shipmentManifest->id;
            } else {
                $dataShipmentManifestTracking['manifest_id'] = $input['shipment_manifest_id'];
                ShipmentManifest::where('id', $input['shipment_manifest_id'])->update(['barcode_box' => $input['barcode_box']]);
            }
            $shipmentManifestTracking = ShipmentManifestTracking::create($dataShipmentManifestTracking);
            DB::commit();

            handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $shipment->order_id);

            return $this->handleSuccess('Scan tracking success', ShipmentManifestTracking::with('shippingService')->find($shipmentManifestTracking->id));
        } catch (\Throwable $exception) {
            DB::rollBack();

            return $exception->getMessage();
        }
    }

    public function allTrackingNumberByManifestId($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = ShipmentManifestTracking::with(['shipmentManifest', 'shippingService', 'shipment:tracking_number,carrier_code,service_code,package_code,created_at,ship_date'])->where('manifest_id', $input['manifest_id']);
        if (!empty($input['tracking_number'])) {
            $query->where('tracking_number', $input['tracking_number']);
        }
        $query->orderBy('id', 'DESC');
        $data = $query->paginate($limit);
        $oneDayAgo = Carbon::now()->subDay();
        $isHasLabelOverTime = ShipmentManifestTracking::where('manifest_id', $input['manifest_id'])
                ->whereHas('shipment', function ($q) use ($oneDayAgo) {
                    $q->where('ship_date', '<', $oneDayAgo);
                })
                ->first() ? true : false;

        return [
            'data' => $data,
            'isHasLabelOverTime' => $isHasLabelOverTime,
        ];
    }

    public function deletedShipmentManifestTracking($id)
    {
        $delete = ShipmentManifestTracking::where('id', $id)->delete();
        if ($delete) {
            return $this->successResponse('Delete tracking number successfully!');
        }

        return $this->errorResponse('Delete tracking number error', Response::HTTP_BAD_REQUEST);
    }

    public function getManifestPendingAndNotPrint($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = ShipmentManifest::query()
            ->withCount(['shipmentManifestTrackings'])
            ->with([
                'getLastShipmentManifestTracking.employee:id,name',
                'getLastShipmentManifestTracking.shipment:id,tracking_number,carrier_code',
                'employee:id,name'
            ])
            ->whereHas('shipmentManifestTrackings')
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->orderBy('updated_at', 'DESC');

        if (!empty($input['tracking_number'])) {
            $trackingNumber = $input['tracking_number'];
            $query->whereHas('shipmentManifestTrackings', function ($q) use ($trackingNumber) {
                $q->where('shipment_manifest_tracking.tracking_number', $trackingNumber);
            });
        }

        if (!empty($input['status'])) {
            $query->where('status', $input['status']);
        } else {
            $query->whereIn('status', [ShipmentManifest::STATUS_SCANNING, ShipmentManifest::STATUS_GENERATING, ShipmentManifest::STATUS_PRINT_WAITING]);
        }

        return $query->paginate($limit);
    }

    public function getManifestPrinted($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = ShipmentManifest::query()
            ->withCount(['shipmentManifestTrackings'])
            ->with([
                'getLastShipmentManifestTracking.employee:id,name',
                'getLastShipmentManifestTracking.shipment:id,tracking_number,carrier_code',
                'employee:id,name', 'employeePrint:id,name'
            ])
            ->whereNotNull('printed_at')
            ->where('status', ShipmentManifest::STATUS_PRINTED)
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->orderBy('printed_at', 'DESC');

        if (!empty($input['tracking_number'])) {
            $trackingNumber = $input['tracking_number'];
            $query->whereHas('shipmentManifestTrackings', function ($q) use ($trackingNumber) {
                $q->where('shipment_manifest_tracking.tracking_number', $trackingNumber);
            });
        }

        if (!empty($input['start_date'])) {
            $query->where('printed_at', '>=', $input['start_date']);
        }

        if (!empty($input['end_date'])) {
            $query->where('printed_at', '<=', $input['end_date']);
        }

        if (!empty($input['employee_printed_id'])) {
            $query->where('employee_printed_id', $input['employee_printed_id']);
        }

        return $query->paginate($limit);
    }

    public function confirmPrintManifest($input)
    {
        $dataLogPrint = [
            'manifest_id' => $input['manifest_id'],
            'employee_printed_id' => $input['employee_id'],
            'printed_at' => now()
        ];
        try {
            DB::beginTransaction();
            if (isset($input['reprint']) && !$input['reprint']) {
                ShipmentManifest::find($input['manifest_id'])->update([
                    'employee_printed_id' => $input['employee_id'],
                    'printed_at' => now(),
                    'status' => ShipmentManifest::STATUS_PRINTED
                ]);
            } else {
                ShipmentManifest::find($input['manifest_id'])->update(['status' => ShipmentManifest::STATUS_PRINTED]);
            }
            ShipmentManifestPrinted::insert($dataLogPrint);
            DB::commit();
        } catch (\Throwable $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        }

        return $this->successResponse('Confirm success');
    }

    public function generateManifest($id, $barcodeBox)
    {
        try {
            $manifest = ShipmentManifest::with(['shippingIntegrationAccount.shippingCarrier', 'shippingIntegrationAccount.shippingIntegration', 'shipmentManifestTrackings.shipment:tracking_number,created_at,ship_date'])
                ->findOrFail($id);

//            $dataNow = Carbon::now();
//            if ($manifest->shippingIntegrationAccount?->shippingCarrier?->code == ShippingCarrier::DHL_ECOMMERCE_CODE ) {
//                $listTracking = '';
//                $isListTracking= false;
//                foreach ($manifest->shipmentManifestTrackings as $item) {
//                    if ($dataNow->diffInDays($item?->shipment->ship_date) >= 1) {
//                        $listTracking = $listTracking . $item->tracking_number . ', ';
//                        $isListTracking = true;
//                    }
//                }
//                $listTracking = rtrim($listTracking, ', ');
//               if($isListTracking) {
//                   return $this->errorResponse("The manifest cannot be generated because there is a shipment that has exceeded the allowable 1 days of creation: $listTracking");
//               }
//            }

            if ($manifest->status !== ShipmentManifest::STATUS_SCANNING) {
                return $this->errorResponse('Manifest has been generated');
            }

            if (!$manifest->shippingIntegrationAccount) {
                return $this->errorResponse('Manifest not map with account shipping');
            }

            if (!$manifest->shippingIntegrationAccount->shippingIntegration) {
                return $this->errorResponse('Manifest not map with API key');
            }

            if (!$manifest->shippingIntegrationAccount->shippingIntegration->api_key) {
                return $this->errorResponse('Manifest have not API key');
            }

            if (isset($manifest->shippingIntegrationAccount->shippingIntegration->status) && !$manifest->shippingIntegrationAccount->shippingIntegration->status) {
                return $this->errorResponse('Manifest map with API key but API inactive');
            }
            $dataShipmentTracking = $manifest->shipmentManifestTrackings->map(function ($item) {
                return ['id' => $item->ship_id_partner];
            })->toArray();

            DB::beginTransaction();
            if ($manifest->barcode_box !== $barcodeBox) {
                $manifest->barcode_box = $barcodeBox;
                $manifest->save();
            }
            $dataBatch = $this->makeScanForm($dataShipmentTracking, $manifest->shippingIntegrationAccount->shippingIntegration->api_key);
            $manifest->batch_id_easypost = $dataBatch->id;
            $manifest->status = ShipmentManifest::STATUS_GENERATING;
            $manifest->state_batch = $dataBatch->state;
            $manifest->save();
            DB::commit();

            return $this->successResponse('Generate success, please wait partner return URL');
        } catch (\Throwable $exception) {
            DB::rollBack();

            return $this->errorResponse($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }

    public function getDataPrintAndReprint($id)
    {
        try {
            $dataManifest = ShipmentManifest::whereNotNull('url')->where('warehouse_id', config('jwt.warehouse_id'))->findOrFail($id);

            return $this->successResponse('Find manifest success', $dataManifest);
        } catch (\Throwable $exception) {
            return $this->errorResponse('Not found');
        }
    }

    public function updateBarcodeBoxByManifestId($barcodeBox, $manifestId)
    {
        try {
            $shipmentManifest = ShipmentManifest::findOrFail($manifestId);
            $shipmentManifest->barcode_box = $barcodeBox;
            $shipmentManifest->save();

            return $this->successResponse('Successfully updated');
        } catch (\Throwable $exception) {
            return $this->errorResponse($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }

    protected function makeScanForm($dataShipmentTracking, $apiKey)
    {
        try {
            $batch = Batch::create(['shipments' => $dataShipmentTracking], $apiKey);

            return $batch;
        } catch (\Throwable $exception) {
            return $exception;
        }
    }
}
