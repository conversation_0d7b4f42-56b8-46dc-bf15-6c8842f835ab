<?php

namespace App\Repositories;

use App\Models\SupplyBox;
use App\Models\SupplyLocation;
use App\Models\SupplyLocationQuantity;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class SupplyLocationRepository extends CommonRepository
{
    const LIMIT = 10;

    public function fetch($input)
    {
        $locationQuantities
            = SupplyLocationQuantity::select('id', 'location_id', DB::raw('SUM(quantity) AS total_quantity'))
                ->groupBy('location_id');
        $supplyBoxes = SupplyBox::select('id', 'location_id', DB::raw('COUNT(*) AS total_box'))
                ->groupBy('location_id');

        $query = SupplyLocation::select('supply_locations.*', 'location_quantities.total_quantity', 'location_boxes.total_box')
            ->where('warehouse_id', $input['warehouse_id'])
            ->leftJoinSub($locationQuantities, 'location_quantities', function (JoinClause $join) {
                $join->on('supply_locations.id', '=', 'location_quantities.location_id');
            })
            ->leftJoinSub($supplyBoxes, 'location_boxes', function (JoinClause $join) {
                $join->on('supply_locations.id', '=', 'location_boxes.location_id');
            });
        if (!empty($input['is_fetch_all'])) {
            return $query->orderBy('id', 'desc')->get();
        }

        if (!empty($input['barcode'])) {
            $barcode = $input['barcode'];
            $query->where('barcode', 'LIKE', '%' . $barcode . '%');
        }

        return $query->latest('supply_locations.id')->paginate($input['limit'] ?? self::LIMIT);
    }

    public function create($input)
    {
        $items = explode("\n", $input['items']);
        $totalInsertOk = 0;
        $currentBarcodeList = SupplyLocation::where('warehouse_id', $input['warehouse_id'])->pluck('barcode');
        foreach ($items as $item) {
            $data = [
                'warehouse_id' => $input['warehouse_id'],
                'barcode' => trim($item),
            ];

            if (!$currentBarcodeList->contains($data['barcode']) && !empty($item)) {
                SupplyLocation::create($data);
                $totalInsertOk++;
                $currentBarcodeList->push($data['barcode']);
            }
        }
        if (!$totalInsertOk) {
            return $this->errorResponse('All supply locations are already existing in Supply Locations.!');
        }

        return response()->json(['totalInsertSuccess' => $totalInsertOk, 'totalInsertFail' => count($items) - $totalInsertOk], Response::HTTP_CREATED);
    }

    public function update($id, $dataUpdate)
    {
        $location = SupplyLocation::find($id);
        if (empty($location)) {
            return $this->errorResponse('Supply location ID not found!');
        }
        SupplyLocation::where('id', $id)->update($dataUpdate);

        return $this->successResponse('Update supply location successfully!');
    }

    public function delete($id)
    {
        $location = SupplyLocation::find($id);
        if (empty($location)) {
            return $this->errorResponse('Supply location ID not found!');
        }

        $totalBox = SupplyBox::where('location_id', $id)->count();
        if ($totalBox) {
            return $this->errorResponse("Can't delete this location because this location contain supply boxes.", Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        SupplyLocation::where('id', $id)->delete();

        return $this->successResponse('Delete supply location successfully!');
    }

    public function updateQuantity($location_id, $supply_id, $quantity = 0)
    {
        $location = SupplyLocationQuantity::where('location_id', $location_id)
            ->where('supply_id', $supply_id)
            ->first();
        if (!empty($location)) {
            SupplyLocationQuantity::where('id', $location->id)->update(['quantity' => intval($location->quantity + $quantity) > 0 ? intval($location->quantity + $quantity) : 0]);
        } else {
            SupplyLocationQuantity::create([
                'location_id' => $location_id,
                'supply_id' => $supply_id,
                'quantity' => $quantity
            ]);
        }
    }
}
