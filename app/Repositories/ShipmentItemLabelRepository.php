<?php


namespace App\Repositories;


use Illuminate\Support\Facades\DB;

class ShipmentItemLabelRepository
{

    const TABLE_NAME = 'shipment_item_label';

    public function checkRecordByLabelIdOrderId($labelId)
    {
        return DB::table(self::TABLE_NAME)
            ->where('label_id', $labelId)
            ->first()
        ;
    }

    public function insertShipmentItemLabel($data)
    {
        DB::table(self::TABLE_NAME)
            ->insert($data)
        ;
    }

}
