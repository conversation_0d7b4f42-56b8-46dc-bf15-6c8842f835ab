<?php

namespace App\Repositories;

use App\Http\Service\PrintAreaService;
use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\ProductColor;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemImage;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class DtfRepository extends CommonRepository
{
    public function countDtf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $count = SaleOrderItemBarcode::countDtfConvert($warehouse_id);
        $total = $count->reduce(function ($i, $obj) {
            return $i += $obj->orderItem->total;
        });

        return [
            'data' => $count,
            'total' => $total
        ];
    }

    public function listPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $printMethod = $input['print_method'];

        return PdfConverted::listPdfDtf($warehouse_id, $limit, $printMethod);
    }

    public function history($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $printMethod = $input['print_method'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;

        return PdfConverted::listHistoryPdfDtf($warehouse_id, $limit, $label_id, $printMethod);
    }

    public function download($input)
    {
        $pdf_converted_id = $input['pdf_converted_id'];
        $pdfConverted = PdfConverted::find($pdf_converted_id);

        if (!$pdfConverted) {
            throw new Exception('Pdf not found', Response::HTTP_NOT_FOUND);
        }

        $pdfConverted->download_status = PdfConverted::ACTIVE;
        $pdfConverted->save();
        $pdfConvertedItems = PdfConvertedItem::where('pdf_converted_id', $pdfConverted->id)->get()->pluck('label_id');
        if (count($pdfConvertedItems) > 0) {
            SaleOrderItemBarcode::whereIn('label_id', $pdfConvertedItems)->update([
                'printed_at' => Carbon::now(),
                'employee_print_id' => (int) $pdfConverted->employee_convert_id
            ]);

            $orderIds = SaleOrderItemBarcode::whereIn('label_id', $pdfConvertedItems)
                                            ->pluck('order_id')
                                            ->unique();

            if (!empty($orderIds)) {
                $printingRepository = new PrintingRepository();

                foreach ($orderIds as $ids) {
                    $printingRepository->updateOrderPrinted($ids);
                    handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $ids);
                }
            }

            // Updated records history
            $updatedItems = SaleOrderItemBarcode::whereIn('label_id', $pdfConvertedItems)
            ->get(['order_id', 'label_id', 'employee_print_id']);

            if ($updatedItems->isNotEmpty()) {
                $dataImportHistory = [];
                foreach ($updatedItems as $item) {
                    $dataImportHistory[] = [
                        'user_id' => null,
                        'employee_id' => $item->employee_print_id,
                        'order_id' => $item->order_id,
                        'type' => SaleOrderHistory::PRINT_NECK,
                        'message' => 'Label ' . $item->label_id . ' printed',
                        'created_at' => Carbon::now()->toDateTimeString(),
                    ];
                }

                SaleOrderHistory::insert($dataImportHistory); // Insert the history data
            }
        }

        return $pdfConverted;
    }

    public function scanLabel($input)
    {
        $label_raw = $input['label_id'];
        $employee_id = $input['employee_id'];
        $warehouse_id = $input['warehouse_id'];

        $printingRepository = new PrintingRepository();
        $label = $printingRepository->removeLabelSide($label_raw);
        $labelPrintSide = $printingRepository->getLabelPrintSide($label_raw);
        $labelObject = SaleOrderItemBarcode::findByLabelPrintedDtf($label, $warehouse_id, $labelPrintSide);
        if (!$labelObject) {
            throw new Exception('Label not found or not print WIP yet or has been generated!', Response::HTTP_NOT_FOUND);
        }

        $labelId = $labelObject->label_id;
        $image = SaleOrderItemImage::findByOrderItemAndSide($labelObject->order_item_id, $labelPrintSide);
        if (!$image) {
            throw new Exception('Image not found!', Response::HTTP_NOT_FOUND);
        }

        // update employee scan
        $labelObject->employee_scan_id = $employee_id;
        $labelObject->save();

        return [
            'label_id' => $labelId,
            'side' => $labelPrintSide,
            'image_id' => $image->id
        ];
    }

    public function scanLabelDTF($input)
    {
        $label_raw = $input['label_id'];
        $employee_id = $input['employee_id'];
        $warehouse_id = $input['warehouse_id'];
        $printMethod = $input['print_method'];

        $printingRepository = new PrintingRepository();
        $label = $printingRepository->removeLabelSide($label_raw);
        $labelPrintSide = $printingRepository->getLabelPrintSide($label_raw);
        $labelObject = SaleOrderItemBarcode::findByLabelPrintedDtf($label, $printMethod, $warehouse_id);
        if (!$labelObject) {
            throw new Exception('Label not found or not print WIP yet or has been generated!', Response::HTTP_NOT_FOUND);
        }
        if ($labelObject->print_method != $printMethod) {
            throw new Exception("print method is {$labelObject->print_method} not support", Response::HTTP_NOT_FOUND);
        }
        $labelId = $labelObject->label_id;

        // update employee scan
        $labelObject->employee_scan_id = $employee_id;
        $labelObject->save();
        $data = [
            'label_id' => $labelId,
            'side' => $labelPrintSide,
        ];
        $printSidesOrderItem = str_split($labelObject->orderItem->print_sides);
        $data['print_area'] = implode(',', $printSidesOrderItem);

        $hasNeckPrintSide = ProductPrintSide::whereIn('name', ProductPrintSide::DTF_NECK_SIDE_NAME)
        ->whereIn('code_wip', $printSidesOrderItem)->exists();
        if ($hasNeckPrintSide) {
            $data['has_neck'] = true;
        }

        return $data;
    }

    public function getOrderItemByLabel($input)
    {
        $data = DB::table('sale_order_item_barcode')
            ->select('sale_order_item_barcode.id', 'sale_order_item_barcode.sku', 'sale_order_item.options', 'sale_order_item.print_side', 'product.name', 'product.sku as product_sku', 'sale_order_item_barcode.label_id', 'sale_order_item_barcode.order_id', 'sale_order.is_fba_order')
            ->join('sale_order_item', 'sale_order_item.sku', '=', 'sale_order_item_barcode.sku')
            ->join('product', 'product.id', '=', 'sale_order_item.product_id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->where('sale_order_item_barcode.label_id', $input['label'])
            ->where('sale_order_item_barcode.is_deleted', '=', 0)
            ->first();
        if (!$data) {
            return null;
        }
        $sku = $data->sku;
        $productColor = ProductColor::where('sku', substr($data->product_sku, 4, 2))->first();
        $data->color_code = $productColor->color_code ?? '';
        $data->order_date = DB::table('sale_order_item_image')
            ->where('sku', $sku)
            ->pluck('order_date')
            ->first();
        $dataOptions = json_decode($data->options);
        $printAreaSevice = new PrintAreaService();
        $data->map_options = $printAreaSevice->mapOptionSaleOrderItem($dataOptions, $sku, $data->order_date);

        return $data;

        $storeName = DB::table('sale_order')
            ->select('store.name', 'sale_order.order_folding_status')
            ->join('store', 'store.id', '=', 'sale_order.store_id')
            ->where('sale_order.id', $data->order_id)
            ->first();

        $data->store_name = $storeName ? $storeName->name : '';
        $data->order_pulled_status = $storeName->order_folding_status;

        return $data;
    }

    public function getImage($map_options, $side)
    {
        foreach ($map_options as $option) {
            if (array_key_exists('print_side', $option) && $option['print_side'] == $side) {
                return $option;
            }
        }

        return [];
    }
}
