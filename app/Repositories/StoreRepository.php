<?php

namespace App\Repositories;

use App\Models\Client;
use App\Models\Country;
use App\Models\SaleOrder;
use App\Models\SaleOrderAccount;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderOnHold;
use App\Models\Setting;
use App\Models\State;
use App\Models\Store;
use App\Models\StoreAddress;
use App\Models\StoreHistory;
use App\Models\StoreOnHoldHistory;
use App\Models\TeamMember;
use App\Models\TeamMemberRolePermission;
use App\Models\User;
use App\Models\UserAccessLog;
use App\Repositories\Contracts\StoreRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class StoreRepository extends CommonRepository implements StoreRepositoryInterface
{
    const ITEM_OF_PAGE = 20;

    const DELIVERY_OPTION_RETURN = 'return';

    const DELIVERY_OPTION_ABANDON = 'abandon';

    public function getAll($params = [])
    {
        $query = Store::select('id', 'name', 'account_id', 'code')
            ->with('account:id,name');

        if (!empty($params['is_active']) && $params['is_active'] == Store::STATUS_ACTIVE) {
            $query->where('is_active', Store::STATUS_ACTIVE);
        }

        if (!empty($params['payment_terms'])) {
            $query->where('payment_terms', $params['payment_terms']);
        }

        return $query->get();
    }

    public function getList(Request $request)
    {
        setTimezone();
        $query = Store::search($request);
        $storeIds = isset($request['store_ids']) ? $request['store_ids'] : '';
        if (isset($storeIds) && is_array($storeIds)) {
            $query = $query->whereIn('store.id', $storeIds);
        }
        if (!empty($request['without_pagination'])) {
            $sortColumn = !empty($request['sort_column']) ? ('store.' . $request['sort_column']) : 'store.name';
            $sortBy = !empty($request['sort_by']) ? $request['sort_by'] : 'ASC';

            return $query->orderBy($sortColumn, $sortBy)->get();
        }

        $input = $request->input();
        $data = $this->getListWithFilter($input);

        return $data;
        //        return $query->orderBy($this->sortColumn, $this->sortBy)->paginate($this->limit);
    }

    public function getListWithFilter($input)
    {
        $query = DB::table('store')
            ->select(
                'store.*',
                'sale_order_account.name as sale_order_account_name',
                'clients.name as client_name', 'user.username as user_name',
                'invoices.invoice_number',
                'invoices.created_at as invoice_created_at',
                'sale_rep.username as sale_rep_user_name',
            )
            ->leftJoin('sale_order_account', 'sale_order_account.id', '=', 'store.account_id')
            ->leftJoin('clients', 'clients.id', '=', 'store.client_id')
            ->leftJoin('user', 'user.id', '=', 'store.user_id')
            ->leftJoin('user as sale_rep', 'sale_rep.id', '=', 'store.sale_rep')
            ->leftJoin('invoices', function ($join) {
                $join->on('invoices.id', '=', DB::raw('(SELECT invoices.id FROM invoices WHERE invoices.store_id = store.id ORDER BY invoices.created_at DESC LIMIT 1)'));
            });

        $storeIds = isset($input['store_ids']) ? $input['store_ids'] : '';
        if (!empty($storeIds) && is_array($storeIds)) {
            $query = $query->whereIn('store.id', $storeIds);
        }

        if (isset($input['name']) && $input['name']) {
            $query->where(function ($query) use ($input) {
                $query->where('store.name', 'LIKE', '%' . $input['name'] . '%')
                    ->orWhere('clients.name', 'LIKE', '%' . $input['name'] . '%')
                    ->orWhere('store.code', 'LIKE', '%' . $input['name'] . '%');
            });
        }

        if (isset($input['sale_order_account']) && $input['sale_order_account']) {
            $query->where('sale_order_account.name', $input['sale_order_account']);
        }
        if (isset($input['store_status'])) {
            $query->where('store.is_active', $input['store_status']);
        }
        if (isset($input['sale_rep'])) {
            $query->where('store.sale_rep', $input['sale_rep']);
        }

        if (isset($input['payment_terms'])) {
            $query->where('store.payment_terms', $input['payment_terms']);
        }

        $sortColumn = !empty($input['sort_column']) ? ('store.' . $input['sort_column']) : 'store.name';
        $sortBy = !empty($input['sort_by']) ? $input['sort_by'] : 'ASC';
        $query = $query->orderBy($sortColumn, $sortBy);

        $limit = self::ITEM_OF_PAGE;
        if (isset($input['limit']) && $input['limit']) {
            $limit = $input['limit'];
        }

        return $query->paginate($limit);
    }

    public static function store($input)
    {
        $dataInsert = [
            'name' => $input['name'],
            'account_id' => $input['account_id'],
            'username' => $input['username'],
            'password' => $input['password'],
            'email' => $input['email'],
            'company' => $input['company'],
            'phone' => $input['phone'],
            'street1' => $input['street1'],
            'street2' => $input['street2'],
            'city' => $input['city'],
            'state' => $input['state'],
            'zip' => $input['zip'],
            'contact_name' => $input['contact_name'],
            'contact_phone' => $input['contact_phone'],
            'contact_email' => $input['contact_email'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        return DB::table('store')
            ->insertGetId($dataInsert);
    }

    public static function update($input)
    {
        $dataUpdate = [
            'name' => $input['name'],
            'account_id' => $input['account_id'],
            'username' => $input['username'],
            'password' => $input['password'],
            'email' => $input['email'],
            'company' => $input['company'],
            'phone' => $input['phone'],
            'street1' => $input['street1'],
            'street2' => $input['street2'],
            'city' => $input['city'],
            'state' => $input['state'],
            'zip' => $input['zip'],
            'contact_name' => $input['contact_name'],
            'contact_phone' => $input['contact_phone'],
            'contact_email' => $input['contact_email'],
            'updated_at' => date('Y-m-d H:i:s'),
            'is_active' => $input['is_active'] ? 1 : 0,
            'is_auto_create_shipping' => $input['is_auto_create_shipping'] ? 1 : 0,
            'date_start_auto_label' => $input['is_auto_create_shipping'] ? date('Y-m-d') : null
        ];

        return DB::table('store')
            ->where('id', $input['id'])
            ->update($dataUpdate);
    }

    public static function storeInfo($input)
    {
        $dataInsert = [
            'name' => $input['name'],
            'code' => $input['code'] ?? null,
            'account_id' => $input['account_id'] ?? null,
            'company' => $input['company'] ?? null,
            'phone' => $input['phone'] ?? null,
            'street1' => $input['street1'] ?? null,
            'street2' => $input['street2'] ?? null,
            'city' => $input['city'] ?? null,
            'state' => $input['state'] ?? null,
            'zip' => $input['zip'] ?? null,
            'country' => $input['country'] ?? null,
            'contact_name' => $input['contact_name'] ?? null,
            'contact_phone' => $input['contact_phone'] ?? null,
            'contact_email' => $input['contact_email'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'is_active' => $input['is_active'] ? 1 : 0,
            'is_on_hold' => $input['is_on_hold'] ? 1 : 0,
            'non_delivery_option' => 'return',
            'template_neck' => $input['template_neck'] ?? 0,
            'limit_order' => $input['limit_order'] ?? 0,
            'client_id' => $input['client_id'] ?? 0,
            'user_id' => Auth::user()->id ?? 0,
            'payment_terms' => $input['payment_terms'] != 0 ? Store::STORE_POSTPAID : Store::STORE_PREPAID,
            'billing_email' => $input['billing_email'] ?? null,
            'sale_rep' => $input['sale_rep'] ?? null,
        ];

        return DB::table('store')
            ->insertGetId($dataInsert);
    }

    public static function checkStoreById($id)
    {
        return DB::table('store')
            ->where('id', $id)
            ->first();
    }

    public static function getDataStoreById($id)
    {
        return Store::with(['storeAddress', 'client', 'userSale'])
            ->where('id', $id)
            ->get()
            ->toArray();
    }

    public static function updateStoreById($dataUpdate, $id)
    {
        return DB::table('store')
            ->where('id', $id)
            ->update($dataUpdate);
    }

    public function saveLog($idStore, $input)
    {
        $keyUpdate = [
            'api_manual',
            'api_merchant_name',
            'api_sample',
            'billing_contact',
            'days_until_bill_due',
            'email',
            'is_calculate_price',
            'is_calculate_shipping',
            'password',
            'username',
            'name'
        ];
        if (!empty($input['order_desk_api_key'])) {
            $keyUpdate[] = 'order_desk_api_key';
        }
        if (!empty($input['order_desk_store_id'])) {
            $keyUpdate[] = 'order_desk_store_id';
        }
        $dataStore = Store::where('id', $idStore)->select($keyUpdate)->first()->toArray();
        $userName = auth()->user()->username;
        $messages = '';
        foreach ($dataStore as $key => $value) {
            if (isset($input[$key]) && $input[$key] != $value) {
                $keyName = str_replace('_', ' ', $key);
                switch ($key) {
                    case 'api_manual':
                    case 'api_merchant_name':
                    case 'api_sample':
                        $oldStatus = $value ? 'active' : 'inactive';
                        $newStatus = $input[$key] ? 'active' : 'inactive';
                        $messages .= "Store {$dataStore['name']}: The $keyName field in the Account & Api tab was updated from '$oldStatus' to '$newStatus' by $userName. \n";
                        break;
                    case 'is_calculate_price':
                        $oldStatus = $value ? 'active' : 'inactive';
                        $newStatus = $input[$key] ? 'active' : 'inactive';
                        $messages .= "Store {$dataStore['name']}: The Automated invoice field in the Account & Api tab was updated from '$oldStatus' to '$newStatus' by $userName. \n";
                        break;
                    case 'is_calculate_shipping':
                        $oldStatus = $value ? 'active' : 'inactive';
                        $newStatus = $input[$key] ? 'active' : 'inactive';
                        $messages .= "Store {$dataStore['name']}: The calculate shipping field in the Account & Api tab was updated from '$oldStatus' to '$newStatus' by $userName. \n";
                        break;
                    case 'billing_contact':
                        $messages .= "Store {$dataStore['name']}: The attention person field in the Account & Api tab was updated from '$value' to '{$input[$key]}' by $userName. \n";
                        break;
                    default:
                        $messages .= "Store {$dataStore['name']}: The $keyName field in the Account & Api tab was updated from '$value' to '{$input[$key]}' by $userName. \n";
                        break;
                }
            }
        }
        if (!empty($input['password'])) {
            $messages .= "Store {$dataStore['name']}: The password field in the Account & Api tab was updated by $userName. \n";
        }
        if ($messages) {
            StoreHistory::create([
                'user_id' => auth()->user()->id,
                'store_id' => $idStore,
                'message' => $messages,
            ]);
            $setting = Setting::where('name', Setting::AlERT_UPDATE_STORE)->first();
            $this->sendGoogleChat($messages, $setting->value);
        }
    }

    public function saveLogGenerateApiKey($store, $token)
    {
        $userName = auth()->user()->username;
        $message = "Store {$store->name}: The API key field in the Account and API key tab was updated from '{$store->token}' to '$token' by $userName. \n";
        StoreHistory::create([
            'user_id' => auth()->user()->id,
            'store_id' => $store->id,
            'message' => $message,
        ]);
        $setting = Setting::where('name', Setting::AlERT_CHANGE_STATUS_CREATE_LABEL_IN_STORE)->first();
        $this->sendGoogleChat($message, $setting->value);
    }

    public static function generateApiKey($id)
    {
        $store = Store::find($id);
        if (!empty($store->token)) {
            $decodeKeySalt = decryptSalt($store->token);
            $arrKey = explode(':', $decodeKeySalt);
            if (array_key_exists('1', $arrKey)) {
                $token = encryptSalt($id . ':' . $arrKey[1] + 1);
            } else {
                $token = encryptSalt($id . ':' . 0);
            }
        } else {
            $token = encryptSalt($id . ':' . 0);
        }
        DB::table('store')
            ->where('id', $id)
            ->update(['token' => $token]);
        Cache::forget($id);

        return $token;
    }

    public function authenticate($params, $request)
    {
        //support login
        if (isset($params['support_login'])) {
            $user = User::where('email', '=', $params['email'])->first(); /// 2
            if (!$user || !Hash::check($params['password'], $user->seller_password)) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
            Auth::guard()->login($user);
            $user = auth()->user();
            UserAccessLog::logUserAccess(
                getClientIp($request),
                $request->header('CF-IPCountry') ?? null,
                $user?->id ?? null,
                'seller',
            );

            return response()->json([
                'access_token' => encryptSalt($user->id . ':' . ':' . ':' . ':' . time() . ':1'),
                'token_type' => 'bearer',
                'user' => $user,
                'is_support_login' => true
            ]);
        }

        //member login
        if (!empty($params['root_username'])) {
            return app(TeamMemberRepository::class)->authenticate($params);
        }

        $client = Client::where('username', $params['username'])->first();

        if (!$client) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        if (!Hash::check($params['password'], $client->password)) {
            $globalPassword = Setting::where('name', 'global_password_seller')->first();

            if (!$globalPassword || $globalPassword->value !== $params['password']) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }
        }

        $store = Store::where(['client_id' => $client->id, 'is_active' => true])->orderBy('created_at', 'DESC')->first();

        if (!$store) {
            return response()->json(['error' => 'No active store'], 401);
        }

        return response()->json([
            'access_token' => encryptSalt(':' . $client->id . ':' . $store->id . ':' . ':' . time()), // 2
            'token_type' => 'bearer',
            'user' => ['id' => $client->id, 'name' => $client->name, 'store_id' => $store->id],
        ]);
    }

    public function getApiKey()
    {
        return Store::select(['token'])->findOrFail(Auth::id());
    }

    public function getStoreUser($userId)
    {
        $user = User::findOrFail($userId);
        $query = Store::select(['id', 'name'])->where('is_active', true);

        if ($user->is_admin != 1 && $user->is_all_store != 1) {
            $storeIds = $user->store_ids ?? [];
            $query = $query->whereIn('id', $storeIds);
        }

        return $query->orderBy('name', 'ASC')->get();
    }

    public function getStoreById($id)
    {
        return Store::find($id);
    }

    public function selectStore($id, $userId)
    {
        $store = Store::select(['id', 'name', 'client_id'])
            ->where('id', $id)
            ->where('is_active', true)
            ->first();

        if (!$store) {
            return response()->json(['error' => 'Store not found'], 404);
        }

        $user = User::find($userId);

        if (!$user || (!$user->is_admin && (!empty($user->store_ids) && !in_array($id, $user->store_ids)))) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return response()->json([
            'access_token' => encryptSalt($userId . ':' . $store->client_id . ':' . $id . ':' . ':' . time()),
            'token_type' => 'bearer',
            'user' => $store,
        ]);
    }

    public function editStore($input)
    {
        DB::beginTransaction();
        try {
            $idStore = $input['id'];
            $input['payment_terms'] = isset($input['payment_terms']) && $input['payment_terms'] == 0 ? Store::STORE_PREPAID : Store::STORE_POSTPAID;
            $store = Store::with(['storeAddressBilling', 'wallet'])->find($idStore);
            if (!$store) {
                return response()->json(['error' => 'Store not found'], 404);
            }
            if ($input['is_on_hold'] != $store->is_on_hold) {
                StoreOnHoldHistory::create([
                    'store_id' => $idStore,
                    'user_id' => \auth()->user()->id,
                    'type' => $input['is_on_hold']

                ]);
                if ($input['is_on_hold'] == Store::IS_ON_HOLD) {
                    $orderIds = SaleOrder::where('store_id', $idStore)->where('order_status', SaleOrder::STATUS_NEW_ORDER)->pluck('id')->toArray();
                    $dataHistory = [];
                    $dataOrderOnHold = [];
                    foreach ($orderIds as $orderId) {
                        $dataOrderOnHold[] = [
                            'user_id' => \auth()->user()->id,
                            'order_id' => $orderId,
                            'store_on_hold' => SaleOrderOnHold::IS_TRUE,
                        ];
                        $dataHistory[] = [
                            'order_id' => $orderId,
                            'user_id' => \auth()->user()->id,
                            'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                            'message' => 'Order status has been changed from New Order to On hold due to unpaid invoice.',
                            'created_at' => date('Y-m-d H:i:s'),
                        ];
                    }
                    SaleOrderOnHold::insert($dataOrderOnHold);
                    $orderOnHoldIds = SaleOrder::where('store_id', $idStore)->where('order_status', SaleOrder::ON_HOLD)->pluck('id')->toArray();
                    SaleOrderOnHold::whereIn('order_id', $orderOnHoldIds)->update(['store_on_hold' => SaleOrderOnHold::IS_TRUE]);

                    SaleOrderHistory::insert($dataHistory);
                    SaleOrder::where('store_id', $idStore)->where('order_status', SaleOrder::STATUS_NEW_ORDER)->update(['order_status' => SaleOrder::STATUS_ON_HOLD]);
                    foreach ($orderIds as $orderId) {
                        handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $orderId);
                    }
                } else {
                    $orderOnHolds = SaleOrderOnHold::with('user')->whereHas('order', function ($q) use ($idStore) {
                        $q->where('order_status', SaleOrder::ON_HOLD)
                            ->where('store_id', $idStore);
                    })
                        ->where('store_on_hold', 1)
                        ->where('visua_on_hold', 0)
                        ->where('manual_on_hold', 0)
                        ->get()->toArray();
                    $dataHistory = [];
                    foreach ($orderOnHolds as $orderOnHold) {
                        $dataHistory[] = [
                            'order_id' => $orderOnHold['order_id'],
                            'user_id' => \auth()->user()->id,
                            'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                            'message' => "Order status has been changed from 'on_hold' to 'new_order' due to the store is no longer on hold.",
                            'created_at' => date('Y-m-d H:i:s'),
                        ];
                    }
                    $orderIds = array_column($orderOnHolds, 'order_id');
                    SaleOrderHistory::insert($dataHistory);
                    SaleOrder::whereIn('id', $orderIds)->update(['order_status' => SaleOrder::STATUS_NEW_ORDER]);
                    SaleOrderOnHold::whereIn('order_id', $orderIds)->where('visua_on_hold', 0)->where('manual_on_hold', 0)->delete();
                    SaleOrderOnHold::whereHas('order', function ($q) use ($idStore) {
                        $q->where('store_id', $idStore);
                    })
                        ->where('store_on_hold', 1)
                        ->where(function ($q) {
                            $q->where('visua_on_hold', 1)
                                ->orWhere('manual_on_hold', 1);
                        })
                        ->update(['store_on_hold' => 0]);
                    foreach ($orderIds as $orderId) {
                        handleJob(SaleOrder::JOB_NOTIFY_STATUS_ORDER, $orderId);
                    }
                }
            }
            $billingAddress = $input['billing_address'] ?? null;
            if (!empty($billingAddress)) {
                StoreAddress::updateOrCreate(
                    ['store_id' => $idStore, 'type_address' => StoreAddressRepository::TYPE_ADDRESS_BILLING],
                    $billingAddress,
                );
            } else {
                StoreAddress::where('store_id', $idStore)->where('type_address', StoreAddressRepository::TYPE_ADDRESS_BILLING)->delete();
            }
            $newBillingAddress = $this->checkDataBillingAddress($store, $input, $billingAddress);
            if ($newBillingAddress) {
                $newBillingAddress['store_id'] = $idStore;
                $input['billing_address_id'] = $this->updateStoreBillingAddress($newBillingAddress);
            }

            unset($input['billing_address']);
            unset($input['id']);
            $keyUpdate = [
                'account_id',
                'street1',
                'name',
                'street2',
                'company',
                'city',
                'phone',
                'country',
                'state',
                'zip',
                'code',
                'template_neck',
                'is_active',
                'contact_email',
                'contact_name',
                'contact_phone',
                'limit_order',
                'is_on_hold',
                'sale_rep',
            ];
            $dataStore = Store::where('id', $idStore)->select($keyUpdate)->first()->toArray();
            $userName = auth()->user()->username;
            $messages = '';
            foreach ($dataStore as $key => $value) {
                if ($input[$key] != $value) {
                    $keyName = str_replace('_', ' ', $key);
                    switch ($key) {
                        case 'is_active':
                            $oldStatus = $value ? 'active' : 'inactive';
                            $newStatus = $input[$key] ? 'active' : 'inactive';
                            $messages .= "Store {$dataStore['name']}: The status field in the Store Info tab was updated from '$oldStatus' to '$newStatus' by $userName. \n";
                            break;
                        case 'is_on_hold':
                            $oldStatus = $value ? 'active' : 'inactive';
                            $newStatus = $input[$key] ? 'active' : 'inactive';
                            $messages .= "Store {$dataStore['name']}: The on-hold field in the Store Info tab was updated from '$oldStatus' to '$newStatus' by $userName. \n";
                            break;
                        case 'account_id':
                            $oldAccount = SaleOrderAccount::where('id', $dataStore['account_id'])->first()->name ?? '';
                            $newAccount = SaleOrderAccount::where('id', $input['account_id'])->first()->name ?? '';
                            $messages .= "Store {$dataStore['name']}: The account field in the Store Info tab was updated from '$oldAccount' to '$newAccount' by $userName. \n";
                            break;
                        case 'street1':
                            $messages .= "Store {$dataStore['name']}: The street 1 field in the Store Info tab was updated from '$value' to '{$input[$key]}' by $userName. \n";
                            break;
                        case 'street2':
                            $messages .= "Store {$dataStore['name']}: The street 2 field in the Store Info tab was updated from '$value' to '{$input[$key]}' by $userName. \n";
                            break;
                        case 'limit_order':
                            if ($value == 0) {
                                $oldLimit = 'Unlimited';
                            } else {
                                $oldLimit = $value;
                            }
                            if ($input[$key] == 0) {
                                $newLimit = 'Unlimited';
                            } else {
                                $newLimit = $input[$key];
                            }
                            $messages .= "Store {$dataStore['name']}: The limit order field in the Store Info tab was updated from '$oldLimit' to '$newLimit' by $userName. \n";
                            break;
                        case 'country':
                            if (empty($value)) {
                                $oldCountry = '';
                            } else {
                                $oldCountry = Country::where('iso2', $value)->first()->name ?? '';
                            }
                            if (empty($input[$key])) {
                                $newCountry = '';
                            } else {
                                $newCountry = Country::where('iso2', $input[$key])->first()->name ?? '';
                            }
                            $messages .= "Store {$dataStore['name']}: The $keyName field in the Store Info tab was updated from '$oldCountry' to '$newCountry' by $userName. \n";
                            break;
                        case 'state':
                            if (empty($value)) {
                                $oldState = '';
                            } else {
                                $oldState = State::where('iso2', $value)->where('country_code', $dataStore['country'])->first()->name ?? '';
                            }
                            if (empty($input[$key])) {
                                $newState = '';
                            } else {
                                $newState = State::where('iso2', $input[$key])->where('country_code', $input['country'])->first()->name ?? '';
                            }
                            $messages .= "Store {$dataStore['name']}: The $keyName field in the Store Info tab was updated from '$oldState' to '$newState' by $userName. \n";
                            break;
                        case 'template_neck':
                            $listTemplateNeck = [
                                0 => 'Swiftpod',
                                1 => 'Printify',
                                2 => 'Others',
                            ];
                            $messages .= "Store {$dataStore['name']}: The template neck field in the Store Info tab was updated from '{$listTemplateNeck[$value]}' to '{$listTemplateNeck[$input[$key]]}' by $userName. \n";
                            break;
                        case 'sale_rep':
                            $oldSaleRep = User::where('id', $dataStore['sale_rep'])->first()->username ?? '';
                            $newSaleRep = User::where('id', $input[$key])->first()->username ?? '';
                            $messages .= "Store {$dataStore['name']}: The $keyName field in the Store Info tab was updated from '$oldSaleRep' to '$newSaleRep' by $userName. \n";
                            break;
                        default:
                            $messages .= "Store {$dataStore['name']}: The $keyName field in the Store Info tab was updated from '$value' to '{$input[$key]}' by $userName. \n";
                            break;
                    }
                }
            }
            self::updateStoreById($input, $idStore);
            DB::commit();
            if ($messages) {
                StoreHistory::create([
                    'user_id' => auth()->user()->id,
                    'store_id' => $idStore,
                    'message' => $messages,
                ]);
                $setting = Setting::where('name', Setting::AlERT_UPDATE_STORE)->first();
                $this->sendGoogleChat($messages, $setting->value);
            }
            DB::commit();

            return response()->json(['data' => 'update success'], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function editStoreShipping($input)
    {
        DB::beginTransaction();
        try {
            $dataStore = Store::find($input['id']);
            $dataUpdate = [
                'is_auto_create_shipping' => $input['is_auto_create_shipping'],
                'date_start_auto_label' => $input['is_auto_create_shipping'] ? date('Y-m-d') : null,
            ];

            self::updateStoreById($dataUpdate, $input['id']);
            if ($input['is_auto_create_shipping'] != $dataStore->is_auto_create_shipping) {
                $setting = Setting::where('name', Setting::AlERT_CHANGE_STATUS_CREATE_LABEL_IN_STORE)->first();
                $message = 'Store ' . $dataStore->name . ' has been changed the status of automatic label create from '
                    . (Store::MODE_AUTO_CREATE_SHIPPING[$dataStore->is_auto_create_shipping] . ' to ' . Store::MODE_AUTO_CREATE_SHIPPING[$input['is_auto_create_shipping']] . ' by ' . auth()->user()->username);
                $this->sendGoogleChat($message, $setting->value);
                StoreHistory::create([
                    'user_id' => auth()->user()->id,
                    'store_id' => $input['id'],
                    'message' => $message,
                ]);
            }
            DB::commit();

            return response()->json(['data' => 'update success'], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function sendGoogleChat($message, $url)
    {
        $params = '{"text": "' . $message . '"}';
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, ($params));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_exec($ch);
        curl_close($ch);
    }

    public function checkDataBillingAddress($store, $params, $paramsAddress)
    {
        $address = $store->storeAddressBilling ?? null;

        $fieldsToCheck = ['name', 'street1', 'city', 'state', 'zip', 'country', 'email', 'street2', 'phone', 'company'];

        foreach ($fieldsToCheck as $field) {
            if (($address->$field ?? null) !== ($paramsAddress[$field] ?? null)) {
                if (!empty($params['billing_contact']) && $store->billing_contact !== ($params['billing_contact'] ?? null)) {
                    $paramsAddress['billing_contact'] = $params['billing_contact'];
                }

                return $paramsAddress;
            }
        }

        $storeField = ['name', 'company', 'phone', 'street1', 'street2', 'state', 'zip', 'city'];

        if (empty($address)) {
            foreach ($storeField as $field) {
                if (($store->$field ?? null) !== ($params[$field] ?? null)) {
                    return $params;
                }
            }
        }

        return null;
    }

    public function updateStoreBillingAddress($params)
    {
        if (empty($params)) {
            return null;
        }
        $data = [
            'name' => $params['name'] ?? null,
            'company' => $params['company'] ?? null,
            'phone' => $params['phone'] ?? null,
            'street1' => $params['street1'] ?? null,
            'street2' => $params['street2'] ?? null,
            'state' => $params['state'] ?? null,
            'zip' => $params['zip'] ?? null,
            'city' => $params['city'] ?? null,
            'country' => $params['country'] ?? null,
            'email' => isset($params['billing_email']) ? $params['billing_email'] : ($params['email'] ?? null),
            'store_id' => $params['store_id'] ?? null,
            'billing_contact' => $params['billing_contact'] ?? null,
        ];

        $walletBillingAddress = WalletBillingAddressRepository::storeBillingAddress($data);

        return $walletBillingAddress->id;
    }

    public function autoGenerateAPIKey()
    {
        $stores = Store::whereNull('token')->get();
        foreach ($stores as $store) {
            DB::beginTransaction();
            $token = self::generateApiKey($store->id);
            $this->saveLogGenerateApiKey($store, $token);
            DB::commit();
        }
    }

    public function getPriorityStores()
    {
        $stores = Setting::where('label', Setting::PRIORITY_STORE)->first();
        if (empty($stores)) {
            return [];
        }

        return explode(',', $stores->value) ?? [];
    }

    public function fetchStoreByClientId($request)
    {
        $query = Store::where(['client_id' => $request->client_id, 'is_active' => true])->select('id', 'name', 'account_id', 'code')->orderBy('created_at', 'desc');

        if (auth()->user()->member_id) {
            $member = TeamMember::where('id', auth()->user()->member_id)->firstOrFail();
            $query->whereIn('id', $member->store_ids);
        }

        return $query->get();
    }

    public function changeStore($request)
    {
        $storeId = $request->store_id ?? null;
        $condition = ['is_active' => true, 'id' => $storeId];

        if (!empty($request->client_id)) {
            $condition['client_id'] = $request->client_id;
        }
        $store = Store::where($condition)
            ->select(
                'id',
                'client_id',
                'name',
                'stripe_id',
                'pm_type',
                'pm_last_four',
                'code',
                'account_id',
                'created_at',
                'updated_at',
                'is_active',
                'username',
                'email',
                'company',
                'phone',
                'street1',
                'street2',
                'city',
                'state',
                'zip',
                'contact_name',
                'contact_phone',
                'contact_email',
                'token',
                'country',
                'non_delivery_option',
                'is_auto_create_shipping',
                'api_manual',
                'api_sample',
                'api_merchant_name',
                'date_start_auto_label',
                'is_calculate_shipping',
                'is_calculate_price',
                'billing_contact',
                'days_until_bill_due',
                'has_note',
                'is_resize',
                'template_neck',
                'limit_order',
                'is_on_hold',
                'payment_terms',
                'user_id',
            )
            ->first();
        if (!$store && $storeId != 0) {
            return response()->json(['data' => 'Store not found'], 404);
        }
        //todo: check if auth user is user model
        $user = auth()->user();
        $userId = $user->user_login_id;

        if ($user->user_login_id) {
            $response = [
                'access_token' => encryptSalt($userId . ':' . ':' . $storeId . ':' . ':' . time()),
                'data' => $store,
            ];
        } elseif ($user->member_id) {
            $member = TeamMember::where('id', $user->member_id)->firstOrFail();

            if (!in_array($storeId, $member->store_ids)) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $response = [
                'access_token' => encryptSalt(':' . $store->client_id . ':' . $storeId . ':' . $member->id . ':' . time()),
                'data' => $store,
            ];
        } else {
            $response = [
                'access_token' => encryptSalt(':' . $store->client_id . ':' . $storeId . ':' . ':' . time()),
                'data' => $store,
            ];
        }

        return response()->json($response, 200);
    }

    public function editStoreSlaExpiration($input)
    {
        DB::beginTransaction();
        try {
            $dataStore = Store::find($input['id']);
            $dataUpdate = [
                'in_stock_date' => $input['in_stock_date'],
                'on_demand_date' => $input['on_demand_date'],
            ];

            self::updateStoreById($dataUpdate, $input['id']);
            $message = 'Store ' . $dataStore->name . ' has changed the status of days to completed for in-stock from '
                . $dataStore->in_stock_date . ' to ' . $input['in_stock_date'] . ' and days to completed on-demand from '
                . $dataStore->on_demand_date . ' to ' . $input['on_demand_date'] . ' by ' . auth()->user()->username;
            StoreHistory::create([
                'user_id' => auth()->user()->id,
                'store_id' => $input['id'],
                'message' => $message,
            ]);
            DB::commit();

            return response()->json(['data' => 'update success'], 200);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            DB::rollBack();

            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function getMeInfo()
    {
        $me = auth()->user();
        $permissions = [];

        //support login
        if ($me->user_login_id) {
            $me = User::where('id', $me->user_login_id)->select('username', 'email')->firstOrFail();
            foreach (TeamMemberRolePermission::listFuction() as $function) {
                $permissions[$function] = TeamMemberRolePermission::VIEW_PERMISSION;
            }

            $me->permissions = $permissions;

            return $me;
        }

        //member login
        if ($me->member_id) {
            $member = TeamMember::with('role.permissions')->findOrFail($me->member_id);

            if (!$member->role->permissions->isEmpty()) {
                foreach ($member->role->permissions as $permission) {
                    $permissions[$permission->function_name] = $permission->permission;
                }
            }

            if ($member->role->name == 'Admin') {
                foreach (TeamMemberRolePermission::listFuction() as $function) {
                    $permissions[$function] = TeamMemberRolePermission::ALL_PERMISSION;
                }
            }

            $member->permissions = $permissions;

            return $member;
        }

        //client login
        if ($me->client_id) {
            $client = Client::select('name', 'username')->findOrFail($me->client_id);
            foreach (TeamMemberRolePermission::listFuction() as $function) {
                $permissions[$function] = TeamMemberRolePermission::ALL_PERMISSION;
            }

            $client->permissions = $permissions;

            return $client;
        }
    }
}
