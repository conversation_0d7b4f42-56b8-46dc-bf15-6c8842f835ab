<?php

namespace App\Repositories;

use App\Models\CostReport;
use App\Models\CostReportHistory;
use App\Models\CostReportLabor;
use App\Models\ProductType;
use App\Models\SaleOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CostReportRepository extends CommonRepository
{
    public function __construct()
    {

    }

    public function create($body)
    {
        $created = false;
        $costReport = CostReport::where('affected_year_at', $body['affected_year_at'])
            ->where('affected_month_at', $body['affected_month_at'])
            ->where('warehouse_id', $body['warehouse_id'])
            ->where('product_type_id', $body['product_type_id'])
            ->first();
        $costReportHistories = CostReportHistory::where('warehouse_id', '=', $body['warehouse_id'])->get();
        DB::beginTransaction();
        try {
            if (!$costReport) {
                $created = true;
                $costReport = CostReport::create($body);
            }
            foreach (CostReport::allTypeCost() as $item) {
                if (!isset($body[$item])) continue;
                if ($costReport->$item != $body[$item]) {
                    $costReport->$item = $body[$item];
                }
                $oldHistory = $costReportHistories
                    ->where('product_type_id', '=', $body['product_type_id'])
                    ->where('affected_year_at', '=', $body['affected_year_at'])
                    ->where('affected_month_at', '=', $body['affected_month_at'])
                    ->where('type', '=', $item)
                    ->last();
                if (!$oldHistory || $oldHistory->value != $body[$item]) {
                    CostReportHistory::create([
                        'warehouse_id' => $body['warehouse_id'],
                        'employee_id' => $body['employee_id'],
                        'product_type_id' => $body['product_type_id'],
                        'type' => $item,
                        'value' => $body[$item],
                        'affected_year_at' => $body['affected_year_at'],
                        'affected_month_at' => $body['affected_month_at'],
                    ]);
                }
            }
            if (!$created) {
                $costReport->save();
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return [
                'status' => false,
                'error' => $exception->getMessage(),
                'message' => 'Add Cost report failed'
            ];
        }

        return $costReport;
    }

    public function fetchCostReport($params)
    {
        $costReports = [];
        CostReport::with('productType')
            ->get()->each(function ($item) use (&$costReports) {
                $costReports[$item->warehouse_id .
                '-' . $item->affected_month_at . '-' . $item->affected_year_at][] = $item;
            });
        foreach ($costReports as $val) {
            $total = array_reduce($val, function ($carry, $item) {
                $data = json_decode($item->options);
                return $carry + ($data->total_item ?? 0);
            }, 0);
            foreach ($val as $result) {
                $result->totalAllItem = $total;
            }
        }
        $costLabor = CostReportLabor::where('warehouse_id', $params['warehouse_id'])->get()->map(function ($item) {
            $item->month = [Carbon::parse($item->pay_period_begin)->month, Carbon::parse($item->pay_period_end)->month];
            $item->year = [Carbon::parse($item->pay_period_begin)->year, Carbon::parse($item->pay_period_end)->year];
            return $item;
        });
        return ProductType::whereIn('name', [ProductType::TEE, ProductType::FLEECE])->pluck('id', 'name')
            ->mapWithKeys(function ($productTypeId, $nameProductType) use ($costReports, $params, $costLabor) {
                $whiteInkData = [];
                $colorInkData = [];
                $pretreatData = [];
                $bagData = [];
                $utilityData = [];
                $shippingData = [];
                $blankData = [];

                $labor = [];
                $affectedYearCostLabor = $costLabor->filter(fn($item) => in_array($params['year'], $item->year));
                for ($i = 1; $i <= 12; $i++) {
                    $currentMonthReport = isset($costReports[$params['warehouse_id'] . '-' . $i . '-' . $params['year']])
                        ? $costReports[$params['warehouse_id'] . '-' . $i . '-' . $params['year']]
                        : null;
                    $currentMonthReport = collect($currentMonthReport)
                        ->where('product_type_id', '=', $productTypeId)
                        ->first();
                    // white ink cc
                    $dataReportInMonth = $currentMonthReport?->options ? json_decode($currentMonthReport->options) : null;

                    $avgConsumeWhite = empty($dataReportInMonth?->total_white_cc) || empty($dataReportInMonth?->total_item)
                        ? 0
                        : round($dataReportInMonth->total_white_cc / $dataReportInMonth->total_item, 2);
                    $whiteInkData[] = [
                        'month' => $i,
                        'total_unit' => $dataReportInMonth?->total_item ?? 0,
                        'total_order' => $dataReportInMonth?->total_order ?? 0,
                        'avg_cc' => $avgConsumeWhite,
                        'cost_per_cc' => $currentMonthReport?->white_ink_cost ?? 0,
                        'cost_per_unit' => empty(floatval($avgConsumeWhite)) || empty(floatval($currentMonthReport?->white_ink_cost))
                            ? 0
                            : round($avgConsumeWhite * $currentMonthReport?->white_ink_cost, 2)
                    ];

                    $avgConsumeColor = empty($dataReportInMonth?->total_color_cc) || empty($dataReportInMonth?->total_item)
                        ? 0
                        : round($dataReportInMonth->total_color_cc / $dataReportInMonth->total_item, 2);
                    $colorInkData[] = [
                        'month' => $i,
                        'total_unit' => $dataReportInMonth?->total_item ?? 0,
                        'total_order' => $dataReportInMonth?->total_order ?? 0,
                        'avg_cc' => $avgConsumeColor,
                        'cost_per_cc' => $currentMonthReport?->color_ink_cost ?? 0,
                        'cost_per_unit' => empty(floatval($avgConsumeColor)) || empty(floatval($currentMonthReport?->color_ink_cost))
                            ? 0
                            : round($avgConsumeColor * $currentMonthReport?->color_ink_cost, 2)
                    ];

                    $pretreatData[] = [
                        'month' => $i,
                        'total_unit' => $dataReportInMonth?->total_item ?? 0,
                        'total_order' => $dataReportInMonth?->total_order ?? 0,
                        'total' => $currentMonthReport?->pretreat_cost,
                        'cost_per_unit' => empty(floatval($currentMonthReport?->pretreat_cost)) || empty(floatval($dataReportInMonth?->total_item))
                            ? 0
                            : round($currentMonthReport?->pretreat_cost / $dataReportInMonth?->total_item, 2)
                    ];

                    $bagData[] = [
                        'month' => $i,
                        'total_unit' => $dataReportInMonth?->total_item ?? 0,
                        'total_order' => $dataReportInMonth?->total_order ?? 0,
                        'cost_per' => $currentMonthReport?->bag_cost,
                        'cost_per_unit' => empty(floatval($currentMonthReport?->bag_cost)) || empty(floatval($dataReportInMonth?->total_item))
                        || empty($dataReportInMonth?->total_order)
                            ? 0
                            : round(
                                $currentMonthReport?->bag_cost /
                                ($dataReportInMonth?->total_item / $dataReportInMonth?->total_order)
                                , 2)
                    ];

                    $utilityData[] = [
                        'month' => $i,
                        'total_unit' => $dataReportInMonth?->total_item ?? 0,
                        'total_order' => $dataReportInMonth?->total_order ?? 0,
                        'total' => $currentMonthReport?->utility_cost,
                        'cost_per_unit' => empty(floatval($currentMonthReport?->utility_cost)) || empty(floatval($dataReportInMonth?->total_item))
                            ? 0
                            : round($currentMonthReport?->utility_cost / $dataReportInMonth?->total_item, 2),
                    ];
                    $shippingData[] = [
                        'month' => $i,
                        'total_unit' => $dataReportInMonth?->total_item ?? 0,
                        'total_order' => $dataReportInMonth?->total_order ?? 0,
                        'total_shipping' => $dataReportInMonth?->total_shipment_cost ?? 0,
                        'cost_per_unit' => empty(floatval($dataReportInMonth?->total_shipment_cost)) || empty(floatval($dataReportInMonth?->total_item))
                            ? 0
                            : round($dataReportInMonth->total_shipment_cost / $dataReportInMonth?->total_item, 2)
                    ];
                    $blankCostTotal = isset($dataReportInMonth?->blank_cost) ? $dataReportInMonth?->blank_cost : 0;
                    $blankData[] = [
                        'month' => $i,
                        'total_unit' => $dataReportInMonth?->total_item ?? 0,
                        'total_order' => $dataReportInMonth?->total_order ?? 0,
                        'total' => $blankCostTotal,
                        'cost_per_unit' => empty(floatval($blankCostTotal)) || empty(floatval($dataReportInMonth?->total_item))
                            ? 0
                            : round($blankCostTotal / $dataReportInMonth?->total_item, 2)
                    ];

                    $totalLaborCost = $affectedYearCostLabor->filter(fn($item) => in_array($i, $item->month))->reduce(function ($carry, $item) use ($i) {
                        if ($item->month[0] < $item->month[1]) {
                            if ($item->month[0] == $i) {
                                $totalDaysInMonthAgo = Carbon::parse($item->pay_period_begin)->daysInMonth - Carbon::parse($item->pay_period_begin)->day + 1;
                                $costPerDays = $item->cost / 14;
                                return $carry + ($totalDaysInMonthAgo * $costPerDays);
                            } else {
                                return $carry + ($item->cost / 14) * Carbon::parse($item->pay_period_end)->day;
                            }
                        }
                        return $carry + $item->cost;
                    }, 0);
                    $labor[] = [
                        'month' => $i,
                        'total_unit' => $dataReportInMonth?->total_item ?? 0,
                        'total_order' => $dataReportInMonth?->total_order ?? 0,
                        'total' => round($totalLaborCost, 2) ?? 0,
                        'cost_per_unit' => empty($totalLaborCost) || empty($currentMonthReport->totalAllItem)
                            ? 0
                            : round($totalLaborCost / $currentMonthReport->totalAllItem, 2)
                    ];
                }
                return [
                    $nameProductType => [
                        "white_ink_cc" => $whiteInkData,
                        "color_ink_cc" => $colorInkData,
                        "pretreat" => $pretreatData,
                        "bag" => $bagData,
                        "utility" => $utilityData,
                        "shipping" => $shippingData,
                        "blank" => $blankData,
                        "labor" => $labor,
                    ]
                ];
            });
    }

    public function calculateBlankCost($params)
    {
        $firstDay = $params['first_day'] = Carbon::parse($params['affected_at'] ?? null)->firstOfMonth()->format('Y-m-d');
        $lastDay = $params['last_day'] = Carbon::parse($params['affected_at'])->addMonth()->firstOfMonth()->format('Y-m-d');
        $query = DB::table('sale_order')
            ->select([DB::raw('sum(sale_order_item.quantity) as total_item, sale_order_item.product_id as product_id')])
            ->join('sale_order_item', 'sale_order.id', '=', 'sale_order_item.order_id')
            ->join('shipment', 'sale_order.shipment_id', '=', 'shipment.id')
            ->where('sale_order.order_status', '=', SaleOrder::STATUS_SHIPPED)
            ->where('sale_order.is_test', '=', SaleOrder::NOT_TEST)
            ->where('sale_order.warehouse_id', '=', $params['warehouse_id'])
            ->where('shipment.created_at', '>=', $firstDay)
            ->where('shipment.created_at', '<=', $lastDay);

        $totalUnitConsume = $this->costReportQueryByTypeProduct($query, $params['product_type'])
            ->groupBy('sale_order_item.product_id')
            ->get();
        $fifo = DB::table('fifo_inventory', 'ff')
            ->select([DB::raw('(ff.value/ ff.end_unit) as cost_avg, ff.product_id')])
            ->where('ff.warehouse_id', '=', $params['warehouse_id'])
            ->where('ff.end_date', '>=', $firstDay)
            ->where('ff.end_date', '<=', $lastDay)
            ->groupBy('ff.product_id')
            ->get()->pluck('cost_avg', 'product_id');
        $totalCostBlank = $totalUnitConsume->reduce(function ($carry, $item) use ($fifo) {
            if ($fifo->has($item->product_id)) {
                return $carry + $item->total_item * $fifo[$item->product_id];
            }
            return $carry;
        }, 0);


        return $totalCostBlank;
    }

    public function fetchCostReportHistory($params)
    {
        $query = CostReportHistory::with(['employee:id,name', 'productType:id,name'])
            ->where('warehouse_id', $params['warehouse_id']);
        if (!empty($params['employee_id'])) {
            $query->where('employee_id', $params['employee_id']);
        }
        if (!empty($params['cost_type'])) {
            $query->whereIn('type', $params['cost_type']);
        }
        if (!empty($params['affected_month_at'])) {
            $query->where('affected_month_at', $params['affected_month_at']);
        }
        if (!empty($params['affected_year_at'])) {
            $query->where('affected_year_at', $params['affected_year_at']);
        }
        return $query->orderBy('created_at', 'desc')->paginate($params['limit']);
    }

    public function getDetailCostReport($params)
    {
        return CostReport::with('productType')->where('warehouse_id', $params['warehouse_id'])
            ->where('affected_year_at', $params['affected_year_at'])
            ->where('affected_month_at', $params['affected_month_at'])
            ->get();
    }

    /**
     * @param $params : (array)[product_type_id => 'Tee'|'Fleece', affected_at=> '2022-12', 'warehouse_id' => 1]
     * @param $type : 'shipment'||'total_item'||'ink_color'||'total_order'
     * @return array|void
     */
    public function calculateCostReport($params, $type)
    {

        $query = DB::table('sale_order')
            ->where('sale_order.order_status', '=', SaleOrder::STATUS_SHIPPED)
            ->where('sale_order.is_test', '=', SaleOrder::NOT_TEST)
            ->where('sale_order.warehouse_id', '=', $params['warehouse_id']);

        $query = $this->costReportTypeQuery($query, $type, $params);

        $query = $this->costReportQueryByTypeProduct($query, $params['product_type']);

        return $query->first();
    }

    private function costReportTypeQuery($query, $type, $params)
    {
        $firstDay = Carbon::parse($params['affected_at'] ?? null)->firstOfMonth()->format('Y-m-d');
        $lastDay = Carbon::parse($params['affected_at'])->addMonth()->firstOfMonth()->format('Y-m-d');

        return match ($type) {
            'shipment' => $query->select([
                DB::raw('round(sum(`shipment`.`shipment_cost`), 2) as total_shipment_cost'),
            ])->join('shipment', 'sale_order.id', '=', 'shipment.order_id')
                ->where('shipment.created_at', '>=', $firstDay)
                ->where('shipment.created_at', '<=', $lastDay),
            'total_item' => $query->select([DB::raw('sum(sale_order_item.quantity) as total_item')])
                ->join('sale_order_item', 'sale_order.id', '=', 'sale_order_item.order_id')
                ->join('shipment', 'sale_order.shipment_id', '=', 'shipment.id')
                ->where('shipment.created_at', '>=', $firstDay)
                ->where('shipment.created_at', '<=', $lastDay),
            'ink_color' => $query->select([
                DB::raw('round(sum(sale_order_item_image.ink_color_cc * sale_order_item.quantity), 2) as total_color_cc'),
                DB::raw('round(sum(sale_order_item_image.ink_white_cc * sale_order_item.quantity), 2) as total_white_cc')
            ])
                ->join('sale_order_item', 'sale_order.id', '=', 'sale_order_item.order_id')
                ->join('sale_order_item_image', 'sale_order_item.id', '=', 'sale_order_item_image.order_item_id')
                ->join('shipment', 'sale_order.shipment_id', '=', 'shipment.id')
                ->where('shipment.created_at', '>=', $firstDay)
                ->where('shipment.created_at', '<=', $lastDay),
            'total_order' => $query->select([
                DB::raw('count(*) as total_order')
            ])->join('shipment', 'sale_order.shipment_id', '=', 'shipment.id')
                ->where('shipment.created_at', '>=', $firstDay)
                ->where('shipment.created_at', '<=', $lastDay),
            default => $query,
        };
    }

    private function costReportQueryByTypeProduct($query, $productType)
    {
        if ($productType == ProductType::FLEECE) {
            $query->whereExists(function ($q) use ($productType) {
                return $q->select('sale_order_item.id')
                    ->from('sale_order_item')
                    ->join('product_style', 'sale_order_item.product_style_sku', '=', 'product_style.sku')
                    ->where('sale_order_item.order_id', '=', DB::raw('`sale_order`.`id`'))
                    ->where('product_style.type', '=', ProductType::FLEECE);
            });
        } else {
            $query->whereExists(function ($q) use ($productType) {
                return $q->select('sale_order_item.id')
                    ->from('sale_order_item')
                    ->join('product_style', 'sale_order_item.product_style_sku', '=', 'product_style.sku')
                    ->where('sale_order_item.order_id', '=', DB::raw('sale_order.id'))
                    ->where('product_style.type', '=', ProductType::TEE);
            })->whereNotExists(function ($q) use ($productType) {
                return $q->select('sale_order_item.id')
                    ->from('sale_order_item')
                    ->join('product_style', 'sale_order_item.product_style_sku', '=', 'product_style.sku')
                    ->where('sale_order_item.order_id', '=', DB::raw('sale_order.id'))
                    ->where('product_style.type', '=', ProductType::FLEECE);
            });
        }
        return $query;
    }

}
