<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class InsightLabelOrdersFulfillmentTimeRepository extends CommonRepository
{
    protected array $orderTypes = [
        'single' => 'Single',
        'multiple' => 'Multiple',
    ];

    protected array $orderFulfillmentTime = [
        'fulfillment_time_0_not_yet' => 'Not Yet in Transit',
        'fulfillment_time_24' => '<= 24 Hours',
        'fulfillment_time_25_29' => '25-29 Hours',
        'fulfillment_time_30_47' => '30-47 Hours',
        'fulfillment_time_48' => '> 48 Hours',
    ];

    public function fulfillmentTimeBuilder($params): Builder
    {
        setTimezone();
        $performanceBuilder = DB::table('sale_order')
            ->select('id')
            ->where('order_type', SaleOrder::ORDER_TYPE_LABEL_ORDER);

        $fulfillmentTimeBuilder = DB::table('sale_order')
            ->join('shipment_label_printed', 'shipment_label_printed.shipment_id', '=', 'sale_order.shipment_id')
            ->join('shipment_transit', 'shipment_transit.shipment_id', '=', 'sale_order.shipment_id');

        if (!empty($params['start'])) {
            $start = Carbon::parse($params['start']);
            $min = $performanceBuilder->clone()
                ->where('created_at', '>=', $start->startOfDay()->format('Y-m-d H:i:s'));

            if (!empty($params['end'])) {
                $end = Carbon::parse($params['end']);
                $min->where('created_at', '<=', $end->endOfDay()->format('Y-m-d H:i:s'));
            }

            $min = $min->orderBy('id')->first();
            $fulfillmentTimeBuilder->where('sale_order.id', '>=', $min->id ?? -1);
        }

        if (!empty($params['end'])) {
            $end = Carbon::parse($params['end']);
            $max = $performanceBuilder->clone()
                ->where('created_at', '<=', $end->endOfDay()->format('Y-m-d H:i:s'));

            if (!empty($params['start'])) {
                $start = Carbon::parse($params['start']);
                $max->where('created_at', '>=', $start->startOfDay()->format('Y-m-d H:i:s'));
            }

            $max = $max->orderByDesc('id')->first();
            $fulfillmentTimeBuilder->where('sale_order.id', '<=', $max->id ?? -1);
        }

        if (!empty($params['order_number'])) {
            $fulfillmentTimeBuilder->where('sale_order.order_number', $params['order_number']);
        }

        $fulfillmentTimeBuilder->where('sale_order.order_type', SaleOrder::ORDER_TYPE_LABEL_ORDER)
            ->where('sale_order.is_test', false)
            ->whereIn('sale_order.order_status', ['shipped', 'in_production', 'on_hold'])
            ->groupBy('sale_order.id');

        return $fulfillmentTimeBuilder;
    }

    public function getOverview($params): array
    {
        $that = $this;
        $params = $this->mappingParamsPreviousPeriod($params);
        $subQuery = $this->fulfillmentTimeBuilder($params)
            ->selectRaw("
                CASE
                    WHEN sale_order.order_quantity = 1 THEN 'single'
                    ELSE 'multiple'
                END AS order_type,
                calc_business_hours(sale_order.created_at,	shipment_transit.in_transit_at) as fulfillment_time
            ");

        $query = DB::table(DB::raw("({$subQuery->toSql()}) as fulfillment_time_tmp"))
            ->mergeBindings($subQuery)
            ->selectRaw("
                order_type,
                CASE
                    WHEN fulfillment_time > 0 AND fulfillment_time < 24 THEN 'fulfillment_time_24'
                    WHEN fulfillment_time >= 24 AND fulfillment_time < 30 THEN 'fulfillment_time_25_29'
                    WHEN fulfillment_time >= 30 AND fulfillment_time < 48 THEN 'fulfillment_time_30_47'
                    WHEN fulfillment_time >= 48 THEN 'fulfillment_time_48'
                    ELSE 'fulfillment_time_0_not_yet'
                END AS fulfillment_time_type,
                count(*) as quantity
            ")
            ->groupByRaw('order_type, fulfillment_time_type');
        $data = $query->get();
        $total = $data->sum('quantity');
        $types = $data->pluck('order_type')->unique()->toArray();
        $fulfillmentTimeType = $data->pluck('fulfillment_time_type')->unique()->sort()->toArray();
        $seriesPieChartOrderType = array_fill_keys($types, 0);
        $seriesPieFulfillmentTimeType = array_fill_keys($fulfillmentTimeType, 0);
        $seriesColumnChart = [];
        $xAxisColumnChart = $query->get()->pluck('fulfillment_time_type')->unique()->sort()->toArray();

        foreach ($types as $type) {
            $seriesColumnChart[$type] = [
                'name' => $this->orderTypes[$type] ?? '',
                'data' => array_fill_keys($xAxisColumnChart, 0),
            ];
        }

        foreach ($data as $item) {
            if (isset($seriesColumnChart[$item->order_type]['data'][$item->fulfillment_time_type])) {
                $seriesColumnChart[$item->order_type]['data'][$item->fulfillment_time_type] += $item->quantity;
            }

            if (isset($seriesPieChartOrderType[$item->order_type])) {
                $seriesPieChartOrderType[$item->order_type] += $item->quantity;
            }

            if (isset($seriesPieFulfillmentTimeType[$item->fulfillment_time_type])) {
                $seriesPieFulfillmentTimeType[$item->fulfillment_time_type] += $item->quantity;
            }
        }

        $fulfillmentTimeList = [];

        foreach ($seriesPieFulfillmentTimeType as $key => $value) {
            $fulfillmentTimeList[] = [
                'fulfillment_time_type' => $key,
                'fulfillment_time_type_label' => $this->orderFulfillmentTime[$key] ?? '',
                'quantity' => $value,
                'rate' => round($value / $total * 100, 2),
            ];
        }

        $seriesColumnChart = array_map(function ($item) {
            $item['data'] = array_values($item['data']);

            return $item;
        }, array_values($seriesColumnChart));

        return [
            'params' => $params,
            'data' => $data,
            'total' => $total,
            'column_chart' => [
                'series' => $seriesColumnChart,
                'categories' => array_values(array_map(function ($item) use ($that) {
                    return $that->orderFulfillmentTime[$item] ?? '';
                }, $xAxisColumnChart)),
            ],
            'pie_chart_order_type' => [
                'series' => array_values($seriesPieChartOrderType),
                'labels' => array_values(array_map(function ($item) use ($that) {
                    return $that->orderTypes[$item] ?? '';
                }, $types)),
            ],
            'pie_chart_fulfillment_time_type' => [
                'series' => array_values($seriesPieFulfillmentTimeType),
                'labels' => array_values(array_map(function ($item) use ($that) {
                    return $that->orderFulfillmentTime[$item] ?? '';
                }, $fulfillmentTimeType)),
            ],
            'fulfillment_time_list' => $fulfillmentTimeList,
            'sql' => interpolateQuery($query),
        ];
    }

    public function getListData($params): array
    {
        $params = $this->mappingParamsPreviousPeriod($params);
        $query = $this->fulfillmentTimeBuilder($params)
            ->selectRaw("
                sale_order.order_number,
                CASE
                    WHEN sale_order.order_quantity = 1 THEN 'single'
                    ELSE 'multiple'
                END AS order_type,
                sale_order.order_quantity,
                sale_order.created_at AS order_date,
                max(shipment_label_printed.printed_date) as printed_date,
                shipment_transit.in_transit_at ,
                calc_business_hours(sale_order.created_at,shipment_transit.in_transit_at) AS fulfillment_time
            ");

        $data = $query->paginate($params['limit'] ?? self::LIMIT);

        foreach ($data as $item) {
            !empty($item->order_date) && $item->order_date = shiftTimezoneToPST($item->order_date)->format('Y-m-d H:i:s');
            !empty($item->printed_date) && $item->printed_date = shiftTimezoneToPST($item->printed_date)->format('Y-m-d H:i:s');
            !empty($item->in_transit_at) && $item->in_transit_at = shiftTimezoneToPST($item->in_transit_at)->format('Y-m-d H:i:s');
            $item->order_type_label = $this->orderTypes[$item->order_type] ?? '';
            $item->fulfillment_time = round($item->fulfillment_time, 2);

            if ($item->fulfillment_time > 0 and $item->fulfillment_time < 24) {
                $item->fulfillment_time_label = $this->orderFulfillmentTime['fulfillment_time_24'];
            } elseif ($item->fulfillment_time >= 24 and $item->fulfillment_time < 30) {
                $item->fulfillment_time_label = $this->orderFulfillmentTime['fulfillment_time_25_29'];
            } elseif ($item->fulfillment_time >= 30 and $item->fulfillment_time < 48) {
                $item->fulfillment_time_label = $this->orderFulfillmentTime['fulfillment_time_30_47'];
            } elseif ($item->fulfillment_time >= 48) {
                $item->fulfillment_time_label = $this->orderFulfillmentTime['fulfillment_time_48'];
            } else {
                $item->fulfillment_time_label = $this->orderFulfillmentTime['fulfillment_time_0_not_yet'];
            }
        }

        return [
            'params' => $params,
            'data' => $data,
            'sql' => interpolateQuery($query),
        ];
    }

    public function mappingParamsPreviousPeriod($params): array
    {
        if (!empty($params['previous'])) {
            $start = Carbon::parse($params['start']);
            $end = Carbon::parse($params['end']);

            if ($start->clone()->startOfMonth()->format('Ymd') == $start->clone()->format('Ymd')
                && $end->clone()->endOfMonth()->format('Ymd') == $end->clone()->format('Ymd')) {
                $params['start'] = $start->clone()->subMonth()->startOfMonth()->format('Y-m-d');
                $params['end'] = $start->clone()->subMonth()->endOfMonth()->format('Y-m-d');
            } else {
                $end = Carbon::parse($params['start'])->subDay();
                $days = Carbon::parse($params['end'])->diff($params['start'])->days;
                $params['start'] = $end->clone()->subDays($days)->format('Y-m-d');
                $params['end'] = $end->clone()->format('Y-m-d');
            }
        }

        return $params;
    }

    public function getPerformance($params)
    {
        $subQuery = $this->fulfillmentTimeBuilder($params)
            ->selectRaw("
                sale_order.order_number,
                calc_business_hours(sale_order.created_at,shipment_transit.in_transit_at) AS fulfillment_time
            ");

        $data = DB::table(DB::raw("({$subQuery->toSql()}) as fulfillment_time_tmp"))
            ->mergeBindings($subQuery)
            ->selectRaw("
                    SUM(CASE WHEN fulfillment_time > 0 AND fulfillment_time < 24 THEN 1 ELSE 0 END) AS orders_0_24,
                    SUM(CASE WHEN fulfillment_time >= 24 AND fulfillment_time < 48 THEN 1 ELSE 0 END) AS orders_24_48,
                    SUM(CASE WHEN fulfillment_time >= 48 AND fulfillment_time < 72 THEN 1 ELSE 0 END) AS orders_48_72,
                    SUM(CASE WHEN fulfillment_time >= 72 AND fulfillment_time < 96 THEN 1 ELSE 0 END) AS orders_72_96,
                    count(*) as total
            ")
            ->first();

        $dataFirst = collect((array) $data)->map(function ($value) {
            return (int) $value;
        })->toArray();

        $total = $dataFirst['total'];

        return [
            [
                'time' => 24,
                'total' => $dataFirst['orders_0_24'],
                'percent' => $total > 0 ? round($dataFirst['orders_0_24'] / $total * 100, 2) : 0,
            ],
            [
                'time' => 48,
                'total' => $dataFirst['orders_24_48'],
                'percent' => $total > 0 ? round($dataFirst['orders_24_48'] / $total * 100, 2) : 0,
            ],
            [
                'time' => 72,
                'total' => $dataFirst['orders_48_72'],
                'percent' => $total > 0 ? round($dataFirst['orders_48_72'] / $total * 100, 2) : 0,
            ],
            [
                'time' => 96,
                'total' => $dataFirst['orders_72_96'],
                'percent' => $total > 0 ? round($dataFirst['orders_72_96'] / $total * 100, 2) : 0,
            ]
        ];
    }
}
