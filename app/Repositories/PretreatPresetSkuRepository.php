<?php


namespace App\Repositories;


use App\Imports\PretreatPresetImport;
use App\Imports\PretreatPresetSKUImport;
use App\Models\PretreatPreset;
use App\Models\PretreatPresetSKU;
use App\Models\PrintingPresetSku;
use App\Models\PrintingPresetSkuHistory;
use App\Models\ProductColor;
use App\Models\ProductStyle;
use App\Repositories\Contracts\PrintingPresetSkuRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class PretreatPresetSkuRepository extends CommonRepository
{
    const LIMIT = 10;

    public function getList(Request $request): LengthAwarePaginator
    {
        $query = PretreatPresetSKU::with(['preset', 'user', 'productColor', 'productStyle']);
        if (!empty($request['pretreat_preset_id'])) {
            $query->where('pretreat_preset_id', $request['pretreat_preset_id']);

        }
        if (!empty($request['style'])) {
            $query->where('style', $request['style']);
        }
        if (!empty($request['color'])) {
            $query->where('color', $request['color']);
        }
        return $query->paginate($request['limit'] ?? self::LIMIT);
    }

    public function getDetail($id): JsonResponse
    {
        $printingPreset = PretreatPresetSKU::find($id);
        if (!$printingPreset) {
            return response()->json([
                'status' => false,
                'message' => 'Printing preset sku not found!'
            ], Response::HTTP_NOT_FOUND);
        }
        return response()->json([
            'status' => true,
            'data' => $printingPreset
        ]);
    }

    private function buildData($request): array
    {
        return [
            'pretreat_preset_id' => $request['pretreat_preset_id'],
            'style' => $request['style'],
            'color' => $request['color'],
        ];
    }

    public function create($request): JsonResponse
    {
        try {
            $data = $this->buildData($request);
            $printingPreset = PretreatPresetSKU::create(array_merge($data, [
                'user_id' => auth()->user()['id']
            ]));
        } catch (\Exception $exception) {
            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json([
            'status' => true,
            'data' => $printingPreset,
            'message' => 'Create pretreat preset sku successfully!'
        ]);
    }

    public function update($id, $request): JsonResponse
    {
        try {
            $data = $this->buildData($request);
            $pretreatPreset = PretreatPresetSKU::where('id', $id)->first();
            if (!$pretreatPreset) {
                return response()->json([
                    'status' => false,
                    'message' => 'Pretreat preset sku not found!'
                ], Response::HTTP_NOT_FOUND);
            }
            foreach ($data as $key => $value) {
                $pretreatPreset[$key] = $value;

            }
            $pretreatPreset->user_id = auth()->user()['id'];

            $pretreatPreset->save();

        } catch (\Exception $exception) {
            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json([
            'status' => true,
            'data' => $pretreatPreset,
            'message' => 'Update pretreat preset sku successfully!'
        ]);
    }


    public function verifyCsvFile($input)
    {
        $data = Excel::toArray(new PretreatPresetSKUImport(), $input->file)[0];
        $invalidData = [];
        $validData = [];

        $dataString = json_encode($data);
        $productStyle = ProductStyle::pluck('sku')->toArray();
        $productColor = ProductColor::pluck('sku')->toArray();
        foreach ($data as $key => $value) {
            $value = array_combine(['preset_name', 'style', 'color'], $value);

            //check duplicate
            $nameCheck = trim(json_encode([$value['preset_name'], $value['style'], $value['color']]), ']');
            if (substr_count($dataString, $nameCheck) > 1) {
                $nameDuplicateExist = trim(json_encode(array_slice($value, 0, 3)), '}');
                if (str_contains(json_encode($invalidData), $nameDuplicateExist)) {
                    continue;
                } else {
                    $invalidData[] = [
                        'row' => $value,
                        'reason' => 'Duplicate data.',
                    ];
                }
            }

            // check value empty
            if (in_array(null, $value, true)) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Empty data',
                ];
                continue;
            }
            if (!in_array($value['style'], $productStyle, true)) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Style not found.',
                ];
                continue;
            }
            if (!in_array($value['color'], $productColor, true)) {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Color not found',
                ];
                continue;
            }
            $preset = PretreatPreset::where('preset_name', $value['preset_name'])->select('id')->first();
            if ($preset) {
                $value['pretreat_preset_id'] = $preset->id;
                unset($value['preset_name']);
                $value['user_id'] = auth()->user()['id'];
                $validData[] = $value;
            } else {
                $invalidData[] = [
                    'row' => $value,
                    'reason' => 'Preset Name not found',
                ];
            }
        }

        return [
            'valid' => $validData,
            'invalid' =>  $invalidData,
            'count_valid' => count($validData),
            'count_invalid' => count($invalidData),
            'total' => count($validData) + count($invalidData),
        ];
    }

    public function importCsv($input)
    {
        $res = $this->verifyCsvFile($input);

        $data = $res['valid'] ?? [];
        if (!$data) return $res;

        try {
            DB::beginTransaction();
            PretreatPresetSKU::upsert(
                $data,
                ['style', 'color'],
                ['pretreat_preset_id', 'style', 'color', 'user_id'],
            );
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return response(['message' => $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response(['message' => 'Import pretreat preset SKU successfully!']);
    }
}
