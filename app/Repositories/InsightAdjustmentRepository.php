<?php

namespace App\Repositories;

use App\Models\Shipment;
use App\Models\Warehouse;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class InsightAdjustmentRepository extends CommonRepository
{
    public function easypostAdjustmentBuilder($params): Builder
    {
        !empty($params['start']) && $params['start'] = Carbon::parse($params['start'])->startOfDay()->format('Y-m-d H:i:s');
        !empty($params['end']) && $params['end'] = Carbon::parse($params['end'])->endOfDay()->format('Y-m-d H:i:s');
        $easypostAdjustmentBuilder = DB::table('shipment')
            ->join('shipment_easypost', 'shipment.id', '=', 'shipment_easypost.shipment_id')
            ->join('easypost_adjustment', 'easypost_adjustment.shipment_id', '=', 'shipment_easypost.easypost_id')
            ->join('sale_order', 'sale_order.id', '=', 'shipment.order_id');

        if (!empty($params['start'])) {
            $easypostAdjustmentBuilder->where('easypost_adjustment.invoice_date', '>=', $params['start']);
        }

        if (!empty($params['end'])) {
            $easypostAdjustmentBuilder->where('easypost_adjustment.invoice_date', '<=', $params['end']);
        }

        if (!empty($params['warehouse_id'])) {
            $easypostAdjustmentBuilder->whereIn('shipment.warehouse_id', $params['warehouse_id']);
        }

        $easypostAdjustmentBuilder->where('easypost_adjustment.adjustment_amount', '<>', 0)
            ->where('shipment.is_deleted', 0)
            ->where('shipment.shipment_account', Shipment::SHIPMENT_ACCOUNT_SWIFTPOD)
            ->where('shipment.carrier_code', 'USPS');

        return $easypostAdjustmentBuilder;
    }

    public function shipmentBuilder($params): Builder
    {
        $shipmentBuilder = DB::table('shipment')
            ->join('sale_order', 'sale_order.id', '=', 'shipment.order_id');

        if (!empty($params['start'])) {
            $start = Carbon::parse($params['start'])->startOfDay()->format('Y-m-d H:i:s');
            $minShipmentId = DB::table('shipment')
                ->select('id')
                ->where('created_at', '>=', $start)
                ->first();

            if (!empty($minShipmentId)) {
                $shipmentBuilder->where('shipment.id', '>=', $minShipmentId->id);
            }

            $shipmentBuilder->where('shipment.created_at', '>=', $start);
        }

        if (!empty($params['end'])) {
            $end = Carbon::parse($params['end'])->endOfDay()->format('Y-m-d H:i:s');
            $shipmentBuilder->where('shipment.created_at', '<=', $end);
        }

        if (!empty($params['warehouse_id'])) {
            $shipmentBuilder->whereIn('shipment.warehouse_id', $params['warehouse_id']);
        }

        $shipmentBuilder->where('shipment.is_deleted', 0)
            ->where('shipment.shipment_account', Shipment::SHIPMENT_ACCOUNT_SWIFTPOD)
            ->where('shipment.carrier_code', 'USPS');

        return $shipmentBuilder;
    }

    public function getOverview($params): array
    {
        $params = $this->mappingParamsPreviousPeriod($params);
        $query = $this->easypostAdjustmentBuilder($params)
            ->selectRaw('
                SUM(easypost_adjustment.adjustment_amount) as `adjustment_total_amount`,
                count(*) as `adjustment_count`
            ');

        return [
            'params' => $params,
            'data' => $query->first(),
            'sql' => interpolateQuery($query),
        ];
    }

    public function totalShipment($params): array
    {
        $params = $this->mappingParamsPreviousPeriod($params);
        $query = $this->shipmentBuilder($params)
            ->selectRaw('COUNT(shipment.id) AS shipment_count');

        return [
            'params' => $params,
            'data' => $query->first(),
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataByWarehouse($params): array
    {
        $subQuery = $this->easypostAdjustmentBuilder($params)
            ->selectRaw('
                shipment.warehouse_id,
                easypost_adjustment.adjustment_reason,
                count(shipment.id) as `adjustment_count`
            ')
            ->groupByRaw('shipment.warehouse_id, easypost_adjustment.adjustment_reason');

        $query = Warehouse::query()
            ->joinSub($subQuery, 'tmp_adjustment', function ($join) {
                $join->on('tmp_adjustment.warehouse_id', '=', 'warehouse.id');
            })
            ->selectRaw('
                tmp_adjustment.*,
                warehouse.name as `warehouse_name`
            ');

        $data = $query->get();
        $series = [];
        $warehouses = $data->pluck('warehouse_name', 'warehouse_id')->toArray();
        $reasons = $data->pluck('adjustment_reason')->unique()->toArray();

        foreach ($reasons as $item) {
            $series[$item] = [
                'name' => $item,
                'data' => array_map(function () {
                    return 0;
                }, $warehouses),
            ];
        }

        foreach ($data as $item) {
            if (isset($series[$item->adjustment_reason]['data'][$item->warehouse_id])) {
                $series[$item->adjustment_reason]['data'][$item->warehouse_id] += $item->adjustment_count;
            }
        }

        $series = array_map(function ($item) {
            $item['data'] = array_values($item['data']);

            return $item;
        }, array_values($series));

        return [
            'categories' => array_values($warehouses),
            'series' => $series,
            'data' => $query->get(),
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataByReason($params): array
    {
        $query = $this->easypostAdjustmentBuilder($params)
            ->selectRaw('
                easypost_adjustment.adjustment_reason,
                count(shipment.id) as `adjustment_count`
            ')
            ->groupByRaw('easypost_adjustment.adjustment_reason');
        $data = $query->get();

        if ($params['start'] && $params['end']) {
            $paramsPrevious = $this->mappingParamsPreviousPeriod($params + ['previous' => true]);
            $queryPrevious = $this->easypostAdjustmentBuilder($paramsPrevious)
                ->selectRaw('
                easypost_adjustment.adjustment_reason,
                count(shipment.id) as `adjustment_count`
            ')
                ->groupByRaw('easypost_adjustment.adjustment_reason');
            $dataPrevious = $queryPrevious->get()->toArray();
        }

        $dataPrevious = !empty($dataPrevious) ? collect($dataPrevious)->pluck('adjustment_count', 'adjustment_reason') : [];

        $data = array_map(function ($item) use ($dataPrevious) {
            if (!empty($dataPrevious[$item->adjustment_reason])) {
                $item->rate_previous = round(($item->adjustment_count - $dataPrevious[$item->adjustment_reason]) / $dataPrevious[$item->adjustment_reason] * 100, 1);
            }

            return $item;
        }, $data->toArray());

        return [
            'data' => $data,
            'sql' => interpolateQuery($query),
            'sql_previous' => !empty($queryPrevious) ? interpolateQuery($queryPrevious) : null,
        ];
    }

    public function getDataByType($params): array
    {
        $params = $this->mappingParamsPreviousPeriod($params);
        $query = $this->easypostAdjustmentBuilder($params)
            ->selectRaw("
                COUNT(shipment.id) as `adjustment_count`,
                CASE
                    WHEN sale_order.order_quantity = 1 THEN 'single_order'
                    ELSE 'multiple_order'
                END as `type`
            ")
            ->groupByRaw('`type`');

        return [
            'params' => $params,
            'data' => $query->get()->pluck('adjustment_count', 'type'),
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataShipmentByType($params): array
    {
        $params = $this->mappingParamsPreviousPeriod($params);
        $query = $this->shipmentBuilder($params)
            ->selectRaw("
                COUNT(shipment.id) AS shipment_count,
                CASE
                    WHEN sale_order.order_quantity = 1 THEN 'single_order'
                    ELSE 'multiple_order'
                END as `type`
            ")
            ->groupByRaw('`type`');

        return [
            'params' => $params,
            'data' => $query->get()->pluck('shipment_count', 'type'),
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataByOrderType($params): array
    {
        $query = $this->easypostAdjustmentBuilder($params)
            ->selectRaw("
                CASE
                    WHEN sale_order.order_quantity = 1 THEN 'Single Order'
                    ELSE 'Multiple Order'
                END as `type`,
                easypost_adjustment.adjustment_reason,
                count(shipment.id) as `adjustment_count`
            ")
            ->groupByRaw('`type`, easypost_adjustment.adjustment_reason');
        $data = $query->get();
        $xAxisColumnChart = $query->get()->pluck('adjustment_reason')->unique()->sortDesc()->toArray();
        $types = $query->get()->pluck('type')->unique()->sortDesc()->toArray();
        $seriesColumnChart = [];
        $seriesPieChart = array_fill_keys($types, 0);
        $labelsPieChart = $types;

        foreach ($types as $type) {
            $seriesColumnChart[$type] = [
                'name' => $type,
                'data' => array_fill_keys($xAxisColumnChart, 0),
            ];
        }

        foreach ($data as $item) {
            if (isset($seriesColumnChart[$item->type]['data'][$item->adjustment_reason])) {
                $seriesColumnChart[$item->type]['data'][$item->adjustment_reason] += $item->adjustment_count;
            }

            if (isset($seriesPieChart[$item->type])) {
                $seriesPieChart[$item->type] += $item->adjustment_count;
            }
        }

        $seriesColumnChart = array_map(function ($item) {
            $item['data'] = array_values($item['data']);

            return $item;
        }, array_values($seriesColumnChart));

        return [
            'data' => $data,
            'column_chart' => [
                'series' => $seriesColumnChart,
                'categories' => array_values($xAxisColumnChart),
            ],
            'pie_chart' => [
                'series' => array_values($seriesPieChart),
                'labels' => array_values($labelsPieChart),
            ],
            'sql' => interpolateQuery($query),
        ];
    }

    public function mappingParamsPreviousPeriod($params): array
    {
        if (!empty($params['previous'])) {
            $start = Carbon::parse($params['start']);
            $end = Carbon::parse($params['end']);

            if ($start->clone()->startOfMonth()->format('Ymd') == $start->clone()->format('Ymd')
                && $end->clone()->endOfMonth()->format('Ymd') == $end->clone()->format('Ymd')) {
                $params['start'] = $start->clone()->subMonth()->startOfMonth()->format('Y-m-d');
                $params['end'] = $start->clone()->subMonth()->endOfMonth()->format('Y-m-d');
            } else {
                $end = Carbon::parse($params['start'])->subDay();
                $days = Carbon::parse($params['end'])->diff($params['start'])->days;
                $params['start'] = $end->clone()->subDays($days)->format('Y-m-d');
                $params['end'] = $end->clone()->format('Y-m-d');
            }
        }

        return $params;
    }
}
