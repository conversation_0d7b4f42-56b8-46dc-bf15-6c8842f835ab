<?php

namespace App\Repositories;
use App\Models\Vendor;
use App\Models\Warehouse;
use App\Repositories\Contracts\VendorRepositoryInterface;
use Validator;

class VendorRepository implements VendorRepositoryInterface
{
    public function fetchAll($input)
    {
        $query = Vendor::where('is_deleted', 0);
        if (!empty($input['type'])) {
            $query->where('type', $input['type']);
        }
        return $query->get();
    }

    public function create($input)
    {
        $warehouse = Vendor::create($input);
        return $warehouse->fresh();
    }

    public function fetch($id)
    {
        return Vendor::find($id);
    }

    public function update($id, $dataUpdate)
    {
        return Vendor::where('id', $id)->update($dataUpdate);
    }

    public function delete($id)
    {
        return Vendor::where('id', $id)->update(['is_deleted'=> 1]);
    }

}
