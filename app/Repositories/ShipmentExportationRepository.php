<?php

namespace App\Repositories;

use App\Models\Exportation;
use App\Models\ExportationPartNumber;
use App\Models\ExportationTracking;
use App\Models\Shipment;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ShipmentExportationRepository extends CommonRepository
{

    protected $partNumberRepository;

    public function __construct(PartNumberRepository $partNumberRepository)
    {
        $this->partNumberRepository = $partNumberRepository;
    }

    public function scanTrackingNumber($input)
    {
        // fix tracking long
        $tracks = [
            $input['tracking_number'],
            substr($input['tracking_number'], 8)
        ];

//        $shipment = Shipment::with('saleOrder', 'items', 'shipmentItemLabels')
        $shipment = Shipment::with('saleOrder', 'items')
            ->whereIn('tracking_number',
                $tracks
            )
            ->where('warehouse_id', config('jwt.warehouse_id'))
            ->where('is_deleted', false)
            ->whereNull('refund_status')
            ->orderByDesc('id')
            ->first();

        // check xem co gan voi order nao khong
        if ($shipment && !$shipment->saleOrder) {
            return $this->handleFail('Tracking number is not associated with any order in my system.');
        }

        // check xem shipment item co hay khong (bo qua voi don sign) -> check product o trong sale order co hay khong
        if ($shipment && $shipment->saleOrder->order_quantity > 1 && $shipment->items->isEmpty()) {
            return $this->handleFail('Order has multiple products, please scan the box barcode.');
        }

//        if($shipment && $shipment->shipmentItemLabels) {
//            $labelNotPartNumber = '';
//            $shipment->shipmentItemLabels->each(function ($item) use (&$labelNotPartNumber) {
//                if (empty($item->part_number_id)) {
//                    $labelNotPartNumber .= $item->label_id . ',';
//                }
//            });
//            if (!empty($labelNotPartNumber)) {
//                return $this->handleFail('Label ' . rtrim($labelNotPartNumber, ',') . ' not associated with any part number in my system.');
//            }
//        }

        $dataExportationTracking = [
            'tracking_number' => $shipment->tracking_number,
            'order_id' => $shipment->saleOrder->id,
            'shipment_id' => $shipment->id,
            'employee_scan_id' => $input['employee_id'],
        ];
        // insert vao trong db
        try {
            DB::beginTransaction();
            if (empty($input['exportation_id'])) {
                $dataExportation = [
                    'number_report' => $input['number_report'],
                    'employee_create_id' => $input['employee_id'],
                    'status' => Exportation::SCANNING,
                ];
                $exportation = Exportation::create($dataExportation);
                $dataExportationTracking['exportation_id'] = $exportation->id;
            } else {
                $dataExportationTracking['exportation_id'] = $input['exportation_id'];
            }
            $exportationTracking = ExportationTracking::create($dataExportationTracking);
            DB::commit();
            return $this->handleSuccess('Scan tracking success', $exportationTracking);
        } catch (\Throwable $e) {
            DB::rollBack();
            return $this->handleFail($e->getMessage());
        }
    }

    public function getAllTrackingNumberByExportationId($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = ExportationTracking::with([
            'shipment.shipmentItemLabels.partNumber.partNumberCountry:name,iso2',
            'saleOrder:id,order_quantity',
        ])
            ->with(['saleOrder.barcodeItems' => function ($q) {
                $q->select('id','part_number_id', 'order_id')
                    ->with('partNumber.country:name,iso2')
                    ->where('is_deleted', false)
                ;
            }])
            ->where('exportation_id', $input['exportation_id']);
        if (!empty($input['tracking_number'])) {
            $query->where('tracking_number', 'like', '%' . $input['tracking_number'] . '%');
        }
        $query->orderBy('id', 'DESC');
        $data = $query->paginate($limit);
        foreach ($data->items() as $key => $value) {
            if ($value?->saleOrder?->order_quantity > 1) {
                $groupedDataPartNumber = [];
                $groupedDataPartNumberCountry = [];
                $totalQuantity = 0;
                foreach ($value?->shipment?->shipmentItemLabels as $shipmentItemLabel) {
                    if($shipmentItemLabel->partNumber) {
                        $groupedDataPartNumber[] = substr($shipmentItemLabel->partNumber?->part_number ?? '', 0, -9);
                        $groupedDataPartNumberCountry[] = $shipmentItemLabel->partNumber?->partNumberCountry?->name ?? '';
                    }
                    $totalQuantity += 1;
                }
                $value->part_number = rtrim(collect($groupedDataPartNumber)->unique()->implode(', '), ', ');
                $value->part_number_country = rtrim(collect($groupedDataPartNumberCountry)->unique()->implode(', '), ', ');
                $value->quantity = $totalQuantity;
            } else {
                foreach ($value->saleOrder?->barcodeItems as $barcodeItem) {
                    $value->part_number = rtrim(substr($barcodeItem?->partNumber?->part_number ?? '', 0, -9), ', ');
                    $value->part_number_country = rtrim($barcodeItem?->partNumber?->partNumberCountry?->name ?? '', ', ');
                    $value->quantity = 1;
                }
            }
        }
        return $data;
    }

    public function deleteShipmentExportationTracking($id)
    {
        $delete = ExportationTracking::where('id', $id)->delete();
        if ($delete) {
            return $this->successResponse('Delete success');
        }
        return $this->errorResponse('Delete fail', Response::HTTP_BAD_REQUEST);
    }

    public function getAllPendingExportation($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = Exportation::withCount('shipmentExportationTrackings')
            ->with(['employee:id,name', 'getLastShipmentExportationTracking.employee:name,id'])
            ->where('status', Exportation::SCANNING)
            ->search($input)
            ->orderBy('updated_at', 'DESC');

        return $query->paginate($limit);
    }

    public function getAllHistoryExportation($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = Exportation::withCount('shipmentExportationTrackings')
            ->with(['employee:id,name', 'getLastShipmentExportationTracking.employee:name,id'])
            ->where('status', Exportation::DOWNLOAD)
            ->search($input)
            ->orderBy('updated_at', 'DESC');
        return $query->paginate($limit);
    }

    public function generateExportation($input)
    {
        try {
            DB::beginTransaction();
            $exportationTrackings = ExportationTracking::with(['shipment', 'shipment.shipmentItemLabels'])
                ->with(['saleOrder.barcodeItems' => function($q) {
                    $q->select('id','part_number_id', 'order_id')
                        ->where('is_deleted', false)
                    ;
                }])
                ->where('exportation_id', $input['exportation_id'])
                ->get();

            $exportationPartNumbers = [];
            foreach ($exportationTrackings as $exportationTracking) {
                    if ($exportationTracking->saleOrder->order_quantity > 1) { // multiple order
                        $totalPartNumberNull = 0;
                        foreach ($exportationTracking->shipment?->shipmentItemLabels as $item) {
                            $partNumberId = $item?->part_number_id;
                            if (!$partNumberId) {
                                $totalPartNumberNull = $totalPartNumberNull + 1;
                                continue;
                            }
                            $exportationPartNumbers[] = [
                                "exportation_id" => $exportationTracking->exportation_id,
                                "quantity" => 1,
                                "part_number_id" => $partNumberId,
                            ];
                        }
                    } else {
                        $exportationPartNumbers[] = [
                            "exportation_id" => $exportationTracking->exportation_id,
                            "quantity" => 1,
                            "part_number_id" => $exportationTracking->saleOrder->barcodeItems->first()?->part_number_id,
                        ];
                    }
            }
            $tempResult = [];
            foreach ($exportationPartNumbers as $record) {
                $partNumberId = $record["part_number_id"];
                if (array_key_exists($partNumberId, $tempResult)) {
                    $tempResult[$partNumberId]["quantity"] += $record["quantity"];
                    $tempResult[$partNumberId]["created_at"] = Carbon::now()->toDateTimeString();
                    $tempResult[$partNumberId]["updated_at"] = Carbon::now()->toDateTimeString();
                } else {
                    $tempResult[$partNumberId] = $record;
                    $tempResult[$partNumberId]["created_at"] = Carbon::now()->toDateTimeString();
                    $tempResult[$partNumberId]["updated_at"] = Carbon::now()->toDateTimeString();
                }
            }
            $mergedExportationPartNumbers = array_values($tempResult);
            ExportationPartNumber::insert($mergedExportationPartNumbers);
            $exportation = Exportation::where('status', Exportation::SCANNING)->where('id', $input['exportation_id'])->first();
            $exportation->status = Exportation::DOWNLOAD;
            $exportation->save();
            DB::commit();
            return $this->successResponse('Successfully generated an exportation report.');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}
