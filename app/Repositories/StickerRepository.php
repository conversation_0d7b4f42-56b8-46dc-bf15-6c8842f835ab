<?php

namespace App\Repositories;

use App\Events\ConvertToAiFileNotification;
use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\ProductStyle;
use App\Models\ProductType;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StickerRepository extends CommonRepository
{
    public function count($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $products = Product::getStickerProductId();
        $results = [];

        if ($products->count()) {
            foreach ($products as $key => $product) {
                $pdf_printed = PdfConverted::findLastCreatedByProductId($product->id, $warehouse_id);

                $results[] = [
                    'last_created_at' => $pdf_printed ? Carbon::parse($pdf_printed->created_at)->format('m/d/Y H:i:s') : null,
                    'product_id' => $product->id,
                    'sku' => $product->sku
                ];
            }
        }

        return [
            'data' => array_values(collect($results)->sortBy('last_created_at')->toArray()),
        ];
    }

    public function list($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];

        return PdfConverted::listSticker($warehouse_id, $limit);
    }

    public function history($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;

        return PdfConverted::listHistorySticker($warehouse_id, $limit, $label_id);
    }

    public function preset($input)
    {
        $product_id = $input['product_id'];
        $product = Product::findByIdAndPresetSkuUv($product_id);

        if (!$product) {
            throw new Exception('Product not found or not to set preset yet!', Response::HTTP_NOT_FOUND);
        }

        if (is_null($product->printingPresetSku->platen_front_size)) {
            throw new Exception('Product not setting platen size yet!', Response::HTTP_BAD_REQUEST);
        }

        $widthFrame = $product->uvPresetSku->page_width * 15;
        $maxItemOnRow = $product->uvPresetSku->max_item_on_row;
        $width = $widthFrame / $maxItemOnRow;

        $platenSize = explode('x', $product->printingPresetSku->platen_front_size);
        $widthItem = (float) $platenSize[0];
        $heightItem = (float) $platenSize[1];

        $height = $heightItem / $widthItem * $width;

        // size preview front end
        return [
            'width_item' => $width,
            'height_item' => $height,
            'width' => $widthFrame,
            'max_item_on_row' => $maxItemOnRow,
        ];
    }

    public function convertToAi($input)
    {
        $pdfConvertedId = $input['pdf_converted_id'];
        $converted = PdfConverted::findToConvertAi($pdfConvertedId);
        if (!$converted) {
            throw new Exception('Must download the file before converting!', Response::HTTP_BAD_REQUEST);
        }
        broadcast(new ConvertToAiFileNotification((array) json_decode($converted->position_illustrator)));

        return [
            'data' => $converted->position_illustrator
        ];
    }

    public function generatePdf($input)
    {
        $employee_id = $input['employee_id'];
        $warehouse_id = config('jwt.warehouse_id');
        $product_id = $input['product_id'] ?? null;
        $options = $input['options'];
        $groupedOptions = [];
        $orderIds = [];

        try {
            DB::beginTransaction();
            foreach ($options as $option) {
                if (!isset($option['image_id']) || !isset($option['side']) || !isset($option['label_id'])) {
                    throw new Exception('Label, side or image not found', Response::HTTP_NOT_FOUND);
                }
                $groupedOptions[$option['side']][] = $option;
            }
            foreach ($groupedOptions as $keySide => $optionsBySide) {
                $printArea = ProductPrintSide::where('code', $keySide)->first();
                if (!$printArea) {
                    throw new Exception('print area not found', Response::HTTP_NOT_FOUND);
                }
                $pdfConverted = PdfConverted::create([
                    'quantity_input' => count($optionsBySide),
                    'quantity' => count($optionsBySide),
                    'product_id' => $product_id,
                    'employee_convert_id' => $employee_id,
                    'warehouse_id' => $warehouse_id,
                    'user_id' => Auth::id(),
                    'convert_percent' => count($optionsBySide),
                    'print_method' => ProductStyle::METHOD_UV,
                    'created_at' => Carbon::now(),
                    'download_status' => PdfConverted::INACTIVE,
                    'options' => $optionsBySide,
                    'convert_status' => PdfConverted::INACTIVE,
                    'type' => ProductType::STICKER,
                    'code_wip' => $printArea->code_wip,
                ]);

                foreach ($optionsBySide as $option) {
                    $barcode = SaleOrderItemBarcode::where('label_id', $option['label_id'])->first();
                    if (!$barcode) {
                        throw new Exception('Barcode not found for label_id: ' . $option['label_id']);
                    }
                    $barcode->save();

                    PdfConvertedItem::create([
                        'label_id' => $option['label_id'],
                        'pdf_converted_id' => $pdfConverted->id,
                        'barcode_id' => $barcode->id,
                        'print_side' => $option['side'],
                        'print_method' => ProductStyle::METHOD_UV,
                    ]);
                    $orderIds[] = $barcode->order_id;
                }
            }
            $orderIds = array_unique($orderIds);

            if (!empty($orderIds)) {
                $printingRepository = new PrintingRepository();

                foreach ($orderIds as $ids) {
                    $printingRepository->updateOrderPrinted($ids);
                }
            }

            DB::commit();

            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
