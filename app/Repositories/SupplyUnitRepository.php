<?php

namespace App\Repositories;
use App\Models\SupplyUnit;
use App\Repositories\Contracts\SupplyUnitRepositoryInterface;
use Validator;

class SupplyUnitRepository extends CommonRepository implements SupplyUnitRepositoryInterface
{
    public function fetchAll($request)
    {
        $query = SupplyUnit::search($request);
        if($request->has('without_pagination')){
            return $query->get();
        }
        return $query->orderBy($this->sortColumn, $this->sortBy)->paginate($this->limit);
    }

    public function create($input)
    {
        $supplyUnit = new SupplyUnit();
        $supplyUnit->name = $input['name'];
        $supplyUnit->save();
        return $supplyUnit;
    }

    public function fetch($id)
    {
        return SupplyUnit::find($id);
    }

    public function update($id, $dataUpdate)
    {
        $supplyUnit = SupplyUnit::find($id);
        if (!$supplyUnit) {
            return null;
        }
        $supplyUnit->name = $dataUpdate['name'];
        $supplyUnit->save();
        return $supplyUnit;
    }

    public function delete($id)
    {
        return SupplyUnit::where('id', $id)->delete();
    }
}
