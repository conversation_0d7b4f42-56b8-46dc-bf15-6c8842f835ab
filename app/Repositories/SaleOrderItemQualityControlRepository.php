<?php

namespace App\Repositories;

use App\Models\Warehouse;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class SaleOrderItemQualityControlRepository extends CommonRepository
{
    const ITEM_OF_PAGE = 20;

    const ID_FILTER = 9512980;

    const DATE_FILTER = '2024-01-01 00:00:00';

    public function list($rawData, $getTotal = null)
    {
        if ($getTotal) {
            return [
                'total' => $this->search($rawData, $getTotal)->count(),
            ];
        }
        $itemOfPage = self::ITEM_OF_PAGE;
        if (isset($rawData['item_of_page']) && $rawData['item_of_page']) {
            $itemOfPage = intval($rawData['item_of_page']);
        }

        $dataSelect = [
            'sale_order_item_quality_control.id as qc_id',
            'sale_order_item_quality_control.status',
            'sale_order_item_quality_control.user_id as qc_user_id',
            'sale_order_item_quality_control.created_at as qc_created_at',
            'sale_order_item_quality_control.updated_at as qc_updated_at',
            'sale_order_item_quality_control.employee_id as qc_employee_id',
            'sale_order_item_quality_control.image as qc_image',
            'sale_order_item_barcode.*'
        ];
        $query = $this->search($rawData);

        if (in_array($rawData['warehouse_id'], Warehouse::WAREHOUSE_MEXICO)) {
            $dataSelect[] = 'country.name as country';
            $query->leftJoin('part_number', 'part_number.id', '=', 'sale_order_item_quality_control.part_number_id')
                ->leftJoin('country', 'country.iso2', '=', 'part_number.country');
        }
        $query->select($dataSelect);
        if (isset($rawData['exportData']) && filter_var($rawData['exportData'], FILTER_VALIDATE_BOOLEAN) == true) {
            return $query->orderBy('sale_order_item_quality_control.created_at', 'DESC')->get();
        }
        $query = $query->orderBy('sale_order_item_quality_control.created_at', 'DESC')->offset((($rawData['page'] ?? 1) - 1) * $itemOfPage)->limit($itemOfPage + 1)->get();

        return new Paginator($query, $itemOfPage, $rawData['page'] ?? 1);
    }

    public function search($rawData, $getTotal = false)
    {
        $query = DB::table('sale_order_item_quality_control')
            ->join('sale_order_item_barcode', 'sale_order_item_quality_control.label_id', '=', 'sale_order_item_barcode.label_id');
        if (!empty($rawData['warehouse_id'])) {
            $query->where('sale_order_item_barcode.warehouse_id', $rawData['warehouse_id']);
        }
        if (isset($rawData['sku']) && $rawData['sku']) {
            $sku_label = $rawData['sku'];
            $query->where(function ($query) use ($sku_label) {
                $query->where('sale_order_item_quality_control.sku', $sku_label)
                    ->orWhere('sale_order_item_quality_control.label_id', $sku_label);
            });
        }
        if (isset($rawData['status']) && $rawData['status'] != '' && $rawData['status'] == 'pass') {
            $query->where('sale_order_item_quality_control.status', '=', 'pass');
        }
        if (isset($rawData['status']) && $rawData['status'] != '' && $rawData['status'] == 'rejected') {
            $query->where('sale_order_item_quality_control.status', '!=', 'pass');
        }
        if (isset($rawData['date']) && $rawData['date']) {
            $startDate = Carbon::parse($rawData['date'][0])->startOfDay()->addHours(7)->toDateTimeString();
            $endDate = Carbon::parse($rawData['date'][1])->endOfDay()->addHours(7)->toDateTimeString();
            if ($startDate >= self::DATE_FILTER && empty($rawData['get_total'])) {
                $query->where('sale_order_item_quality_control.id', '>', self::ID_FILTER);
            }
            $query->whereBetween('sale_order_item_quality_control.created_at', [$startDate, $endDate]);
        }
        if ($getTotal) {
            return $query;
        }
        if (isset($rawData['exportData']) && filter_var($rawData['exportData'], FILTER_VALIDATE_BOOLEAN) == true) {
            return $query->orderBy('sale_order_item_quality_control.created_at', 'DESC');
        }

        return $query;
    }
}
