<?php

namespace App\Repositories;

use App\Models\BatchNumberTemp;
use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemImage;
use App\Models\TimeTracking;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class NeckRepository extends CommonRepository
{
    public function countDtf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $count = SaleOrderItemBarcode::countDtfConvert($warehouse_id);
        $total = $count->reduce(function ($i, $obj) {
            return $i += $obj->orderItem->total;
        });

        return [
            'data' => $count,
            'total' => $total
        ];
    }

    public function listPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];

        return PdfConverted::listPdfDtfNeck($warehouse_id, $limit);
    }

    public function history($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;

        return PdfConverted::listHistoryPdfDtfNeck($warehouse_id, $limit, $label_id);
    }

    public function download($input)
    {
        $pdf_converted_id = $input['pdf_converted_id'];
        $pdfConverted = PdfConverted::find($pdf_converted_id);

        if (!$pdfConverted) {
            throw new Exception('Pdf not found', Response::HTTP_NOT_FOUND);
        }
        $oldDownloadStatus = $pdfConverted->download_status;
        $pdfConverted->download_status = PdfConverted::ACTIVE;
        $pdfConverted->save();
        $pdfConvertedItems = PdfConvertedItem::where('pdf_converted_id', $pdfConverted->id)->get()->pluck('label_id');
        if (count($pdfConvertedItems) > 0) {
            SaleOrderItemBarcode::whereIn('label_id', $pdfConvertedItems)->update([
                'printed_at' => Carbon::now(),
                'employee_print_id' => (int) $pdfConverted->employee_convert_id
            ]);

            $orderIds = SaleOrderItemBarcode::whereIn('label_id', $pdfConvertedItems)
                                            ->pluck('order_id')
                                            ->unique();

            if (!empty($orderIds)) {
                $printingRepository = new PrintingRepository();

                foreach ($orderIds as $ids) {
                    $printingRepository->updateOrderPrinted($ids);
                    handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $ids);
                }
            }
            if ($oldDownloadStatus != PdfConverted::ACTIVE) {
                // Updated records history
                $updatedItems = SaleOrderItemBarcode::whereIn('label_id', $pdfConvertedItems)
                ->get(['order_id', 'label_id', 'employee_print_id']);

                if ($updatedItems->isNotEmpty()) {
                    $dataImportHistory = [];
                    foreach ($updatedItems as $item) {
                        $dataImportHistory[] = [
                            'user_id' => null,
                            'employee_id' => $item->employee_print_id,
                            'order_id' => $item->order_id,
                            'type' => SaleOrderHistory::PRINT_NECK,
                            'message' => 'Label ' . $item->label_id . ' printed',
                            'created_at' => Carbon::now()->toDateTimeString(),
                        ];
                    }

                    SaleOrderHistory::insert($dataImportHistory); // Insert the history data
                }
            }
        }
        TimeTracking::where('id', $input['id_time_checking'])->update(['end_time' => now()]);

        return $pdfConverted;
    }

    public function scanLabel($input)
    {
        $label_raw = $input['label_id'];
        $employee_id = $input['employee_id'];
        $warehouse_id = $input['warehouse_id'];

        $printingRepository = new PrintingRepository();
        $label = $printingRepository->removeLabelSide($label_raw);
        $labelPrintSide = $printingRepository->getLabelPrintSide($label_raw);
        $productPrintSide = ProductPrintSide::findNeckInnerByCode($labelPrintSide);

        if (!$productPrintSide) {
            throw new Exception('The print area must be neck inner!', Response::HTTP_NOT_FOUND);
        }

        // check label generated in other batch neck
        $exists = false;
        $checkGenerated = PdfConvertedItem::findLabelGenerated($label, $labelPrintSide, PrintMethod::NECK);

        if ($checkGenerated) {
            $exists = true;
            $labelObject = SaleOrderItemBarcode::findByLabelPrintedDtfNeck($label, $warehouse_id);
        } else {
            $labelObject = SaleOrderItemBarcode::findByLabelPrintedNeckDtf($label, $warehouse_id, $labelPrintSide);
        }

        if (!$labelObject) {
            throw new Exception('Label not found or not print WIP yet!', Response::HTTP_NOT_FOUND);
        }

        $labelId = $labelObject->label_id;
        $image = SaleOrderItemImage::findByOrderItemAndSide($labelObject->order_item_id, $labelPrintSide);
        if (!$image) {
            throw new Exception('Image not found!', Response::HTTP_NOT_FOUND);
        }

        // check product spec
        $product = Product::getSpecByProductSku($image->product_sku);
        if (!$product) {
            throw new Exception("Product spec of SKU: {$image->product_sku} does not exists", Response::HTTP_NOT_FOUND);
        }

        // update employee scan
        $labelObject->employee_scan_id = $employee_id;
        $labelObject->save();

        return [
            'label_id' => $labelId,
            'side' => $labelPrintSide,
            'image_id' => $image->id,
            'exists' => $exists
        ];
    }

    public function generateBatchNumber()
    {
        $checkBatch = BatchNumberTemp::orderByDesc('id')->first();

        if (!$checkBatch) {
            $batchNumber = '#' . Carbon::now()->format('ymd') . '-' . Carbon::now()->format('Hi');
            BatchNumberTemp::create([
                'batch_number' => $batchNumber,
                'created_at' => Carbon::now()
            ]);

            return $batchNumber;
        }

        $time = now();

        if (Carbon::parse($checkBatch->created_at)->format('y-m-d H:i') >= Carbon::parse($time)->format('y-m-d H:i')) {
            $time = Carbon::parse($checkBatch->created_at)->addMinute();
        }

        $batchNumber = '#' . Carbon::parse($time)->format('ymd') . '-' . Carbon::parse($time)->format('Hi');

        BatchNumberTemp::create([
            'batch_number' => $batchNumber,
            'created_at' => $time
        ]);

        return $batchNumber;
    }
}
