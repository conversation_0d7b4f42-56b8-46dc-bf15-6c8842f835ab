<?php
namespace App\Repositories\Contracts;


interface EmployeeRepositoryInterface {

    public function fetchAll($input);

    public function create($input);

    public function fetch($id);

    public function update($id, $dataUpdate);

    public function delete($id);

    public function timeChecking($input);

    public function logoutTimeChecking($id);

    public function logoutTimeCheckingForMultipleStaging($input);

    public function mobileLogin($input);

    public function loginApp($input);

}
