<?php

namespace App\Repositories;

use App\Models\Supply;
use App\Models\SupplyBox;
use App\Models\SupplyInventory;
use App\Models\SupplyInventoryDeduction;
use App\Models\SupplyQuantity;
use App\Models\TimeTracking;
use App\Repositories\Contracts\SupplyDeductionRepositoryInterface;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class SupplyDeductionRepository extends CommonRepository implements SupplyDeductionRepositoryInterface
{
    public function __construct(protected SupplyLocationRepository $supplyLocationRepository)
    {
        parent::__construct();
    }

    public function fetchAll($request)
    {
        $query = SupplyInventoryDeduction::with(
            [
                'user:id,username',
                'supply:id,name,sku',
                'employee:id,name,code',
                'supplyBox:id,barcode'
            ])
            ->search($request);
        if ($request->has('export')) {
            if (!empty($request['start_date']) && !empty($request['end_date'])) {
                $from = Carbon::parse($request['start_date'])->startOfDay();
                $to = Carbon::parse($request['end_date'])->endOfDay();
                $query->whereBetween('created_at', [$from, $to]);
            }

            return $query->get();
        }

        return $query->orderBy($this->sortColumn, $this->sortBy)->paginate($this->limit);
    }

    public function create($request)
    {
        try {
            $supply = Supply::find($request->supply_id);
            if (!$supply) {
                return response()->json([
                    'supply_id' => ['supply not found']
                ], 422);
            }
            $supplyQuantity = SupplyQuantity::where('supply_id', $request->supply_id)
                ->where('warehouse_id', config('jwt.warehouse_id'))->first();
            if (!$supplyQuantity) {
                return response()->json([
                    'supply_id' => ['supply inventory not found']
                ], 422);
            }
            if ($request->quantity > $supplyQuantity->quantity) {
                return response()->json([
                    'quantity' => ['The quantity may not be greater than ' . $supplyQuantity->quantity . '.']
                ], 422);
            }
            DB::beginTransaction();
            $supplyQuantity->quantity -= $request->quantity;
            $supplyQuantity->save();
            $supplyBox = [];
            if (!empty($request->box_barcode)) {
                $supplyBox = SupplyBox::where([
                    'barcode' => $request->box_barcode
                ])->first();

                $supplyBox->quantity -= $request->quantity;
                $supplyBox->save();
                if ($supplyBox->quantity <= 0) {
                    $supplyBox->quantity = 0;
                    $supplyBox->delete();
                }
                $this->supplyLocationRepository->updateQuantity($supplyBox->location_id, $supplyBox->supply_id, -1 * $request->quantity);
            }

            $supplyInventoryDeduction = new SupplyInventoryDeduction([
                'warehouse_id' => config('jwt.warehouse_id'),
                'supply_id' => $request->supply_id,
                'quantity' => $request->quantity,
                'employee_id' => $request->employee_id,
                'user_id' => auth()->user()->id,
                'box_id' => !empty($supplyBox) ? $supplyBox?->id : null,
                'is_deleted' => SupplyInventoryDeduction::IS_NOT_DELETED
            ]);
            $supplyInventoryDeduction->save();
            $supplyInventory = new SupplyInventory([
                'type' => SupplyInventory::TYPE_OUTPUT,
                'supply_id' => $request->supply_id,
                'object_id' => $supplyInventoryDeduction->id,
                'direction' => SupplyInventory::DIRECTION_OUTPUT,
                'object_name' => SupplyInventory::OBJECT_DEDUCTION,
                'quantity' => $request->quantity,
                'warehouse_id' => config('jwt.warehouse_id'),
                'user_id' => auth()->user()->id,
                'is_deleted' => SupplyInventory::IS_NOT_DELETED
            ]);
            $supplyInventory->save();

            $timeTracking = TimeTracking::find($request->id_time_checking);
            $timeTracking->quantity += $request->quantity;
            $timeTracking->end_time =  date('Y-m-d H:i:s');
            $timeTracking->save();
            DB::commit();

            return $supplyInventoryDeduction;
        } catch (\Exception $exception) {
            DB::rollBack();

            return response()->json([
                'quantity' => ['error' => $exception->getMessage()]
            ], 422);
        }
    }

    public function revert($id)
    {
        try {
            $supplyDeduction = SupplyInventoryDeduction::find($id);
            if (!$supplyDeduction) {
                return response()->json([
                    'id' => ['supply deduction not found']
                ], 422);
            }
            DB::beginTransaction();
            if (!empty($supplyDeduction->box_id)) {
                $supplyBox = SupplyBox::withTrashed()->find($supplyDeduction->box_id);

                $supplyBox->quantity += $supplyDeduction->quantity;
                $supplyBox->deleted_at = null;
                $supplyBox->save();
                $this->supplyLocationRepository->updateQuantity($supplyBox->location_id, $supplyBox->supply_id, $supplyDeduction->quantity);
            }
            $supplyDeduction->is_deleted = SupplyInventoryDeduction::IS_DELETED;
            $supplyDeduction->save();

            $supplyInventory = SupplyInventory::where('object_name', SupplyInventory::OBJECT_DEDUCTION)
                ->where('object_id', $supplyDeduction->id)
                ->first();
            if ($supplyInventory) {
                $supplyInventory->is_deleted = SupplyInventory::IS_DELETED;
                $supplyInventory->save();
            }
            $supplyQuantity = SupplyQuantity::where('supply_id', $supplyDeduction->supply_id)
                ->where('warehouse_id', config('jwt.warehouse_id'))
                ->first();
            $supplyQuantity->quantity += $supplyDeduction->quantity;
            $supplyQuantity->save();
            DB::commit();

            return $supplyDeduction;
        } catch (\Exception $exception) {
            DB::rollBack();

            return response()->json([
                'quantity' => ['error' => $exception->getMessage()]
            ], 422);
        }
    }
}
