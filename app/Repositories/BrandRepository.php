<?php

namespace App\Repositories;

use App\Models\Brand;
use App\Repositories\Contracts\BrandRepositoryInterface;

class BrandRepository implements BrandRepositoryInterface
{
    public function fetchAll($input)
    {
        // TODO: Implement fetchAll() method.
       return Brand::get();
    }

    public function create($input)
    {
        return Brand::create($input);
    }

     public function fetch($id)
     {
         return Brand::find($id);
     }

    public function update($id, $dataUpdate)
    {
        return Brand::where('id', $id)->update($dataUpdate);
    }
    public function delete($id)
    {
        return Brand::where('id', $id)->delete();
    }



}
