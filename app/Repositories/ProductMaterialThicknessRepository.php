<?php

namespace App\Repositories;

use App\Models\ProductMaterialThickness;
use App\Models\ProductPrintSide;

class ProductMaterialThicknessRepository extends CommonRepository
{
    private array $materialThicknessCode = [
        'A' => 0,
        'B' => 1,
        'C' => 2,
        'D' => 3,
        'E' => 4,
        'F' => 5,
        'G' => 6,
        'H' => 7,
    ];

    public function getList($params)
    {
        $data = ProductMaterialThickness::with(['productType:id,name', 'productStyle:id,name', 'productPrintSide:id,name']);

        if (!empty($params['product_type_id'])) {
            $data->where('product_type_id', $params['product_type_id']);
        }

        if (!empty($params['product_style_id'])) {
            $data->where('product_style_id', $params['product_style_id']);
        }

        if (!empty($params['product_print_side_id'])) {
            $data->where('product_print_side_id', $params['product_print_side_id']);
        }

        return $data->orderBy('product_type_id')
            ->orderBy('product_style_id')
            ->paginate($params['limit'] ?? self::LIMIT);
    }

    public function getMaterialThickness($key)
    {
        return array_key_exists($key, $this->materialThicknessCode) ? $this->materialThicknessCode[$key] : null;
    }

    public function getSetting($productTypeId, $productPrintSideCode, $productStyleId)
    {
        $productPrintSide = ProductPrintSide::where('code', $productPrintSideCode)->first();
        $productPrintSideId = $productPrintSide->id ?? -1;
        $settingPriority = ProductMaterialThickness::where('product_type_id', $productTypeId)
            ->where('product_style_id', $productStyleId)
            ->where('product_print_side_id', $productPrintSideId)
            ->first();

        if (!empty($settingPriority)) {
            return $settingPriority;
        }

        $setting = ProductMaterialThickness::where('product_type_id', $productTypeId)
            ->where('product_style_id', $productStyleId)
            ->where('product_print_side_id', 0)
            ->first();

        if (!empty($setting)) {
            return $setting;
        }

        $setting = ProductMaterialThickness::where('product_type_id', $productTypeId)
            ->where('product_style_id', 0)
            ->where('product_print_side_id', $productPrintSideId)
            ->first();

        if (!empty($setting)) {
            return $setting;
        }

        $setting = ProductMaterialThickness::where('product_type_id', $productTypeId)
            ->where('product_style_id', 0)
            ->where('product_print_side_id', 0)
            ->first();

        return $setting;
    }

    public function getMaterialThicknessCode($productTypeId, $productPrintSideCode, $productStyleId, $default = false)
    {
        $setting = $this->getSetting($productTypeId, $productPrintSideCode, $productStyleId);

        if (!empty($setting)) {
            return $this->getMaterialThickness($setting->material_thickness);
        }

        if ($default) {
            return $this->getMaterialThickness('D');
        }

        return null;
    }

    public function store($input)
    {
        return ProductMaterialThickness::create($input);
    }

    public function update($input, $id)
    {
        $record = ProductMaterialThickness::findOrFail($id);
        $record->fill($input);

        return $record->save();
    }

    public function delete($id)
    {
        $record = ProductMaterialThickness::findOrFail($id);

        return $record->delete();
    }
}
