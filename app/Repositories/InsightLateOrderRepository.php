<?php

namespace App\Repositories;

use App\Models\SaleOrder;
use App\Models\Store;
use App\Models\Warehouse;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class InsightLateOrderRepository extends CommonRepository
{
    protected array $productionFilterStatus = [
        'pulled' => 'Pulled',
        'pretreated' => 'Pretreated',
        'printed' => 'Printed',
        'qc' => 'QC’ed',
        'labeled' => 'Labeled',
        'folded' => 'Folded',
        'not_started' => 'Other',
    ];

    protected array $saleOrderStatus = [
        'new_order' => 'New Order',
        'in_production' => 'In Production',
        'shipped' => 'Shipped',
        'cancelled' => 'Cancelled',
        'in_production_cancelled' => 'In Production Cancelled',
        'rejected' => 'Rejected',
        'on_hold' => 'On Hold',
        'draft' => 'Draft',
    ];

    public function saleOrderBuilder($params): Builder
    {
        setTimezone();
        $storeSampleCodes = explode(',', env('STORE_CODE_SAMPLE_ORDER', 'STTR'));
        $storeSampleIds = Store::whereIn('code', $storeSampleCodes)->pluck('id')->toArray();
        $saleOrderBuilder = DB::table('sale_order')
            ->whereNotIn('sale_order.store_id', $storeSampleIds)
            ->where('sale_order.is_test', false)
            ->whereIn('sale_order.order_status', [SaleOrder::NEW_ORDER, SaleOrder::IN_PRODUCTION, SaleOrder::ON_HOLD])
            ->whereRaw('calc_business_hours(sale_order.created_at, NOW()) > 72');

        if (!empty($params['store_id'])) {
            $saleOrderBuilder->whereIn('sale_order.store_id', $params['store_id']);
        }

        if (!empty($params['warehouse_id'])) {
            $saleOrderBuilder->whereIn('sale_order.warehouse_id', $params['warehouse_id']);
        }

        return $saleOrderBuilder;
    }

    public function builder($params): Builder
    {
        return $this->saleOrderBuilder($params)
            ->join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->join('sale_order_item_barcode', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item.id')
            ->whereNotNull('sale_order_item_barcode.print_method');
    }

    public function getDataPrintMethod($params)
    {
        $query = $this->builder($params)->selectRaw('
            count(DISTINCT sale_order.id) AS `quantity`,
            sale_order_item_barcode.print_method,
            CASE
                WHEN calc_business_hours (sale_order.created_at, NOW()) > 72 AND calc_business_hours (sale_order.created_at, NOW()) <= 96 THEN "3-4"
                WHEN calc_business_hours (sale_order.created_at, NOW()) > 96 AND calc_business_hours (sale_order.created_at, NOW()) <= 120 THEN "4-5"
                ELSE "5-"
            END AS `days`
        ')->groupByRaw('sale_order_item_barcode.print_method, `days`');
        $data = $query->get();
        $printMethods = collect($data)->whereNotNull('print_method')
            ->where('print_method', '<>', '')
            ->sortDesc()
            ->pluck('print_method')
            ->unique()
            ->toArray();
        $printMethods = array_fill_keys($printMethods, 0);
        $output = [
            '3-4' => [
                'name' => '3-4 days',
                'data' => $printMethods,
            ],
            '4-5' => [
                'name' => '4-5 days',
                'data' => $printMethods,
            ],
            '5-' => [
                'name' => 'Over 5 days',
                'data' => $printMethods,
            ]
        ];

        foreach ($data as $item) {
            foreach ($output as $key => &$outputItem) {
                if ($key == $item->days && isset($outputItem['data'][$item->print_method])) {
                    $outputItem['data'][$item->print_method] += $item->quantity;
                }
            }
        }

        foreach ($output as &$outputItem) {
            $outputItem['data'] = array_values($outputItem['data']);
        }

        return [
            'data_raw' => $data,
            'categories' => array_keys($printMethods),
            'series' => array_values($output),
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataPrintMethodTable($params)
    {
        $sortColumn = $params['sort_column'] ?? 'qty_orders';
        $sortBy = $params['sort_by'] ?? 'DESC';

        $subQuery = $this->builder($params)
            ->selectRaw('
                sale_order.id,
                sale_order.store_id,
                sale_order.order_quantity
            ')
            ->groupBy('sale_order.id');
        $query = Store::query()
            ->joinSub($subQuery, 'tmp_orders', function ($join) {
                $join->on('store.id', '=', 'tmp_orders.store_id');
            })->selectRaw('
                store.id,
                store.name,
                store.code,
                count(tmp_orders.id) as `qty_orders`,
                sum(tmp_orders.order_quantity) as `qty_order_items`
            ')
            ->groupBy('store.id');
        if ($sortColumn == 'qty_orders') {
            $query->orderByRaw("$sortColumn $sortBy, qty_order_items DESC");
        } elseif ($sortColumn == 'qty_order_items') {
            $query->orderByRaw("$sortColumn $sortBy, qty_orders DESC");
        }
        $total = $query->get()->sum('qty_orders');
        $data = $query->get()->map(function ($item) use ($total) {
            $item->rate = $total > 0 ? round($item->qty_orders / $total * 100, 1) : null;

            return $item;
        });

        return [
            'data' => $data->toArray(),
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataOrderType($params)
    {
        $subQuery = $this->builder($params)
            ->selectRaw('
                sale_order_item.order_id,
                CASE
                    WHEN sale_order.order_quantity = 1 THEN "S"
                    ELSE "M"
                END AS `order_type`
            ')
            ->groupBy('sale_order.id');

        $query = DB::table(DB::raw("({$subQuery->toSql()}) as tmp_orders"))
            ->selectRaw('
                tmp_orders.order_type,
                count(tmp_orders.order_id) as `qty_order_items`
            ')
            ->groupBy('tmp_orders.order_type')
            ->mergeBindings($subQuery);
        $data = $query->get();

        return [
            'data_raw' => $data,
            'series' => $data->pluck('qty_order_items'),
            'labels' => $data->pluck('order_type')->map(function ($item) {
                return $item == 'S' ? 'Single' : 'Multiple';
            }),
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataOrderWarehouse($params)
    {
        $warehouses = Warehouse::all()->pluck('name', 'id')->toArray();
        $query = $this->builder($params)
            ->selectRaw('
                sale_order.warehouse_id,
	            COUNT(DISTINCT sale_order.id) as `quantity`
            ')
            ->groupBy('sale_order.warehouse_id');
        $data = $query->get();

        return [
            'data_raw' => $data,
            'series' => $data->pluck('quantity'),
            'labels' => $data->pluck('warehouse_id')->map(function ($warehouseId) use ($warehouses) {
                return $warehouses[$warehouseId] ?? 'Unknown';
            }),
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataProductSkuTable($params)
    {
        $sortColumn = $params['sort_column'] ?? 'qty_order_items';
        $sortBy = $params['sort_by'] ?? 'DESC';
        $subQuery = $this->builder($params)
            ->selectRaw('
                sale_order_item.id,
                sale_order_item.product_sku,
                sale_order_item.quantity
            ')
            ->groupBy('sale_order_item.id');

        $query = DB::table(DB::raw("({$subQuery->toSql()}) as tmp_orders"))
            ->join('product', 'tmp_orders.product_sku', '=', 'product.sku')
            ->selectRaw('
                tmp_orders.product_sku,
                product.name,
	            sum(tmp_orders.quantity) as `qty_order_items`
            ')
            ->groupBy('tmp_orders.product_sku')
            ->orderByRaw("$sortColumn $sortBy")
            ->mergeBindings($subQuery);
        $data = $query->get();

        return [
            'data' => $data,
            'sql' => interpolateQuery($query),
        ];
    }

    public function getDataOverviewTotal($params)
    {
        $subQuery = $this->builder($params)
            ->selectRaw('
                sale_order.id,
                sale_order.store_id,
                sale_order.order_quantity
            ');

        if (!empty($params['order_number'])) {
            $subQuery->where('sale_order.order_number', $params['order_number']);
        }

        $subQuery->groupBy('sale_order.id');
        $query = DB::table(DB::raw("({$subQuery->toSql()}) as tmp_orders"))
            ->selectRaw('
                count(tmp_orders.id) as `qty_orders`,
	            sum(tmp_orders.order_quantity) as `qty_order_items`
            ')
            ->mergeBindings($subQuery);
        $data = $query->first();

        return [
            'data' => $data,
            'sql' => interpolateQuery($query),
        ];
    }

    public function getList($params, $isPaginate = true)
    {
        $sortColumn = $params['sort_column'] ?? 'created_at';
        $sortBy = $params['sort_by'] ?? 'ASC';
        $query = $this->builder($params)
            ->selectRaw('
                sale_order.id,
                sale_order.order_number,
                sale_order.created_at,
                sale_order.order_status,
                sale_order.order_quantity,
                calc_business_hours(sale_order.created_at, NOW()) AS `late_time_number`,
                sale_order.store_id,
                sale_order.tag,
                sale_order.production_status,
                sale_order.order_type,
                sale_order.is_create_manual,
                sale_order.is_manual,
                sale_order.is_fba_order,
                sale_order.is_xqc,
                sale_order.shipping_method,
                shipment.tracking_number,
                sale_order_item.sku as sale_order_item_sku,
                sale_order_item.quantity as sale_order_item_quantity
            ')
            ->leftJoin('shipment', 'shipment.id', '=', 'sale_order.shipment_id');

        if (!empty($params['order_number'])) {
            $query->where('sale_order.order_number', $params['order_number']);
        }

        $query->groupBy('sale_order_item.id');

        if (!empty($params['get_total'])) {
            return $query->get()->count();
        }

        $query->orderByRaw("$sortColumn $sortBy");

        if ($isPaginate) {
            $data = $query->simplePaginate($params['limit'] ?? 25);
        } else {
            $data = $query->get();
        }

        $stores = DB::table('store')
            ->select(['id', 'name', 'code'])
            ->whereIn('id', $data->pluck('store_id')->unique()->toArray())
            ->get();

        foreach ($data as &$item) {
            $store = $stores->where('id', $item->store_id)->first();

            if ($item->late_time_number > 72 && $item->late_time_number <= 96) {
                $item->late_time = '3-4 days';
            } elseif ($item->late_time_number > 96 && $item->late_time_number <= 120) {
                $item->late_time = '4-5 days';
            } else {
                $item->late_time = 'Over 5 days';
            }

            $item->order_date = shiftTimezoneToPST($item->created_at)->format('m/d/y') ?? '';
            $item->order_status_label = $this->saleOrderStatus[$item->order_status] ?? '';
            $item->production_status_label = $this->productionFilterStatus[$item->production_status] ?? '';
            $item->store_code = $store->code ?? '';
            $item->store_name = $store->name ?? '';
        }

        return [
            'data' => $data,
            'sql' => interpolateQuery($query),
        ];
    }
}
