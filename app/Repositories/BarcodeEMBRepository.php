<?php

namespace App\Repositories;

use App\Models\BarcodePrinted;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\ProductStyle;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class BarcodeEMBRepository
{
    public function countPendingFba($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;

        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total, barcode_printed_time.printed_at')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($warehouse_id, $store_id) {
                $join->on('barcode_printed_time.is_fba', DB::raw(1));
                $join->where('barcode_printed_time.warehouse_id', $warehouse_id);
                $join->where('barcode_printed_time.store_id', $store_id);
                $join->whereNull('barcode_printed_time.account_id');
                $join->whereNull('barcode_printed_time.is_xqc');
                $join->whereNull('barcode_printed_time.is_eps');
                $join->whereNull('barcode_printed_time.style_sku');
                $join->whereNull('barcode_printed_time.is_manual');
                $join->whereNull('barcode_printed_time.is_reprint');
                $join->whereNull('barcode_printed_time.is_reroute');
            });

        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }
        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        }
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);
        $st->where('sale_order.is_fba_order', SaleOrder::ACTIVE);
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        return $st->first();
    }

    public function countPendingReroute($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;

        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total, barcode_printed_time.printed_at')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($warehouse_id, $store_id) {
                $join->on('barcode_printed_time.is_reroute', DB::raw(1));
                $join->where('barcode_printed_time.warehouse_id', $warehouse_id);
                $join->where('barcode_printed_time.store_id', $store_id);
                $join->whereNull('barcode_printed_time.account_id');
                $join->whereNull('barcode_printed_time.is_xqc');
                $join->whereNull('barcode_printed_time.is_eps');
                $join->whereNull('barcode_printed_time.style_sku');
                $join->whereNull('barcode_printed_time.is_manual');
                $join->whereNull('barcode_printed_time.is_reprint');
                $join->whereNull('barcode_printed_time.is_fba');
            });

        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }
        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        }
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->whereNotNull('sale_order_item_barcode.employee_reroute_id');
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        return $st->first();
    }

    public function countPendingReprint($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;

        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total, barcode_printed_time.printed_at')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($warehouse_id, $store_id) {
                $join->on('barcode_printed_time.is_reprint', DB::raw(1));
                $join->where('barcode_printed_time.warehouse_id', $warehouse_id);
                $join->where('barcode_printed_time.store_id', $store_id);
                $join->whereNull('barcode_printed_time.account_id');
                $join->where('barcode_printed_time.is_xqc', null);
                $join->where('barcode_printed_time.is_eps', null);
                $join->where('barcode_printed_time.style_sku', null);
                $join->where('barcode_printed_time.is_manual', null);
                $join->whereNull('barcode_printed_time.is_reroute');
                $join->whereNull('barcode_printed_time.is_fba');
            });

        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }
        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        }
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->whereNotNull('sale_order_item_barcode.label_root_id');
        $st->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE);
        $st->where('sale_order_item_barcode.reprint_status', 0);
        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL);
        $st->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        return $st->first();
    }

    public function countPendingManualProcess($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;

        $st = DB::table('sale_order_item_barcode')
            ->selectRaw('COUNT(*) AS total, barcode_printed_time.printed_at')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($warehouse_id, $store_id) {
                $join->where('barcode_printed_time.warehouse_id', $warehouse_id);
                $join->where('barcode_printed_time.store_id', $store_id);
                $join->whereNull('barcode_printed_time.account_id');
                $join->where('barcode_printed_time.is_xqc', null);
                $join->where('barcode_printed_time.is_eps', null);
                $join->where('barcode_printed_time.style_sku', null);
                $join->whereNull('barcode_printed_time.is_reroute');
                $join->whereNull('barcode_printed_time.is_fba');
                $join->where('barcode_printed_time.is_manual', SaleOrder::IS_MANUAL);
            });

        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }
        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        }

        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order.is_manual', SaleOrder::IS_MANUAL);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->where('sale_order_item.ink_color_status', SaleOrderItem::ACTIVE);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        return $st->first();
    }

    public function countPendingStyle($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;

        $st = DB::table('sale_order_item_barcode')
            ->select(DB::raw('product_style.name, sale_order_item.product_style_sku, COUNT(*) total, barcode_printed_time.printed_at'))
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($warehouse_id, $store_id) {
                $join->on('barcode_printed_time.style_sku', 'sale_order_item.product_style_sku');
                $join->where('barcode_printed_time.print_method', ProductStyle::METHOD_EMB);
                $join->where('barcode_printed_time.warehouse_id', $warehouse_id);
                $join->where('barcode_printed_time.store_id', $store_id);
                $join->whereNull('barcode_printed_time.account_id');
                $join->where('barcode_printed_time.is_xqc', null);
                $join->where('barcode_printed_time.is_eps', null);
                $join->whereNull('barcode_printed_time.is_reprint');
                $join->whereNull('barcode_printed_time.is_manual');
                $join->whereNull('barcode_printed_time.is_reroute');
                $join->whereNull('barcode_printed_time.is_fba');
                $join->whereNull('barcode_printed_time.color_sku');
            })
            ->where('sale_order.is_xqc', 0)
            ->where('sale_order.is_eps', 0)
            ->where('sale_order_item.ink_color_status', 1)
            ->where('sale_order_item_barcode.barcode_printed_id', 0)
            ->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);

        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }
        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        }
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058));
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565));
        $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
        $st->whereNull('sale_order_item_barcode.label_root_id');
        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);
        $st->groupBy('sale_order_item.product_style_sku')
            ->orderBy('barcode_printed_time.printed_at', 'ASC')
            ->orderBy('total', 'DESC');

        return $st->get();
    }

    public function countPendingStyleXQC($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->select(DB::raw('barcode_printed_time.printed_at as printed_at, COUNT(*) as total'))
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($store_id, $warehouse_id) {
                $join->on('barcode_printed_time.is_xqc', '=', DB::raw(1));
                $join->where('barcode_printed_time.store_id', $store_id);
                $join->where('barcode_printed_time.style_sku', null);
                $join->where('barcode_printed_time.warehouse_id', $warehouse_id);
                $join->whereNull('barcode_printed_time.account_id');
                $join->whereNull('barcode_printed_time.is_reprint');
                $join->whereNull('barcode_printed_time.is_manual');
                $join->whereNull('barcode_printed_time.is_reroute');
                $join->whereNull('barcode_printed_time.is_fba');
            });

        $st->where('sale_order.is_xqc', 1);
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
        $st->whereNull('sale_order_item_barcode.label_root_id');
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        }
        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $r = $st->first();
        $r->sql = $st->toSql();

        return $r;
    }

    public function countPendingWarehouse($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? 0;

        //    $styleColors = $this->getStyleColorsMobileWip($args);

        $st = DB::table('warehouse')
            ->select(DB::raw('warehouse.id, warehouse.name,barcode_printed_time.printed_at,  COUNT(*) as total'))
            ->join('sale_order', 'warehouse.id', 'sale_order.warehouse_id')
            ->join('sale_order_item_barcode', 'sale_order.id', 'sale_order_item_barcode.order_id')
            /*   ->join('sale_order_item', function ($join) {
                   $join->on('sale_order_item_barcode.sku', 'sale_order_item.sku')
                       ->where('sale_order_item.image_convert_status', 1);
               })*/
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) {
                $join->on('barcode_printed_time.warehouse_id', 'warehouse.id');
                $join->where('barcode_printed_time.account_id', null);
                $join->where('barcode_printed_time.store_id', null);
                $join->where('barcode_printed_time.style_sku', null);
                $join->where('barcode_printed_time.is_xqc', null);
                $join->where('barcode_printed_time.is_eps', null);
                $join->whereNull('barcode_printed_time.is_reprint');
                $join->whereNull('barcode_printed_time.is_manual');
                $join->whereNull('barcode_printed_time.is_reroute');
                $join->whereNull('barcode_printed_time.is_fba');
                $join->whereNull('barcode_printed_time.is_insert');
            });
        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }
        //   $st->whereNotIn(DB::raw('LEFT(sale_order_item.product_sku, 6)'), $styleColors);
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        // $st->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        // $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
        // $st->whereNull('sale_order_item_barcode.label_root_id');
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);

        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        return $st->where('sale_order_item_barcode.barcode_printed_id', 0)
            //  ->where('sale_order.order_date', '>=', '2022-02-17')

            ->orderBy('total', 'DESC')->first();
    }

    public function countPendingStyleEps($args)
    {
        $warehouse_id = $args['warehouse_id'] ?? null;
        $store_id = $args['store_id'] ?? null;
        $st = DB::table('sale_order_item_barcode')
            ->select(DB::raw('barcode_printed_time.printed_at as printed_at, COUNT(*) as total'))
            ->join('sale_order', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->leftJoin('barcode_printed_time', function ($join) use ($store_id, $warehouse_id) {
                $join->on('barcode_printed_time.is_eps', '=', DB::raw(1));
                $join->where('barcode_printed_time.store_id', $store_id);
                $join->where('barcode_printed_time.style_sku', null);
                $join->where('barcode_printed_time.warehouse_id', $warehouse_id);
                $join->whereNull('barcode_printed_time.account_id');
                $join->whereNull('barcode_printed_time.is_reprint');
                $join->whereNull('barcode_printed_time.is_manual');
                $join->whereNull('barcode_printed_time.is_reroute');
                $join->whereNull('barcode_printed_time.is_fba');
            });

        $st->where('sale_order.is_eps', 1);
        $st->where('sale_order.is_xqc', 0);
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order.is_manual', '<>', SaleOrder::IS_MANUAL);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
        $st->whereNull('sale_order_item_barcode.label_root_id');
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        if (!empty($args['priorityStores'])) {
            if ($store_id && in_array($store_id, $args['priorityStores'])) {
                $st->where('sale_order.store_id', $store_id);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        }
        if ($warehouse_id > 0) {
            $st->where('sale_order.warehouse_id', $warehouse_id);
        }
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        $st->whereNull('sale_order_item_barcode.employee_reroute_id');
        $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        $r = $st->first();
        $r->sql = $st->toSql();

        return $r;
    }

    public function fetchBarcodeByPrintedID($id)
    {
        $st = DB::table('sale_order_item_barcode')
            ->select(DB::Raw('sale_order_item_barcode.label_id, sale_order_item_barcode.id,
                sale_order_item_barcode.sku ,sale_order_item_barcode.order_id,
                sale_order.is_xqc,
                sale_order.is_eps,
                sale_order_item_barcode.barcode_number,
                sale_order_item_barcode.order_quantity,
                product.color color_formatted,
                product.size size_formatted,
                product.style style_formatted,
                product_style.id product_style_id,
                sale_order_account.name account_name,
                sale_order.account_id,
                store.name store_name,
                store.code store_code,
                store.id store_id,
                store.is_resize is_resize,
                sale_order.external_number order_external_number,
                sale_order.is_fba_order is_fba,
                sale_order.order_date,
                sale_order_item.print_side,
                sale_order_item.print_sides,
                sale_order_item.ink_color,
                sale_order_item.id order_item_id,
                pretreat_preset.preset_name pretreat_name,
                sale_order.is_manual'))
            ->join('sale_order_item', function ($join) {
                $join->on('sale_order_item.id', 'sale_order_item_barcode.order_item_id')
                    ->leftJoin('pretreat_preset_sku', function ($subJoin) {
                        $subJoin->on('sale_order_item.product_style_sku', 'pretreat_preset_sku.style');
                        $subJoin->on('sale_order_item.product_color_sku', 'pretreat_preset_sku.color');
                        $subJoin->leftJoin('pretreat_preset', 'pretreat_preset.id', '=', 'pretreat_preset_sku.pretreat_preset_id');
                    });
            })
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'sale_order_item.product_style_sku', '=', 'product_style.sku')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_account', 'sale_order_account.id', '=', 'sale_order.account_id')
            ->leftJoin('store', 'store.id', '=', 'sale_order.store_id')
            ->where('barcode_printed_id', $id);

        $st->orderBy('product.sku', 'ASC');
        $st->orderBy('sale_order.order_time', 'ASC')
            ->orderBy('sale_order_item_barcode.id', 'ASC');

        return $st->get();
        //  $r->sql = $st->toSql();
        //  return $r;
    }

    public function countPendingStore($args, $isPriorityStore = false)
    {
        $st = DB::table('store')
            ->select(DB::raw('store.id, store.name, store.code,  COUNT(*) as total'))
            ->join('sale_order', 'store.id', 'sale_order.store_id')
            ->join('sale_order_item_barcode', 'sale_order.id', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item_barcode.order_item_id', 'sale_order_item.id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku');
        if (!empty($args['warehouse_id'])) {
            $st->where('sale_order.warehouse_id', $args['warehouse_id']);
        }
        if (!empty($args['priorityStores'])) {
            if ($isPriorityStore) {
                $st->whereIn('sale_order.store_id', $args['priorityStores']);
            } else {
                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
            }
        }
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item_barcode.is_deleted', 0);
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);

        return $st->groupBy('sale_order.store_id')
            ->orderBy('total', 'DESC')
            ->get();
    }

    public function fetchPendingBarcodePrinted()
    {
        $st = DB::table('barcode_printed');

        return $st->where('convert_status', 0)->where('print_method', BarcodePrinted::METHOD_DTG)
            ->whereNull('color_sku')
            // ->where('quantity', '>', 0)
            ->first();
    }

    public function fetchPendingBarcodeDTFPrinted()
    {
        $st = DB::table('barcode_printed');

        return $st->where('convert_status', 0)
            ->whereNull('color_sku')
            //  ->where('quantity', '>', 0)
            ->where('print_method', BarcodePrinted::METHOD_DTF)->first();
    }

    public function fetchPendingBarcodeBlankShirtPrinted()
    {
        return BarcodePrinted::where('convert_status', 0)
            ->where('print_method', BarcodePrinted::METHOD_BLANK)
            ->whereNull('color_sku')
            ->first();
    }

    public function fetchPendingBarcodePretreatedShirtPrinted()
    {
        return BarcodePrinted::where('convert_status', 0)
            ->where('print_method', BarcodePrinted::METHOD_PRETREATED)
            ->whereNull('color_sku')
            ->first();
    }

    public function updateBarcodePrinted($id, $data)
    {
        try {
            return DB::table('barcode_printed')
                ->where('id', $id)->update($data);
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }

    public function fetchBarcodePrinted($input)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : 10;
        $keyword = !empty($input['keyword']) ? trim($input['keyword']) : '';
        $pending = !empty($input['pending']) ? trim($input['pending']) : '';
        $print_status = !empty($input['print_status']) ? trim($input['print_status']) : '';
        $warehouse_id = !empty($input['warehouse_id']) ? trim($input['warehouse_id']) : '';

        $st = DB::table('barcode_printed')
            ->select([
                'barcode_printed.*', 'employee.code', 'employee.name as employee_name',
                'sale_order_account.name as account_name',
                'store.name as store_name',
                'product_style.name as style_name',
                'store.code as store_code',
                'product_color.name as color_name'
            ]);

        if ($keyword != '') {
            $st->join('sale_order_item_barcode', function ($join) use ($keyword) {
                $join->on('sale_order_item_barcode.barcode_printed_id', 'barcode_printed.id');
                $join->where(function ($query) use ($keyword) {
                    $query->where('sale_order_item_barcode.sku', $keyword);
                    $query->orWhere('sale_order_item_barcode.label_id', '=', $keyword);
                });
            });
            //051222-SJ-S-000506-1
        }

        $st->leftJoin('employee', 'employee.id', 'barcode_printed.employee_id')
            ->leftJoin('sale_order_account', 'sale_order_account.id', 'barcode_printed.account_id')
            ->leftJoin('store', 'store.id', 'barcode_printed.store_id')
            ->leftJoin('product_style', 'product_style.sku', 'barcode_printed.style_sku')
            ->leftJoin('product_color', 'product_color.sku', 'barcode_printed.color_sku');

        if ($pending != '') {
            $st->where(function ($query) {
                $query->where('barcode_printed.print_status', 0);
                /*->where('barcode_printed.convert_status', 1);*/
            });
        }

        if ($warehouse_id > 0) {
            $st->where('barcode_printed.warehouse_id', $warehouse_id);
        }

        if ($print_status != '') {
            $st->where('barcode_printed.print_status', $print_status);
        }

        $st->where('barcode_printed.print_method', ProductStyle::METHOD_EMB);

        $wips = $st->orderBy('barcode_printed.id', 'desc')->paginate($limit);

        foreach ($wips->items() as $wip) {
            $fileName = "/barcode/$wip->id.pdf";

            $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();

            if (Storage::disk('public')->exists($fileName)) {
                $wip->wip_url = env('STORAGE_URL', '') . "$fileName?v=" . rand();
            }
        }

        return $wips;
    }

    public function insertBarcodePrinted($data)
    {
        return DB::table('barcode_printed')->insertGetId($data);
    }

    public function updateBarcodePrintedItem($args)
    {
        $time = date('Y-m-d H:i:s');
        $st = DB::table('sale_order_item_barcode')
            ->select(['sale_order_item_barcode.id'])
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->join('product_style', 'product_style.sku', 'sale_order_item.product_style_sku')
            ->join('product', 'product.sku', 'sale_order_item.product_sku');

        if ($args['warehouse_id'] > 0) {
            $st->where('sale_order.warehouse_id', $args['warehouse_id']);
        }
//        if (!empty($args['priorityStores'])) {
//            if (!empty($args['store_id']) && in_array($args['store_id'], $args['priorityStores'])) {
//                $st->where('sale_order.store_id', $args['store_id']);
//            } else {
//                $st->whereNotIn('sale_order.store_id', $args['priorityStores']);
//            }
//        }
        $st->where('sale_order_item_barcode.barcode_printed_id', 0);
        $st->whereNotIn('sale_order.order_status', SaleOrder::ARRAY_STATUS_INACTIVE);
        $st->where('sale_order_item_barcode.print_method', ProductStyle::METHOD_EMB);
        $st->where('sale_order_item.ink_color_status', 1);
        $st->where('product_style.type', '!=', Product::TYPE_INSERT_PRINTING);
        $st->where('sale_order.id', '>=', env('ID_SALE_ORDER_VALID', 5629058)); // '2022-02-17'
        $st->where('sale_order_item_barcode.id', '>', env('ID_SALE_ORDER_ITEM_BARCODE_VALID', 400136565)); // '2022-02-17'
        if ($args['is_manual'] == SaleOrder::IS_MANUAL) {
            $st->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::ACTIVE);
            //chung comment vi de cho cac don manual ma reprint se vao line san xuat manual
            // $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
            // $st->whereNull('sale_order_item_barcode.label_root_id');
            $st->where('sale_order.is_manual', SaleOrder::IS_MANUAL);
            $st->whereNull('sale_order_item_barcode.employee_reroute_id');
            $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        } elseif ($args['is_reroute'] == 1) {
            $st->where('sale_order_item_barcode.is_deleted', 0);
            $st->whereNotNull('sale_order_item_barcode.employee_reroute_id');
            $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        } elseif ($args['is_fba'] == 1) {
            $st->where('sale_order_item_barcode.is_deleted', 0);
            $st->where('sale_order.is_fba_order', SaleOrder::ACTIVE);
        } elseif ($args['is_reprint'] == 1) {
            $st->where('sale_order.is_manual', SaleOrder::IS_NOT_MANUAL);
            $st->where('sale_order_item_barcode.is_deleted', SaleOrderItemBarcode::ACTIVE);
            $st->whereNotNull('sale_order_item_barcode.label_root_id');
            $st->whereNull('sale_order_item_barcode.employee_reroute_id');
            $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        } else {
            if ($args['is_xqc'] == 1) {
                $st->where('sale_order.is_xqc', 1);
            } elseif ($args['is_eps'] == 1) {
                $st->where('sale_order.is_eps', 1);
                $st->where('sale_order.is_xqc', 0);
            } else {
                if ($args['style_sku'] != null) {
                    $st->where('sale_order_item.product_style_sku', $args['style_sku']);
                }
                $st->whereRaw(' ( sale_order.is_xqc = 0 AND sale_order.is_eps = 0 ) ');
                $st->where('sale_order_item.id', '>=', env('ID_SALE_ORDER_ITEM_VALID', 7562806)); // '2022-02-17'
            }
            $st->where('sale_order_item_barcode.is_deleted', 0);
            $st->where('sale_order_item_barcode.reprint_status', SaleOrderItemBarcode::NOT_REPRINTED);
            $st->whereNull('sale_order_item_barcode.label_root_id');
            $st->where('sale_order.is_manual', SaleOrder::IS_NOT_MANUAL);
            $st->whereNull('sale_order_item_barcode.employee_reroute_id');
            $st->where('sale_order.is_fba_order', SaleOrder::INACTIVE);
        }
        if (isset($args['count']) && $args['count'] == 1) {
            return ['sql' => $st->toSql(), 'arg' => $args, 'count' => $st->count()];
        }
        $st->whereRaw('1 = 1 LIMIT ?', [$args['limit']]);

        $st->update([
            'employee_pull_id' => $args['employee_id'],
            'barcode_printed_id' => $args['barcode_printed_id'],
            'print_barcode_at' => $time
        ]);

        $saleOrderIds = SaleOrderItemBarcode::where('barcode_printed_id', $args['barcode_printed_id'])->pluck('order_id')->toArray();

        $chunkOrderIds = array_chunk(array_unique($saleOrderIds), 100);

        foreach ($chunkOrderIds as $orderIds) {
            // Update barcode printed status and time
            $newOrderIds = SaleOrder::whereIn('id', $orderIds)->where('order_status', SaleOrder::NEW_ORDER)->pluck('id')->toArray();
            if (!empty($newOrderIds)) {
                handleJob(SaleOrder::JOB_NOTIFY_MULTIPLE_STATUS_ORDER, $newOrderIds);
            }
            SaleOrder::whereIn('id', $orderIds)
                ->where('order_status', SaleOrder::NEW_ORDER)
                ->update([
                    'order_status' => SaleOrder::IN_PRODUCTION,
                    'order_production_at' => $time
                ]);
        }

        DB::table('barcode_printed')->where('id', $args['barcode_printed_id'])
            ->update(['quantity' => count($saleOrderIds)]);
    }

    public function updateLastBarcodePrintedTime($data)
    {
        $check = DB::table('barcode_printed_time')
            ->where('store_id', $data['store_id'] ?? null)
            ->whereNull('account_id')
            ->where('is_xqc', $data['is_xqc'])
            ->where('is_eps', $data['is_eps'])
            ->where('is_reprint', $data['is_reprint'])
            ->where('style_sku', $data['style_sku'])
            ->where('is_manual', $data['is_manual'])
            ->where('is_reroute', $data['is_reroute'])
            ->where('is_fba', $data['is_fba'])
            ->where('warehouse_id', $data['warehouse_id'])
            ->where('print_method', PrintMethod::EMB)
            ->whereNull('color_sku')
            ->first();
        if ($check) {
            DB::table('barcode_printed_time')->where('id', $check->id)
                ->update(['printed_at' => date('Y-m-d H:i:s')]);
        } else {
            $data['print_method'] = PrintMethod::EMB;
            $list = ['store_id', 'warehouse_id', 'account_id', 'is_xqc', 'style_sku', 'is_reprint', 'is_manual', 'is_reroute', 'is_fba', 'is_eps', 'color_sku', 'print_method'];
            $insert['printed_at'] = date('Y-m-d H:i:s');
            foreach ($list as $item) {
                $insert[$item] = $data[$item] ?? null;
            }

            DB::table('barcode_printed_time')->insert($insert);
        }
    }

    public function getStartEndBarcodePrinted($id)
    {
        return DB::table('barcode_printed')->select('*')
            ->where('id', $id)->first();

        $start = DB::table('sale_order_item_barcode')
            ->select('sale_order_item_barcode.sku', 'sale_order_item_barcode.label_id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item.id', 'sale_order_item_barcode.order_item_id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->where('barcode_printed_id', $id)
            ->orderBy('product.sku', 'ASC')
            ->orderBy('sale_order.order_time', 'ASC')
            ->orderBy('sale_order_item_barcode.id', 'ASC')
            ->limit(1)->first();

        $end = DB::table('sale_order_item_barcode')
            ->select('sale_order_item_barcode.sku', 'sale_order_item_barcode.label_id')
            ->join('sale_order', 'sale_order.id', '=', 'sale_order_item_barcode.order_id')
            ->join('sale_order_item', 'sale_order_item.id', 'sale_order_item_barcode.order_item_id')
            ->join('product', 'product.sku', '=', 'sale_order_item.product_sku')
            ->where('barcode_printed_id', $id)
            ->orderBy('product.sku', 'DESC')
            ->orderBy('sale_order.order_time', 'DESC')
            ->orderBy('sale_order_item_barcode.id', 'DESC')
            ->limit(1)->first();

        return [
            'label_1' => $start->label_id,
            'label_2' => $end->label_id,
            'start' => $start->sku,
            'end' => $end->sku
        ];
    }

    public function getLabel($label)
    {
        return SaleOrderItemBarcode::query()->where('label_id', $label)->first();
    }

    public function getBarcodePrintedById($id)
    {
        return DB::table('barcode_printed')
            ->where('id', $id)
            ->first();
    }

    public function fetchPendingBarcodeEMBPrinted()
    {
        $st = DB::table('barcode_printed');

        return $st->where('convert_status', 0)
            ->where('print_method', BarcodePrinted::EMB_PRINT_METHOD)
            ->whereNull('color_sku')
            ->first();
    }
}
