<?php

namespace App\Repositories;

use App\Models\Country;
use App\Models\State;
use App\Repositories\Contracts\CountryRepositoryInterface;
use Validator;

class CountryRepository implements CountryRepositoryInterface
{
    public function fetchAll($input)
    {

        if (!empty($input['has_paginator'])) {
            $limit = $input['limit'] ?? 10;
            $countries = Country::orderBy('name', 'asc');
            if (isset($input['has_numeric_code'])) {
                if ($input['has_numeric_code']) {
                    $countries->whereNotNull('code');
                } else {
                    $countries->whereNull('code');
                }
            }
            if (!empty($input['keyword'])) {
                $keyword = $input['keyword'];
                $countries->where('iso2', $keyword)->orWhere('name', 'LIKE', "%$keyword%")->orWhereRaw('FIND_IN_SET(?, code) > 0', [$keyword]);
            }
            return $countries->paginate($limit);
        }
        if(!empty($input['keyword'])){
            $keyword = $input['keyword'];
            return Country::where('iso2', $keyword)->orWhere('code', 'LIKE', "%$keyword%")->orderBy('name', 'asc')->get(['id', 'name']);
        }
        $origin = $input['is_origin'] ?? null;
        $countries = Country::query();
        return is_null($origin) ? $countries->get() : $countries->where('is_origin', 1)->get(['id', 'name']);
    }

    public function getCountryPartNumber($input)
    {
        $origin = $input['is_origin'] ?? null;
        $countries = Country::query();

        if (!empty($input['product_id'])) {
            $countries->whereHas('partNumbers', function ($q) use ($input) {
                $q->whereIn('product_id', $input['product_id']);
            });
        }
        if (!empty($input['style'])) {
            $countries->whereHas('partNumbers.product', function ($q) use ($input) {
                $q->where('style', $input['style']);
            });
        }
        if (!empty($input['color'])) {
            $countries->whereHas('partNumbers.product', function ($q) use ($input) {
                $q->where('color', $input['color']);
            });
        }
        if (!empty($input['size'])) {
            $countries->whereHas('partNumbers.product', function ($q) use ($input) {
                $q->where('size', $input['size']);
            });
        }
        return is_null($origin) ? $countries->get() : $countries->where('is_origin', 1)->get(['id', 'name']);
    }

    public function fetchStateByCountryId($id)
    {
        return State::where('country_id', $id)->get();
    }

    public function create($input)
    {

    }

    public function fetch($id)
    {
    }

    public function update($id, $dataUpdate)
    {
        $country = Country::find($id);
        $country->code = !empty($dataUpdate['code']) ? implode(",", $dataUpdate['code']) : null;
        $country->save();
        return $country;
    }

    public function delete($id)
    {
    }

    public function validateCode($id, $code)
    {
        return !Country::whereRaw('FIND_IN_SET(?, code) > 0', [$code])->exists();
    }

}
