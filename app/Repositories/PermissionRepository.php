<?php


namespace App\Repositories;


use App\Repositories\Contracts\PermissionRepositoryInterface;
use Spatie\Permission\Models\Permission;

class PermissionRepository implements PermissionRepositoryInterface
{
    public function create($input)
    {
        // TODO: Implement create() method.
        Permission::create($input);
        return response()->json(['message' => 'success']);

    }

    public function edit($input, $id)
    {
        // TODO: Implement edit() method.
        $permission = Permission::where('id', $id)->first();

        if (!$permission) {
            return response()->json(['message' => ["Permission not found"]], 422);
        }

        $permission->update($input);
        return response()->json(['message' => 'success']);

    }

    public function delete($id)
    {
        // TODO: Implement delete() method.
        $permission = Permission::where('id', $id)->first();

        if (!$permission) {
            return response()->json(['message' => ["Permission not found"]], 422);
        }

        $permission->delete();

        return response()->json(['message' => 'success']);
    }

    public function fetchAll()
    {
        // TODO: Implement fetchAll() method.

        return  Permission::get();
    }

}
