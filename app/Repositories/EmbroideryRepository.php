<?php

namespace App\Repositories;

use App\Http\Service\AlertService;
use App\Models\EmbroideryHistory;
use App\Models\EmbroideryTask;
use App\Models\ImageHash;
use App\Models\PrintingPresetSku;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Repositories\Contracts\EmbroideryRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class EmbroideryRepository extends CommonRepository implements EmbroideryRepositoryInterface
{
    public function authenticate($params)
    {
        $credentials = [
            'username' => $params['username'],
            'password' => $params['password'],
        ];
        if (Auth::guard('embroidery')->attempt($credentials)) {
            $user = Auth::guard('embroidery')->user();
        } else {
            return response()->json(['error' => 'Incorrect username or password. Please try again.'], 401);
        }

        return self::createToken($user);
    }

    public static function createToken($user)
    {
        return response()->json([
            'access_token' => encryptSalt($user->id . ':' . time()),
            'token_type' => 'bearer',
            'user' => [
                'name' => $user->name,
                'role' => $user->role,
                'team_id' => $user->team_id,
            ],
        ]);
    }

    public function listTask(Request $request)
    {
        // setTimezone();
        $user = auth()->user();

        $query = EmbroideryTask::with(['saleOrder:id,order_number as external_number,order_date', 'SaleOrderItem:id,sku'])
           ->whereDoesntHave('image.imageHash', function ($q) {
               $q->where('is_ip_violation', 1);
           });
        if (!empty($request->external_number)) {
            // Add a condition to filter based on saleOrder's external_number
            $query->whereHas('saleOrder', function ($subQuery) use ($request) {
                $subQuery->where('order_number', $request->external_number);
            });
        }

        if ($request->has('id')) {
            $query->where('embroidery_task.id', $request->id);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('embroidery_task.status', $request->status);
        }
        // Filter by start_at and end_at
        if ($request->has('start_at')) {
            $query->whereDate('embroidery_task.created_at', '>=', $request->start_at);
        }

        if ($request->has('end_at')) {
            $query->whereDate('embroidery_task.created_at', '<=', $request->end_at);
        }

        // Filter by team_id
        if ($request->has('team_id') && $user->role == 'admin') {
            $query->where('embroidery_task.embroidery_team_id', $request->team_id);
        }

        if ($user->role == 'leader') {
            if ($request->has('status')) {
                $query->where('embroidery_task.embroidery_team_id', $user->team_id);
            } else {
                $query->where(function ($query) use ($user) {
                    $query->where('embroidery_team_id', $user->team_id)
                          ->orWhere('embroidery_task.status', 'pending');
                });
            }
        } elseif ($user->role == 'editor') {
            $query->where('embroidery_task.embroidery_user_id', $user->id);
        }

        if ($request->has('print_area')) {
            $query->where('embroidery_task.print_area', '=', $request->print_area);
        }

        if ($request->has('embroidery_user_name')) {
            $query->join('embroidery_user as user', 'user.id', '=', 'embroidery_task.embroidery_user_id')
                  ->where('user.name', 'like', '%' . $request->embroidery_user_name . '%')
                  ->select('embroidery_task.*', 'user.name as username', 'user.role', 'user.email', 'user.team_id', 'user.is_active');
        }

        if (!empty($request->sku)) {
            $query->whereHas('saleOrderItem', function ($subQuery) use ($request) {
                $subQuery->where('sku', $request->sku);
            });
        }

        $query->whereNull('parent_id');
        if ($user->role == 'editor') {
            $query->orderByRaw("FIELD(embroidery_task.status, 'rejected', 'pending', 'in_progress', 'in_review', 'completed', 'cancelled')")
            ->orderBy('embroidery_task.created_at');
        } else {
            $query->orderByRaw("FIELD(embroidery_task.status,'in_review','pending','rejected','in_progress','completed','cancelled')")
            ->orderBy('embroidery_task.created_at');
        }
        // $query->distinct();

        // Pagination with limit
        $limit = $request->has('limit') ? $request->limit : 20;

        $result = $query->paginate($limit);

        return $result;
    }

    public function countTask(Request $request)
    {
        $user = auth()->user();

        $query = EmbroideryTask::whereDoesntHave('image.imageHash', function ($q) {
            $q->where('is_ip_violation', 1);
        });
        $query->whereNull('parent_id');

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by start_at and end_at
        if ($request->has('start_at')) {
            $query->where('created_at', '>=', $request->start_at);
        }

        if ($request->has('end_at')) {
            $query->where('created_at', '<=', $request->end_at);
        }

        // Filter by team_id
        if ($request->has('team_id') && $user->role == 'admin') {
            $query->where('embroidery_team_id', $request->team_id);
        }

        if ($user->role == 'leader') {
            if ($request->has('status')) {
                $query->where('embroidery_team_id', $user->team_id);
            } else {
                $query->where(function ($query) use ($user) {
                    $query->where('embroidery_team_id', $user->team_id)
                          ->orWhere('status', 'pending');
                });
            }
        } elseif ($user->role == 'editor') {
            $query->where(function ($query) use ($user) {
                $query->where('embroidery_user_id', $user->id)
                ->orWhere('status', 'pending');
            });
        }
        // Pagination with limit
        $result = $query
        ->selectRaw('status, COUNT(*) as status_count')
        ->groupBy('status')
        ->get();
        $data = [];
        foreach ($result as $item) {
            $data[$item['status']] = [
                'type' => $item['status'],
                'total' => $item['status_count']
            ];
        }
        $listStatus = [
            EmbroideryTask::STATUS_PENDING,
            EmbroideryTask::STATUS_IN_PROGRESS,
            EmbroideryTask::STATUS_IN_REVIEW,
            EmbroideryTask::STATUS_REJECT,
            EmbroideryTask::STATUS_COMPLETED,
            EmbroideryTask::STATUS_CANCELLED,
        ];
        $sortedData = [];
        foreach ($listStatus as $type) {
            $sortedData[] = $data[$type] ?? [
                'type' => $type,
                'total' => 0,
            ];
        }

        return $sortedData;
    }

    public function getDetail($id)
    {
        $data = EmbroideryTask::with([
            'SaleOrderItem:id,options,product_id,product_sku',
            'saleOrder:id,external_number,order_date',
            // 'SaleOrderItem.images' => function ($q) use ($task) {
            //     // Use the image_hash_id from EmbroideryTask to filter images
            //     $q->where('image_hash_id', $task->image_hash_id);
            // },
            'saleOrderImage:id,image_hash_id,image_url',
            'assignUser:id,name',
            'history'
        ])->where('id', $id)->firstOrFail();

        $productPrintSide = ProductPrintSide::where('name', trim(strtolower($data['print_area'])))->first();
        $productPrintSku = PrintingPresetSku::where([
            'sku' => $data->SaleOrderItem?->product_sku,
        ])->first();

        $saleOrderItem = optional($data)->SaleOrderItem;
        $linkPreviewFile = '';
        if ($saleOrderItem) {
            $options = json_decode($saleOrderItem->options);
            if (!empty($options)) {
                foreach ($options as $key => $option) {
                    $optionName = explode('.', $option->name);
                    if (!empty($optionName[0]) && $optionName[0] == SaleOrderItem::PREVIEW_FILES && !empty($optionName[1]) && strtolower($optionName[1]) == str_replace(' ', '', trim(strtolower($data['print_area'])))) {
                        $linkPreviewFile = $option->value;
                    }
                }
            }
        }
        $data['preview_file'] = ($linkPreviewFile) ? $linkPreviewFile : null;
        $data['image_file'] = ($data->saleOrderImage) ? $data->saleOrderImage['image_url'] : null;
        // unset($data->SaleOrderItem);

        $data->printing_preset_sku_size = null;
        if (!empty($productPrintSide) && !empty($productPrintSku)) {
            $printSize = strval($productPrintSide->code_name . '_size');
            $data->printing_preset_sku_size = $productPrintSku[$printSize];
        }
        unset($data->saleOrderImage);

        return $data;
    }

    public function getTask()
    {
        $user = auth()->user();
        $currTask = EmbroideryTask::wherein('status', [EmbroideryTask::STATUS_IN_PROGRESS, EmbroideryTask::STATUS_REJECT])
        ->where('embroidery_user_id', $user->id)
        ->get();
        $user = auth()->user();
        $currTask = EmbroideryTask::whereIn('status', [EmbroideryTask::STATUS_IN_PROGRESS, EmbroideryTask::STATUS_REJECT])
            ->where('embroidery_user_id', $user->id)
            ->get();

        if ($currTask->isEmpty()) {
            $findTask = EmbroideryTask::where('status', EmbroideryTask::STATUS_PENDING)
                ->whereNull('embroidery_user_id')
                ->orderBy('created_at', 'ASC')
                ->firstOrFail();

            return response()->json($findTask);
        } else {
            return response()->json(['code' => 422, 'message' => 'Please finish uploading your current task before taking on a new one.']);
        }
    }

    public function uploadFile($data)
    {
        $user = auth()->user();
        $task = EmbroideryTask::find($data['task_id']);
        $file = $data['file'];
        $md5Hash = md5_file($file);
        $fileExist = 'embroidery/file/' . $task->link_url;

        $fileExtension = '.' . $file->getClientOriginalExtension();

        if ($task->color_url) {
            if (Str::endsWith($task->color_url, strtoupper($fileExtension)) || Str::endsWith($task->color_url, strtolower($fileExtension))) {
                return [
                    'message' => 'The CT0 file is already in this format. Please change it to another format.',
                    'status' => 422
                ];
            }
        }

        if (\Storage::disk('s3')->exists($fileExist)) {
            \Storage::disk('s3')->delete($fileExist);
        }
        $binaryData = $file->get();
        $filename = $task->image_hash_id . '.' . $file->getClientOriginalExtension();
        $prefix = '/embroidery/file/';

        $uploadPath = \Storage::disk('s3')->put($prefix . $filename, $binaryData);

        $task->update([
            'embroidery_file_md5' => $md5Hash,
            'link_url' => $filename,
            'link_original_name' => $file->getClientOriginalName()
        ]);

        $this->updateStatus($task->id);

        $historyLog = EmbroideryHistory::create([
            'task_id' => $task->id,
            'action' => 'upload',
            'embroidery_user_id' => $user->id,
            'note' => $user->username . ' uploaded TBF file'
        ]);

        SaleOrderHistory::create([
            'order_id' => $task->sale_order_id,
            'type' => SaleOrderHistory::UPLOAD_EMBROIDERY_FILE,
            'message' => "A user uploaded a TBF file for \"$task->print_area\" area on Task ID #" . $task->id . ' via the emb tool.',
            'created_at' => Carbon::now()->toDateTimeString()
        ]);

        return [
            'message' => 'Upload file successful',
            'status' => 200
        ];
    }

    public function uploadFileColor($data)
    {
        $user = auth()->user();
        $task = EmbroideryTask::find($data['task_id']);
        $file = $data['file'];
        $fileExist = 'embroidery/color/' . $task->color_url;

        $fileExtension = '.' . $file->getClientOriginalExtension();

        if ($task->link_url) {
            if (Str::endsWith($task->link_url, strtoupper($fileExtension)) || Str::endsWith($task->link_url, strtolower($fileExtension))) {
                return [
                    'message' => 'The TBF file is already in this format. Please change it to another format.',
                    'status' => 422
                ];
            }
        }
        // Start the transaction
        DB::beginTransaction();

        try {
            if (\Storage::disk('s3')->exists($fileExist)) {
                \Storage::disk('s3')->delete($fileExist);
            }

            $binaryData = $file->get();
            $filename = $task->image_hash_id . '.' . $file->getClientOriginalExtension();
            $prefix = '/embroidery/color/';
            \Storage::disk('s3')->put($prefix . $filename, $binaryData);

            $task->update([
                'color_url' => $filename,
                'color_original_name' => $file->getClientOriginalName()
            ]);

            $this->updateStatus($task->id);

            EmbroideryHistory::create([
                'task_id' => $task->id,
                'action' => 'upload',
                'embroidery_user_id' => $user->id,
                'note' => $user->username . ' uploaded CT0 file'
            ]);

            SaleOrderHistory::create([
                'order_id' => $task->sale_order_id,
                'type' => SaleOrderHistory::UPLOAD_EMBROIDERY_COLOR,
                'message' => "A user uploaded a CT0 file for \"$task->print_area\" area on Task ID #" . $task->id . ' via the emb tool.',
                'created_at' => Carbon::now()->toDateTimeString()
            ]);

            // Commit the transaction if all operations succeed
            DB::commit();

            return [
                'message' => 'Upload file successful',
                'status' => 200
            ];
        } catch (\Exception $e) {
            // Rollback the transaction if any operation fails
            DB::rollback();

            // Log or handle the exception as needed
            return [
                'message' => 'Upload file failed: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    public function uploadEmbFile($data)
    {
        $user = auth()->user();
        $task = EmbroideryTask::find($data['task_id']);
        $file = $data['file'];

        $fileExist = 'embroidery/evidence/' . $task->emb_url;

        // Start the transaction
        DB::beginTransaction();

        try {
            if (\Storage::disk('s3')->exists($fileExist)) {
                \Storage::disk('s3')->delete($fileExist);
            }

            $binaryData = $file->get();
            $filename = $task->image_hash_id . '.' . $file->getClientOriginalExtension();
            $prefix = '/embroidery/evidence/';
            \Storage::disk('s3')->put($prefix . $filename, $binaryData);

            $task->update([
                'emb_url' => $filename,
                'emb_original_name' => $file->getClientOriginalName()
            ]);

            $this->updateStatus($task->id);

            EmbroideryHistory::create([
                'task_id' => $task->id,
                'action' => 'upload',
                'embroidery_user_id' => $user->id,
                'note' => $user->username . ' uploaded EMB file'
            ]);

            SaleOrderHistory::create([
                'order_id' => $task->sale_order_id,
                'type' => SaleOrderHistory::UPLOAD_EMBROIDERY_EVIDENCE,
                'message' => "A user uploaded a EMB file for \"$task->print_area\" area on Task ID #" . $task->id . ' via the emb tool.',
                'created_at' => Carbon::now()->toDateTimeString()
            ]);

            // Commit the transaction if all operations succeed
            DB::commit();

            return [
                'message' => 'Upload EMB file successfully.',
                'status' => 200
            ];
        } catch (\Exception $e) {
            // Rollback the transaction if any operation fails
            DB::rollback();

            return [
                'message' => 'Upload file failed: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    public function getPrintArea()
    {
        $listArea = EmbroideryTask::select('print_area')->groupBy('print_area')->get();

        return $listArea;
    }

    public function updateStatus($id)
    {
        $task = EmbroideryTask::find($id);
        if ($task && $task->link_url && $task->color_url) {
            return $task->update(['status' => EmbroideryTask::STATUS_IN_REVIEW]);
        }
    }

    public function updateWarningDigitized($id)
    {
        try {
            DB::beginTransaction();
            $user = auth()->user();
            $task = EmbroideryTask::find($id);
            if ($task) {
                $task->update(['is_not_digitized' => true,
                    'status' => EmbroideryTask::STATUS_IN_REVIEW
                ]);

                EmbroideryHistory::create([
                    'task_id' => $task->id,
                    'action' => 'warning',
                    'embroidery_user_id' => $user->id,
                    'note' => '#task ' . $task->id . ' was unable to be digitized by ' . $user->username
                ]);
                DB::commit();

                return [
                    'message' => 'Task updated successfully.',
                    'status' => 200
                ];
            } else {
                return [
                    'message' => 'Task not found.',
                    'status' => 404
                ];
            }
        } catch (\Exception $e) {
            DB::rollBack();

            return [
                'message' => 'An error occurred: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    public function commentDigitized($data)
    {
        try {
            DB::beginTransaction();
            $user = auth()->user();
            $task = EmbroideryTask::find($data['id']);
            if ($task) {
                $task->update([
                    'is_not_digitized' => false,
                    'status' => EmbroideryTask::STATUS_IN_PROGRESS
                ]);

                EmbroideryHistory::create([
                    'task_id' => $task->id,
                    'action' => 'comment',
                    'embroidery_user_id' => $user->id,
                    'note' => $user->username . ': ' . $data['value']
                ]);
                DB::commit();

                return [
                    'message' => 'Task updated successfully.',
                    'status' => 200
                ];
            } else {
                return [
                    'message' => 'Task not found.',
                    'status' => 404
                ];
            }
        } catch (\Exception $e) {
            DB::rollBack();

            return [
                'message' => 'An error occurred: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    public function rejectDigitized($id)
    {
        try {
            DB::beginTransaction();

            $user = auth()->user();
            $task = $this->findTaskById($id);
            if (!$task) {
                return $this->response('Task not found.', 404);
            }
            $task->update(['status' => EmbroideryTask::STATUS_CANCELLED]);

            $this->cancelTask($task, $user);
            $this->logTaskRejection($task, $user);

            $order = $this->findOrderById($task->sale_order_id);
            $orderStatus = $order->order_status;
            $this->rejectOrder($order);
            $this->logOrderRejection($order, $orderStatus);

            $orderItem = $this->findOrderItemById($task->order_item_id);
            $this->updateImageHash($task->image_hash_id);
            $this->sendAlert($task, $orderItem, $order);

            if (!$task->parent_id) {
                $this->cancelSecondaryTasks($task, $user, $order);
            }

            $otherTasks = $this->findOtherTasks($task);
            if (count($otherTasks) > 0) {
                $this->handleOtherTasks($otherTasks, $user, $order);
            }

            DB::commit();

            return $this->response('Task updated successfully.', 200);
        } catch (\Exception $e) {
            DB::rollback();

            return $this->response('An error occurred: ' . $e->getMessage(), 500);
        }
    }

    private function findTaskById($id)
    {
        return EmbroideryTask::find($id);
    }

    private function response($message, $status)
    {
        return ['message' => $message, 'status' => $status];
    }

    private function cancelTask($task, $user)
    {
        EmbroideryHistory::create([
            'task_id' => $task->id,
            'action' => 'comment',
            'embroidery_user_id' => $user->id,
            'note' => 'Task has been cancelled by leader/admin. order have image UNABLE TO BE DIGITIZED'
        ]);
    }

    private function logTaskRejection($task, $user)
    {
        EmbroideryHistory::create([
            'task_id' => $task->id,
            'action' => 'comment',
            'embroidery_user_id' => $user->id,
            'note' => 'Task has been rejected by leader/admin. Image was UNABLE TO BE DIGITIZED'
        ]);
    }

    private function findOrderById($id)
    {
        return SaleOrder::find($id);
    }

    private function rejectOrder($order)
    {
        $order->update([
            'order_status' => SaleOrder::STATUS_REJECT,
            'rejected_reason' => SaleOrder::UNABLE_TO_BE_DIGITIZED_REJECTED_REASON,
            'rejected_at' => Carbon::now()->toDateTimeString(),
        ]);
    }

    private function logOrderRejection($order, $orderStatus)
    {
        SaleOrderHistory::create([
            'order_id' => $order->id,
            'type' => SaleOrderHistory::REJECT_EMBROIDERY_FILE,
            'message' => "Order status changed from '$orderStatus' to 'Rejected' by Admin/Leader due to unable to be digitized.",
            'created_at' => Carbon::now()->toDateTimeString()
        ]);
    }

    private function findOrderItemById($id)
    {
        return SaleOrderItem::find($id);
    }

    private function updateImageHash($imageHashId)
    {
        $imageHash = ImageHash::find($imageHashId);
        $imageHash->update(['is_not_digitized' => true]);
    }

    private function sendAlert($task, $orderItem, $order)
    {
        $alertService = new AlertService();
        $message = "*Task ID: {$task->id}, *SKU Rejected: {$orderItem->sku} - *Order Cancelled: {$order->order_number}\n";
        $alertService->alertEmbroideryDigitized($message);
    }

    private function cancelSecondaryTasks($task, $user, $order)
    {
        $secondaryTasks = EmbroideryTask::where('parent_id', $task->id)
            ->whereNotIn('status', [EmbroideryTask::STATUS_CANCELLED, EmbroideryTask::STATUS_REJECT, EmbroideryTask::STATUS_COMPLETED])
            ->orderBy('id', 'desc')
            ->get();
        if (count($secondaryTasks) > 0) {
            foreach ($secondaryTasks as $secondaryTask) {
                $secondaryTask->update(['status' => EmbroideryTask::STATUS_CANCELLED]);
                EmbroideryHistory::create([
                    'task_id' => $secondaryTask->id,
                    'action' => 'comment',
                    'embroidery_user_id' => $user->id,
                    'note' => 'Task has been cancelled by the leader/admin. Order has images UNABLE TO BE DIGITIZED.'
                ]);
            }
        }
    }

    private function findOtherTasks($task)
    {
        return EmbroideryTask::where('sale_order_id', $task->sale_order_id)->where('id', '!=', $task->id)->get();
    }

    private function handleOtherTasks($otherTasks, $user, $order)
    {
        foreach ($otherTasks as $otherTask) {
            $task = EmbroideryTask::find($otherTask->id);
            $task->update(['status' => EmbroideryTask::STATUS_CANCELLED]);
            $this->cancelTask($task, $user);
            if (!$task->parent_id) {
                $this->cancelSecondaryTasks($task, $user, $order);
            }
        }
    }
}
