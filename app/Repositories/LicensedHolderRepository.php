<?php

namespace App\Repositories;

use App\Models\LicensedDesign;
use App\Models\LicensedDesignPrintFile;
use App\Models\LicensedHolder;

class LicensedHolderRepository extends CommonRepository
{
    public function getLicensedHolders($params)
    {
        setTimezone();
        $query = LicensedHolder::with('user:id,username');

        if (!empty($params['licensed_code'])) {
            $query->where('licensed_code', $params['licensed_code']);
        }

        if (!empty($params['licensed_holder'])) {
            $query->where('licensed_holder', $params['licensed_holder']);
        }

        if (empty(($params['limit']))) {
            return $query->orderByDesc('id')->get();
        }

        return $query->orderByDesc('id')->paginate($params['limit'] ?? self::LIMIT);
    }

     public function storeLicensedHolders($params)
     {
         $dataHolderInsert = [];

         foreach ($params as $newHolder) {
             $dataHolderInsert[] = [
                 'licensed_code' => $newHolder['licensed_code'],
                 'licensed_holder' => $newHolder['licensed_holder'],
                 'status' => $newHolder['status'] ?? LicensedHolder::STATUS_ACTIVE,
                 'created_at' => now(),
                 'created_by' => auth()->user()->id,
             ];
         }

         return LicensedHolder::insert($dataHolderInsert);
     }

    public function updateHolders($id, $params)
    {
        $holder = LicensedHolder::findOrFail($id);
        $holder->status = $params['status'];
        $holder->save();
        $licensedDesign = LicensedDesign::where('licensed_holder_id', $id)->pluck('licensed_design_id')->toArray();

        LicensedDesignPrintFile::whereIn('licensed_design_id', $licensedDesign)->update(['status' => $params['status']]);

        return $holder;
    }
}
