<?php

namespace App\Repositories;

use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderSSactivewear;
use GuzzleHttp\Client;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ToolOrderRepository
{
    public function sync($input)
    {
        if ($input['vendor_id'] == 3) {
            return $this->syncSsactivewear($input);
        }

        if ($input['vendor_id'] == 7) {
            return $this->syncAl<PERSON><PERSON>roder($input);
        }
    }

    private function syncAlphaBroder($input)
    {
        $order = DB::table('purchase_order_alphabroder')
            ->where('order_number', $input['order_number'])
            ->where('invoice_number', $input['invoice_number'])
            ->first();

        if (empty($order)) {
            return response()->json(['invoice_number' => ['Not found Order']], 422);
        }

        $cartons = (array) json_decode($order->box_ftp);

        $orderNumber = $input['order_number'];
        $data = $this->get_xml_from_url($input['order_number']);

        $order = [];
        if (isset($data['order']['@attributes'])) {
            if ($orderNumber = $data['order']['@attributes']['orderreferenceno']) {
                $order = $data['order'];
            }
        } else {
            foreach ($data['order'] as $orderItem) {
                if ($orderNumber == $orderItem['@attributes']['orderreferenceno']) {
                    $order = $orderItem;
                }
            }
        }

        if (!empty($order)) {
            if (!empty($order['shoppingbox']['lineitem'])) {
                $line = $order['shoppingbox']['lineitem'];

                if (isset($line['style'])) { /// case : order chỉ có 1 order item
                    $product = $this->getProductByGtin($line['upccode']);

                    if (empty($product)) {
                        return response()->json(['order_number' => ['gtin not found ' . $line['upccode']]], 422);
                    } else {
                        return $this->insertPurchaseOrderAndOrderBox($order, $line, $cartons, $orderNumber);
                    }
                } else {
                    $arrGtin = array_unique(array_column($line, 'upccode'));
                    $status = true;
                    $arrGtinNotFound = [];
                    foreach ($arrGtin as $gtin) {
                        if (empty($this->getProductByGtin($gtin))) {
                            $arrGtinNotFound[] = $gtin;
                            $status = false;
                        }
                    }
                    if ($status == false) {
                        $gtinNotFoundText = implode(',', $arrGtinNotFound);

                        return response()->json(['order_number' => ['gtin not found ' . $gtinNotFoundText]], 422);
                    } else {
                        return $this->insertPurchaseOrderAndOrderBox($order, $line, $cartons, $orderNumber);
                    }
                }
            }
        }
    }

    public function get_xml_from_url($poNumber)
    {
        try {
            $url = "https://www.alphabroder.com/cgi-bin/service/xml/xmlOrderStatusV2.w?on=$poNumber&bn=0&cn=3439218&user=swiftpod&pwd=Pod1337!&all=y";
            $context = stream_context_create(['http' => ['header' => 'Accept: application/xml']]);
            $xml = file_get_contents($url, false, $context);
            $xml = simplexml_load_string($xml);
            $json = json_encode($xml);
            $array = json_decode($json, true);

            return $array;
        } catch (\Exception $exception) {
            //dd($exception->getMessage());
            return [];
        }
    }

    private function insertPurchaseOrderAndOrderBox($order, $line, $cartons, $orderNumber)
    {
        try {
            DB::beginTransaction();
            $traking_number = $order['shipviapage']['shipmethods']['shipmethod']['selectoption']['tracking'];
            $cartonsOrderId = $order['shipviapage']['shipmethods']['shipmethod']['selectoption']['cartonid'];

            $orderIsExisted = DB::table('purchase_order')
                ->where('po_number', $order['customerpo'])
                ->where('invoice_number', $order['invoiceinfo']['invoicenum'])
                ->first();

            if (!empty($orderIsExisted)) {
                $orderId = $orderIsExisted->id;
            } else {
                $purcharOrder = [
                    'order_number' => $orderNumber,
                    'po_number' => $order['customerpo'],
                    'invoice_number' => $order['invoiceinfo']['invoicenum'],
                    'order_date' => Carbon::parse($order['invoiceinfo']['invoicedate'])->format('Y-m-d H:i:s'),
                    'delivery_date' => Carbon::parse($order['invoiceinfo']['invoiceduedate'])->format('Y-m-d H:i:s'),
                    'order_status' => PurchaseOrder::SHIPPED_STATUS,
                    'tracking_number' => is_array($traking_number) ? $traking_number[0] : $traking_number,
                    'warehouse_id' => $order['shippingpage']['shiptoaddresses']['address']['city'] == 'SAN JOSE' ? 1 : 2, /// Fix cung San Joe la 1, Texas = 2;
                    'vendor_id' => 7, /// Alphabroder có id = 7
                    'user_id' => 22 // username : API
                ];
                $orderId = DB::table('purchase_order')->insertGetId($purcharOrder);
                $productKeyGtin = [];
                if (isset($line['style'])) {
                    $product = $this->getProductByGtin($line['upccode']);
                    $orderItem = [
                        'po_id' => $orderId,
                        'product_id' => $product->id,
                        'quantity' => $line['pieces'],
                        // "quantity_onhand"=>$line->qtyShipped,
                        'price' => $line['price'],
                        'total' => $line['amount'],
                    ];
                    DB::table('purchase_order_item')->insert($orderItem);

                    $productKeyGtin[$line['upccode']] = $product;
                } else {
                    foreach ($line as $item) {
                        $product = $this->getProductByGtin($item['upccode']);
                        $orderItem = [
                            'po_id' => $orderId,
                            'product_id' => $product->id,
                            'quantity' => $item['pieces'],
                            // "quantity_onhand"=>$line->qtyShipped,
                            'price' => $item['price'],
                            'total' => $item['amount'],
                        ];
                        DB::table('purchase_order_item')->insert($orderItem);

                        $productKeyGtin[$item['upccode']] = $product;
                    }
                }
            }

            //todo : insert box va box item
            if (is_array($cartonsOrderId)) {
                foreach ($cartonsOrderId as $cartonId) {
                    if (isset($cartons[$cartonId])) {
                        $tracking_number_in_box = $cartons[$cartonId][0][3];
                        $orderBox = DB::table('purchase_order_box')
                            ->where('tracking_number', $tracking_number_in_box)
                            ->first();

                        if (empty($orderBox)) {
                            $box = [
                                'po_id' => $orderId,
                                'box_number' => $cartonId,
                                'invoice_number' => $order['invoiceinfo']['invoicenum'],
                                'tracking_number' => $tracking_number_in_box,
                                'created_at' => date('Y-m-d H:i:s'),
                            ];
                            $orderBoxId = DB::table('purchase_order_box')->insertGetId($box);

                            foreach ($cartons[$cartonId] as $itemBox) {
                                $gtin = $itemBox[8];
                                if (!isset($productKeyGtin[$gtin])) {
                                    $gtin = '00' . $gtin;
                                }
                                $product = $productKeyGtin[$gtin];

                                $box_item_existed = DB::table('purchase_order_box_item')
                                    ->where('po_id', $orderId)
                                    ->where('po_box_id', $orderBoxId)
                                    ->where('product_id', $product->id)
                                    ->first();

                                if (empty($box_item_existed)) {
                                    $box_item = [
                                        'po_id' => $orderId,
                                        'po_box_id' => $orderBoxId,
                                        'sku' => $product->sku,
                                        'product_id' => $product->id,
                                        // "external_sku" => $itemLine->sku,
                                        'gtin' => $gtin,
                                        'quantity' => $itemBox[7],
                                        'quantity_api' => $itemBox[7],
                                        'created_at' => date('Y-m-d H:i:s'),
                                    ];
                                    DB::table('purchase_order_box_item')->insert($box_item);
                                }
                            }
                        }
                    }
                }
            } else {
                if (isset($cartons[$cartonsOrderId])) {
                    $tracking_number_in_box = $cartons[$cartonsOrderId][0][3];
                    $orderBox = DB::table('purchase_order_box')
                        ->where('tracking_number', $tracking_number_in_box)
                        ->first();

                    if (empty($orderBox)) {
                        $box = [
                            'po_id' => $orderId,
                            'box_number' => $cartonsOrderId,
                            'invoice_number' => $order['invoiceinfo']['invoicenum'],
                            'tracking_number' => $tracking_number_in_box,
                            'created_at' => date('Y-m-d H:i:s'),
                        ];
                        $orderBoxId = DB::table('purchase_order_box')->insertGetId($box);

                        foreach ($cartons[$cartonsOrderId] as $key => $itemBox) {
                            $gtin = $itemBox[8];
                            if (!isset($productKeyGtin[$gtin])) {
                                $gtin = '00' . $gtin;
                            }
                            $product = $productKeyGtin[$gtin];

                            $box_item_existed = DB::table('purchase_order_box_item')
                                ->where('po_id', $orderId)
                                ->where('po_box_id', $orderBoxId)
                                ->where('product_id', $product->id)
                                ->first();

                            if (empty($box_item_existed)) {
                                $box_item = [
                                    'po_id' => $orderId,
                                    'po_box_id' => $orderBoxId,
                                    'sku' => $product->sku,
                                    'product_id' => $product->id,
                                    // "external_sku" => $itemLine->sku,
                                    'gtin' => $gtin,
                                    'quantity' => $itemBox[7],
                                    'quantity_api' => $itemBox[7],
                                    'created_at' => date('Y-m-d H:i:s'),
                                ];

                                DB::table('purchase_order_box_item')->insert($box_item);
                            }
                        }
                    }
                }
            }

            DB::commit();
        } catch (\Exception $exception) {
            // dd($exception);
            DB::rollBack();

            return response()->json(['order_number' => ['insert not success']], 422);
        }

        return response()->json(['order_number' => ['insert success']]);
    }

    private function syncSsactivewear($input)
    {
        $order = DB::table('purchase_order_ssactivewear')
            ->where('order_number', $input['order_number'])
            ->where('invoice_number', $input['invoice_number'])
            ->first();

        if (empty($order)) {
            return response()->json(['invoice_number' => ['Not found Order']], 422);
        }
        $resData = $this->pullOrder($order->invoice_number);
        if (empty($resData)) {
            return response()->json(['invoice_number' => ['data is empty']], 422);
        }
        $status = 1;
        if (!isset($resData->boxes)) {
            $status = 0;
        } else {
            foreach ($resData->boxes as $itemBox) {
                if (empty($itemBox->trackingNumber)) {
                    $status = 0;
                }
            }
        }
        if ($status == 0) {
            return response()->json(['invoice_number' => ['Missing tracking number']], 422);
        }
        $gtin_miss = [];
        if (!empty($resData->lines)) {
            $arrGtin = array_unique(array_column($resData->lines, 'gtin'));
            $status = true;
            foreach ($arrGtin as $gtin) {
                if (empty($this->getProductByGtin($gtin))) {
                    $gtin_miss[] = $gtin;
                    $status = false;
                }
            }
            if ($status == false) {
                if (!empty($gtin_miss)) {
                    $arrGtinLog = array_unique($gtin_miss);
                    $gtinNotFoundText = implode(',', $arrGtinLog);

                    return response()->json(['invoice_number' => ['gtins not found:' . $gtinNotFoundText]], 422);
                }
            }
        }

        return $this->handleSsactivewearOrder($resData);
    }

    public function handleSsactivewearOrder($ssacOrder)
    {
        try {
            DB::beginTransaction();
            $warehouse_id = $ssacOrder->shippingAddress->zip == '95112' ? 1 : 2; /// Fix cung San Joe la 1, Texas = 2;

            $orderId = DB::table('purchase_order')->insertGetId(
                [
                    'order_number' => $ssacOrder->orderNumber,
                    'po_number' => $ssacOrder->poNumber,
                    'invoice_number' => $ssacOrder->invoiceNumber,
                    'order_date' => Carbon::parse($ssacOrder->orderDate)->format('Y-m-d H:i:s'),
                    'delivery_date' => Carbon::parse($ssacOrder->shipDate)->format('Y-m-d H:i:s'),
                    'order_status' => $ssacOrder->orderStatus,
                    'tracking_number' => $ssacOrder->trackingNumber,
                    'warehouse_id' => $warehouse_id,
                    'vendor_id' => 3, /// SSActivewear có id = 3
                    'user_id' => 22 // username : API
                ]);

            if (!empty($ssacOrder->lines)) {
                foreach ($ssacOrder->lines as $line) {
                    $product = $this->getProductByGtin($line->gtin);
                    $quantity = $line->qtyOrdered > 0 ? $line->qtyOrdered : 0;
                    DB::table('purchase_order_item')->insert([
                        'po_id' => $orderId,
                        'product_id' => $product->id,
                        'quantity' => $quantity,
                        'price' => $line->price,
                        'total' => ($line->price) * $quantity,
                    ]);
                    ProductQuantityRepository::updateInComeQuantity($warehouse_id, $product->id, $quantity);
                }
            }

            ///Todo : insert boxes, boxes item
            foreach ($ssacOrder->boxes as $box) {
                $orderBoxId = DB::table('purchase_order_box')->insertGetId([
                    'po_id' => $orderId,
                    'box_number' => $box->boxNumber,
                    'invoice_number' => $ssacOrder->invoiceNumber,
                    'tracking_number' => $box->trackingNumber,
                    'created_at' => date('Y-m-d H:i:s'),
                ]);

                foreach ($box->lines as $itemLine) {
                    $quantity = $itemLine->qtyOrdered > 0 ? $itemLine->qtyOrdered : 0;
                    $product = $this->getProductByGtin($itemLine->gtin);
                    //$product = Product::where('gtin', $itemLine->gtin)->first();
                    DB::table('purchase_order_box_item')->insert([
                        'po_id' => $orderId,
                        'po_box_id' => $orderBoxId,
                        'sku' => !empty($product->sku) ? $product->sku : null,
                        'product_id' => !empty($product->id) ? $product->id : null,
                        'external_sku' => $itemLine->sku,
                        'gtin' => $itemLine->gtin,
                        'quantity' => $quantity,
                        'quantity_api' => $itemLine->qtyOrdered,
                        'created_at' => date('Y-m-d H:i:s'),
                    ]);
                }
            }

            ///Todo : Update nguoc lai
            DB::table('purchase_order_ssactivewear')
                ->where('order_number', $ssacOrder->orderNumber)
                ->where('invoice_number', $ssacOrder->invoiceNumber)
                ->update(['po_id' => $orderId, 'status_insert_order' => PurchaseOrderSSactivewear::STATUS_INSERT_ORDER_SUCCESS]);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            return response()->json(['order_number' => ['insert not success']], 422);
        }

        return response()->json(['order_number' => ['insert success']]);
    }

    public function getProductByGtin($gtin)
    {
        $query = "SELECT * FROM product
                    WHERE COALESCE(INSTR(?, gtin)) > 0 AND gtin is not null and gtin != '' AND is_deleted = 0 LIMIT 1";
        $data = DB::select($query, [$gtin]);
        if (!empty($data)) {
            return $data[0];
        }

        return [];
    }

    private function pullOrder($invoiceNumber)
    {
        try {
            $client = new Client();
            $response = $client->request(
                'GET', /*instead of POST, you can use GET, PUT, DELETE, etc*/
                "https://api.ssactivewear.com/v2/orders/$invoiceNumber?lines=true&Boxes=true&mediatype=json",
                [
                    'auth' => ['509745', '8bedc55d-3de6-4535-b6c8-6c2d8fe86c64'] /*if you don't need to use a password, just leave it null*/
                ],
            );
        } catch (\Exception $exception) {
            return [];
        }
        $data = json_decode($response->getBody()->getContents());

        return $data[0];
    }
}
