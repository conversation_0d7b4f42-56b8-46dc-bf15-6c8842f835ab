<?php

namespace App\Repositories;

use App\Models\ProductRankHistory;

class ProductRankHistoryRepository extends CommonRepository
{
    public function getDetail($productId)
    {
        $result = [];
        $quarter = now()->subQuarter();
        $data = ProductRankHistory::query()
            ->where('product_id', $productId)
            ->orderByDesc('year')
            ->orderByDesc('quarter')
            ->get();

        while ($quarter->year >= 2022) {
            $year = $quarter->year;
            $item = $data->where('year', $year)
                ->where('quarter', $quarter->quarter)
                ->first();

            if (!array_key_exists($year, $result)) {
                $result[$year] = [
                    'year' => $year,
                    'data' => [],
                ];
            }

            $result[$year]['data'][] = [
                'year' => $year,
                'quarter' => $quarter->quarter,
                'total_revenue' => $item->total_revenue ?? null,
                'total_unit_sold' => $item->total_unit_sold ?? null,
                'rank' => $item->rank ?? null,
            ];
            $quarter->subQuarter();
        }

        return array_values($result);
    }
}
