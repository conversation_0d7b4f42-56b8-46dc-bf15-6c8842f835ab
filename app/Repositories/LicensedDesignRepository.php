<?php

namespace App\Repositories;

use App\Models\LicensedDesign;
use App\Models\LicensedDesignPrintFile;
use App\Models\LicensedHolder;
use App\Models\LicensedProduct;
use Log;
use Maatwebsite\Excel\Facades\Excel;
use <PERSON><PERSON>\JWTAuth\Claims\Collection;

class LicensedDesignRepository extends CommonRepository
{
    public function getLicensedDesigns($params)
    {
        setTimezone();
        $query = LicensedDesignPrintFile::with('design.holder', 'user:id,username');

        if (!empty($params['licensed_design_id'])) {
            $query->where('licensed_design_id', $params['licensed_design_id']);
        }

        if (!empty($params['licensed_code'])) {
            $query->whereHas('design.holder', function ($query) use ($params) {
                $query->where('licensed_code', $params['licensed_code']);
            });
        }

        if (!empty($params['licensed_holder'])) {
            $query->whereHas('design.holder', function ($query) use ($params) {
                $query->where('licensed_holder', $params['licensed_holder']);
            });
        }

        if (empty(($params['limit']))) {
            return $query->orderByDesc('id')->get();
        }

        return $query->orderByDesc('id')->paginate($params['limit']);
    }

    public function createLicensedDesign($params)
    {
        $printFileInsert = [];
        $designInsert = [];
        $uniqueDesigns = [];
        try {
            foreach ($params['data'] ?? [] as $value) {
                $printFileInsert[] = [
                    'licensed_design_id' => $value['licensed_design_id'],
                    'version' => $value['version'],
                    'print_area' => $value['print_area'],
                    'design_url' => $value['design_url'],
                    'created_at' => now(),
                    'created_by' => auth()->user()->id,
                    'status' => true,
                ];

                $designKey = $value['licensed_holder_id'] . '_' . $value['licensed_design_id'];

                if (!isset($uniqueDesigns[$designKey])) {
                    $designInsert[] = [
                        'licensed_holder_id' => $value['licensed_holder_id'],
                        'licensed_design_id' => $value['licensed_design_id'],
                        'created_at' => now(),
                    ];
                    $uniqueDesigns[$designKey] = true; // Đánh dấu giá trị này đã tồn tại
                }
            }

            LicensedDesignPrintFile::insert($printFileInsert);
            LicensedDesign::insert($designInsert);

            return true;
        } catch (\Throwable $th) {
            Log::error('LicensedDesignRepository.createLicensedDesign: ', [$th]);

            return false;
        }
    }

    public function updateLicensedDesign($id, $params)
    {
        $licensedDesgin = LicensedDesignPrintFile::findOrFail($id);
        LicensedDesignPrintFile::where('licensed_design_id', $licensedDesgin->licensed_design_id)->update(['status' => $params['status']]);
        LicensedProduct::where('licensed_design_id', $licensedDesgin->licensed_design_id)->update(['status' => $params['status']]);

        return $licensedDesgin;
    }

    public function validateImportLicensedDesign($file)
    {
        $data = Excel::toCollection(new Collection(), $file)->first();
        $licensedHolders = LicensedHolder::where('status', LicensedHolder::STATUS_ACTIVE)->get();
        $licenseProducts = LicensedProduct::where('status', LicensedProduct::STATUS_ACTIVE)->pluck('licensed_design_id')->toArray();
        $licenseDesignPrintFiles = LicensedDesignPrintFile::pluck('licensed_design_id')->toArray();
        $licenseDesigns = LicensedDesign::pluck('licensed_design_id')->toArray();
        $fileHeading = ['Licensed Code', 'Licensed Design ID', 'Version', 'Print Area', 'Design URL'];
        $validatedData = [];
        $countValid = 0;
        $validData = [];

        foreach ($data as $key => $row) {
            if ($key === 0) {
                if (array_values(array_filter($row->toArray(), function ($value) {
                    return $value !== null && $value !== '';
                })) !== $fileHeading) {
                    return [
                        'status' => false,
                        'message' => 'Import file does not match template.'
                    ];
                }

                $row->push('Error');
                $validatedData[] = $row->toArray();

                continue;
            }

            $rowArray = $validRow = $row->toArray();
            $errorMessages = [];
            $holder = $row[0];
            $designId = $row[1];
            $version = $row[2];
            $printArea = str_replace(' ', '_', $row[3]);
            $designUrl = $row[4];
            $holder = $licensedHolders->where('licensed_code', $holder)->first();

            if ($holder) {
                $validRow[0] = $holder->id;
            } else {
                $errorMessages[] = 'holder not found - inactive or not existing';
            }

            if (!in_array($designId, $licenseProducts)) {
                $errorMessages[] = 'design ID not found - inactive or not existing';
            }

            if (strlen($designId) > 50) {
                $errorMessages[] = 'design ID too long - max 50 characters';
            }

            if (in_array($designId, $licenseDesigns) || in_array($designId, $licenseDesignPrintFiles)) {
                $errorMessages[] = 'design ID duplicated - design ID has been saved with existing URL Links in Designs tab';
            }

            if (!in_array($version, [LicensedDesignPrintFile::DEFAULT_VERSION, LicensedDesignPrintFile::BLACK_VERSION, LicensedDesignPrintFile::WHITE_VERSION])) {
                $errorMessages[] = 'invalid version - not default, white or black';
            }

            if (!in_array($printArea, [LicensedDesignPrintFile::INNER_NECK_LABEL_PRINT_AREA, LicensedDesignPrintFile::FRONT_PRINT_AREA])) {
                $errorMessages[] = 'invalid print area - not front or inner neck label';
            }

            if (!filter_var($designUrl, FILTER_VALIDATE_URL)) {
                $errorMessages[] = 'Invalid Design URL.';
            }

            $errorColumn = implode(', ', $errorMessages);
            $rowArray[] = $errorColumn;
            $validatedData[] = $rowArray;

            if (empty($errorColumn)) {
                $countValid++;
                $rowArray[3] = $printArea;
                $validData[] = $validRow;
            }
        }

        return [
            'data' => $validatedData,
            'count_valid' => $countValid,
            'valid' => $this->formatValidData($validData),
            'total' => count($data) == 0 ? 0 : count($data) - 1,
            'error' => $countValid < count($data) ? true : false,
            'status' => true,
        ];
    }

    public function formatValidData($data)
    {
        return array_map(function ($row) {
            return [
                'licensed_holder_id' => $row[0],
                'licensed_design_id' => $row[1],
                'version' => $row[2],
                'print_area' => str_replace(' ', '_', $row[3]),
                'design_url' => $row[4],
            ];
        }, $data);
    }
}
