<?php

namespace App\Repositories;

use App\Models\Department;
use App\Models\Employee;
use DateTime;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class PerformanceReportRepository extends CommonRepository
{
    public function fetchPerformanceReportForManager($request)
    {
        $query = DB::table('time_tracking')
            ->select('department.id AS department_id', 'department.name AS department',
                'time_tracking.job_type', 'employee_id', 'employee.name AS employee_name',
                DB::raw('SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) AS hours'),
                DB::raw('SUM(time_tracking.`quantity`) AS output'),
                DB::raw('(SUM(time_tracking.`quantity`) / SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) )AS average'),
                DB::raw('MIN(time_tracking.start_time) AS start_time_tracking'),
                DB::raw('MAX(time_tracking.end_time) AS end_time_tracking'),
            )
            ->join('department_job_type', 'department_job_type.job_type', '=', 'time_tracking.job_type')
            ->join('department', 'department.id', '=', 'department_job_type.department_id')
            ->join('employee', 'employee.id', '=', 'time_tracking.employee_id')
            ->where('department.performance_report', Department::PERFORMANCE_REPORT)
            ->where('employee.performance_report', Employee::PERFORMANCE_REPORT)
            ->where('employee.warehouse_id', config('jwt.warehouse_id'))
            ->where('department.id', $request['department_id'])
            ->whereDate('time_tracking.start_time', '>=', $request['start_date'])
            ->whereDate('time_tracking.start_time', '<=', $request['end_date'])
            ->where(DB::raw('TIMESTAMPDIFF(SECOND, time_tracking.start_time, time_tracking.end_time)'), '>=', value: 900)
            ->groupBy([
                'department.id',
                'employee_id',
                'department_job_type.job_type'
            ]);

        if (!empty($request['job_type'])) {
            $query->where('department_job_type.job_type', $request['job_type']);
        }
        $data = $query->get()->toArray();

        if (empty($data)) {
            return [];
        }

        $dataPerformance['department'] = $data[0]->department;
        $dataPerformance['output'] = array_sum(array_column($data, 'output'));
        $dataPerformance['hours'] = array_sum(array_column($data, 'hours'));
        $dataPerformance['start_time_tracking'] = min(array_column($data, 'start_time_tracking'));
        $dataPerformance['end_time_tracking'] = max(array_column($data, 'end_time_tracking'));
        $average = $dataPerformance['hours'] > 0 ? $dataPerformance['output'] / $dataPerformance['hours'] : '0';
        $dataPerformance['average'] = $average > $dataPerformance['output'] ? $dataPerformance['output'] : $average;
        $dataPerformance['employees'] = $this->refactorLogByJobType($data, $request['start_date'], $request['end_date']);
        $dataPerformance['total_ink'] = array_sum(array_column($dataPerformance['employees'], 'total_ink'));

        return $dataPerformance;
    }

    public function fetchPerformanceReportForManagerV2($request)
    {
        $taskType = [
            'quality_control' => 'qc',
        ];
        if (array_key_exists($request['job_type'], $taskType)) {
            $jobType = $taskType[$request['job_type']];
        } else {
            $jobType = $request['job_type'];
        }
        $department = Department::find($request['department_id']);
        $query = DB::table('employee_work_logs')
            ->join('department_job_type', 'department_job_type.job_type', '=', 'employee_work_logs.task_type')
            ->select('department.id AS department_id', 'department.name AS department', 'employee_work_logs.average_ink_usage',
                'department_job_type.job_type', 'employee_id', 'employee.name AS employee_name', 'employee_work_logs.work_date', 'employee_work_logs.total_tasks', 'employee_work_logs.total_hours_worked',
                DB::raw('COUNT(employee_work_logs.work_date) AS total_times'),
            )
            ->join('department', 'department.id', '=', 'department_job_type.department_id')
            ->join('employee', 'employee.id', '=', 'employee_work_logs.employee_id')
            ->where('department.performance_report', Department::PERFORMANCE_REPORT)
            ->where('employee.performance_report', Employee::PERFORMANCE_REPORT)
            ->where('employee.warehouse_id', config('jwt.warehouse_id'))
            ->where('department.id', $request['department_id'])
            ->where('employee.department', $department->name)
            ->where('department_job_type.job_type', $jobType)
            ->where('employee_work_logs.work_date', '>=', $request['start_date'])
            ->where('employee_work_logs.work_date', '<=', $request['end_date'])
            ->groupBy([
                'employee_id',
                'employee_work_logs.work_date',
            ])->orderBy('employee_id', 'ASC');
        if (!empty($request['employee_id'])) {
            $query->where('employee_id', $request['employee_id']);
        }
        $sql = interpolateQuery($query);
        $data = $query->get()->toArray();

        if (empty($data)) {
            return [$sql];
        }
        $index = 0;
        foreach ($data as $employee) {
            if (empty($dataPerformance['employees'][$index])) {
                $dataPerformance['employees'][$index]['employee_id'] = $employee->employee_id;
                $dataPerformance['employees'][$index]['employee_name'] = $employee->employee_name;
                $dataPerformance['employees'][$index]['total_outputs'] = $employee->total_tasks;
                $dataPerformance['employees'][$index]['total_work_days'] = 1;
                $dataPerformance['employees'][$index]['total_hours_worked'] = $employee->total_hours_worked;
                $dataPerformance['employees'][$index]['average_ink_usage'] = $employee->average_ink_usage;
            } elseif ($dataPerformance['employees'][$index]['employee_id'] == $employee->employee_id) {
                $dataPerformance['employees'][$index]['total_outputs'] += $employee->total_tasks;
                $dataPerformance['employees'][$index]['total_work_days']++;
                $dataPerformance['employees'][$index]['total_hours_worked'] += $employee->total_hours_worked;
                $dataPerformance['employees'][$index]['average_ink_usage'] += $employee->average_ink_usage;
            } else {
                $index++;
                $dataPerformance['employees'][$index]['employee_id'] = $employee->employee_id;
                $dataPerformance['employees'][$index]['employee_name'] = $employee->employee_name;
                $dataPerformance['employees'][$index]['total_outputs'] = $employee->total_tasks;
                $dataPerformance['employees'][$index]['total_work_days'] = 1;
                $dataPerformance['employees'][$index]['total_hours_worked'] = $employee->total_hours_worked;
                $dataPerformance['employees'][$index]['average_ink_usage'] = $employee->average_ink_usage;
            }
            $dataPerformance['employees'][$index]['data'][] = [
                'work_date' => $employee->work_date,
                'total_tasks' => $employee->total_tasks,
                'total_hours_worked' => $employee->total_hours_worked,
                'average_ink_usage' => $employee->average_ink_usage,
            ];
        }
        $dataPerformance['sql'] = $sql;

        return $dataPerformance;
    }

    public function fetchTopPerformanceEmployees($request)
    {
        $query = DB::table('time_tracking')
            ->select('employee_id', 'employee.name AS employee_name', 'department.name AS department',
                'time_tracking.job_type',
                DB::raw(' IF(SUM(time_tracking.`quantity`) / SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time))/3600)> SUM(time_tracking.`quantity`),
                SUM(time_tracking.`quantity`),
                SUM(time_tracking.`quantity`) / SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time))/3600)) AS average'),
            )
            ->join('department_job_type', 'department_job_type.job_type', '=', 'time_tracking.job_type')
            ->join('department', 'department.id', '=', 'department_job_type.department_id')
            ->join('employee', 'employee.id', '=', 'time_tracking.employee_id')
            ->where('department.performance_report', Department::PERFORMANCE_REPORT)
            ->where('employee.performance_report', Employee::PERFORMANCE_REPORT)
            ->where('employee.warehouse_id', config('jwt.warehouse_id'))
            ->where('department.id', $request['department_id'])
            ->whereDate('time_tracking.start_time', '>=', $request['start_date'])
            ->whereDate('time_tracking.start_time', '<=', $request['end_date'])
            ->where(DB::raw('TIMESTAMPDIFF(SECOND, time_tracking.start_time, time_tracking.end_time)'), '>=', 900)
            ->groupBy([
                'employee_id',
                'department.id',
                'department_job_type.job_type'
            ]);

        if (!empty($request['job_type'])) {
            $query->where('department_job_type.job_type', $request['job_type']);
        }

        if (!empty($request['sort_by'])) {
            $sortBy = !empty($request['sort_by']) && in_array(strtolower($request['sort_by']), ['asc', 'desc']) ? $request['sort_by'] : 'asc';
            $query->orderBy('average', $sortBy);
        }
        if (!empty($request['limit'])) {
            $query->limit($request['limit']);
        }

        return $query->get();
    }

    private function generateDateRange(Carbon $start_date, Carbon $end_date)
    {
        $dates = [];
        for ($date = $start_date->copy(); $date->lte($end_date); $date->addDay()) {
            $dates[] = $date->format('Y-m-d');
        }

        return $dates;
    }

    public function fetchPerformanceByDepartments($input)
    {
        $inputDepartmentIds = !empty($input['department_ids']) ? $input['department_ids'] : [];

        return $this->buildDataPerformanceOfDepartmentEachRangeDate($inputDepartmentIds, $input['start_date'], $input['end_date']);
    }

    public function getDepartmentWithNewLogic($departmentId): bool
    {
        $department = Department::find($departmentId);

        switch (strtolower($department->name)) {
            case strtolower(Department::DEPARTMENT_PULLING):
                return true;
            default:
                return false;
        }
    }

    public function getPerformanceByDepartment($input)
    {
        $inputDepartmentIds = !empty($input['department_id']) ? [$input['department_id']] : [];

        return $this->buildDataPerformanceOfDepartmentEachRangeDate($inputDepartmentIds, $input['start_date'], $input['end_date']);
    }

    private function buildDataPerformanceOfDepartmentEachRangeDate($inputDepartmentIds, $startDate, $endDate)
    {
        $departments = $this->fetchDepartmentByIds($inputDepartmentIds);

        $dates = $this->generateDateRange(Carbon::create($startDate), Carbon::create($endDate));
        $datesAppendDepartment = array_map(function ($date) use ($departments) {
            return [
                'date' => $date,
                'departments' => $departments
            ];
        }, $dates);

        $dataTimeTracking = $this->fetchTimeTrackingOfDeptByRankDate($startDate, $endDate, $inputDepartmentIds);

        //Todo : transform data tạm
        $datesAppendTime = array_map(function ($date) use ($dataTimeTracking) {
            $tmp['date'] = $date['date'];
            $depArr = [];
            foreach ($date['departments'] as $dep) {
                $d['id'] = $dep->id;
                $d['name'] = $dep->name;
                $k = [];
                foreach ($dataTimeTracking as $itemTimeTracking) {
                    if (strpos($itemTimeTracking->start_time, $date['date']) !== false && $dep->id == $itemTimeTracking->department_id) {
                        $k[] = $itemTimeTracking;
                    }
                }
                $d['time_tracking'] = $k;
                array_push($depArr, $d);
            }
            $tmp['departments'] = $depArr;

            return $tmp;
        }, $datesAppendDepartment);

        //Todo : transform data
        $res = array_map(function ($item) {
            $departments = array_map(function ($itemDept) {
                $valDep['id'] = $itemDept['id'];
                $valDep['name'] = $itemDept['name'];
                $valDep['output'] = array_sum(array_column($itemDept['time_tracking'], 'output'));
                $valDep['hours'] = array_sum(array_column($itemDept['time_tracking'], 'hours'));
                $valDep['average'] = $valDep['hours'] > 0 ? ($valDep['output'] / $valDep['hours']) : '0';

                return $valDep;
            }, $item['departments']);

            $item['departments'] = $departments;

            return $item;
        }, $datesAppendTime);

        return $res;
    }

    private function fetchTimeTrackingOfDeptByRankDate($startDate, $endDate, $ids)
    {
        $query = DB::table('time_tracking')
            ->select(
                'department_job_type.department_id',
                'department.name AS department',
                'start_time',
                DB::raw('SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) AS hours'),
                DB::raw('SUM(time_tracking.`quantity`) AS output'),
            )
            ->join('department_job_type', 'department_job_type.job_type', '=', 'time_tracking.job_type')
            ->join('department', 'department.id', '=', 'department_job_type.department_id')
            ->join('employee', 'employee.id', '=', 'time_tracking.employee_id')
            ->whereDate('time_tracking.start_time', '>=', $startDate)
            ->whereDate('time_tracking.start_time', '<=', $endDate);

        $query->where('department.performance_report', Department::PERFORMANCE_REPORT)
              ->where('employee.performance_report', Employee::PERFORMANCE_REPORT)
             ->where('employee.warehouse_id', config('jwt.warehouse_id'));

        if (!empty($ids)) {
            $query->whereIn('department.id', $ids);
        }
        $query->groupBy(['department_job_type.department_id', 'start_time']);

        return  $query->get()->toArray();
    }

    private function fetchTimeTrackingOfEmployeeByRankDate($startDate, $endDate, $ids, $departmentId)
    {
        $query = DB::table('time_tracking')
            ->select('employee_id', 'employee.name AS employee_name', 'department.name AS department',
                'time_tracking.start_time',
                DB::raw('SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) AS hours'),
                DB::raw('SUM(time_tracking.`quantity`) AS output'),
            )
            ->join('department_job_type', 'department_job_type.job_type', '=', 'time_tracking.job_type')
            ->join('department', 'department.id', '=', 'department_job_type.department_id')
            ->join('employee', 'employee.id', '=', 'time_tracking.employee_id')
            ->where('department.id', $departmentId);

        if (!empty($ids)) {
            $query->whereIn('employee_id', $ids);
        }
        $query->groupBy([
            'employee_id',
            'time_tracking.start_time'
        ]);

        $query->where('department.performance_report', Department::PERFORMANCE_REPORT)
              ->where('employee.performance_report', Employee::PERFORMANCE_REPORT)
              ->where('employee.warehouse_id', config('jwt.warehouse_id'));

        if (!empty($startDate)) {
            $query->whereDate('time_tracking.start_time', '>=', $startDate);
        }
        if (!empty($input['end_date'])) {
            $query->whereDate('time_tracking.start_time', '<=', $endDate);
        }

        return $query->get()->toArray();
    }

    public function summaryPerformanceReportForLeader($input)
    {
        $reportSelect = $this->calculatePerformanceReportOfDepartment($input['department_id'], $input['start_date'], $input['end_date']);

        if (empty($reportSelect)) {
            return [
                'members' => [
                    'value' => 0,
                    'percent' => 0,
                ],
                'hours' => [
                    'value' => 0,
                    'percent' => 0,
                ],
                'output' => [
                    'value' => 0,
                    'percent' => 0,
                ],
                'average' => [
                    'value' => 0,
                    'percent' => 0,
                ],
            ];
        }

        $startDateBefore3day = Carbon::parse($input['start_date'])->subDays(3)->toDateString();
        $endDateBefore3day = Carbon::parse($input['end_date'])->subDays(3)->toDateString();
        $reportSelectBefore3Day = $this->calculatePerformanceReportOfDepartment($input['department_id'], $startDateBefore3day, $endDateBefore3day);
        $data = [
            'members' => [
                'value' => $reportSelect->members,
                'percent' => $this->calculatePercentWithin3Day($reportSelect->members, $reportSelectBefore3Day->members),
            ],
            'hours' => [
                'value' => $reportSelect->hours,
                'percent' => $this->calculatePercentWithin3Day($reportSelect->hours, $reportSelectBefore3Day->hours),
            ],
            'output' => [
                'value' => $reportSelect->output,
                'percent' => $this->calculatePercentWithin3Day($reportSelect->output, $reportSelectBefore3Day->output),
            ],
            'average' => [
                'value' => $reportSelect->average,
                'percent' => $this->calculatePercentWithin3Day($reportSelect->average, $reportSelectBefore3Day->average),
            ],
        ];

        return $data;
    }

    private function calculatePercentWithin3Day($currentSelect, $before3Day)
    {
        $currentSelect = !is_null($currentSelect) ? $currentSelect : 0;
        $before3Day = !is_null($before3Day) ? $before3Day : 0;

        return  $before3Day > 0 ? (($currentSelect - $before3Day) / $before3Day) * 100 : 100;
    }

    private function calculatePerformanceReportOfDepartment($departmentId, $startDate, $endDate)
    {
        $query = DB::table('time_tracking')
            ->select('department.id AS department_id', 'department.name AS department',
                DB::raw('COUNT(DISTINCT employee_id) AS members'),
                DB::raw('SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) AS hours'),
                DB::raw('SUM(time_tracking.`quantity`) AS output'),
                DB::raw('(SUM(time_tracking.`quantity`) / SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) )AS average'),
            )
            ->join('department_job_type', 'department_job_type.job_type', '=', 'time_tracking.job_type')
            ->join('department', 'department.id', '=', 'department_job_type.department_id')
            ->join('employee', 'employee.id', '=', 'time_tracking.employee_id')
            ->groupBy('department_job_type.department_id');

        $query->where('department.performance_report', Department::PERFORMANCE_REPORT)
              ->where('employee.performance_report', Employee::PERFORMANCE_REPORT)
            ->where('employee.warehouse_id', config('jwt.warehouse_id'));

        $query->where('department_job_type.department_id', $departmentId)
            ->whereDate('time_tracking.start_time', '>=', $startDate)
            ->whereDate('time_tracking.start_time', '<=', $endDate);

        return $query->first();
    }

    public function fetchReportAllEmployees($input)
    {
        $query = DB::table('time_tracking')
            ->select('employee_id', 'employee.name AS employee_name', 'department.name AS department',
                'time_tracking.job_type',
                DB::raw('SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) AS hours'),
                DB::raw('SUM(time_tracking.`quantity`) AS output'),
                DB::raw('(SUM(time_tracking.`quantity`) / SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) )AS average'),
            )
            ->join('department_job_type', 'department_job_type.job_type', '=', 'time_tracking.job_type')
            ->join('department', 'department.id', '=', 'department_job_type.department_id')
            ->join('employee', 'employee.id', '=', 'time_tracking.employee_id')
            ->where('department.id', $input['department_id'])
            ->groupBy([
                'employee_id'
            ]);

        $query->where('department.performance_report', Department::PERFORMANCE_REPORT)
               ->where('employee.performance_report', Employee::PERFORMANCE_REPORT)
            ->where('employee.warehouse_id', config('jwt.warehouse_id'));

        if (!empty($input['start_date'])) {
            $query->whereDate('time_tracking.start_time', '>=', $input['start_date']);
        }
        if (!empty($input['end_date'])) {
            $query->whereDate('time_tracking.start_time', '<=', $input['end_date']);
        }

//        if (!empty($input['sort_by'])) {
//            $sortBy = !empty($input['sort_by']) && in_array(strtolower($input['sort_by']), ['asc', 'desc']) ? $input['sort_by'] : 'asc';
//            $query->orderBy('average', $sortBy);
//        }
//        if (!empty($input['top'])) {
//            $query->limit($input['top']);
//        }
        return $query->get()->toArray();
    }

    public function fetchReportAllEmployeeByTime($input)
    {
        $inputEmployeeIds = !empty($input['employee_ids']) ? $input['employee_ids'] : [];

        $employeeRepo = new EmployeeRepository();
        $employees = $employeeRepo->getEmployeeOfDepartment($input['department_id'], $inputEmployeeIds);

        $dates = $this->generateDateRange(Carbon::create($input['start_date']), Carbon::create($input['end_date']));

        $datesAppendEmployees = array_map(function ($date) use ($employees) {
            return [
                'date' => $date,
                'employees' => $employees
            ];
        }, $dates);
        $dataTimeTracking = $this->fetchTimeTrackingOfEmployeeByRankDate($input['start_date'], $input['end_date'], $inputEmployeeIds, $input['department_id']);

        // dd($datesAppendEmployees, $dataTimeTracking);

        //Todo : transform data tạm
        $datesAppendTime = array_map(function ($date) use ($dataTimeTracking) {
            $tmp['date'] = $date['date'];
            $depArr = [];
            foreach ($date['employees'] as $dep) {
                $d['id'] = $dep->employee_id;
                $d['name'] = $dep->employee_name;
                $k = [];
                foreach ($dataTimeTracking as $itemTimeTracking) {
                    if (strpos($itemTimeTracking->start_time, $date['date']) !== false && $dep->employee_id == $itemTimeTracking->employee_id) {
                        $k[] = $itemTimeTracking;
                    }
                }
                $d['time_tracking'] = $k;
                array_push($depArr, $d);
            }
            $tmp['employees'] = $depArr;

            return $tmp;
        }, $datesAppendEmployees);

        //Todo : transform data
        $res = array_map(function ($item) {
            $employees = array_map(function ($itemDept) {
                $valDep['id'] = $itemDept['id'];
                $valDep['name'] = $itemDept['name'];
                $valDep['output'] = array_sum(array_column($itemDept['time_tracking'], 'output'));
                $valDep['hours'] = array_sum(array_column($itemDept['time_tracking'], 'hours'));
                $valDep['average'] = $valDep['hours'] > 0 ? ($valDep['output'] / $valDep['hours'] > $valDep['output'] ? $valDep['output'] : $valDep['output'] / $valDep['hours']) : '0';

                return $valDep;
            }, $item['employees']);
            $item['employees'] = $employees;

            return $item;
        }, $datesAppendTime);

        return $res;
    }

    public function refactorLogByJobType($data, $startTime, $endTime)
    {
        $dataAvgPrinting = [];
        $department = !empty($data[0]->department) ? $data[0]->department : (!empty($data[0]['department']) ? $data[0]['department'] : '');
        if ($department == Department::DEPARTMENT_PRINTING) {
            $dataAvgPrinting = $this->getAveragePerformanceForPrinting($startTime, $endTime);
        }
        $dataNew = [];
        foreach ($data as $item) {
            $dataNew[$item->job_type][] = $item;
        }

        $transformData = array_map(function ($itemGroup) use ($dataAvgPrinting) {
            $item['job_type'] = $itemGroup[0]->job_type;
            $item['outputInv'] = array_sum(array_column($itemGroup, 'output'));
            $item['hoursInv'] = array_sum(array_column($itemGroup, 'hours'));
            $averageInv = $item['hoursInv'] > 0 ? $item['outputInv'] / $item['hoursInv'] : '0';
            $item['averageInv'] = $averageInv > $item['outputInv'] ? $item['outputInv'] : $averageInv;
            $item['start_time_tracking'] = min(array_column($itemGroup, 'start_time_tracking'));
            $item['end_time_tracking'] = max(array_column($itemGroup, 'end_time_tracking'));
            $employees = array_map(function ($itemEmployee) use ($dataAvgPrinting) {
                $employee['id'] = $itemEmployee->employee_id;
                $employee['employee_name'] = $itemEmployee->employee_name;
                $employee['output'] = $itemEmployee->output;
                $employee['hours'] = $itemEmployee->hours;
                $employee['average'] = !is_null($itemEmployee->average) ? ($itemEmployee->average > $itemEmployee->output ? $itemEmployee->output : $itemEmployee->average) : '0';
                $employee['job_type'] = $itemEmployee->job_type;
                $employee['start_time_tracking'] = $itemEmployee->start_time_tracking;
                $employee['end_time_tracking'] = $itemEmployee->end_time_tracking;
                $employee['job_type'] = $itemEmployee->job_type;
                if ($itemEmployee->department == Department::DEPARTMENT_PRINTING) {
                    $employee['avg_ink'] = isset($dataAvgPrinting[$itemEmployee->employee_id]) ? $dataAvgPrinting[$itemEmployee->employee_id]['avg_ink'] : '0';
                    $employee['total_ink'] = isset($dataAvgPrinting[$itemEmployee->employee_id]) ? $dataAvgPrinting[$itemEmployee->employee_id]['total_ink'] : '0';
                }

                return $employee;
            }, $itemGroup);
            $item['total_ink'] = array_sum(array_column($employees, 'total_ink'));
            $item['employees'] = array_values($employees);

            return $item;
        }, $dataNew);

        return array_values($transformData);
    }

    public function fetchDepartments()
    {
        return Department::with('jobTypes')
            ->where('performance_report', Department::PERFORMANCE_REPORT)
            ->get();
    }

    public function fetchDepartmentsForReport()
    {
        return DB::table('department')
            ->where('performance_report', Department::PERFORMANCE_REPORT)
            ->select('id', 'name')
            ->get();
    }

    public function fetchDepartmentByIds($departmentIds = [])
    {
        $query = DB::table('department')
            ->where('performance_report', Department::PERFORMANCE_REPORT)
            ->select('id', 'name');
        if ($departmentIds) {
            $query->whereIn('id', $departmentIds);
        }

        return $query->get()->toArray();
    }

    public function getAveragePerformanceForPrinting($startDate, $endDate)
    {
        $data = DB::table('sale_order_item_barcode')
            ->select('sale_order_item_barcode.employee_print_id',
                DB::raw('((SUM(sale_order_item_image.ink_color_cc) + SUM(sale_order_item_image.ink_white_cc)) / COUNT(DISTINCT sale_order_item_barcode.id)) AS avg_ink'),
                DB::raw('(SUM(sale_order_item_image.ink_color_cc) + SUM(sale_order_item_image.ink_white_cc)) AS total_ink'),
            )
            ->join('sale_order_item_image', 'sale_order_item_barcode.order_item_id', '=', 'sale_order_item_image.order_item_id')
            ->whereDate('sale_order_item_barcode.printed_at', '>=', $startDate)
            ->whereDate('sale_order_item_barcode.printed_at', '<=', $endDate)
            ->whereRaw('(sale_order_item_image.ink_color_cc + sale_order_item_image.ink_white_cc) > ?', [0])
            ->groupBy('sale_order_item_barcode.employee_print_id')
            ->get();
        if (empty($data)) {
            return [];
        }
        $arrAvg = [];
        $data->map(function ($item) use (&$arrAvg) {
            if ($item->avg_ink) {
                $arrAvg[$item->employee_print_id] = [
                    'avg_ink' => $item->avg_ink,
                    'total_ink' => $item->total_ink,
                ];
            }
        });

        return $arrAvg;
    }

    public function fetchEmployeePerformance($request)
    {
        setTimezone();
        $lastYear = Carbon::now()->subYear()->year;
        $timezone = 'America/Los_Angeles';
        // Get the current time in PST
        $currentTime = Carbon::now($timezone);
        $userId = $request['employee_id'];
        $departmentId = $request['department_id'];
        $data = [];
        // Calculate user's total average
        $user = Employee::find($userId);

        if ($user && $user->created_at) {
            // Calculate the tenure (age) of the user
            $createdDate = Carbon::parse($user->created_at);
            $currentDate = Carbon::now();
            $diff = $createdDate->diff($currentDate); // Calculate the difference in years

            $data['age'] = $diff->y . ' year' . ($diff->y > 1 ? 's' : '') . ' '
            . $diff->m . ' month' . ($diff->m > 1 ? 's' : '');
        }

        $userQuery = DB::table('time_tracking')
        ->where('employee_id', $userId)
        ->where('id', $request['time_tracking_id'])
        ->first();

        $endTime = new DateTime($currentTime->format('Y-m-d H:i:s'));
        $startTime = new DateTime($userQuery->start_time);

        $diffInSeconds = $endTime->getTimestamp() - $startTime->getTimestamp();

        $userTotalMinutes = number_format($diffInSeconds / 60, 2);

        $userTotalOutput = $userQuery->quantity ?? 0;
        $userTotalMinutes = is_numeric($userTotalMinutes) ? $userTotalMinutes : 0;
        $userTotalOutput = is_numeric($userTotalOutput) ? $userTotalOutput : 0;
        $data['average_user'] = round($userTotalMinutes > 0 ? ($userTotalOutput * 60) / $userTotalMinutes : 0);

        // Calculate department's total average
        $lastYearData = DB::table('time_tracking')
        ->select(
            'department.id AS department_id',
            'department.name AS department',
            'time_tracking.job_type',
            'employee_id',
            'employee.name AS employee_name',
            DB::raw('SUM(time_tracking.quantity) AS department_total_output'),
            DB::raw('SUM(TIME_TO_SEC(TIMEDIFF(end_time, start_time ))/3600) AS department_total_hours'),
        )
        ->join('department_job_type', 'department_job_type.job_type', '=', 'time_tracking.job_type')
        ->join('department', 'department.id', '=', 'department_job_type.department_id')
        ->join('employee', 'employee.id', '=', 'time_tracking.employee_id')
        ->where('department.performance_report', Department::PERFORMANCE_REPORT)
        ->where('employee.performance_report', Employee::PERFORMANCE_REPORT)
        ->where('department.id', $departmentId)
        ->where('employee.warehouse_id', $request['warehouse_id'])
        ->whereYear('time_tracking.start_time', $lastYear)
        ->whereYear('time_tracking.end_time', $lastYear);
        if (!empty($request['job_type'])) {
            $lastYearData->where('time_tracking.job_type', $request['job_type']);
        }

        $lastYearData = $lastYearData->groupBy([
            'employee_id',
            'department_job_type.job_type'
        ])->get();
        // Calculate performance for each employee

        $performances = $lastYearData->map(function ($data) {
            return [
                'employee_id' => $data->employee_id,
                'performance' => $data->department_total_hours != 0 ? ($data->department_total_output / $data->department_total_hours) : 0
            ];
        });

        // Sort performances in ascending order
        $sortedPerformances = $performances->sortByDesc('performance');
        // Calculate thresholds for removing top 10% and bottom 50%
        $totalEmployee = $sortedPerformances->count();
        $top10PercentIndex = ceil($totalEmployee * 0.10);
        $bottom50PercentIndex = ceil($totalEmployee * 0.50);
        if ($totalEmployee > $bottom50PercentIndex + $top10PercentIndex) {
            $topItems = $sortedPerformances->splice(0, $top10PercentIndex);
            $bottomItems = $sortedPerformances->splice(-$bottom50PercentIndex);
            $averagePerformance = $sortedPerformances->avg('performance');
        } else {
            // Not enough data to remove top 10% and bottom 50%, use all data
            $averagePerformance = $sortedPerformances->avg('performance');
        }

        // Calculate the average performance of the remaining data
        $data['average_last_year'] = $averagePerformance > 0 ? round($averagePerformance) : 0;

        $data['average_current_year'] = ($data['average_last_year'] > 0) ? round(1.05 * $data['average_last_year']) : 0;
        $data['last_year'] = $lastYear;
        $data['current_year'] = $currentTime->year;

        return $data;
    }
}
