<?php

namespace App\Repositories;

use App\Models\ScreenClient;
use App\Models\ScreenDesign;
use App\Models\ScreenOrder;
use App\Models\ScreenOrderAddress;
use App\Models\ScreenOrderCsv;
use App\Models\ScreenOrderHistory;
use App\Models\ScreenOrderItem;
use App\Models\ScreenOrderItemImage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ScreenOrderRepository extends CommonRepository
{
    public function getList($input)
    {
        $sortBy = !empty($input['sort_by']) ? $input['sort_by'] : 'desc';
        $sortColumn = (!empty($input['sort_column'])) ? $input['sort_column'] : 'created_at';
        $limit = !empty($input['limit']) ? $input['limit'] : $this->limit;
        $query = ScreenOrder::with(['createdBy', 'client']);
        if (!empty($input['external_number'])) {
            $query->where('external_number', 'LIKE', '%' . $input['external_number'] . '%');
        }
        if (!empty($input['order_number'])) {
            $query->where('order_number', 'LIKE', '%' . $input['order_number'] . '%');
        }
        if (!empty($input['client_id'])) {
            $query->where('client_id', $input['client_id']);
        }
        if (!empty($input['order_type'])) {
            $query->where('order_type', $input['order_type']);
        }

        if (!empty($input['filter_name']) && $input['filter_name'] != 'all') {
            $query->where('order_status', $input['filter_name']);
        }

        return $query->orderBy($sortColumn, $sortBy)->paginate($limit);
    }

    public function getDetail($id)
    {
        return ScreenOrder::with(['client.billingAddress', 'client.clientAddress', 'createdBy', 'items', 'items.images.design', 'items.images.mockup', 'items.product', 'shippingAddress', 'returnAddress', 'history.user'])->find($id);
    }

    public function getCountByStatus($input)
    {
        return ScreenOrder::where('order_status', $input['order_status'])->count();
    }

    public function importOrderHistories($input)
    {
        $sortBy = !empty($input['sort_by']) ? $input['sort_by'] : 'desc';
        $sortColumn = (!empty($input['sort_column'])) ? $input['sort_column'] : 'created_at';
        $limit = !empty($input['limit']) ? $input['limit'] : $this->limit;
        $query = ScreenOrderCsv::with(['user']);

        return $query->orderBy($sortColumn, $sortBy)->paginate($limit);
    }

    public function importCsv($input)
    {
    }

    public function transformOrder($rows)
    {
        $orderQuantity = 0;
        foreach ($rows as $row) {
            $orderQuantity += $row['Quantity'];
            $order = [
                'external_number' => $row["Customer's reference number"],
                'client_id' => $row['Client'],
                'buyer_name' => $row['Buyer'],
                'order_type' => $row['Order Type'],
                'shipped_at' => $row['Shipped by'] ? Carbon::parse($row['Shipped by'])->format('Y-m-d') : null,
                'pay_terms' => $row['Pay Terms'],
                'freight_terms' => $row['Freight Terms'],
                'tax_rate' => $row['Tax Rate'],
                'tax' => $row['Tax'],
                'shipping_and_handling_fee' => $row['S & H'],
                'other_fee' => $row['Other fees'],
                'client_note' => $row['Client comments'],
                'internal_note' => $row['Internal comments'],
                'ship_via' => $row['Ship Via'],
                'free_on_board' => $row['FOB'],
                'order_total' => $row['Total value of order'],
                'user_id' => auth()->id(),
            ];
        }
        $client = ScreenClient::where('name', $order['client_id'])->first();
        $order['client_id'] = $client?->id;
        $order['order_quantity'] = $orderQuantity;

        return $order;
    }

    public function transformOrderItem($rows, $orderId)
    {
        $data = [];

        return $data;
    }

    public function transformOrderItemImage($rows, $orderId)
    {
        $data = [];
        foreach ($rows as $row) {
            $orderItem = [
                'client_id' => $row['Client'],
                'screen_order_id' => $orderId,
                'sku' => $row['SKU'],
                'quantity' => $row['Quantity'],
                'unit_cost' => $row['Unit Cost'],
            ];
            $data[] = $orderItem;
        }

        return $data;
    }

    public function rule()
    {
        return [
            'Customer\'s reference number' => ['required', 'unique:screen_orders,external_number'],
            'Client' => ['required', 'exists:screen_clients,name'],
            'Buyer' => ['required', 'string', 'max:1000'],
            'Order Type' => ['required', Rule::in([ScreenOrder::ORDER_ECOM_TYPE, ScreenOrder::ORDER_STORE_TYPE])],
            'Shipped by' => ['required', 'regex:/^(0?[1-9]|1[0-2])\/(0?[1-9]|[12][0-9]|3[01])\/(\d{4})$/'],
            'SKU' => ['required', 'exists:product,sku'
            ],
            'Design ID 1' => ['nullable',
                Rule::exists('screen_designs', 'design_id')->where('type', ScreenDesign::DESIGN_TYPE),
            ],
            'Print area key 1' => ['nullable', 'exists:product_print_side,code_name'],
            'Mockup ID 1' => ['nullable',
                Rule::exists('screen_designs', 'design_id')->where('type', ScreenDesign::MOCKUP_TYPE),

            ],
            'Quantity' => ['required', 'integer', 'min:1'],
            'Unit Cost' => ['required', 'numeric', 'min:0'],
            'Address 1' => ['required', 'max:255'],
            'Address 2' => ['nullable', 'max:255'],
            'City' => ['required', 'max:255'],
            'State' => ['required_if:Country,US', 'max:255'],
            'ZipCode' => ['required', 'max:255', 'string', 'regex:/^\d+$/'],
            'Country' => ['required', 'exists:country,iso2'],
            'Client comments' => ['nullable', 'max:1000'],
            'Internal comments' => ['nullable', 'max:1000'],
            'Ship Via' => ['required'],
            'FOB' => ['required'],
            'Pay Terms' => ['required'],
            'Freight Terms' => ['nullable'],
            'Tax Rate' => ['nullable', 'numeric', 'min:0'],
            'Tax' => ['nullable', 'numeric', 'min:0'],
            'S & H' => ['nullable', 'numeric', 'min:0'],
            'Other fees' => ['nullable', 'numeric', 'min:0'],
            'Total value of order' => ['required', 'numeric', 'min:0'],

        ];
    }

    public function customMessages()
    {
        return [
            'Customer\'s reference number.required' => 'The customer\'s reference number is required.',
            'Customer\'s reference number.unique' => 'The customer\'s reference number has already been taken.',
            'Client.required' => 'The client is required.',
            'Client.exists' => 'The client does not exist.',
            'Buyer.required' => 'The buyer is required.',
            'Order Type.required' => 'The order type is required.',
            'Order Type.in' => 'The selected order type is invalid.',
            'Shipped by.required' => 'The shipped by is required.',
            'Shipped by.date_format' => 'The shipped by does not match the format Y-m-d.',
            'SKU.required' => 'The sku is required.',
            'SKU.exists' => 'The sku does not exist.',
            'Design ID 1.exists' => 'The selected design id 1 does not exist.',
            'Mockup ID 1.exists' => 'The selected mockup id 1 does not exist.',
            'Quantity.required' => 'The quantity is required.',
            'Quantity.integer' => 'The quantity must be an integer.',
            'Unit Cost.required' => 'The unit cost is required.',
            'Unit Cost.numeric' => 'The unit cost must be a number.',
            'Address 1.required' => 'The address 1 is required.',
            'Address 1.max' => 'The address 1 must not be greater than :max characters.',
            'Address 2.max' => 'The address 2 must not be greater than :max characters.',
            'City.required' => 'The city is required.',
            'City.max' => 'The city must not be greater than :max characters.',
            'State.max' => 'The state must not be greater than :max characters.',
            'State.required_if' => 'The state is required when city is US.',
            'ZipCode.required' => 'The zip code is required.',
            'ZipCode.max' => 'The zip code must not be greater than :max characters.',
        ];
    }

    public function createOrder($item)
    {
        $inputOrder = $this->transformOrder($item);
        $order = ScreenOrder::create($inputOrder);
        $formatDate = Carbon::now('America/Los_Angeles')->format('mdY');
        $type = ScreenOrder::TYPE;

        $dataFormatLabel = DB::select("CALL create_screen_order_number('$formatDate', '$type')");
        $orderNumber = $dataFormatLabel[0]->number;
        $order->order_number = $orderNumber;
        $order->save();
        ScreenOrderHistory::create([
            'user_id' => auth()->id(),
            'screen_order_id' => $order->id,
            'type' => 'create',
            'message' => 'Order successfully created via CSV file upload.',
        ]);

        return $order;
    }

    public function createOrderAddress($items, $order)
    {
        if (!empty($items[0])) {
            $row = $items[0];
            $orderAddress = [
                'client_id' => $order->client_id,
                'screen_order_id' => $order->id,
                'street1' => $row['Address 1'],
                'street2' => $row['Address 2'],
                'city' => $row['City'],
                'zipcode' => $row['ZipCode'],
                'country' => $row['Country'],
                'type' => ScreenOrderAddress::TO_ADDRESS,
            ];
            if (!empty($row['State'])) {
                $orderAddress['state'] = $row['State'];
            }

            return ScreenOrderAddress::create($orderAddress);
        }
    }

    public function createOrderItem($items, $order, $header)
    {
        foreach ($items as $row) {
            $orderItem = [
                'client_id' => $order->client_id,
                'screen_order_id' => $order->id,
                'sku' => $row['SKU'],
                'quantity' => $row['Quantity'],
                'unit_price' => $row['Unit Cost'],
            ];
            $item = ScreenOrderItem::create($orderItem);
            $printAreaKeys = $header->filter(function ($item) {
                return Str::contains($item, 'Print area key');
            });

            foreach ($printAreaKeys as $printAreaKey) {
                $keys = explode(' ', $printAreaKey);
                $indexKey = end($keys);
                $data = [
                    'screen_order_item_id' => $item->id,
                    'screen_order_id' => $order->id,
                    'print_side' => $row[$printAreaKey],
                    'sku' => $row['SKU'],
                ];
                $design = ScreenDesign::where('design_id', $row['Design ID ' . $indexKey])->first();
                if (!empty($design)) {
                    $data['design_id'] = $design->id;
                }
                if (!empty($row['Mockup ID ' . $indexKey])) {
                    $mockup = ScreenDesign::where('design_id', $row['Mockup ID ' . $indexKey])->first();
                    if (!empty($mockup)) {
                        $data['mockup_id'] = $mockup->id;
                    }
                }
                ScreenOrderItemImage::create($data);
            }
        }

        return true;
    }

    public function transformImage($item)
    {
        $data = [];
        foreach ($item as $row) {
            $image = [
                'order_number' => $row['Customer\'s reference number'],
                'client_id' => $row['Client'],
                'buyer_name' => $row['Buyer'],
                'order_type' => $row['Order Type'],

            ];

            $data[] = $image;
        }

        return $data;
    }

    public function transformItem($item)
    {
        $data = [];
        foreach ($item as $row) {
            $order = [
                'order_number' => $row['Customer\'s reference number'],
                'client_id' => $row['Client'],
                'buyer_name' => $row['Buyer'],
                'order_type' => $row['Order Type'],

            ];

            $data[] = $order;
        }

        return $data;
    }

    public function updateOrder($input, $id)
    {
        try {
            $order = ScreenOrder::find($id);
            if (!$order) {
                throw new \Exception('Order not found');
            }
            $histories = [];
            if (isset($input['order_status']) && $order->order_status != $input['order_status']) {
                $this->updateStatus($order, $input['order_status']);
                unset($input['order_status']);
            }
            foreach ($input as $key => $value) {
                if ($order->$key != $value) {
                    $histories[] = [
                        'user_id' => auth()->id(),
                        'screen_order_id' => $order->id,
                        'type' => 'update',
                        'message' => ScreenOrder::MAP_KEYS[$key] . ' changed from \'' . ($key == 'order_status' ? str_replace('_', ' ', $order->$key) : $order->$key) . '\' to \'' . $value . '\'',
                    ];
                }
            }
            $order->update($input);
            ScreenOrderHistory::insert($histories);

            return $order;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function updateStatus(&$order, $status)
    {
        try {
            $currentStatus = $order->order_status;
            $arrayAllow = [];
            if (in_array($currentStatus, [ScreenOrder::NEW_ORDER_STATUS])) {
                $arrayAllow = [ScreenOrder::CANCELLED_STATUS, ScreenOrder::IN_PRODUCTION_STATUS];
            } elseif (in_array($currentStatus, [ScreenOrder::IN_PRODUCTION_STATUS])) {
                $arrayAllow = [ScreenOrder::CANCELLED_STATUS, ScreenOrder::READY_TO_STATUS];
            } elseif ($currentStatus == ScreenOrder::READY_TO_STATUS) {
                $arrayAllow = [ScreenOrder::COMPLETED_STATUS, ScreenOrder::CANCELLED_STATUS];
            }
            $arrayAllow[] = $currentStatus;
            if (!in_array($status, $arrayAllow)) {
                throw new \Exception('Invalid status');
            }
            $order->update(['order_status' => $status]);
            ScreenOrderHistory::create([
                'user_id' => auth()->id(),
                'screen_order_id' => $order->id,
                'type' => 'update',
                'message' => 'Order status changed from \'' . str_replace('_', ' ', $currentStatus) . '\' to \'' . str_replace('_', ' ', $status) . '\'',
            ]);

            return $order;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function updateAddress($input, $id)
    {
        try {
            $address = ScreenOrderAddress::find($id);
            if (!$address) {
                throw new \Exception('Address not found');
            }
            $addressString = $address->street1 . ', ' . ($address->street2 ? $address->street2 . ', ' : '') . $address->city . ', ' . $address->state . ', ' . $address->zipcode . ', ' . $address->country;
            $inputString = $input['street1'] . ', ' . ($input['street2'] ? $input['street2'] . ', ' : '') . $input['city'] . ', ' . $input['state'] . ', ' . $input['zipcode'] . ', ' . $input['country'];
            $message = 'Address changed from \'' . $addressString . '\' to \'' . $inputString . '\'';
            $address->update($input);

            ScreenOrderHistory::create([
                'user_id' => auth()->id(),
                'screen_order_id' => $address->screen_order_id,
                'type' => 'update',
                'message' => $message,
            ]);

            return $address;
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
