<?php

namespace App\Repositories;

use App\Models\AppUsers;

class AppUsersRepository extends CommonRepository
{
    public static function getList($request)
    {
        $query = AppUsers::query();
        $query = $query->select([
            'app_users.*',
        ]);

        if (!empty($request['user'])) {
            $query->where('user_id', $request['user']);
        }
        $ipss = $query->orderBy('id', 'desc')->get();

        return $ipss;
    }
}
