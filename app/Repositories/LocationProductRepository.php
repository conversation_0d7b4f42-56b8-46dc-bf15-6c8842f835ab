<?php

namespace App\Repositories;

use App\Models\LocationProduct;

class LocationProductRepository
{
    public static function updateQuantity($location_id, $product_id, $quantity = 0)
    {
        $location = LocationProduct::where('location_id', $location_id)
            ->where('product_id', $product_id)
            ->first();
        if (!empty($location)) {
            LocationProduct::where('id', $location->id)->update(['quantity' => $location->quantity + $quantity]);
        } else {
            LocationProduct::create([
                'location_id' => $location_id,
                'product_id' => $product_id,
                'quantity' => $quantity
            ]);
        }
    }

    public static function updateQuantityRbt($location_id, $product_id, $quantity = 0)
    {
        $location = LocationProduct::where('location_id', $location_id)
            ->where('product_id', $product_id)
            ->first();
        if (!empty($location)) {
            LocationProduct::where('id', $location->id)->update(['quantity' => $quantity]);
        } else {
            LocationProduct::create([
                'location_id' => $location_id,
                'product_id' => $product_id,
                'quantity' => $quantity
            ]);
        }
    }
}
