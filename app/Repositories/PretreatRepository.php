<?php

namespace App\Repositories;

use App\Models\Client;
use App\Models\Employee;
use App\Models\PretreatPreset;
use App\Models\PretreatPresetSKU;
use App\Models\PrintingPreset;
use App\Models\PrintSetting;
use App\Models\ProductColor;
use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderSla;
use App\Models\Setting;
use App\Models\SkuBulbSetting;
use App\Models\Store;
use App\Models\StoreProductStyleResize;
use stdClass;

class PretreatRepository extends CommonRepository
{
    private array $inkColor = [
        0 => 'white_ink',
        1 => 'black_ink',
        2 => 'mix_ink',
    ];

    private array $inkColorXqc = [
        0 => 'white_ink_xqc',
        1 => 'black_ink_xqc',
        2 => 'mix_ink_xqc',
    ];

    private array $inkColorPurple = [
        'normal' => 'purple_ink',
        'xqc' => 'purple_ink_xqc'
    ];

    private array $platenSizeToIndex = [
        '16x21' => 0,
        '16x18' => 1,
        '14x16' => 2,
        '10x12' => 3,
        '7x8' => 4,
    ];

    private array $inkColorName = [
        0 => 'White Ink Only',
        1 => 'Black Ink Only',
        2 => 'Color Ink'
    ];

    private float $pixelToMmRatio = 0.084667 * 10; // 300 dpi

    public function getBarcodeLabel($label)
    {
        return SaleOrderItemBarcode::query()->where('label_id', $label)
            ->where('is_deleted', 0)
            ->first();
    }

    public function updatePretreat($employee_id, $saleOrderItemBarcodeId)
    {
        $dataUpdate = [
            'employee_pretreat_id' => $employee_id,
            'pretreated_at' => date('Y-m-d H:i:s')
        ];
        SaleOrderItemBarcode::where('id', $saleOrderItemBarcodeId)
            ->where('is_deleted', '=', 0)
            ->update($dataUpdate);
    }

    public function updateFluidGrams($barcode, $side, $fluidGrams)
    {
        $saleOrder = SaleOrder::where('id', $barcode->order_id)->first();

        if (!empty($saleOrder)) {
            $dataUpadte = [
                'fluid_grams' => round($fluidGrams, 2)
            ];
            SaleOrderItemImage::query()
                ->where('sku', $barcode->sku)
                ->where('print_side', $side . '')
                ->where('order_id', $saleOrder->id)
                ->whereNotNull('color_new')
                ->update($dataUpadte);
        }
    }

    public function getDataAllInOne($barcode, $side, $empployeeId)
    {
        $output = $this->getOutputImage($barcode, $side, $empployeeId, true);

        if (gettype($output) == 'array' && isset($output['status'])) {
            return $output;
        } else {
            $output->arx = $this->getArx($output);
            $printingXml = $this->convertPrintingXml($output);
            $output->XMLData = $this->xmlDataV2($output, $printingXml);

            return ['status' => true, 'data' => $output];
        }
    }

    public function getDataOnlyPretreat($barcode, $side, $empployeeId, $version = 1)
    {
        $output = $this->getOutputImage($barcode, $side, $empployeeId);

        if (gettype($output) == 'array' && isset($output['status'])) {
            return $output;
        } else {
            if ($version == 1) {
                $output->XMLData = $this->xmlDataV1($output);
            } else {
                $output->XMLData = $this->xmlDataV2($output);
            }

            return ['status' => true, 'data' => $output];
        }
    }

    public function getArx($output)
    {
        $platenSize = explode('x', $output->position->platen_printing->origin);
        $sku = $output->sku;
        $side = $output->image->print_side_code;
        $file_name = "$sku-$side.arx6";
        $image_name = "$sku-$side.png";
        $preset_name = "$sku-$side.xml";
        $adjust = !empty($output->position->adjustArxMm) ? $output->position->adjustArxMm : $output->position->adjustMm;

        if (!isset($adjust['offset_top'])) {
            $offset_left = sprintf('%04d', $adjust['offset_left']) . '0000';
        } else {
            $offset_left = sprintf('%04d', $adjust['offset_left']) . sprintf('%04d', $adjust['offset_top']);
        }

        $arx = [
            'is_resize' => $output->image->is_resize,
            'ink_color' => $this->inkColorName[$output->image->color_new],
            'sku' => $sku,
            'label_id' => $output->label,
            'preview' => $output->image->preview,
            'thumb' => $output->image->thumb,
            'adjust' => $adjust,
            'platen_size' => $output->position->platen_printing->origin,
            'platen_width' => $platenSize[0] ?? 0,
            'platen_height' => $platenSize[1] ?? 0,
            'platen_width_haft' => $platenSize[0] / 2,
            'platen_height_haft' => $platenSize[1] / 2,
            'print_size' => $output->position->platen_printing->print_size,
            'print_position' => $output->position->platen_printing->print_position,
            'image_url' => !empty($adjust['custom_artwork']) ? $output->image->trim_artwork_s3 : (is_null($output->image->artwork_s3) ? $output->image->image_url : $output->image->artwork_s3),
            'image_name' => $image_name,
            'image_size' => sprintf('%04d', $adjust['image_width']) . '' . sprintf('%04d', $adjust['image_height']),
            'offset_left' => $offset_left,
            'preset_name' => $preset_name,
            'preset' => $output->printingPreset->name,
            'preset_data' => $output->printingPreset->data,
            'file_name' => $file_name,
            'side' => $output->image->print_side,
            'thumb_s3' => $output->image->thumb_s3,
            'artwork_s3' => $output->image->artwork_s3,
        ];

        return $arx;
    }

    public function getOutputImage($barcode, $side, $empployeeId, $isIncludePrinting = false)
    {
        $output = new \stdClass();
        $saleOrder = SaleOrder::with('store')->where('id', $barcode->order_id)->first();
        $output->label = $barcode->label_id;
        $output->order_id = $barcode->order_id;

        if (empty($saleOrder)) {
            return [
                'status' => false,
                'message' => 'Order not found'
            ];
        }

        $clientId = $saleOrder->store ? $saleOrder->store->client_id : 0;

        if (!empty($saleOrder->order_status) && in_array($saleOrder->order_status, SaleOrder::ARRAY_STATUS_INACTIVE)) {
            return [
                'status' => false,
                'message' => 'Sale order is ' . str_replace('_', ' ', $saleOrder->order_status)
            ];
        }

        $output->external_number = $saleOrder->external_number;
        $output->store = $saleOrder->store->name;
        $employee = Employee::query()->where('id', $empployeeId)->first();

        if ($employee == false) {
            return [
                'status' => false,
                'message' => 'Employee not found'
            ];
        }

        if ($employee->warehouse_id != $saleOrder->warehouse_id) {
            return [
                'status' => false,
                'message' => 'This label belongs to another warehouse'
            ];
        }

        $image = SaleOrderItemImage::query()
            ->with('product.productStyle')
            ->where('sku', $barcode->sku)
            ->where('print_side', $side . '')
            ->where('order_id', $saleOrder->id)
            ->whereNotNull('color_new')
            ->first();

        if (!$image) {
            if ($side == 0) {
                $side = 1;
                $image = SaleOrderItemImage::query()
                    ->with('product.productStyle')
                    ->where('sku', $barcode->sku)
                    ->where('print_side', $side . '')
                    ->where('is_double_side', 0)
                    ->where('order_id', $saleOrder->id)
                    ->whereNotNull('color_new')
                    ->first();
            }

            if (!$image) {
                return [
                    'status' => false,
                    'message' => 'Image not found!'
                ];
            }
        }

        $print_side = ProductPrintSide::findByCode($side);

        if (!$print_side) {
            return [
                'status' => false,
                'message' => 'Print side not found'
            ];
        }
        //khong the null duoc vi o controller da lay code vung in trong csdl, nhưng cứ để đây cho chắc

        //doan nay chi kiem tra xe store co off resize hay khong (hoac store off hoac off theo style)
        $isResize = true;

        if (!empty($saleOrder->store->id) && !empty($image->product->productStyle->sku)) {
            if (!$saleOrder->store->is_resize) {
                $isResize = false;
            } else {
                $objTurnOff = StoreProductStyleResize::where('store_id', $saleOrder->store->id)
                    ->where('product_style_sku', $image->product->productStyle->sku)
                    ->where('is_active', StoreProductStyleResize::ACTIVED)
                    ->first();

                if (!empty($objTurnOff)) {
                    $isResize = false;
                }
            }
        }

        if (!empty($image->custom_platen) && $image->custom_platen == '16x21') {
            //log history
            saleOrderHistory(
                null,
                $empployeeId,
                $saleOrder->id,
                SaleOrderHistory::UPDATE_ORDER_PRINTED_TYPE,
                'Printing failed because the platen is larger than 14x16 inches on the printer: DL2400.',
            );

            return [
                'status' => false,
                'message' => 'Platen 16x21 is not supported.'
            ];
        }

        $image->is_resize = $isResize;
        $image->print_side_code_name = $print_side->code_name;
        $saleOrderItem = SaleOrderItem::with(['product', 'printingPresetSku', 'productStyle'])
            ->where('id', $image->order_item_id)
            ->first();
        $platen_size = 'platen_' . $print_side->code_name . '_size';

        if (empty($saleOrderItem->printingPresetSku->{$platen_size})) {
            //log history
            saleOrderHistory(
                null,
                $empployeeId,
                $saleOrder->id,
                SaleOrderHistory::UPDATE_ORDER_PRINTED_TYPE,
                'Printing failed due to a missing printing preset.',
            );

            return [
                'status' => false,
                'message' => $platen_size . ' is missing'
            ];
        }

        $pretreatPresetSku = PretreatPresetSKU::with('preset')
            ->where('style', $saleOrderItem->product_style_sku)
            ->where('color', $saleOrderItem->product_color_sku)
            ->first();

        if (empty($pretreatPresetSku) || empty($pretreatPresetSku->preset)) {
            //log history
            saleOrderHistory(
                null,
                $empployeeId,
                $saleOrder->id,
                SaleOrderHistory::UPDATE_ORDER_PRINTED_TYPE,
                'Printing failed due to a missing pretreat preset.',
            );

            return [
                'status' => false,
                'message' => 'Preset pretreat not found!'
            ];
        }

        $pretreatPreset = $pretreatPresetSku->preset;

        if ($image->color_new == 1) {
            $pretreatPreset = PretreatPreset::where('preset_name', PretreatPreset::PRETREAT_FOR_BLACK_INK_ONLY)->first();

            if (empty($pretreatPreset)) {
                return [
                    'status' => false,
                    'message' => 'Preset pretreat not found!'
                ];
            }
        }

        $pretreatPreset->cure_temperature_origin = $pretreatPreset->cure_temperature;
        $pretreatPreset->cure_temperature = ($pretreatPreset->cure_temperature - 32) * 5 / 9;
        $pretreatPreset->press_temperature = ($pretreatPreset->press_temperature - 32) * 5 / 9;
        $pretreatPreset->print_cure_temperature = ($pretreatPreset->print_cure_temperature - 32) * 5 / 9;
        $pretreatPreset->density = $pretreatPreset->density / 234;
        $output->pretreatPreset = $pretreatPreset;
        $productColor = ProductColor::where('sku', $saleOrderItem->product_color_sku)->first();

        if (empty($productColor)) {
            return [
                'status' => false,
                'message' => 'Product color not found!'
            ];
        }

        $saleOrderItem->product->color_code = $productColor->color_code;
        $output->product = $saleOrderItem->product;
        $output->productColorId = $productColor->id;
        $productTypeId = $saleOrderItem->productStyle->productType->id ?? 0;
        $productStyleId = $saleOrderItem->productStyle->id ?? 0;
        $output->productStyleId = $productStyleId;
        $output->materialThickness = resolve(ProductMaterialThicknessRepository::class)->getMaterialThicknessCode($productTypeId, $side, $productStyleId, true);
        $heatPress = resolve(ProductMaterialThicknessRepository::class)->getSetting($productTypeId, $side, $productStyleId)->heat_press ?? true;
        $output->heatPressSetting = !empty($heatPress) ? 'true' : 'false';
        $output->position = $this->calculatePosition($image, $saleOrderItem);
        $output->orderDate = '';
        $printSides = SaleOrderItemImage::with('printSide')->where('order_item_id', $saleOrderItem->id)->get();

        foreach ($printSides as $item) {
            if ($item->printSide->code == $side) {
                $item->printSide->is_current = true;
                $output->orderDate = $item->order_date;
            }

            $output->printSides[] = $item->printSide;
        }

        $output->sku = $barcode->sku;
        $image_name = $barcode->sku . "-$side.png";
        $thumb = env('STORAGE_URL', '') . '/250/' . $image->order_date . '/' . $image_name;
        $thumb_s3 = env('AWS_S3_URL') . '/thumb/250/' . $image->order_date . '/' . $image_name;
        $trim_artwork_s3 = env('AWS_S3_URL') . '/artwork/' . $image->order_date . '/trim/' . $image_name;
        $artwork_s3 = $image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS ? env('AWS_S3_URL') . '/artwork/' . $image->order_date . '/' . $image_name : null;
        $preview = '';
        $dataOptions = json_decode($saleOrderItem->options);

        foreach ($dataOptions as $key => $item) {
            if ($item->name == 'PreviewFiles.' . str_replace(' ', '_', $print_side->name)) {
                $preview = $item->value;
                break;
            }
        }

        $preview = $preview == '' ? $thumb_s3 : $preview;
        $output->image = (object) [
            'image_url' => $image->image_url,
            'thumb' => $thumb,
            'thumb_s3' => $thumb_s3,
            'trim_artwork_s3' => $trim_artwork_s3,
            'artwork_s3' => $artwork_s3,
            'preview' => $preview,
            'color_new' => $image->color_new,
            'is_resize' => $image->is_resize,
            'pretreat_info' => json_decode($image->pretreat_info),
            'width' => $image->image_width,
            'height' => $image->image_height,
            'dpi' => $image->image_dpi,
            'print_side' => $print_side->name,
            'print_side_code' => $print_side->code,
        ];

        if ($isIncludePrinting) {
            if ($image->is_purple && $saleOrder->is_xqc) {
                $ink = $this->inkColorPurple['xqc'];
            } elseif ($image->is_purple) {
                $ink = $this->inkColorPurple['normal'];
            } elseif ($saleOrder->is_xqc) {
                $ink = $this->inkColorXqc[$image->color_new] ?? '';
            } else {
                $ink = $this->inkColor[$image->color_new] ?? '';
            }

            if ($ink == '') {
                return [
                    'status' => false,
                    'message' => 'Preset not found'
                ];
            }

            $presetName = $saleOrderItem->printingPresetSku->{$ink};
            //change custom preset for redbubble client
            if (in_array($clientId, Client::REDBUBBLE_CLIENT_ID)) {
                $presetName = PrintingPreset::convertPresetForRedBubble($presetName);
            }

            $printingPreset = PrintingPreset::where('name', $presetName)->first();

            if (empty($printingPreset)) {
                //log history
                saleOrderHistory(
                    null,
                    $empployeeId,
                    $saleOrder->id,
                    SaleOrderHistory::UPDATE_ORDER_PRINTED_TYPE,
                    'Printing failed due to a missing printing preset.',
                );

                return [
                    'status' => false,
                    'message' => 'Preset printing not found!'
                ];
            }

            // change platen by product sku
            $xmlContent = str_replace(
                '<byPlatenSize>2</byPlatenSize>',
                '<byPlatenSize>' . $this->platenSizeToIndex[$saleOrderItem->printingPresetSku->{$platen_size}] . '</byPlatenSize>',
                $printingPreset->data,
            );
            $xmlContent = str_replace('bUniDirection', 'bUniPrint', $xmlContent);
            $xmlContent = str_replace('byGtx4PauseSpan', 'byPauseSpan', $xmlContent);
            $xmlContent = str_replace('byGtx4Pause', 'byPause', $xmlContent);

            if ($ink == 'mix_ink' || $ink == 'mix_ink_xqc') {
                $xmlContent = str_replace('</byInk>', '</byInk><bySubInk>1</bySubInk>', $xmlContent);
            }

            $xmlContent = str_replace('<mode>gtx4</mode>', '<mode>gtx6</mode>', $xmlContent);
            $printingPreset->data = trim($xmlContent);
            $printingPreset->ink = $ink;
            $output->printingPreset = $printingPreset;
        }

        setTimezone();
        $slaOrder = SaleOrderSla::where('order_id', $saleOrder->id)->first();
        if ($slaOrder && in_array($saleOrder->order_status, [SaleOrder::NEW_ORDER, SaleOrder::STATUS_IN_PRODUCTION])) {
            $slaOrder->sla_expired_at_utc = $slaOrder->expired_at ? shiftTimezoneToUTC($slaOrder->expired_at) : null;
        } else {
            $slaOrder = null;
        }
        $output->slaOrder = $slaOrder;

        return $output;
    }

    public function calculatePosition($image, $saleOrderItem)
    {
        try {
            if ($image->store_id = Store::PRINTIFY_API_ID && $image?->custom_platen == '16x18') {
                $saleOrderItem->printingPresetSku['platen_' . $image->print_side_code_name . '_size'] = $image?->custom_platen;
            }

            $platenPretreatSize = [
                'width' => 18,
                'height' => 18,
            ];

            $jsonPrintConfig = resolve(SettingRepository::class)->getSettingByName(Setting::PRINT_CONFIGURATION);

            if (!empty($jsonPrintConfig)) {
                $setting = json_decode($jsonPrintConfig->value, true);
                $platenSetting = explode('x', $setting['dl_2400']['platen_pretreat'] ?? '');

                if (count($platenSetting) == 2) {
                    $platenPretreatSize['width'] = $platenSetting[0];
                    $platenPretreatSize['height'] = $platenSetting[1];
                }
            }

            $expandPretreat = 1.5; //inch
            $platenPrintMarginTop = 0.5; //inch
            $result = new stdClass();
            $result->platen_pretreat_size = (object) $platenPretreatSize;
            $platenPrintSize = explode('x', $saleOrderItem->printingPresetSku['platen_' . $image->print_side_code_name . '_size']);
            $printSize = explode('x', $saleOrderItem->printingPresetSku[$image->print_side_code_name . '_size']);
            $printPosition = explode('x', $saleOrderItem->printingPresetSku[$image->print_side_code_name . '_position']);
            $imageDpi = explode('x', $image->image_dpi);
            $dpi = empty($imageDpi[0]) ? 300 : $imageDpi[0];

            //tính toán tỷ lệ phần trăm của platen in so với platen pretreat
            $result->platen_printing = (object) [
                'origin' => $saleOrderItem->printingPresetSku['platen_' . $image->print_side_code_name . '_size'],
                'print_size' => $saleOrderItem->printingPresetSku[$image->print_side_code_name . '_size'],
                'print_position' => $saleOrderItem->printingPresetSku[$image->print_side_code_name . '_position'],
                'width' => 1,
                'height' => 1,
                'top' => $platenPrintMarginTop / $platenPretreatSize['height']
            ];

            if (!empty($platenPrintSize[0]) && is_numeric($platenPrintSize[0])) {
                $result->platen_printing->width = $platenPrintSize[0] / $platenPretreatSize['width'];
            } else {
                $platenPrintSize[0] = $platenPretreatSize['width'];
            }

            if (!empty($platenPrintSize[1]) && is_numeric($platenPrintSize[1])) {
                $result->platen_printing->height = $platenPrintSize[1] / $platenPretreatSize['height'];
            } else {
                $platenPrintSize[1] = $platenPretreatSize['height'];
            }

            // goi sang hàm adjust của printing repository để tính toán kích thước và khoảng cách của ảnh so với platen in
            $printingRepository = new PrintingRepository();
            $presetToMm = [
                'image_width' => $image->image_width * $printingRepository->pixelToMmRatio,
                'image_height' => $image->image_height * $printingRepository->pixelToMmRatio,
                'preset_print_area_width' => empty($printSize[0]) ? $platenPrintSize[0] * $printingRepository->inchToMm : $printSize[0] * $printingRepository->inchToMm,
                'preset_print_area_height' => empty($printSize[1]) ? $platenPrintSize[1] * $printingRepository->inchToMm : $printSize[1] * $printingRepository->inchToMm,
                'platen_width' => $platenPrintSize[0] * $printingRepository->inchToMm,
                'platen_height' => $platenPrintSize[1] * $printingRepository->inchToMm,
                'print_area_top' => empty($printPosition[0]) ? 0 : $printPosition[0] * $printingRepository->inchToMm,
                'print_area_left' => empty($printPosition[1]) ? null : $printPosition[1] * $printingRepository->inchToMm,
            ];
            $presetToMm['print_area_left'] = $presetToMm['print_area_left'] ?? ($presetToMm['platen_width'] - $presetToMm['preset_print_area_width']) / 2;

            if ($image->is_resize && $saleOrderItem->printingPresetSku[$image->print_side_code_name . '_size'] == '14x10') {
                $trimInfo = json_decode($image->pretreat_info);
                $h = isset($trimInfo->top) && isset($trimInfo->height) ? $trimInfo->top + $trimInfo->height : 1;
                $bottomPointHeight = $h * $image->image_height * $this->pixelToMmRatio;
                $presetToMm['bottom_point_height'] = $bottomPointHeight;
                $adjustMm = $printingRepository->adjustHoodie($presetToMm);
            } elseif (!$image->is_resize && round($presetToMm['image_width']) > $presetToMm['platen_width'] && $image->store_id != Store::PRINTIFY_API_ID) {
                // lay adjust cua anh trim de tao file arx va detect ink color
                $trimInfo = json_decode($image->pretreat_info);
                $adjustArxMm = $printingRepository->adjustImageOverSize($presetToMm, $trimInfo);
                $adjustArxMm['custom_artwork'] = true;
                $result->adjustArxMm = $adjustArxMm;

                // kiem tra neu vung trim doi xung lon hon platen thi resize anh goc sao cho vung trim doi xung nam tron ven trong platen
                $minLeftRight = min($trimInfo->left, 1 - $trimInfo->left - $trimInfo->width);
                $widthTrimSymmetrical = round($presetToMm['image_width'] * (1 - 2 * $minLeftRight));
                $heightTrimSymmetrical = round($presetToMm['image_height'] * ($trimInfo->top + $trimInfo->height));

                if ($widthTrimSymmetrical > $presetToMm['platen_width'] || $heightTrimSymmetrical > $presetToMm['platen_height']) {
                    $ratio = min($presetToMm['platen_width'] / $widthTrimSymmetrical, $presetToMm['platen_height'] / $heightTrimSymmetrical);
                    $presetToMm['image_width'] = $presetToMm['image_width'] * $ratio;
                    $presetToMm['image_height'] = $presetToMm['image_height'] * $ratio;
                }

                //van lay adjust ca anh goc vi ben duoi se lay vi tri anh trim theo anh goc de in
                $adjustMm = $printingRepository->adjust($image->is_resize, $presetToMm);
            } else {
                $adjustMm = $printingRepository->adjust($image->is_resize, $presetToMm);
            }

            $adjust = [
                'offset_left' => round($adjustMm['offset_left'] / $printingRepository->inchToMm, 5),
                'offset_top' => round($adjustMm['offset_top'] / $printingRepository->inchToMm, 5),
                'image_width' => round($adjustMm['image_width'] / $printingRepository->inchToMm, 5),
                'image_height' => round($adjustMm['image_height'] / $printingRepository->inchToMm, 5),
            ];
            $result->adjustMm = $adjustMm;
            $result->adjust = (object) $adjust;

            //tính toán tỷ lệ phần trăm của ảnh so với platen in
            $result->artwork = (object) [
                'width' => round($adjust['image_width'] / $platenPrintSize[0], 5),
                'height' => round($adjust['image_height'] / $platenPrintSize[1], 5),
                'top' => round($adjust['offset_top'] / $platenPrintSize[1], 5),
                'left' => round($adjust['offset_left'] / $platenPrintSize[0], 5),
            ];
            $result->scale = round($adjust['image_width'] / ($image->image_width / $dpi), 2);

            //tính toán vị trí của ảnh so với platen pretreat, đơn vị inch
            if (!empty($image->pretreat_info)) {
                $pretreatInfo = json_decode($image->pretreat_info);
                $leftPlatenPrintToLeftPlatenPretreat = ($platenPretreatSize['width'] - $platenPrintSize[0]) / 2;
                $leftTrimArtworkToLeftPlatenPretreat = $pretreatInfo->left * $adjust['image_width'] + $adjust['offset_left'] + $leftPlatenPrintToLeftPlatenPretreat;
                $result->pretreat_zone = (object) [
                    'top' => $pretreatInfo->top * $adjust['image_height'] + $adjust['offset_top'] - $expandPretreat,
                    'left' => $leftTrimArtworkToLeftPlatenPretreat - ($platenPretreatSize['width'] - $adjust['image_width'] * $pretreatInfo->width) / 2,
                    'width' => $pretreatInfo->width * $adjust['image_width'] + 2 * $expandPretreat,
                    'height' => $pretreatInfo->height * $adjust['image_height'] + 2 * $expandPretreat,
                    'expand_x' => $expandPretreat,
                    'expand_y' => $expandPretreat,
                ];
            } else {
                $result->pretreat_zone = (object) [
                    'top' => 0,
                    'left' => 0,
                    'width' => $platenPretreatSize['width'],
                    'height' => $platenPretreatSize['height'],
                    'expand_x' => 0,
                    'expand_y' => 0,
                ];
            }

            $result->pretreat_zone->top += $platenPrintMarginTop;

            //xử lý nếu vùng pretreat vượt quá platen pretreat
            if ($result->pretreat_zone->top < 0 || $result->pretreat_zone->top + $result->pretreat_zone->height > $platenPretreatSize['height']) {
                $maxValue = max(0 - $result->pretreat_zone->top, $result->pretreat_zone->top + $result->pretreat_zone->height - $platenPretreatSize['height']);
                $expandPretreatY = $expandPretreat - $maxValue;
                $result->pretreat_zone->top = $result->pretreat_zone->top + $maxValue;
                $result->pretreat_zone->height = $result->pretreat_zone->height - 2 * $maxValue;
                $result->pretreat_zone->expand_y = $expandPretreatY;
            }

            $leftBorder = $result->pretreat_zone->left - $result->pretreat_zone->width / 2;
            $rightBorder = $result->pretreat_zone->left + $result->pretreat_zone->width / 2;

            if ($leftBorder + $platenPretreatSize['width'] / 2 < 0 || $rightBorder > $platenPretreatSize['width'] / 2) {
                $maxValue = max(0 - $leftBorder + $platenPretreatSize['width'] / 2, $rightBorder - $platenPretreatSize['width'] / 2);
                $expandPretreatX = $expandPretreat - $maxValue;
                $result->pretreat_zone->width = $result->pretreat_zone->width - 2 * $maxValue;
                $result->pretreat_zone->expand_x = $expandPretreatX;
            }

            $left = empty($leftTrimArtworkToLeftPlatenPretreat) ? 0 : round(($leftTrimArtworkToLeftPlatenPretreat - $expandPretreat) / $platenPretreatSize['width'], 5);
            $left = max($left, 0);

            // min pretreat with spayzone is 16 in
            $minWidth = 16;
            $result->pretreat_zone->width = abs($result->pretreat_zone->width);

            if ($result->pretreat_zone->width <= $minWidth) {
                if ((int) $platenPretreatSize['width'] <= $minWidth) {
                    $minWidth = $platenPretreatSize['width'];
                }

                $customLeft = $left - ($minWidth - $result->pretreat_zone->width) / 2 / $platenPretreatSize['width'];
                $overPlaten = $customLeft * $platenPretreatSize['width'] + $minWidth;

                if ($overPlaten >= $platenPretreatSize['width']) {
                    $customLeft -= ($overPlaten - $platenPretreatSize['width']) / $platenPretreatSize['width'];
                }

                if ($customLeft < 0) {
                    $customLeft = 0;
                }
            }

            $sprayWidth = $result->pretreat_zone->width;
            $sprayTop = $result->pretreat_zone->top;
            $sprayLeft = $result->pretreat_zone->left;
            $sprayHeight = $result->pretreat_zone->height;
            $overPlaten = ($platenPretreatSize['width'] / 2 - $minWidth / 2 - $sprayLeft) * -2;
            $sprayCustomWidth = $overPlaten > 0 ? $minWidth + $overPlaten : $minWidth;
            $result->sprayHeight = $sprayHeight;
            $result->top = $result->pretreat_zone->top;

            if (!empty($image->pretreat_info)) {
                $pretreatInfo = json_decode($image->pretreat_info);
                $heightReality = $pretreatInfo->height * $image->image_height / $dpi * $result->scale;
                $totalSprayOffset = $sprayHeight - $heightReality;
                $result->totalSprayOffset = $totalSprayOffset;

                // 4 : Top - 2, Bottom: 2
                if ($totalSprayOffset > 4) {
                    $sprayHeight -= $totalSprayOffset - 4;
                    $sprayTop += (($totalSprayOffset - 4) / 2) / 2;
                } else {
                    $sprayHeight += (4 - $totalSprayOffset);
                    $sprayTop -= (4 - $totalSprayOffset) / 2;
                }
            }

            $pretreatSideTop = $sprayTop / $platenPretreatSize['height'];
            $result->pretreat_side = (object) [
                'top' => $pretreatSideTop <= 0 ? 0 : round($pretreatSideTop, 5),
                'left' => isset($customLeft) ? round($customLeft, 5) : $left,
                'width' => round(max($result->pretreat_zone->width, $minWidth) / $platenPretreatSize['width'], 5),
                'height' => round($sprayHeight / $platenPretreatSize['height'], 5),
            ];

            $top = round($sprayTop, 5);
            $height = round($sprayHeight, 5);
            $adjustTopAndHeight = $this->adjustTopAndHeight($top, $height);

            //lam tron 5 chu so thap phan cho pretreat_zone
            $result->pretreat_zone = (object) [
                'top' => $adjustTopAndHeight['top'],
                'left' => round($sprayLeft, 5),
                'width' => round($sprayWidth >= $minWidth ? $sprayWidth : $sprayCustomWidth, 5),
                'height' => $adjustTopAndHeight['height'],
                'expand_x' => round($result->pretreat_zone->expand_x, 5),
                'expand_y' => round($result->pretreat_zone->expand_y, 5),
            ];
        } catch (\Throwable $th) {
            \Log::error('calculatePosition error - saleOrderItemId: ' . $saleOrderItem->id);
            throw $th;
        }

        return $result;
    }

    public function adjustTopAndHeight(float $top, float $height, float $maxHeight = 20): array
    {
        $originalHeight = $height;

        if (($top >= 0 && $top + $height <= $maxHeight) || ($top < 0 && $height <= $maxHeight)) {
            return [
                'top' => round($top, 5),
                'height' => round($height, 5)
            ];
        }

        if ($top >= 0) {
            $maxTotal = $maxHeight;
            $newHeight = 2 * ($maxTotal - $top - $originalHeight / 2);
            $newHeight = max(0, min($originalHeight, $newHeight));
            $newTop = $top + ($originalHeight - $newHeight) / 2;
        } else {
            $newHeight = min($originalHeight, $maxHeight);
            $reduced = $originalHeight - $newHeight;
            $newTop = $top + $reduced / 2;

            if ($newTop > 0 && $newTop + $newHeight > $maxHeight) {
                $newHeight = 2 * ($maxHeight - $top - $originalHeight / 2);
                $newHeight = max(0, min($originalHeight, $newHeight));
                $newTop = $top + ($originalHeight - $newHeight) / 2;
            }
        }

        return [
            'top' => round($newTop, 5),
            'height' => round($newHeight, 5)
        ];
    }

    public function parseXml($xml)
    {
        $fileChange = str_replace(["\n", "\r", "\t"], '', $xml);
        $fileTrim = trim(str_replace('"', "'", $fileChange));
        $fileTrim = str_replace('&', '&amp;', $fileTrim);
        $resultXml = simplexml_load_string($fileTrim);
        $result = (object) [
            'byInk' => $resultXml->byInk ?? 0,
            'bEcoMode' => $resultXml->bEcoMode ?? 'false',
            'byDoublePrint' => $resultXml->byDoublePrint ?? 0,
            'colorTrans' => $resultXml->colorTrans ?? 0,
            'byResolution' => $resultXml->byResolution ?? 0,
            'byHighlight' => $resultXml->byHighlight ?? 0,
            'byMask' => $resultXml->byMask ?? 0,
            'byInkVolume' => $resultXml->byInkVolume ?? 0,
            'bMultiple' => $resultXml->bMultiple ?? 'false',
            'bTransColor' => $resultXml->bTransColor ?? 'false',
            'bMaterialBlack' => $resultXml->bMaterialBlack ?? 'false',
            'byTolerance' => $resultXml->byTolerance ?? 0,
            'byMinWhite' => $resultXml->byMinWhite ?? 0,
            'byChoke' => $resultXml->byChoke ?? 0,
            'bUniPrint' => $resultXml->bUniPrint ?? 'false',
            'bDivide' => $resultXml->bDivide ?? 'false',
            'byDivideSpan' => $resultXml->byDivideSpan ?? 0,
            'bPause' => $resultXml->bPause ?? 'false',
            'byPauseSpan' => $resultXml->byPauseSpan ?? 0,
            'iCyanBalance' => $resultXml->iCyanBalance ?? 0,
            'iMagentaBalance' => $resultXml->iMagentaBalance ?? 0,
            'iYellowBalance' => $resultXml->iYellowBalance ?? 0,
            'iBlackBalance' => $resultXml->iBlackBalance ?? 0,
            'bySaturation' => $resultXml->bySaturation ?? 0,
            'byBrightness' => $resultXml->byBrightness ?? 0,
            'byContrast' => $resultXml->byContrast ?? 0,
        ];

        return $result;
    }

    public function convertPrintingXml($output)
    {
        $inkCombination = [
            'ColorInkOnly',
            'WhiteInkOnly',
            'ColorAndWhiteInk',
            'BlackInkOnly',
        ];

        $inkCombinationMapping = [
            'CMYKOG' => 'ColorInkOnly',
            'CMYKOGAndWhiteInk' => 'ColorAndWhiteInk',
            'CMYKOGAndWhiteInkEco' => 'ColorAndWhiteInkEco',
        ];

        $pretreatPreset = $output->pretreatPreset;
        $combinationCMYKOG = (!empty($pretreatPreset->cure_temperature_origin) && !empty($pretreatPreset->cure_time)) ? 'CMYKOGAndWhiteInk' : 'CMYKOG';
        $printingPreset = $this->parseXml($output->printingPreset->data);
        $byPauseSpan = $printingPreset->byPauseSpan * 10000000; // 1s = 10000000 ticks

        if ($output->printingPreset->ink == 'mix_ink' || $output->printingPreset->ink == 'mix_ink_xqc') {
            $inkCombinationValue = $combinationCMYKOG;
        } else {
            $inkCombinationValue = $inkCombination[(int) $printingPreset->byInk];
        }

        if (!empty($printingPreset->bEcoMode) && $printingPreset->bEcoMode == 'true' && in_array($inkCombinationValue, ['ColorAndWhiteInk', 'CMYKOGAndWhiteInk'])) {
            $inkCombinationValue = $inkCombinationValue == 'ColorAndWhiteInk' ? 'ColorAndWhiteInkEco' : 'CMYKOGAndWhiteInkEco';
        }

        if (!empty(request()->input('isCMYK'))) {
            $inkCombinationValue = $inkCombinationMapping[$inkCombinationValue] ?? $inkCombinationValue;
        }

        $useDoublePass = $printingPreset->byDoublePrint == 0 ? 'false' : 'true';

        $bulbPowers = [];
        $generalBulbPowers = PrintSetting::where('key', 'bulb_power_printing')->first();
        $skuBulbPowers = SkuBulbSetting::where([
            'color_id' => $output->productColorId,
            'style_id' => $output->productStyleId,
            'type' => SkuBulbSetting::TYPE_PRINTING,
            'is_active' => 1
        ])->first();

        if (!empty($skuBulbPowers)) {
            $bulbPowers = json_decode($skuBulbPowers->data, true);
        } elseif (!empty($generalBulbPowers)) {
            $bulbPowers = json_decode($generalBulbPowers->data, true);
        }

        $xmlBulbPower = '';

        if (!empty($bulbPowers)) {
            $xmlBulbPower .= '                <BulbPowers>' . PHP_EOL;
            foreach ($bulbPowers as $key => $bulbPower) {
                $xmlBulbPower .= "                    <int>{$bulbPower}</int>" . PHP_EOL;
            }
            $xmlBulbPower .= '                </BulbPowers>';
        }

        $xml = "
            <Print>
                <TransparentColors>
                    <ColorValue>
                        <Value>{$printingPreset->colorTrans}</Value>
                    </ColorValue>
                </TransparentColors>
                <Enabled>true</Enabled>
                <TrackingID>{$output->label}</TrackingID>
                <MaterialThicknessInt>{$output->materialThickness}</MaterialThicknessInt>
                <PrintResolutionInt>{$printingPreset->byResolution}</PrintResolutionInt>
                <InkCombination>{$inkCombinationValue}</InkCombination>
                <UseDoublePass>{$useDoublePass}</UseDoublePass>
                <DoublePassDelayInt>{$printingPreset->byDoublePrint}</DoublePassDelayInt>
                <WhiteHighlightInt>{$printingPreset->byHighlight}</WhiteHighlightInt>
                <WhiteMaskInt>{$printingPreset->byMask}</WhiteMaskInt>
                <ColorInkVolumeInt>{$printingPreset->byInkVolume}</ColorInkVolumeInt>
                <ColorMultiPass>{$printingPreset->bMultiple}</ColorMultiPass>
                <OmitBlack>{$printingPreset->bMaterialBlack}</OmitBlack>
                <OmitWhite>false</OmitWhite>
                <UseTransparentColors>{$printingPreset->bTransColor}</UseTransparentColors>
                <TransparentToleranceInt>{$printingPreset->byTolerance}</TransparentToleranceInt>
                <MinimumWhiteBaseInt>{$printingPreset->byMinWhite}</MinimumWhiteBaseInt>
                <ChokeWidthInt>{$printingPreset->byChoke}</ChokeWidthInt>
                <UnidirectionalPrinting>{$printingPreset->bUniPrint}</UnidirectionalPrinting>
                <UseTwoLayerWhite>{$printingPreset->bDivide}</UseTwoLayerWhite>
                <TwoLayerWhiteDelay>{$printingPreset->byDivideSpan}</TwoLayerWhiteDelay>
                <UseWhiteColorDelay>{$printingPreset->bPause}</UseWhiteColorDelay>
                <WhiteColorDelayTicks>{$byPauseSpan}</WhiteColorDelayTicks>
                <CMYKInt>
                    <CyanBalance>{$printingPreset->iCyanBalance}</CyanBalance>
                    <MagentaBalance>{$printingPreset->iMagentaBalance}</MagentaBalance>
                    <YellowBalance>{$printingPreset->iYellowBalance}</YellowBalance>
                    <BlackBalance>{$printingPreset->iBlackBalance}</BlackBalance>
                </CMYKInt>
                <ColorProcessingInt>
                    <Saturation>{$printingPreset->bySaturation}</Saturation>
                    <Brightness>{$printingPreset->byBrightness}</Brightness>
                    <Contrast>{$printingPreset->byContrast}</Contrast>
                </ColorProcessingInt>
            </Print>
            <PrintCure>
                <Enabled>true</Enabled>
                <TrackingID>{$output->label}</TrackingID>
                <Temperature>{$output->pretreatPreset->print_cure_temperature}</Temperature>
                <Time>{$output->pretreatPreset->print_cure_time}</Time>
                <TopFanPower>0</TopFanPower>
                <BottomFanPower>0</BottomFanPower>
                <CoolerPower>0</CoolerPower>
                <ExhaustPower>0</ExhaustPower>
                {$xmlBulbPower}
            </PrintCure>
        ";

        return $xml;
    }

    public function xmlDataV1($output)
    {
        $pretreatPreset = $output->pretreatPreset;
        $manualScale = $output->position->scale;
        $printSideName = $output->image->print_side;
        $result = "
            <SynergyGarment xmlns='http://schemas.datacontract.org/2004/07/PretreaterMain' xmlns:i='http://www.w3.org/2001/XMLSchema-instance'>
                <Color>{$output->product->color}</Color>
                <DateOrdered>{$output->orderDate}</DateOrdered>
                <Description>{$output->product->name}</Description>
                <Images>
                    <SynergyImageSettings>
                        <BottomAirPower>100</BottomAirPower>
                        <BulbPowers xmlns:a='http://schemas.microsoft.com/2003/10/Serialization/Arrays'>
                            <a:double>100</a:double>
                            <a:double>100</a:double>
                            <a:double>100</a:double>
                            <a:double>50</a:double>
                            <a:double>50</a:double>
                            <a:double>0</a:double>
                            <a:double>0</a:double>
                            <a:double>0</a:double>
                            <a:double>100</a:double>
                        </BulbPowers>
                        <ControlMode>0</ControlMode>
                        <CoolerPower>100</CoolerPower>
                        <CounterID>0</CounterID>
                        <CropToImage>false</CropToImage>
                        <CureTemperature>{$pretreatPreset->cure_temperature}</CureTemperature>
                        <CureTime>{$pretreatPreset->cure_time}</CureTime>
                        <Density>{$pretreatPreset->density}</Density>
                        <ExhaustPower>100</ExhaustPower>
                        <FinishedQuantity>0</FinishedQuantity>
                        <Fluid>1</Fluid>
                        <FluidName i:nil='true'/>
                        <Head i:nil='true'/>
                        <Height>{$output->position->pretreat_zone->height}</Height>
                        <HorizontalOffset>{$output->position->pretreat_zone->left}</HorizontalOffset>
                        <IgnoreArtFile i:nil='true'/>
                        <ImageHorizontalAlignment>CENTER</ImageHorizontalAlignment>
                        <ImageLocationInfo>
                            <APIKey i:nil='true'/>
                            <APISecret i:nil='true'/>
                            <BucketName i:nil='true'/>
                            <FolderID i:nil='true'/>
                            <RegionName i:nil='true'/>
                            <ServerType>LOCAL</ServerType>
                            <Uri>{$output->image->trim_artwork_s3}</Uri>
                        </ImageLocationInfo>
                        <ImageScaleMethod i:nil='true'/>
                        <ImageURL/>
                        <ImageVerticalAlignment>CENTER</ImageVerticalAlignment>
                        <Location>{$printSideName}</Location>
                        <LocationDisplayColor i:nil='true'/>
                        <ManualImageScale>{$manualScale}</ManualImageScale>
                        <Nozzle i:nil='true'/>
                        <Passes>1</Passes>
                        <ThumbnailLocationInfo>
                            <APIKey i:nil='true'/>
                            <APISecret i:nil='true'/>
                            <BucketName i:nil='true'/>
                            <FolderID i:nil='true'/>
                            <RegionName i:nil='true'/>
                            <ServerType>LOCAL</ServerType>
                            <Uri>{$output->image->preview}</Uri>
                        </ThumbnailLocationInfo>
                        <ThumbnailURL/>
                        <TopAirPower>100</TopAirPower>
                        <TotalQuantity>1</TotalQuantity>
                        <VerticalOffset>{$output->position->pretreat_zone->top}</VerticalOffset>
                        <Width>{$output->position->pretreat_zone->width}</Width>
                    </SynergyImageSettings>
                </Images>
                <Notes>{$output->label}</Notes>
                <Notify i:nil='true'/>
                <OrderID>{$output->external_number}</OrderID>
                <PopupMessage i:nil='true'/>
                <ProductID>{$output->product->gtin}</ProductID>
                <QuantityProcessed>0</QuantityProcessed>
                <QuantityRemaining>1</QuantityRemaining>
                <SKU>{$output->product->sku}</SKU>
                <Size>{$output->product->size}</Size>
                <Source/>
                <Style>{$output->product->style}</Style>
                <Tags/>
                <Type/>
                <Version>3</Version>
            </SynergyGarment>
        ";

        return $result;
    }

    public function xmlDataV2($output, $printXML = '')
    {
        $pretreatPreset = $output->pretreatPreset;
        $manualScale = $output->position->scale;
        $printSideName = $output->image->print_side;
        $bulbPowers = [];
        $generalBulbPowers = PrintSetting::where('key', 'bulb_power_pretreat')->first();
        $skuBulbPowers = SkuBulbSetting::where([
            'color_id' => $output->productColorId,
            'style_id' => $output->productStyleId,
            'type' => SkuBulbSetting::TYPE_PRETREAT,
            'is_active' => 1
        ])->first();

        if (!empty($skuBulbPowers)) {
            $bulbPowers = json_decode($skuBulbPowers->data, true);
        } elseif (!empty($generalBulbPowers)) {
            $bulbPowers = json_decode($generalBulbPowers->data, true);
        }

        $xmlBulbPower = '';

        if (!empty($bulbPowers)) {
            $xmlBulbPower .= '                <BulbPowers>' . PHP_EOL;

            foreach ($bulbPowers as $key => $bulbPower) {
                $xmlBulbPower .= "                    <int>{$bulbPower}</int>" . PHP_EOL;
            }

            $xmlBulbPower .= '                </BulbPowers>';
        }

        $pretreatStatus = (!empty($pretreatPreset->cure_temperature_origin) && !empty($pretreatPreset->cure_time)) ? 'true' : 'false';
        $result = "
            <ProductInfo xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'>
                <Version>1</Version>
                <OrderID>{$output->external_number}</OrderID>
                <ProductID>{$output->product->gtin}</ProductID>
                <FinishedQuantity>0</FinishedQuantity>
                <TotalQuantity>1</TotalQuantity>
                <Description>{$output->product->name}</Description>
                <Tags/>
                <Notes>{$output->label}</Notes>
                <Size>{$output->product->size}</Size>
                <Color>{$output->product->color}</Color>
                <SKU>{$output->product->sku}</SKU>
                <Style>{$output->product->style}</Style>
                <Type/>
                <Alert xsi:nil='true'/>
                <Art>
                    <ArtInfo>
                        <Location>{$printSideName}</Location>
                        <Platens>
                            <string>{$output->position->platen_printing->origin}</string>
                        </Platens>
                        <Processed>false</Processed>
                        <TrackingID>Art-{$output->label}</TrackingID>
                        <FinishedQuantity>0</FinishedQuantity>
                        <TotalQuantity>1</TotalQuantity>
                        <CropToImage>false</CropToImage>
                        <ManualImageScale>{$manualScale}</ManualImageScale>
                        <ImageVerticalAlignment>CENTER</ImageVerticalAlignment>
                        <ImageHorizontalAlignment>CENTER</ImageHorizontalAlignment>
                        <VerticalOffset>{$output->position->pretreat_zone->top}</VerticalOffset>
                        <HorizontalOffset>{$output->position->pretreat_zone->left}</HorizontalOffset>
                        <Height>{$output->position->pretreat_zone->height}</Height>
                        <Width>{$output->position->pretreat_zone->width}</Width>
                        <Angle xsi:nil='true'/>
                        <ArtFile>
                            <ServerType>LOCAL</ServerType>
                            <Uri>{$output->image->trim_artwork_s3}</Uri>
                        </ArtFile>
                        <Pretreat>
                            <Enabled>{$pretreatStatus}</Enabled>
                            <TrackingID>{$output->label}</TrackingID>
                            <SprayDensity>{$pretreatPreset->density}</SprayDensity>
                            <Passes>1</Passes>
                            <FluidName>Fluid1</FluidName>
                        </Pretreat>
                        <PretreatCure>
                            <Enabled>{$pretreatStatus}</Enabled>
                            <TrackingID>{$output->label}</TrackingID>
                            <Temperature>{$pretreatPreset->cure_temperature}</Temperature>
                            <Time>{$pretreatPreset->cure_time}</Time>
                            <TopFanPower>0</TopFanPower>
                            <BottomFanPower>0</BottomFanPower>
                            <CoolerPower>0</CoolerPower>
                            <ExhaustPower>0</ExhaustPower>
                            {$xmlBulbPower}
                        </PretreatCure>
                        <PretreatPress>
                            <Enabled>{$output->heatPressSetting}</Enabled>
                            <TrackingID>{$output->label}</TrackingID>
                            <Temperature>{$pretreatPreset->press_temperature}</Temperature>
                            <Time>{$pretreatPreset->press_time}</Time>
                            <Pressure>{$pretreatPreset->pressure}</Pressure>
                        </PretreatPress>
                        {$printXML}
                    </ArtInfo>
                </Art>
            </ProductInfo>
        ";

        return $result;
    }
}
