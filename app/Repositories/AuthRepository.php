<?php

namespace App\Repositories;

use App\Models\User;
use App\Models\UserAccessLog;
use Google_Client;

class AuthRepository
{
    protected $warehouseRepository;

    public function __construct(WarehouseRepository $warehouseRepository)
    {
        $this->warehouseRepository = $warehouseRepository;
    }

    public function doLogin($inputs)
    {
        if (!$token = auth()->attempt($inputs)) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return $this->createNewToken($token);
    }

    public function doLoginAccounting($inputs, $request)
    {
        if (!$token = auth()->attempt($inputs)) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        checkPermissionAccounting(auth()->user());
        UserAccessLog::logUserAccess(
            getClientIp($request),
            $request->header('CF-IPCountry') ?? null,
            auth()->user()?->id ?? null,
            'accounting',
        );

        return $this->createNewTokenAccounting($token);
    }

    public function doLogout()
    {
        return auth()->logout();
    }

    public function doRegister($input)
    {
        $user = User::create(array_merge(
            $input,
            ['password' => bcrypt($input['password'])],
        ));

        return response()->json([
            'message' => 'User successfully registered',
            'user' => $user
        ], 201);
    }

    public function createNewToken($token)
    {
        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth()->factory()->getTTL() * 120,
            'user' => $this->fetchUserWarehouseByUser(auth()->user()),
        ]);
    }

    public function createNewTokenLogUserIp($token, $request)
    {
        UserAccessLog::logUserAccess(
            getClientIp($request),
            $request->header('CF-IPCountry') ?? null,
            auth()->user()?->id,
            $request->source ?? null,
        );

        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth()->factory()->getTTL() * 120,
            'user' => $this->fetchUserWarehouseByUser(auth()->user()),
        ]);
    }

    public function fetchUserWarehouseByUser($user)
    {
        if ($user->is_admin) {
            $user = User::with('roles')->find($user->id);
            $user = $this->decodeDataInRole($user);
            $warehouses = $this->warehouseRepository->fetchAll();
            $user->warehouses = $warehouses->toArray();
        } else {
            $user = User::with('warehouses', 'roles')->find($user->id);
            $user = $this->decodeDataInRole($user);
        }
//        if (!$user->is_all_store) {
//            $storeIds = UserStore::where('user_id', $user->id)->pluck('store_id')->toArray() ?? [];
//            $user->store_ids = $storeIds;
//        }

        return $user;
    }

    public function decodeDataInRole($user)
    {
        foreach ($user->roles as $item) {
            if (!empty($item->data)) {
                $item->data = json_decode($item->data);
            }
        }

        return $user;
    }

    public function createNewTokenAccounting($token)
    {
        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => 604800, // 1 week
            'user' => [
                'username' => auth()->user()->username,
                'email' => auth()->user()->email
            ],
        ]);
    }

    public function doLoginWithGoogle($request)
    {
        $client = new Google_Client(['client_id' => config('services.google.client_id')]);
        $payload = $client->verifyIdToken($request->id_token);

        if (!$payload) {
            return response()->json(['message' => 'Invalid token'], 401);
        }

        $user = User::where('email', $payload['email'])->first();

        if (!$user) {
            return response()->json(['message' => 'Account does not exist'], 401);
        }
        $app = !empty($request->app) ? $request->app : null;

        if (!empty($request->app)) {
            UserAccessLog::logUserAccess(
                getClientIp($request),
                $request->header('CF-IPCountry') ?? null,
                auth()->user()?->id ?? null,
                $app,
            );
        }

        $token = auth()->login($user);

        switch ($app) {
            case 'accounting':
                checkPermissionAccounting(auth()->user());

                return $this->createNewTokenAccounting($token);
            case 'seller':
                return response()->json([
                    'access_token' => encryptSalt($user->id . ':' . ':' . ':' . ':' . time() . ':1'),
                    'token_type' => 'bearer',
                    'user' => $user,
                    'is_support_login' => true
                ]);
            case 'lavender':
                if (empty(auth()->user()->is_lavender_editor)) {
                    return response()->json(['error' => 'Unauthorized'], 401);
                }

                return app(LavenderAuthRepository::class)->createNewToken($token, false);
            default:
                return $this->createNewToken($token);
        }
    }
}
