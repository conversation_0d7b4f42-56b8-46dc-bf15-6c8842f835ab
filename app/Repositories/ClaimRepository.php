<?php

namespace App\Repositories;
use App\Models\Claim;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Validator;
use App\Repositories\Contracts\ClaimRepositoryInterface;

class ClaimRepository implements ClaimRepositoryInterface
{
    const LIMIT = 10;

    public function fetchAll($input = null)
    {
        $limit = !empty($input['limit']) ? $input['limit'] : self::LIMIT;
        $query = Claim::select("*")->with(['employee']);
        $warehouseId = $input['warehouse_id'];
        $query->whereHas('employee', function($q) use ($warehouseId) {
            $q->where('employee.warehouse_id', $warehouseId);
        });
        $keyword = $input['keyword'];
        if(!empty($input['keyword'])){
            $query->where('label_id','LIKE', '%' . $keyword . '%');
            $query->orWhere('ticket_id','LIKE', '%' . $keyword . '%');
        }
        if(!empty($input['type'])){
            $query->where('type', '=', $input['type']);
        }
        if (!empty($input['date_start']) && !empty($input['date_end'])) {
            $query->whereBetween('created_at', [$input['date_start'], $input['date_end']]);
        }
        $sortColumn = !empty($input['sort_column']) ? $input['sort_column'] :'id';
        $sortBy     = !empty($input['sort_by']) ? $input['sort_by'] : 'desc';
        return  $query->orderBy($sortColumn,$sortBy)->paginate($limit);
    }

    public function create($input)
    {
        return Claim::create($input);
    }

    public function fetch($id)
    {
        return Claim::find($id);
    }

    public function update($id, $dataUpdate)
    {
        return Claim::where('id', $id)->update($dataUpdate);
    }

    public function delete($id)
    {
        return Claim::where('id', $id)->delete();
    }
}
