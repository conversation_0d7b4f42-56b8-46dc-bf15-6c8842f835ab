<?php

namespace App\Repositories;

use App\Models\ChangeLog;
use Illuminate\Support\Facades\DB;

class ChangeLogRepository
{
    public function saveAndUpdate($data)
    {
        $updateData = [
            'data' => $data['data'],
            'source' => $data['source'],
            'updated_at' => $data['is_noti'] ? now() : DB::raw('updated_at'),
        ];

        ChangeLog::updateOrInsert(['source' => $data['source']], $updateData);
    }

    public function getChangeLog($source)
    {
        return ChangeLog::where('source', $source)->first();
    }
}
