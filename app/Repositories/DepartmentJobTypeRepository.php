<?php


namespace App\Repositories;


use Illuminate\Support\Facades\DB;

class DepartmentJobTypeRepository
{
    private $table = 'department_job_type';

    public function create($department, $data)
    {
        $result = $this->transformToArray($department, $data);
        return DB::table($this->table)->insert($result);
    }

    public function edit($department, $data)
    {
        $this->delete($department);
        $result = $this->transformToArray($department, $data);
        return DB::table($this->table)->insert($result);
    }

    public function delete($department)
    {
        return DB::table('department_job_type')
            ->where('department_id', $department)
            ->delete();
    }

    public function transformToArray($id, $data)
    {
        return collect($data)->map(function ($item) use ($id) {
            return array(
                'department_id' => $id,
                'job_type' => $item
            );
        })->toArray();
    }

    public function list()
    {
        return DB::table('time_tracking')->groupBy('job_type')->pluck('job_type');
    }

}