<?php

namespace App\Repositories;

use App\Builder\BarcodeOrnamentBuilder;
use App\Models\BarcodePrinted;
use App\Models\PdfConverted;
use App\Models\PdfConvertedItem;
use App\Models\PrintMethod;
use App\Models\Product;
use App\Models\SaleOrder;
use App\Models\SaleOrderBarcode\SaleOrderItemBarcode;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItemImage;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class OrnamentRepository extends CommonRepository
{
    protected BarcodeOrnamentBuilder $barcodeOrnamentBuilder;

    const FLAG_IS_TIKTOK = 3;

    const ACTIVE = 1;

    public function __construct(BarcodeOrnamentBuilder $barcodeOrnamentBuilder)
    {
        parent::__construct();
        $this->barcodeOrnamentBuilder = $barcodeOrnamentBuilder;
    }

    public function countPendingPriorityStore($input)
    {
        $subQuery = $this->barcodeOrnamentBuilder
            ->selectPendingByStore($input)
            ->getQuery()
            ->groupBy('sale_order_item_barcode.store_id')
            ->selectRaw('COUNT(*) as count, sale_order_item_barcode.store_id');

        $query = DB::table('store')
            ->joinSub($subQuery, 'tmp', function ($join) {
                $join->on('tmp.store_id', '=', 'store.id');
            })->selectRaw('store.id, store.code, tmp.count');

        return [
            'sql' => interpolateQuery($query),
            'stores' => $query->get(),
        ];
    }

    public function count($input)
    {
        $results = [];
        $total = 0;
        $input['print_method'] = PrintMethod::UV;
        $query = $this->barcodeOrnamentBuilder->selectPendingProductSku($input)
            ->getQuery()
            ->selectRaw('COUNT(*) AS count, sale_order_item.product_sku, sale_order_item.product_id')
            ->groupBy('sale_order_item.product_sku');
        $data = $query->get();
        $sql = interpolateQuery($query);

        foreach ($data as $item) {
            $total += $item->count;
            $results[] = [
                'product_id' => $item->product_id,
                'count' => $item->count,
                'sku' => $item->product_sku,
                'last_printed_at' => $this->getLastPrintedTime('product_id', array_merge($input, ['product_id' => $item->product_id]))
            ];
        }

        return [
            'total' => $total,
            'data' => array_values(collect($results)->sortBy('last_printed_at')->toArray()),
            'sql' => $sql,
        ];
    }

    public function countPendingTiktok($input)
    {
        $input['print_method'] = PrintMethod::UV;
        $query = $this->barcodeOrnamentBuilder->selectPendingTiktok($input)
            ->getQuery()
            ->selectRaw('COUNT(*) AS count');

        $r['count'] = $query->first()->count ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('tiktok', $input);

        return $r;
    }

    public function countPendingBulkOrder($input)
    {
        $input['print_method'] = PrintMethod::UV;
        $query = $this->barcodeOrnamentBuilder->selectPendingBulkOrder($input)
            ->getQuery()
            ->selectRaw('COUNT(*) AS count');

        $r['count'] = $query->first()->count ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('is_bulk_order', $input);

        return $r;
    }

    public function countPendingReprintOrder($input)
    {
        $input['print_method'] = PrintMethod::UV;
        $query = $this->barcodeOrnamentBuilder->selectPendingReprintOrder($input)
            ->getQuery()
            ->selectRaw('COUNT(*) AS count');

        $r['count'] = $query->first()->count ?? 0;
        $r['sql'] = interpolateQuery($query);
        $r['printed_at'] = $this->getLastPrintedTime('is_reprint', $input);

        return $r;
    }

    public function index($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $wips = BarcodePrinted::listBarcodeOrnament($warehouse_id, $limit);

        $wips->getCollection()->transform(function ($wip) {
            $fileName = "/barcode/$wip->id.pdf";
            $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();

            if (Storage::disk('public')->exists($fileName)) {
                $wip->wip_url = env('STORAGE_URL', '') . "$fileName?v=" . rand();
            }

            return $wip;
        });

        return $wips;
    }

    public function generateBarcode($input)
    {
        $limit = $input['quantity'];
        $employee_id = $input['employee_id'];
        $warehouse_id = config('jwt.warehouse_id');
        $data = [
            'store_id' => 0,
            'quantity_input' => $limit,
            'product_id' => null,
            'is_tiktok' => null,
            'is_bulk_order' => null,
            'is_reprint' => null,
            'employee_id' => $employee_id,
            'warehouse_id' => $warehouse_id,
            'user_id' => Auth::id(),
            'convert_percent' => $limit,
            'print_method' => BarcodePrinted::METHOD_UV,
            'created_at' => Carbon::now(),
            'print_status' => BarcodePrinted::INACTIVE,
            'convert_status' => BarcodePrinted::FAILED
        ];

        if (!empty($input['is_tiktok'])) {
            $data['is_tiktok'] = self::FLAG_IS_TIKTOK;
            $query = $this->barcodeOrnamentBuilder->selectPendingTiktok($input);
        } elseif (!empty($input['is_bulk_order'])) {
            $data['is_bulk_order'] = self::ACTIVE;
            $query = $this->barcodeOrnamentBuilder->selectPendingBulkOrder($input);
        } elseif (!empty($input['is_reprint'])) {
            $data['is_reprint'] = self::ACTIVE;
            $query = $this->barcodeOrnamentBuilder->selectPendingReprintOrder($input);
        } elseif (!empty($input['product_id'])) {
            $product = Product::findByIdAndPresetSkuUv($input['product_id']);

            if (!$product) {
                throw new Exception('Product not found or not to set preset yet!', Response::HTTP_NOT_FOUND);
            }

            $data['product_id'] = $input['product_id'];
            $query = $this->barcodeOrnamentBuilder->selectPendingProductSku($input);
        }

        if (empty($query)) {
            throw new \Exception('Server Error! Not found query.', Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        try {
            DB::beginTransaction();
            $query = $query->getQuery();

            if (!empty($input['store_id']) && !empty($input['priorityStores']) && in_array($input['store_id'], $input['priorityStores'])) {
                $data['store_id'] = $input['store_id'];
            }

            $barcode_printed = BarcodePrinted::create($data);
            $barcode_printed->quantity = $query->whereRaw('1 = 1 LIMIT ?', [$limit])->update([
                'barcode_printed_id' => $barcode_printed->id,
                'employee_pull_id' => $employee_id,
                'print_barcode_at' => now(),
            ]);
            $barcode_printed->convert_status = BarcodePrinted::INACTIVE;
            $barcode_printed->save();
            $productSkusInvalid = $this->getProductSkuInvalid($barcode_printed->id);

            if (!empty($productSkusInvalid)) {
                $productSkus = implode(', ', $productSkusInvalid);
                throw new Exception("Product $productSkus not found or not set preset yet!", Response::HTTP_NOT_FOUND);
            }

            // change status order to in production
            $order_ids = SaleOrderItemBarcode::getArrayOrderIds($barcode_printed->id);
            SaleOrder::changeStatusToInProduction($order_ids);
            DB::commit();

            if (count($order_ids) > 0) {
                foreach ($order_ids as $ids) {
                    handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $ids);
                }
            }

            return $barcode_printed;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getProductSkuInvalid($barcodePrintedId): array
    {
        return DB::table('sale_order_item_barcode')
            ->join('sale_order_item', 'sale_order_item.id', '=', 'sale_order_item_barcode.order_item_id')
            ->leftJoin('printing_preset_sku', 'printing_preset_sku.sku', '=', 'sale_order_item.product_sku')
            ->leftJoin('uv_preset_sku', 'uv_preset_sku.product_sku', '=', 'sale_order_item.product_sku')
            ->where('sale_order_item_barcode.barcode_printed_id', $barcodePrintedId)
            ->whereNull('printing_preset_sku.sku')
            ->whereNull('uv_preset_sku.product_sku')
            ->groupBy('sale_order_item.product_sku')
            ->pluck('sale_order_item.product_sku')
            ->toArray();
    }

    public function history($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;
        $wips = BarcodePrinted::listHistoryBarcodeOrnament($warehouse_id, $limit, $label_id);

        $wips->getCollection()->transform(function ($wip) {
            $fileName = "/barcode/$wip->id.pdf";
            $wip->wip_url = env('AWS_URL', '') . "$fileName?v=" . rand();

            if (Storage::disk('public')->exists($fileName)) {
                $wip->wip_url = env('STORAGE_URL', '') . "$fileName?v=" . rand();
            }

            return $wip;
        });

        return $wips;
    }

    public function countPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $products = Product::getOrnamentProductId();
        $results = [];

        if ($products->count()) {
            foreach ($products as $key => $product) {
                $pdf_printed = PdfConverted::findLastCreatedByProductId($product->id, $warehouse_id);
                $results[] = [
                    'last_created_at' => $pdf_printed ? Carbon::parse($pdf_printed->created_at)->format('m/d/Y H:i:s') : null,
                    'product_id' => $product->id,
                    'sku' => $product->sku
                ];
            }
        }

        return [
            'data' => array_values(collect($results)->sortBy('last_created_at')->toArray()),
        ];
    }

    public function removeLabelSide($label)
    {
        $label = trim($label);
        $parts = explode('-', $label);

        return implode('-', array_slice($parts, 0, 5));
    }

    public function scanLabel($input)
    {
        $label_raw = $input['label_id'];
        $employee_id = $input['employee_id'];
        $product_id = $input['product_id'];
        $warehouse_id = $input['warehouse_id'];
        $label = $this->removeLabelSide($label_raw);
        $printingRepository = new PrintingRepository();
        $labelPrintSide = $printingRepository->getLabelPrintSide($label_raw);
        $labelObject = SaleOrderItemBarcode::findByLabelPrinted($label, $product_id, $warehouse_id, $labelPrintSide);
        if (!$labelObject) {
            throw new Exception('Label not found or not print WIP yet or has been generated!', Response::HTTP_NOT_FOUND);
        }

        $labelId = $labelObject->label_id;
        $sku_side = $labelObject->sku . '-' . $labelPrintSide;
        $image = SaleOrderItemImage::findByOrderItemAndSide($labelObject->order_item_id, $labelPrintSide);

        if (!$image) {
            throw new Exception('Image not found!', Response::HTTP_NOT_FOUND);
        }

        // update employee scan
        $labelObject->employee_scan_id = $employee_id;
        $labelObject->save();
        $image_url = $image->upload_s3_status == SaleOrderItemImage::SYNC_S3_SUCCESS ? env('AWS_URL') . '/artwork/' . $image->order_date . '/' . $sku_side . '.png' : $image->image_url;

        return [
            'label_id' => $labelId,
            'image_url' => $image_url,
            'side' => $labelPrintSide,
            'image_id' => $image->id
        ];
    }

    public function generatePdf($input, $printMethod, $type = null, $batchNumber = null, $countryId = null)
    {
        $employee_id = $input['employee_id'];
        $warehouse_id = config('jwt.warehouse_id');
        $product_id = $input['product_id'] ?? null;
        $options = $input['options'];

        try {
            DB::beginTransaction();
            $pdf_converted = PdfConverted::create([
                'quantity_input' => count($options),
                'product_id' => $product_id,
                'employee_convert_id' => $employee_id,
                'warehouse_id' => $warehouse_id,
                'user_id' => Auth::id(),
                'convert_percent' => count($options),
                'print_method' => $printMethod,
                'created_at' => Carbon::now(),
                'download_status' => PdfConverted::INACTIVE,
                'options' => $options,
                'convert_status' => PdfConverted::FAIL,
                'type' => $type,
                'batch_number' => $batchNumber,
                'country_id' => $countryId,
            ]);
            // add queue update production status
            $orderIds = [];
            // insert pivot table
            $quantity = 0;

            foreach ($options as $option) {
                $barcode = SaleOrderItemBarcode::findByLabelId($option['label_id']);

                if (!empty($option['image_id'])) {
                    $image = SaleOrderItemImage::findByIdAndSide($option['image_id'], $option['side']);
                }

                if ($barcode) {
                    // if ($printMethod == PrintMethod::FILM) {
                    //     SaleOrderItemBarcode::where('order_item_id', $barcode->order_item_id)->update([
                    //         'employee_print_id' => $employee_id,
                    //         'printed_at' => Carbon::now()
                    //     ]);
                    // } else {
                    //     $barcode->update([
                    //         'employee_print_id' => $employee_id,
                    //         'printed_at' => Carbon::now()
                    //     ]);
                    // }

                    $orderIds[] = $barcode->order_id;
                }

                $pivot = PdfConvertedItem::create([
                    'label_id' => $option['label_id'],
                    'pdf_converted_id' => $pdf_converted->id,
                    'barcode_id' => $barcode ? $barcode->id : null,
                    'print_side' => isset($image) ? $option['side'] : null,
                    'print_method' => $printMethod,
                ]);

                if ($pivot) {
                    $quantity = $quantity + 1;
                }
            }

            $pdf_converted->quantity = $quantity;
            $pdf_converted->convert_status = PdfConverted::INACTIVE;
            $pdf_converted->save();
            DB::commit();

            $orderIds = array_unique($orderIds);

            if (!empty($orderIds)) {
                $printingRepository = new PrintingRepository();

                foreach ($orderIds as $ids) {
                    handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, value: $ids);
                }
            }

            return $pdf_converted;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception('Server Error!', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function listPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];

        return PdfConverted::listPdfOrnament($warehouse_id, $limit);
    }

    public function historyPdf($input)
    {
        $warehouse_id = $input['warehouse_id'];
        $limit = $input['limit'];
        $label_id = !empty($input['label_id']) ? $input['label_id'] : null;

        return PdfConverted::listHistoryPdfOrnament($warehouse_id, $limit, $label_id);
    }

    public function preset($input)
    {
        $product_id = $input['product_id'];
        $product = Product::findByIdAndPresetSkuUv($product_id);

        if (!$product) {
            throw new Exception('Product not found or not to set preset yet!', Response::HTTP_NOT_FOUND);
        }

        $platenSize = explode('x', $product->printingPresetSku->platen_size);
        $widthItem = (float) $platenSize[0] * 40;
        $heightItem = (float) $platenSize[1] * 40;
        $maxItemOnRow = $product->uvPresetSku->max_item_on_row;
        $maxRow = $product->uvPresetSku->max_row;
        $maxItem = $maxItemOnRow * $maxRow;
        $isCircle = $product->uvPresetSku->is_circle;

        // size preview front end
        return [
            'width_item' => $widthItem,
            'height_item' => $heightItem,
            'width' => $widthItem * $maxItemOnRow + 4 * $maxItemOnRow,
            'height' => $heightItem * $maxRow + 4 * $maxRow,
            'max_row' => $maxRow,
            'max_item_on_row' => $maxItemOnRow,
            'max_item' => $maxItem,
            'is_circle' => $isCircle,
        ];
    }

    public function download($input)
    {
        $pdf_converted_id = $input['pdf_converted_id'];
        $pdf_converted = PdfConverted::find($pdf_converted_id);
        if (!$pdf_converted) {
            throw new Exception('File not found!', Response::HTTP_NOT_FOUND);
        }
        $oldDownloadStatus = $pdf_converted->download_status;
        $pdf_converted->download_status = PdfConverted::ACTIVE;
        $pdf_converted->download_at = Carbon::now();
        $pdf_converted->save();

        $pdf_converted_items = PdfConvertedItem::where('pdf_converted_id', $pdf_converted->id)->get()->pluck('label_id');
        if (count($pdf_converted_items) > 0) {
            $itemsBarcode = SaleOrderItemBarcode::whereIn('label_id', $pdf_converted_items)->update([
                'printed_at' => Carbon::now(),
                'employee_print_id' => (int) $pdf_converted->employee_convert_id
            ]);
            $orderIds = SaleOrderItemBarcode::whereIn('label_id', $pdf_converted_items)
                                            ->pluck('order_id')
                                            ->unique();

            if (!empty($orderIds)) {
                $printingRepository = new PrintingRepository();

                foreach ($orderIds as $ids) {
                    $printingRepository->updateOrderPrinted($ids);
                    handleJob(SaleOrder::JOB_UPDATE_PRODUCTION_STATUS, $ids);
                }
            }
            if ($oldDownloadStatus != PdfConverted::ACTIVE) {
                // Updated records history
                $updatedItems = SaleOrderItemBarcode::whereIn('label_id', $pdf_converted_items)
                    ->get(['order_id', 'label_id', 'employee_print_id']);

                if ($updatedItems->isNotEmpty()) {
                    $dataImportHistory = [];
                    foreach ($updatedItems as $item) {
                        $dataImportHistory[] = [
                            'user_id' => null,
                            'employee_id' => $item->employee_print_id,
                            'order_id' => $item->order_id,
                            'type' => SaleOrderHistory::PRINT_ORNAMENT,
                            'message' => 'Label ' . $item->label_id . ' printed',
                            'created_at' => Carbon::now()->toDateTimeString(),
                        ];
                    }

                    SaleOrderHistory::insert($dataImportHistory); // Insert the history data
                }
            }
        }

        return $pdf_converted;
    }

    public function getLastPrintedTime($type, $args)
    {
        $types = [
            'is_bulk_order' => null,
            'is_fba' => null,
            'is_reroute' => null,
            'is_tiktok' => null,
            'is_manual' => null,
            'is_reprint' => null,
            'is_xqc' => null,
            'is_eps' => null,
            'style_sku' => null,
            'product_id' => null,
        ];

        switch ($type) {
            case 'tiktok':
                $types['is_tiktok'] = self::FLAG_IS_TIKTOK;
                break;
            case 'is_bulk_order':
                $types['is_bulk_order'] = self::ACTIVE;
                break;
            case 'is_reprint':
                $types['is_reprint'] = self::ACTIVE;
                break;
            case 'product_id':
                $types['product_id'] = $args['product_id'];
                break;
        }

        $query = DB::table('barcode_printed')
            ->where('print_method', $args['print_method'])
            ->where('warehouse_id', $args['warehouse_id']);

        if (!empty($args['store_id']) && !empty($args['priorityStores']) && in_array($args['store_id'], $args['priorityStores'])) {
            $query->where('store_id', $args['store_id']);
        } elseif (!empty($args['priorityStores'])) {
            $query->whereNotIn('store_id', $args['priorityStores']);
        }

        foreach ($types as $key => $value) {
            if ($value !== null) {
                $query->where($key, $value);

                continue;
            }

            $query->where(function ($q) use ($key) {
                $q->whereNull($key)
                    ->orWhere($key, 0);
            });
        }

        $barcodePrinted = $query->orderByDesc('id')->first();

        if (!empty($barcodePrinted?->created_at)) {
            return Carbon::parse($barcodePrinted->created_at)->format('Y-m-d H:i:s');
        }

        return null;
    }
}
