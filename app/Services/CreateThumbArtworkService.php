<?php

namespace App\Services;

use App\Http\Service\OrderEditingService;
use App\Models\EmbroideryTask;
use App\Models\ImageHash;
use App\Models\IntegrateLog;
use App\Models\PrintMethod;
use App\Models\ProductPrintArea;
use App\Models\ProductPrintSide;
use App\Models\ProductSkuMatching;
use App\Models\ProductStyle;
use App\Models\QueueJob;
use App\Models\RedBubblePrintOffset;
use App\Models\SaleOrder;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemBarcode;
use App\Models\SaleOrderItemImage;
use App\Models\SaleOrderOnHold;
use App\Models\Setting;
use App\Models\Store;
use App\Models\Tag;
use App\Models\User;
use App\Models\VisuaDetectImage;
use App\Repositories\LavenderRepository;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CreateThumbArtworkService
{
    protected $img;

    protected $imageId;

    protected $fileName;

    protected $attempts;

    protected $maxRetries;

    public $pathS3 = '/artwork/';

    public function __construct($imageId, $attempts, $maxRetries)
    {
        $this->imageId = $imageId;
        $this->attempts = $attempts;
        $this->maxRetries = $maxRetries;
    }

    public function handle()
    {
        try {
            jobEcho("image id: {$this->imageId} v2024-03-21");
            $item = SaleOrderItemImage::with(['product.productStyle', 'printSide', 'printingPresetSku'])->find($this->imageId);
            $storeExcludeCheckIP = Setting::where('name', Store::STORE_EXCLUDE_CHECK_IP)->first();
            $storeExcludeCheckIP = explode(',', $storeExcludeCheckIP->value) ?? [];
            VisuaDetectImage::where('image_id', $item->id)->delete();
            $imageUrl = $this->addHttp($item->image_url);
            $image = file_get_contents($imageUrl);
            $hash_md5 = md5($image);
            $hash = ImageHash::findByMd5($hash_md5);
            $order = SaleOrder::find($item->order_id);
            $tag = OrderEditingService::addTag($order->tag, Tag::SYSTEM_REJECT_ID);

            if (!$hash) {
                $hash = ImageHash::create(['hash_md5' => $hash_md5]);
            } elseif ($order->order_status != SaleOrder::REJECTED && !in_array($item->store_id, $storeExcludeCheckIP)) {
                if ($order->store_id == Store::STORE_REDBUBBLE) {
                    SaleOrderItemImage::where('id', $item->id)->update(['image_hash_id' => $hash->id]);
                }

                if ($hash->is_ip_violation) {
                    jobEcho('Reject order: ' . $item->order_id);

                    if ($order->store_id == Store::STORE_REDBUBBLE) {
                        resolve(LavenderRepository::class)->rejectOrderRedBubble($order->id, User::SYSTEM, $tag);
                    } else {
                        resolve(LavenderRepository::class)->rejectOrder($order->id, User::SYSTEM, $tag);
                    }
                } elseif ($order->store_id == Store::STORE_REDBUBBLE) {
                    resolve(LavenderRepository::class)->rejectOrderRedBubble($order->id, User::SYSTEM, $tag);
                }

                if ($order->store_id == Store::STORE_REDBUBBLE) {
                    $checkImage = SaleOrderItemImage::where('id', $item->id)->first();

                    if (empty($checkImage)) {
                        return;
                    }
                }
            }

            $checkRotate = ProductPrintArea::where('name', $item->printSide?->name)->where('product_style_id', $item->product?->productStyle?->id)->first();

            if ($checkRotate->print_method == PrintMethod::EMB
                && EmbroideryTask::hasValidExtension($item->image_url, SaleOrderItemImage::VALID_EMB_EXTENSIONS)
                || $checkRotate->print_method == PrintMethod::EMB
                && EmbroideryTask::hasCustomValidExtension($item->image_url, SaleOrderItemImage::VALID_EMB_EXTENSIONS)) {
                $input = [
                    'order_item_id' => $item->order_item_id,
                    'sale_order_id' => $item->order_id,
                    'sale_order_image_id' => $item->id,
                    'print_area' => $checkRotate?->name ?? null,
                    'print_size' => $checkRotate?->print_size ?? null,
                    'image_hash_id' => $hash->id ?? null,
                ];
                $item->thumb_250 = 1;
                $item->thumb_750 = 1;
                $item->image_hash_id = $hash->id;
                $item->save();
                handleJob(QueueJob::CREATE_EMBROIDERY_TASK, $input);

                return;
            }

            $side = $item->print_side;
            $this->fileName = $item->order_date . '/' . $item->sku . '-' . $side . '.png';
            jobEcho("file name: {$this->fileName}");
            $imagick = new \Imagick();

            if (!empty($item->product?->productStyle?->type) && $item->product?->productStyle?->type == ProductStyle::TYPE_INSERT) {
                jobEcho('type insert');
                // Kiểm tra định dạng tệp
                $finfo = new \finfo(FILEINFO_MIME_TYPE);
                $mime_type = $finfo->buffer($image);
                // pdf thi se convert sang anh png

                if ($mime_type == 'application/pdf') {
                    $image = $this->convertPdfToPng($image, $imagick);
                }

                // muc dich cho anh artwork s3
                $item->image_ext = substr($mime_type, strpos($mime_type, '/') + 1);
                jobEcho("ext: {$item->image_ext}");
            }

            $imagick->readImageBlob($image);
            // check file size > 50MB then optimize image
            $fileSize = strlen($image);

            if ($fileSize > 20 * 1024 * 1024) {
                $imagick->stripImage(); // Loại bỏ tất cả thông tin màu sắc hoặc profile ICC từ hình ảnh
                //   $imagick->optimizeImageLayers(); // Tối ưu hóa các layer trong hình ảnh
                jobEcho('optimize image');
            } else {
                jobEcho('file size: ' . $fileSize / 1024 / 1024 . 'MB');
            }

            // kiem tra xem co can phai xoay anh 180 do khong
            if ($checkRotate && $checkRotate->rotate) {
                // xoay anh 180 độ
                $imagick->rotateImage(new \ImagickPixel(), 180);
            }

            $this->img = $imagick;
            // process for redbubble
            if ($item->store_id == Store::STORE_REDBUBBLE && strpos($item->image_url, 'trimmed') !== false) {
                $this->checkRedBubble($item);
            }

            $width = $this->img->getImageWidth();
            $height = $this->img->getImageHeight();
            $result = $this->createThumbNoTrim(250);

            if (!$result) {
                if ($this->attempts == $this->maxRetries && !empty($item)) {
                    OrderIssueService::logIssue([
                        'order_id' => $item->order_id,
                        'order_item_id' => $item->order_item_id,
                        'sku' => $item->sku,
                        'type' => SaleOrderHistory::DOWNLOAD_ERROR,
                        'side' => $item->printSide?->code_name,
                        'side_name' => $item->printSide?->name,
                    ]);
                    $this->setError($item);
                }

                return $this->setError($item);
            }

            $result = $this->proofThumb(500);

            if (!$result) {
                if ($this->attempts == $this->maxRetries && !empty($item)) {
                    OrderIssueService::logIssue([
                        'order_id' => $item->order_id,
                        'order_item_id' => $item->order_item_id,
                        'sku' => $item->sku,
                        'type' => SaleOrderHistory::DOWNLOAD_ERROR,
                        'side' => $item->printSide?->code_name,
                        'side_name' => $item->printSide?->name,
                    ]);
                    $this->setError($item);
                }

                return $this->setError($item);
            }

            $result = $this->createThumb(750);

            if (!$result) {
                if ($this->attempts == $this->maxRetries && !empty($item)) {
                    OrderIssueService::logIssue([
                        'order_id' => $item->order_id,
                        'order_item_id' => $item->order_item_id,
                        'sku' => $item->sku,
                        'type' => SaleOrderHistory::DOWNLOAD_ERROR,
                        'side' => $item->printSide?->code_name,
                        'side_name' => $item->printSide?->name,
                    ]);
                    $this->setError($item);
                }

                return $this->setError($item);
            }
            if (!in_array($item->store_id, $storeExcludeCheckIP)) {
                if ($hash->is_ip_violation === null) {
                    jobEcho('Visua detect');
                    $storeOnHold = Setting::where('name', Store::STORE_ON_HOLD_SETTING_TEST)->first();
                    $stores = explode(',', $storeOnHold->value) ?? [];
                    if (!empty($stores) && (in_array($item->store_id, $stores) || in_array('all', $stores))) {
                        handleJob(VisuaDetectImage::DETECT_DESIGN_JOB, ['url' => env('AWS_S3_URL') . "/thumb/750/$this->fileName", 'image_id' => $item->id]);
                    }
                } elseif ($hash->is_ip_violation === 0) {
                    $order = SaleOrder::with(['orderOnHold', 'visualDetectImages' => function ($q) use ($item) {
                        $q->where('is_ip_violation', true)
                            ->where('image_id', '!=', $item->id)
                            ->whereNull('user_detect_id');
                    }])->where('id', $item->order_id)->first();
                    if (count($order->visualDetectImages) == 0 && !empty($order->orderOnHold->visua_on_hold)) {
                        if ($order && $order->tag) {
                            $tag = explode(',', $order->tag);
                            $tag = array_diff($tag, [Tag::VISUA_DETECTED_ID]);
                            $order->tag = implode(',', $tag);
                        }

                        if ($order->orderOnHold->store_on_hold) {
                            SaleOrderOnHold::where('order_id', $item->order_id)->update([
                                'visua_on_hold' => false
                            ]);
                        } else {
                            SaleOrderOnHold::where('order_id', $item->order_id)->delete();
                            if ($order->order_status == SaleOrder::ON_HOLD) {
                                $order->order_status = SaleOrder::NEW_ORDER;
                            }
                            $order->save();
                            SaleOrderHistory::create([
                                'order_id' => $item->order_id,
                                'type' => SaleOrderHistory::UPDATE_ORDER_STATUS_TYPE,
                                'message' => 'Order status changed from "on hold" to "new order" by System',
                            ]);
                        }
                    }
                }
            }

            $item->image_width = $width;
            $item->image_height = $height;
            jobEcho("image width: {$item->image_width}");
            jobEcho("image height: {$item->image_height}");
            $item->image_size = strlen($image);
            // $item->thumb_750 = 1;
            $item->thumb_250 = 1;
            $item->image_hash_id = $hash->id;
            $item->save();

            if ($checkRotate->print_method == PrintMethod::EMB) {
                $input = [
                    'order_item_id' => $item->order_item_id,
                    'sale_order_id' => $item->order_id,
                    'sale_order_image_id' => $item->id,
                    'print_area' => $checkRotate?->name ?? null,
                    'print_size' => $checkRotate?->print_size ?? null,
                    'image_hash_id' => $hash->id,
                ];
                handleJob(QueueJob::CREATE_EMBROIDERY_TASK, $input);
            }

            $data = [
                'order_id' => $item->order_id,
                'order_item_id' => $item->order_item_id,
                'sku' => $item->sku,
                'type' => SaleOrderHistory::DOWNLOAD_ERROR,
                'side' => $item->printSide?->code_name,
                'side_name' => $item->printSide?->name,
            ];

            OrderIssueService::resolveIssue($data);

            if (in_array($checkRotate->print_method, [PrintMethod::DTF, PrintMethod::FILM, PrintMethod::NECK])) {
                if (!in_array($item->printSide?->name, ProductPrintSide::DTF_NECK_SIDE_NAME)) {
                    $barcodes = SaleOrderItemBarcode::where('order_item_id', $item->order_item_id)->get();

                    foreach ($barcodes as $barcode) {
                        $inputQueue = [
                            'order_id' => $item->order_id,
                            'order_item_id' => $item->order_item_id,
                            'sku' => $item->sku,
                            'side' => $item->printSide?->code_name,
                            'code' => $item->printSide?->code,
                            'side_name' => $item->printSide?->name,
                            'label_id' => $barcode->label_id,
                        ];
                        handleJob(QueueJob::CONVERT_IMAGE_DTF, $inputQueue);
                        jobEcho("file name DTF: {$this->fileName}");
                    }
                }
            }

            if ($checkRotate->print_method == PrintMethod::LATEX) {
                $barcodes = SaleOrderItemBarcode::where('order_item_id', $item->order_item_id)->get();

                foreach ($barcodes as $barcode) {
                    $inputQueue = [
                        'order_id' => $item->order_id,
                        'order_item_id' => $item->order_item_id,
                        'sku' => $item->sku,
                        'side' => $item->printSide?->code_name,
                        'code' => $item->printSide?->code,
                        'side_name' => $item->printSide?->name,
                        'label_id' => $barcode->label_id,
                    ];
                    handleJob(QueueJob::CONVERT_IMAGE_LATEX, $inputQueue);
                    jobEcho("file name Canvas: {$this->fileName}");
                }
            }

            Log::channel('image')->info("$item->id: $width x $height ($item->image_size) ($imageUrl) create thumb successfully");
        } catch (\Exception $e) {
            jobEcho($e->getMessage());
            jobEcho("attempts: {$this->attempts}");
            jobEcho("maxtries: {$this->maxRetries}");

            if ($this->attempts == $this->maxRetries && !empty($item)) {
                OrderIssueService::logIssue([
                    'order_id' => $item->order_id,
                    'order_item_id' => $item->order_item_id,
                    'sku' => $item->sku,
                    'type' => SaleOrderHistory::DOWNLOAD_ERROR,
                    'side' => $item->printSide?->code_name,
                    'side_name' => $item->printSide?->name,
                ]);

                if ($item->store_id == Store::STORE_REDBUBBLE) {
                    $urlGoogleSpace = Setting::where('name', Setting::GOOGLE_SPACE_RB_ORDER_FAIL)->first();
                    sendGoogleChat($e->getMessage(), $urlGoogleSpace?->value ?? '');
                }

                $this->setError($item);
            }

            Log::channel('image')->error(($item->id ?? '') . ': ' . $e->getMessage());
            throw $e;
        }
    }

    protected function cloneImage()
    {
        return clone $this->img;
    }

    protected function createThumbNoTrim($width = 250)
    {
        jobEcho('create thumb 250');
        $img = $this->cloneImage();
        $img->thumbnailImage($width, 0, false);
        $img->setImageFormat('png');
        $status = Storage::disk('s3')->put("/thumb/250/$this->fileName", $img->getImageBlob());
        $img = null;

        return $status;
    }

    protected function createThumb($width = 750)
    {
        jobEcho('create thumb 750');
        $img = $this->cloneImage();
        $img->thumbnailImage($width, 0, false);
        $img->setImageFormat('png');
        $status = Storage::disk('s3')->put("/thumb/750/$this->fileName", $img->getImageBlob());
        $img = null;

        return $status;
    }

    protected function proofThumb($width = 500)
    {
        jobEcho('create printproof');
        $image = $this->cloneImage();
        $image->thumbnailImage($width, 0, false);
        $text = 'SWIFTPOD - ' . Carbon::now()->format('Y/m/d H:i:s');
        $draw = new \ImagickDraw();
        $draw->setFont('tahoma.ttf');
        // $draw->setFont('DejaVu Sans Mono');
        $draw->setFontSize(40);
        $draw->setFillColor('white');
        $draw->setGravity(\Imagick::GRAVITY_SOUTHEAST);
        $draw->setFillOpacity(0.2);

        for ($i = 1; $i <= 7; $i += 2) {
            $image->annotateImage($draw, 17, 120 * $i - 1, -45, $text);
        }

        $draw->setFillColor('grey');
        $draw->setFillOpacity(0.6);

        for ($i = 1; $i <= 7; $i += 2) {
            $image->annotateImage($draw, 20, 120 * $i - 1, -45, $text);
        }

        /* Give image a format */
        $image->setImageFormat('png');
        $status = Storage::disk('s3')->put("/thumb/proof/$this->fileName", $image->getImageBlob());
        $image = null;

        return $status;
    }

    protected function setError($image)
    {
        $image->thumb_750 = 2;
        $image->thumb_250 = 2;
        $image->save();
    }

    protected function addHttp($url)
    {
        // return $url;
        if (empty($url)) {
            return '';
        }

        // Search the pattern
        if (!preg_match('~^(?:f|ht)tps?://~i', $url)) {
            // If not exist then add http
            $url = 'https://' . $url;
        }

        // Return the URL
        return $url;
    }

    protected function convertPdfToPng($image, $imagick)
    {
        // Sử dụng ImageMagick để chuyển đổi PDF thành PNG
        $imagick->setResolution(300, 300); // Đặt độ phân giải cho ảnh đầu ra (tuỳ chọn)
        $imagick->readImageBlob($image);
        $imagick->setImageFormat('png');

        return $imagick->getImagesBlob();
    }

    /**
     * Convert image to print size only for RedBubble (front, back)
     *
     * @param $file
     * @return Imagick
     */
    public function convertRedBubble($printSizeWidthInch, $printSizeHeightInch, $offsetXMM, $offsetYMM, $printSizeInPalletOffsetYMM, $dpi = 200)
    {
        $palletWidthMM = 396;
        /* $palletHeightMM = 497;*/
        $printSizeInPalletOffsetY = $printSizeInPalletOffsetYMM * ($dpi / 25);
        $printSizeWidth = $printSizeWidthInch * $dpi;
        $printSizeHeight = $printSizeHeightInch * $dpi;
        // pixel / dpi = inch => inch * 25.4 = mm ; pixel = mm * dpi / 25.4
        // mm to pixel
        $offsetX = $offsetXMM * ($dpi / 25);
        $offsetY = $offsetYMM * ($dpi / 25);
        $offsetX = floor($offsetX);
        /*  $palletHeight = $palletHeightMM * ($dpi / 25.4);*/
        $palletWidth = $palletWidthMM * ($dpi / 25.4);
        $printSizeInPalletOffsetX = ($palletWidth - $printSizeWidth) / 2;
        $printSizeInPalletOffsetX = floor($printSizeInPalletOffsetX);
        // calculate offset of design in platen (platen in pallet)
        $designInPlatenOffsetX = $offsetX - $printSizeInPalletOffsetX;
        $designInPlatenOffsetY = $offsetY - $printSizeInPalletOffsetY;

        var_dump("offsetX: $offsetX | $designInPlatenOffsetX");
        var_dump("offsetY: $offsetY | $designInPlatenOffsetY");
        var_dump("printSizeInPalletOffsetYMM: $printSizeInPalletOffsetYMM  $printSizeInPalletOffsetY");

        // $designInPlatenOffsetX = ceil($designInPlatenOffsetX);
        // calculate offset of design in pallet
        // create image with size of platen use imagick
        $printSize = new \Imagick();
        $printSize->newImage($printSizeWidth, $printSizeHeight, new \ImagickPixel('transparent'));
        $printSize->setImageFormat('png');
        // add design to platen
        //$design = $this->img;
        $printSize->compositeImage($this->img, \Imagick::COMPOSITE_DEFAULT, $designInPlatenOffsetX, $designInPlatenOffsetY);

        return $printSize;
    }

    public function checkRedBubble($item)
    {
        $storeRedBubbleID = Store::STORE_REDBUBBLE;

        if ($item->store_id != $storeRedBubbleID) {
            return false;
        }

        $saleOrderItem = SaleOrderItem::where('id', $item->order_item_id)
            ->where('store_id', $storeRedBubbleID)
            ->where('order_id', $item->order_id)
            ->first();

        if (!$saleOrderItem) {
            throw new \Exception("Not found sale order item: $item->order_item_id of redbubble.");
        }

        //  $item->printSide->name convert to lower case and space to _
        $printSizeName = strtolower(str_replace(' ', '_', $item->printSide->name)) . '_size';
        $printSize = $item->printingPresetSku->{$printSizeName};
        //var_dump($item->printingPresetSku->toArray());
        //jobEcho("print size: $printSizeName $printSize");
        //var_dump($printSizeName);
        $printSizeParts = explode('x', $printSize);
        $printSizeWidthInch = $printSizeParts[0];
        $printSizeHeightInch = $printSizeParts[1];
        // find setting redbubble from table rebubble_print_offset
        $productSku = $item->product->sku;
        $productMatched = ProductSkuMatching::query()->where('store_id', $storeRedBubbleID)
            ->where('swiftpod_sku', $productSku)
            ->first();

        if (!$productMatched) {
            $message = "Not found attribute redbubble for product sku: $productSku";
            throw new \Exception($message);
        }

        parse_str($productMatched->attribute, $attribute);
        $style = $attribute['style'];
        $size = $attribute['size'];
        var_dump($productMatched->attribute);
        $dataRedBubbleLog = IntegrateLog::query()->where('order_id', $item->order_id)->where('store_id', $storeRedBubbleID)->first();

        if (empty($dataRedBubbleLog)) {
            $message = "Not found sale order redbubble integrate log: $this->imageId";
            throw new \Exception($message);
        }

        $dataRedBubble = json_decode($dataRedBubbleLog->json);
        $dataPrintSideRb = $item->print_side == 1 ? 'back' : 'front';

        $printOffset = RedBubblePrintOffset::query()
            ->where('style', $style)
            ->where('size', $size)
            ->where('side', $dataPrintSideRb)
            ->first();

        if (!$printOffset) {
            $message = "Not found print offset for style: $style and size: $size";
            throw new \Exception($message);
        }

        $offsetXMM = 0;
        $offsetYMM = 0;
        $dpi = 0;
        $checkExistItem = false;
        $shipments = $dataRedBubble?->shipments;

        foreach ($shipments as $dataItemShipments) {
            foreach ($dataItemShipments?->items as $dataRb) {
                if ($dataRb->item_id != $saleOrderItem->external_id) {
                    continue;
                }

                $productRedbubble = buildRedbubbleAttribute($dataRb?->product_info?->product, $dataRb?->product_info?->properties?->body_color, $dataRb?->product_info?->properties?->size);

                if ($dataRb?->product_info?->properties?->print_location == $dataPrintSideRb && $productRedbubble == $productMatched->attribute) {
                    foreach ($dataRb?->assets as $asset) {
                        if ($asset?->description == 'trimmed') {
                            if ($asset?->url == $item->image_url) {
                                // check image có đúng là của item đang xử lý không vì phía trên chỉ so sánh theo style color size
                                $offsetXMM = $asset?->metadata?->x_offset_mm;
                                $offsetYMM = $asset?->metadata?->y_offset_mm;
                                $dpi = $asset?->metadata?->ppi;
                                $checkExistItem = true;
                            }
                        }
                    }
                }
            }
        }

        if (!$checkExistItem) {
            $message = "Not found trimmed in redbubble integrate log: $this->imageId";
            throw new \Exception($message);
        }

        $printSizeInPalletOffsetYMM = $printOffset->y_offset_mm;
        var_dump("printSizeInPalletOffsetYMM: $printSizeInPalletOffsetYMM");
        var_dump($printSizeWidthInch, $printSizeHeightInch, $offsetXMM, $offsetYMM, $printSizeInPalletOffsetYMM, $dpi);
        $image = $this->convertRedBubble($printSizeWidthInch, $printSizeHeightInch, $offsetXMM, $offsetYMM, $printSizeInPalletOffsetYMM, $dpi);
        // upload to s3
        $side = $item->print_side;
        $path = $item->order_date . '/' . $item->sku . '-' . $side . '.png';

        if (!empty($item->product?->productStyle?->type) && $item->product?->productStyle?->type == ProductStyle::TYPE_INSERT && $item->image_ext == 'pdf') {
            $path = $item->order_date . '/' . $item->sku . '-' . $side . '.' . $item->image_ext;
        }

        $pathFileS3 = $this->pathS3 . $path;
        Storage::disk('s3')->put($pathFileS3, $image->getImageBlob());
        $this->img = $image;
    }
}
