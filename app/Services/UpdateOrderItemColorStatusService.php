<?php

namespace App\Services;

use App\Models\EmbroideryTask;
use App\Models\PrintMethod;
use App\Models\ProductColor;
use App\Models\ProductPrintArea;
use App\Models\SaleOrderHistory;
use App\Models\SaleOrderItem;
use App\Models\SaleOrderItemImage;
use App\Models\Store;
use App\Repositories\LavenderRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class UpdateOrderItemColorStatusService
{
    /**
     * @var int
     */
    protected $orderItemId;

    protected $attempts;

    protected $maxRetries;

    public function __construct($orderItemId, $attempts, $maxRetries)
    {
        $this->orderItemId = $orderItemId;
        $this->attempts = $attempts;
        $this->maxRetries = $maxRetries;
    }

    public function handle()
    {
        try {
            jobEcho("$this->orderItemId | update order item color status");
            $saleOrderItem = SaleOrderItem::with(['images.printSide', 'product.productStyle'])->findOrFail($this->orderItemId);
            $dstOrder = false;

            if ($saleOrderItem) {
                foreach ($saleOrderItem->images as $key => $image) {
                    $area = ProductPrintArea::where('name', $image->printSide?->name)->where('product_style_id', $saleOrderItem->product?->productStyle?->id)->first();

                    if ($area?->print_method == PrintMethod::EMB) {
                        if ($image->thumb_250 != 1 || $image->thumb_750 != 1) {
                            jobEcho("$this->orderItemId | job suspended. waiting create thumb embroidery file $image->id before update color status");

                            return;
                        }

                        $dstOrder = true;
                    }
                }
            }

            if ($dstOrder) {
                $embroideryTask = EmbroideryTask::where('order_item_id', $saleOrderItem->id)->where('status', '!=', 'completed')->get();

                foreach ($embroideryTask as $task) {
                    if (in_array($task->status, [EmbroideryTask::STATUS_PENDING, EmbroideryTask::STATUS_IN_PROGRESS, EmbroideryTask::STATUS_IN_REVIEW, EmbroideryTask::STATUS_REJECT])) {
                        jobEcho("$this->orderItemId | job suspended. waiting review embroidery file before update color status");

                        return;
                    }
                }
            }

            $dtgPrintSize = ProductPrintArea::with('productPrintSide')
                ->whereHas('productStyle', function ($q) use ($saleOrderItem) {
                    $q->where('sku', $saleOrderItem->product_style_sku);
                })
                ->where('print_method', PrintMethod::DTG)
                ->get()
                ->pluck('productPrintSide.code');
            $imageItems = SaleOrderItemImage::with('printSide')->where('order_item_id', $this->orderItemId)
                ->whereIn('print_side', $dtgPrintSize)
                ->get();
            $detectedImages = $imageItems->filter(function ($item) {
                return $item->color_new !== null;
            });

            // if all images have color detected
            jobEcho("$this->orderItemId | detectedImages: {$detectedImages->count()} | imageItems: {$imageItems->count()}");

            if ($detectedImages->count() < $imageItems->count()) {
                jobEcho("$this->orderItemId | job suspended. waiting detect color for all images before update color status");

                return;
            }

            $failItems = $detectedImages->filter(function ($item) {
                return $item->color_new == SaleOrderItemImage::COLOR_ERROR;
            });

            // if all images have color detected and have item with color error
            if ($failItems->count() > 0) {
                throw new \Exception("$this->orderItemId failItems variable > 0");
            }

            $blackItems = $imageItems->filter(function ($item) {
                return $item->color_new == SaleOrderItemImage::COLOR_BLACK;
            });
            $blackInkColor = 0;

            if ($imageItems->count() > 0 && $blackItems->count() == $imageItems->count()) {
                $blackInkColor = SaleOrderItemImage::COLOR_BLACK;
            }

            if ($saleOrderItem->store_id == Store::STORE_REDBUBBLE) {
                $result = SaleOrderItem::where('id', $this->orderItemId)->update([
                    'ink_color' => $blackInkColor,
                    'ink_color_detected_at' => Carbon::now(),
                ]);

                $hasPendingItemsNotDetectColor = SaleOrderItem::where('order_id', $saleOrderItem->order_id)
                    ->whereNull('ink_color_detected_at')
                    ->exists();

                if (!$hasPendingItemsNotDetectColor) {
                    $orderItemReject = SaleOrderItemImage::with('orderItem.product')
                        ->where('order_id', $saleOrderItem->order_id)
                        ->get()
                        ->filter(function ($image) {
                            return ($image->orderItem->product->color == ProductColor::WHITE_COLOR && $image->color_new === SaleOrderItemImage::COLOR_WHITE)
                                || ($image->orderItem->product->color == ProductColor::BLACK_COLOR && $image->color_new === SaleOrderItemImage::COLOR_BLACK);
                        })
                        ->pluck('orderItem.id')
                        ->unique()
                        ->toArray();

                    if (!empty($orderItemReject)) {
                        $orderItemQty = SaleOrderItem::where('order_id', $saleOrderItem->order_id)->count();

                        if (count($orderItemReject) < $orderItemQty) {
                            resolve(LavenderRepository::class)->rejectOrderItemsRedBubbleWhenDetectSameColor($saleOrderItem->order_id, $orderItemReject);
                        } else {
                            resolve(LavenderRepository::class)->rejectOrderRedBubbleWhenDetectSameColor($saleOrderItem->order_id);
                        }
                    } else {
                        $result = SaleOrderItem::where('order_id', $saleOrderItem->order_id)->update([
                            'ink_color_status' => SaleOrderItem::DETECT_INK_COLOR_DONE,
                        ]);
                    }
                }
            } else {
                $result = SaleOrderItem::where('id', $this->orderItemId)->update([
                    'ink_color' => $blackInkColor,
                    'ink_color_status' => SaleOrderItem::DETECT_INK_COLOR_DONE,
                    'ink_color_detected_at' => Carbon::now(),
                ]);

                if ($saleOrderItem->store_id == Store::PRINTIFY_API_ID) {
                    $allCustomPlaten = SaleOrderItemImage::query()
                        ->select('custom_platen')
                        ->where('order_item_id', $this->orderItemId)
                        ->whereIn('print_side', [0, 1])
                        ->groupBy('custom_platen')
                        ->get()
                        ->count();

                    if ($allCustomPlaten > 1) {
                        SaleOrderItemImage::query()
                            ->where('order_item_id', $this->orderItemId)
                            ->whereIn('print_side', [0, 1])
                            ->update(['custom_platen' => '16x18']);
                    }
                }
            }

            jobEcho("$this->orderItemId | $result | ink_color: $blackInkColor | ink_color_status: " . SaleOrderItem::DETECT_INK_COLOR_DONE);
            Log::channel('job_update_item_color_status_success')->info("$this->orderItemId | $result | ink_color: $blackInkColor | ink_color_status: " . SaleOrderItem::DETECT_INK_COLOR_DONE);

            foreach ($imageItems as $item) {
                OrderIssueService::resolveIssue([
                    'order_id' => $item->order_id,
                    'order_item_id' => $item->order_item_id,
                    'sku' => $item->sku,
                    'type' => SaleOrderHistory::DETECT_COLOR_ERROR,
                    'side' => $item->printSide->code_name ?? '',
                    'side_name' => $item->printSide->name ?? ''
                ]);
            }
        } catch (\Exception $e) {
            jobEcho("$this->orderItemId | update order item color status: " . $e->getMessage());
            jobEcho("attempts: {$this->attempts}");
            jobEcho("maxRetries: {$this->maxRetries}");

            if ($this->attempts == $this->maxRetries && !empty($failItems) && $failItems->count() > 0) {
                foreach ($failItems as $item) {
                    OrderIssueService::logIssue([
                        'order_id' => $item->order_id,
                        'order_item_id' => $item->order_item_id,
                        'sku' => $item->sku,
                        'type' => SaleOrderHistory::DETECT_COLOR_ERROR,
                        'side' => $item->printSide->code_name ?? '',
                        'side_name' => $item->printSide->name ?? '',
                    ]);
                }
            }

            Log::channel('image')->error("$this->orderItemId | update order item color status: " . $e->getMessage());
            throw $e;
        }
    }
}
