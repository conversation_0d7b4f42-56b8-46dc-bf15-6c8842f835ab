<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use QuickBooksOnline\API\Facades\Vendor;

class QBVendorService extends QBBaseService
{
    const ENTITY_TYPE = 'Vendor';

    public function __construct()
    {
        parent::__construct();
    }

    public function buildRequest($item): array
    {
        return [
            'CompanyName' => $item->name,
            'DisplayName' => "{$item->id}. {$item->name}",
            'PrimaryEmailAddr' => [
                'Address' => $item->email,
            ],
            'PrimaryPhone' => [
                'FreeFormNumber' => $item->phone
            ],
            'Mobile' => [
                'FreeFormNumber' => $item->cell_phone
            ],
        ];
    }

    public function create($item)
    {
        $dataService = $this->configureData();
        $dataRequest = $this->buildRequest($item);
        $entry = $dataService->Add(Vendor::create($dataRequest));
        $this->updateVendorQBRef($item, $entry->Id ?? ($item->qb_ref - 1));

        if (empty($entry)) {
            $this->logLastError($dataService, $item->id, self::ENTITY_TYPE, __METHOD__, $dataRequest);
        }

        return $entry;
    }

    public function update($item)
    {
        $dataService = $this->configureData();
        $qbItem = $dataService->FindById(self::ENTITY_TYPE, $item->qb_ref);

        if (!$qbItem) {
            throw new \Exception('Not found vendor in Quick Book, id: ' . $item->id);
        }

        $dataRequest = $this->buildRequest($item);
        $entry = $dataService->Update(Vendor::update($qbItem, $dataRequest));

        if ($entry) {
            $this->updateVendorQBRef($item, $entry->Id);
        } else {
            $this->logLastError($dataService, $item->id, self::ENTITY_TYPE, __METHOD__, $dataRequest);
        }

        return $entry;
    }

    public function delete($item)
    {
        $dataService = $this->configureData();
        $qbItem = $dataService->FindById(self::ENTITY_TYPE, $item->qb_ref);

        if (!$qbItem) {
            throw new \Exception('Not found vendor in Quick Book, id: ' . $item->id);
        }

        $entry = $dataService->Update(Vendor::update($qbItem, ['Active' => false]));

        if ($entry) {
            $this->deleteVendorQBRef($item);
        }

        return $entry;
    }

    public function updateVendorQBRef($item, $ref)
    {
        if ($item->qb_ref <= 0) {
            DB::table('vendor')
                ->where('id', $item->id)
                ->where('qb_ref', '<=', 0)
                ->update(['qb_ref' => $ref]);
        }
    }

    public function deleteVendorQBRef($item): void
    {
        DB::table('vendor')
            ->where('id', $item->id)
            ->update(['qb_ref' => 0]);
    }
}
