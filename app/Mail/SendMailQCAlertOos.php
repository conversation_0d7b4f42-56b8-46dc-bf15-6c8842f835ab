<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendMailQCAlertOos extends Mailable
{
    use Queueable, SerializesModels;

    public $details;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($details)
    {
        $this->details = $details;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $mail = $this->subject('#' . $this->details['id'] . ' - QC FAILED - OOS - ' . $this->details['product_sku'])
        ->with('details', $this->details)
                     ->replyTo($this->details['to'])
                     ->view('emails.qc_oos');

        // Add multiple CC addresses if any
        if (isset($this->details['cc_to'])) {
            $mail->cc($this->details['cc_to']); // Handle multiple CC addresses
        }

        return $mail;
    }
}
