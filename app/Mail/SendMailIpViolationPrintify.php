<?php

namespace App\Mail;

use App\Exports\IPViolationOrderExportByRejectAt;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class SendMailIpViolationPrintify extends Mailable
{
    use Queueable, SerializesModels;

    private $details;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($details)
    {
        $this->details = $details;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('Daily IP Violation Report -' . Carbon::yesterday('America/Los_Angeles')->format('mdy'))
            ->view('emails.daily_ip_violation_printify')
            ->to($this->details['to'])
            ->cc($this->details['cc_to'])
            ->attachData(
                Excel::raw(new IPViolationOrderExportByRejectAt($this->details), \Maatwebsite\Excel\Excel::XLSX),
                'Printify_IPviolation_' . Carbon::yesterday()->format('mdy') . '.xlsx',
                [
                    'mime' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ],
            );
    }
}
