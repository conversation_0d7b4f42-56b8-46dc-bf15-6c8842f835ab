<?php

namespace App\Exports;

use App\Models\ExportationPartNumber;
use App\Models\ProductTypeWeight;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;


class ExportationExport implements FromView
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function view(): View
    {
        $dataExport = ExportationPartNumber::with('partNumber.product.productStyle:type,name,sleeve_length',
            'partNumber.product.productSize:name',
            'partNumber.partNumberCountry:iso2,name',
            'partNumber.product.brand:id,name',
            'partNumber.productSpec')
            ->select('part_number_id', 'exportation_id', 'created_at', DB::raw('sum(quantity) as quantity_total'))
            ->groupBy('part_number_id')
            ->where('exportation_id', $this->request->exportation_id)
            ->get();

        $dataWeight = ProductTypeWeight::get()->toArray();
        $groupedData = [];
        foreach ($dataExport as $value) {
            $key = substr($value->partNumber->part_number ?? '', 0, -9) . ($value->partNumber->partNumberCountry->name ?? '');
            if (!isset($groupedData[$key])) {
                $groupedData[$key] = [
                    'part_number' => substr($value->partNumber->part_number ?? '', 0, -9),
                    'description' => $value->partNumber->product->description ?? null,
                    'fabric_content' => $value?->partNumber?->productSpec?->fabric_content ?? null,
                    'sleeve' => $value->partNumber->product->productStyle->sleeve_length ?? null,
                    'weight' => $this->calculatorWeightFromStyleAndSize($value->partNumber->product->productStyle->type ?? null, $value->partNumber->product->productSize->name ?? null, $value->quantity_total ?? 0, $dataWeight) ?? 0,
                    'quantity' => $value->quantity_total ?? 0,
                    'brand' => $value->partNumber->product->brand->name ?? null,
                    'country' => $value->partNumber->partNumberCountry->name ?? null,
                ];
            } else {
                $groupedData[$key]['quantity'] = $groupedData[$key]['quantity'] + $value->quantity_total ?? 0;
                $groupedData[$key]['weight'] = $groupedData[$key]['weight'] +
                $this->calculatorWeightFromStyleAndSize($value->partNumber->product->productStyle->type ?? null, $value->partNumber->product->productSize->name ?? null, $value->quantity_total ?? 0, $dataWeight) ?? 0;
            }
        }
        return view('exportationExport', [
            'total' => count(array_values($groupedData)),
            'data' => array_values($groupedData),
            'date' => $dataExport && isset($dataExport[0]) ? $dataExport[0]->created_at->format('M, jS Y') : null
        ]);
    }

    public function calculatorWeightFromStyleAndSize($style, $size, $quantity, $dataWeight)
    {
        $weightProduct = array_filter($dataWeight, function ($var) use ($style, $size) {
            return $var['name'] === ucfirst($style) && $var['size'] === ucfirst($size);
        });

        if (empty($weightProduct)) {
            $weightProduct = array_filter($dataWeight, function ($v) use ($style, $size) {
                return $v['name'] === ucfirst($style) && $v['size'] === "any";
            });
        }

        $weightProduct = empty($weightProduct) ? 0 : array_shift($weightProduct)['weight_oz'];
        $weight = $this->convertOzToKg((float)$weightProduct * $quantity);
        return $weight;
    }

    public function convertOzToKg($weight)
    {
        return $weight * 0.0283495;
    }

}
