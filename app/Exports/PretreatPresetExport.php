<?php

namespace App\Exports;

use App\Models\PretreatPreset;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set("memory_limit", "2048M");

class PretreatPresetExport implements FromCollection, WithHeadings
{
    private $request;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        if (empty($this->request['mode'])) {
            $data = [];
        } else {
            $data = $this->getAll();
        }
        return collect($data);
    }

    public function headings(): array
    {
        return ["Preset Name",  "Density (gram)", "Cure time (s)", "Cure temperature (°F)", "Press time (s)", "Press temperature (°F)", "Pressure (PSI)", "Print Cure time (s)", "Print Cure temperature (°F)"];
    }
    public function getAll(): array
    {
        $dataPreset = PretreatPreset::query()->get();
        $data = [];
        foreach ($dataPreset as $key => $item) {
            $data[] = [
                'preset_name' => $item->preset_name,
                'density' => $item->density,
                'cure_time' => $item->cure_time,
                'cure_temperature' => $item->cure_temperature,
                'press_time' =>  $item->press_time,
                'press_temperature' => $item->press_temperature,
                'pressure' => $item->pressure,
                // 'print_time' => $item->print_time,
                'print_cure_time' => $item->print_cure_time,
                'print_cure_temperature' => $item->print_cure_temperature,
            ];
        }
        return $data;
    }
}
