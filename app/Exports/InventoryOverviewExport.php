<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class InventoryOverviewExport implements FromCollection, WithHeadings
{
    protected $query;

    public function __construct($query)
    {
        $this->query = $query;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query_select = 'SELECT
                tb.sku,
                tb.style,
                tb.size,
                tb.color,
                tb.gtin,
                COALESCE(product_rank_histories.rank, "-") AS product_rank,
                COALESCE(tb.total_box, "0") AS total_box,
                COALESCE(tb.box_quantiy, "0") AS rack_quantity,
                COALESCE(tb.quantity_product, "0") AS pulling_quantity,
                COALESCE(SUM(product_quantity.`incoming_stock`), "0") AS incoming_stock';
        $query_select .= $this->query;
        $data = DB::select($query_select);

        return collect($data);
    }

    public function headings(): array
    {
        return [
            'SKU',
            'Style',
            'Size',
            'Color',
            'GTIN',
            'SKU Rank',
            'Total Box',
            'Rack Quantity',
            'Pulling Quantity',
            //'Percent Quantity',
            'Incoming Stock',
        ];
    }
}
