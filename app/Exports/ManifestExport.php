<?php


namespace App\Exports;

use App\Models\ShipmentManifestTracking;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use App\Models\ShipmentManifest;
use Illuminate\Support\Carbon;

class ManifestExport implements FromCollection, WithHeadings
{
    protected $manifestId;

    public function __construct($manifestId)
    {
        $this->manifestId = $manifestId;
    }

    public function collection()
    {
        $data = collect();

        ShipmentManifestTracking::query()->with(['employee', 'shipmentManifest', 'shipment:tracking_number,ship_date'])
            ->where('manifest_id', $this->manifestId)
            ->chunkById(1000, function ($chunk) use ($data) {
                try {
                    foreach ($chunk as $item) {
                        $data->push($this->transformData($item));
                    }
                } catch (\Throwable $th) {
                    \Log::error($th->getMessage());
                }
            }, 'shipment_manifest_tracking.id', 'id');

        if(!empty($data)) {
            $data = $data->sortBy(function ($item) {
                return Carbon::parse($item[4])->timestamp;
            })->map(function ($item) {
                return array_slice($item, 0, 4);
            })->values()->all();
        }

        return collect($data);
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        // TODO: Implement headings() method.
        return [
            "Tracking number",
            "Created at",
            "Employee scan",
            "Note"
        ];
    }

    /**
     * @param $row
     * @return array
     */
    public function transformData($row)
    {
        $dataNow = Carbon::now();
        return [
            $row->tracking_number ?? null,
            $row->created_at ?? null,
            $row->employee->name ?? null,
            in_array($row->shipmentManifest?->status, [ShipmentManifest::STATUS_SCANNING, ShipmentManifest::STATUS_GENERATING])
                ? ($dataNow->diffInDays($row->shipment->ship_date) >= 1 ? 'Exceeding the 1-day creation limit' : '')
                : (($row->shipmentManifest?->created_at->diffInDays($row->shipment->ship_date) >= 1) ? 'Exceeding the 1-day creation limit' : ''),
            $row->shipment->ship_date ?? null,
        ];
    }

}
