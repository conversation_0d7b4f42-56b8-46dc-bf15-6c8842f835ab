<?php

namespace App\Exports;

use App\Repositories\StoreProductRepository;
use Maatwebsite\Excel\Concerns\FromCollection;

class CatalogPricingExport implements FromCollection
{
    protected array $requestData;

    public function __construct($requestData)
    {
        $this->requestData = $requestData;
    }

    public function collection()
    {
        $storeId = $this->requestData['store_id'];
        $styles = array_map('strval', $this->requestData['styles']);
        $storeProductRepo = app(StoreProductRepository::class);
        $result = $storeProductRepo->getDataPrintPriceDetail($storeId, $styles);

        return collect($result);
    }
}
