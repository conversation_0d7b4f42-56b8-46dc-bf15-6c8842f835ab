<?php

namespace App\Exports;

use App\Models\ProductPrintSide;
use App\Models\SaleOrder;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

ini_set('memory_limit', '8G');

class SaleOrderOpenExport implements FromCollection, WithHeadings
{
    protected $input;

    public function __construct($input)
    {
        $this->input = $input;
        $this->dataPrintArea = $this->getDataPrintArea();
    }

    public function collection()
    {
        $query = SaleOrder::join('warehouse', 'warehouse.id', '=', 'sale_order.warehouse_id')
            ->Join('sale_order_item', 'sale_order_item.order_id', '=', 'sale_order.id')
            ->where('sale_order.is_test', 0)
            ->whereIn('sale_order.order_status', ['new_order', 'in_production'])
            ->whereDate('sale_order.created_at', '>=', \Carbon\Carbon::now()->subDays(30));

        if ($this->input['type'] !== 'OpenOrders') {
            $query->where('sale_order.order_type', SaleOrder::ORDER_TYPE_TIKTOK_ORDER);
        }

        if (!empty($this->input['warehouse']) && $this->input['warehouse'] !== 'all') {
            $query->where('sale_order.warehouse_id', $this->input['warehouse']);
        }

        if (!empty($this->input['store_id'])) {
            $query->where('sale_order.store_id', $this->input['store_id']);
        }

        $query->orderBy('sale_order.id')
            ->selectRaw('
            warehouse.name as warehouse_name,
            sale_order.order_number,
            sale_order.order_status,
            sale_order.order_type,
            sale_order_item.sku,
            sale_order_item.quantity,
            sale_order_item.print_sides
        ');
        $data = [];

        $query->chunk(5000, function ($items) use (&$data) {
            $newData = $this->convertData($items);
            $data = array_merge($data, $newData);
        });

        return collect($data);
    }

    public function headings(): array
    {
        return ['Warehouse', 'Order Number', 'Order Status', 'Order Type', 'Item SKU', 'Quantity', 'Print Area'];
    }

    public function getDataPrintArea()
    {
        return ProductPrintSide::pluck('name', 'code_wip')->toArray();
    }

    public function convertData($data = [])
    {
        $newData = [];
        foreach ($data as $item) {
            $newData[] = [
                'warehouse_name' => $item->warehouse_name,
                'order_number' => $item->order_number,
                'order_status' => ucwords(str_replace('_', ' ', $item->order_status)),
                'order_type' => SaleOrder::LIST_ORDER_TYPE[(string) $item->order_type],
                'sku' => $item->sku,
                'quantity' => $item->quantity,
                'print_sides' => rtrim($this->getPrintArea($item->print_sides), ', '),
            ];
        }

        return $newData;
    }

    public function getPrintArea($printSides)
    {
        $namePrintArea = '';
        $sides = str_split($printSides);
        if (count($sides) > 0 && $printSides) {
            foreach ($sides as $side) {
                $namePrintArea .= $this->dataPrintArea[$side] . ', ' ?? '';
            }
        }

        return $namePrintArea;
    }
}
