<?php

namespace App\Exports;

use App\Models\Invoice;
use App\Models\InvoiceSaleOrder;
use App\Models\SaleOrder;
use App\Models\SaleOrderItem;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

// TODO: Change to queue for handle large data
class PromotionInvoiceExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder
{
    protected Invoice $invoice;

    protected $printSide;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->invoice->load(['store']);
    }

    public function bindValue(Cell $cell, $value)
    {
        if ($cell->getColumn() === 'A') {
            if (is_numeric($value)) {
                $cell->setValueExplicit($value, DataType::TYPE_STRING);

                return true;
            }
        }

        return parent::bindValue($cell, $value);
    }

    public function collection()
    {
        setTimezone();
        $hasPromotion = false;
        $data = collect();
        $i = 1;
        $allSaleOrderIds = InvoiceSaleOrder::where('invoice_id', $this->invoice->id)->pluck('sale_order_id');
        $chunkSaleOrderIds = $allSaleOrderIds->chunk(500);
        foreach ($chunkSaleOrderIds as $saleOrderIds) {
            SaleOrderItem::whereIn('sale_order.id', $saleOrderIds)
                ->join('sale_order', 'sale_order_item.order_id', '=', 'sale_order.id')
                ->join('sale_order_promotions', 'sale_order_item.id', '=', 'sale_order_promotions.order_item_id')
                ->join('promotions', 'sale_order_promotions.promotion_id', '=', 'promotions.id')
                ->join('promotion_types', 'promotions.promotion_type_id', '=', 'promotion_types.id')
                ->leftJoin('shipment', 'sale_order.shipment_id', 'shipment.id')
                ->select([
                    'sale_order.id as sale_order_id',
                    'sale_order.store_id',
                    'sale_order.order_type',
                    'sale_order.tag',
                    'sale_order.order_number',
                    'sale_order.external_number',
                    'sale_order.order_status',
                    'sale_order.created_at',
                    'sale_order_item.id as id',
                    'sale_order_item.order_id',
                    'sale_order_item.quantity',
                    'sale_order_item.product_id',
                    'sale_order_item.sku',
                    'sale_order_item.product_sku',
                    'shipment.ship_date',
                    'shipment.created_at as shipment_created_at',
                    'sale_order_promotions.print_area',
                    'sale_order_promotions.amount',
                    'sale_order_promotions.promotion_id',
                    'promotion_types.name as promotion_type_name',

                ])
                ->chunkById(1000, function ($chunk) use ($data, &$i, &$hasPromotion) {
                    echo "Chunk $i has " . $chunk->count() . ' items, time: ' . Carbon::now()->format('Y-m-d H:i:s') . PHP_EOL;
                    $hasPromotion = true;
                    try {
                        foreach ($chunk as $item) {
                            $data->push($this->transformData($item));
                        }
                    } catch (\Throwable $th) {
                        \Log::error($th->getMessage());
                    }
                    $i++;
                }, 'sale_order_item.id', 'id');
        }

        if ($hasPromotion) {
            $this->invoice->has_promotion_order_invoice = Invoice::ACTIVE;
            $this->invoice->save();
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            $this?->invoice?->store->name . ' Order #',
            'Print Provider Order #',
            'Order Type',
            'Status',
            'Product SKU',
            'Order Created',
            'Ship Date',
            'Item Quantity',
            'Print Area',
            'Promotion Fee',
            'Promotion Type',
        ];
    }

    private function transformData($row)
    {
        return [
            $row->external_number,
            $row->order_number,
            SaleOrder::transformOrderType($row->order_type, $row->tag),
            $row->order_status,
            $row->product_sku,
            date('m/d/Y H:i', strtotime($row->created_at)),
            $row->shipment_created_at ? date('m/d/Y H:i', strtotime($row->shipment_created_at)) : '',
            $row->quantity,
            $row->print_area,
            $row->amount,
            $row->promotion_type_name,

        ];
    }
}
