<?php

namespace App\Exports;

use App\Models\PurchaseOrder;
use App\Models\Tag;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

ini_set('memory_limit', '2048M');

class PurchaseOrderExport implements FromCollection, WithHeadings, WithEvents
{
    protected $request;

    protected $tags;

    protected $vendors;

    protected $users;

    public function __construct($request)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $tagModel = new Tag();
        $this->tags = $tagModel::all()->pluck('name', 'id');

        $vendorModel = new Vendor();
        $this->vendors = $vendorModel::all()->pluck('name', 'id');

        $userModel = new User();
        $this->users = $userModel::all()->pluck('username', 'id');

        $query = DB::table('purchase_order_item')->leftJoin('purchase_order', function ($join) {
            $join->on('purchase_order_item.po_id', 'purchase_order.id');
        })->leftJoin('product', function ($join) {
            $join->on('purchase_order_item.product_id', 'product.id');
        })->leftJoin('purchase_order_box', function ($join) {
            $join->on('purchase_order_box.po_id', '=', 'purchase_order_item.po_id');
        })->where('purchase_order.warehouse_id', $this->request['warehouse_id']);

        if (!empty($this->request['date'])) {
            $date = explode(',', $this->request['date']);
            $from = Carbon::parse($date[0])->startOfDay();
            $to = Carbon::parse($date[1])->endOfDay();
            $query->whereBetween('purchase_order.order_date', [$from, $to]);
        }
        $tags = !empty($this->request['tag']) ? $this->request['tag'] : '';
        if (!empty($tags) && is_array($tags) && count($tags) > 0) {
            foreach ($tags as $key => $tag) {
                if ($key === 0) {
                    $query->whereRaw('FIND_IN_SET(?,tag)', $tag);
                } else {
                    $query->orWhereRaw('FIND_IN_SET(?,tag)', $tag);
                }
            }
        }
        $query->select(
            'purchase_order.*',
            'purchase_order_item.id as id',
            'purchase_order_item.total as total',
            'purchase_order_item.quantity as quantity',
            'purchase_order_item.quantity_onhand',
            'product.sku as sku',
            'product.name as product_name',
            DB::raw('COUNT(purchase_order_box.box_number) AS total_box'),
        );
        $sortColumn = !empty($this->request['sort_column']) ? $this->request['sort_column'] : 'purchase_order_item.id';
        $sortBy = !empty($this->request['sort_by']) ? $this->request['sort_by'] : 'desc';
        $query->orderBy($sortColumn, $sortBy);
        $query->groupBy('purchase_order_item.id');
        $data = [];
        $query->chunkById(5000, function ($items) use (&$data) {
            $newData = $this->convertData($items->toArray());
            $data = array_merge($data, $newData);
        }, 'purchase_order_item.id', 'id');

        return collect($data);
    }

    public function headings(): array
    {
        return ['PO#', 'Order#', 'Invoice#', 'Order Date', 'Vendor', 'Status', 'Tracking#', 'ETA', 'SKU', 'Product', 'Quantity', 'Received Quantity', 'Incoming Quantity', 'Amount', 'Tag', 'User', 'Box'];
    }

    public function convertData($data = [])
    {
        $newData = [];
        foreach ($data as $item) {
            array_push($newData, [
                'po_number' => $item->po_number ?? '',
                'order_number' => $item->order_number ?? '',
                'invoice_number' => $item->invoice_number ?? '',
                'order_date' => $item->order_date ?? '',
                'vendor_name' => !empty($item->vendor_id) && isset($this->vendors[$item->vendor_id]) ? $this->vendors[$item->vendor_id] : '',
                'status' => $this->getOrderStatus($item->order_status),
                'tracking_number' => $item->tracking_number ?? '',
                'delivery_date' => $item->delivery_date ?? '',
                'sku' => $item->sku ?? '',
                'product' => $item->product_name,
                'qty' => $item->quantity ?? '',
                'received_quantity' => $item->quantity_onhand ?? 0,
                'incoming_quantity' => in_array($item->order_status, [PurchaseOrder::COMPLETED_STATUS, PurchaseOrder::CANCELLED_STATUS]) ? 0 : $item->quantity - $item->quantity_onhand,
                'total_price' => isset($item->total) ? round((float) $item->total, 2) : null,
                'tag' => $this->getTag($item->tag),
                'username' => !empty($item->user_id) && isset($this->users[$item->user_id]) ? $this->users[$item->user_id] : '',
                'total_box' => $item->total_box ?? '',
            ]);
        }

        return $newData;
    }

    public function getOrderStatus($status)
    {
        $orderStatus = [
            'new_order' => 'New Order',
            'not_shipped' => 'Not Shipped',
            'shipped' => 'Shipped',
            'delivered' => 'Delivered',
            'completed' => 'Completed',
            'partial_received' => 'Partial Received',
            'cancelled' => 'Cancelled',
            'incompleted' => 'Incompleted',
            'error' => 'Error',

        ];

        return isset($orderStatus[$status]) ? $orderStatus[$status] : '';
    }

    public function getTag($tag)
    {
        if (empty($tag)) {
            return '';
        }
        $newTag = [];
        $arr = explode(',', $tag);
        foreach ($arr as $value) {
            if (isset($this->tags[$value])) {
                array_push($newTag, $this->tags[$value]);
            }
        }

        return implode(',', $newTag);
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Giả sử cột total_price là cột N = 14
                $event->sheet->getDelegate()
                    ->getStyle('N2:N10000') // từ hàng 2 đến 10000
                    ->getNumberFormat()
                    ->setFormatCode('"$"#,##0.00');
            },
        ];
    }
}
