<?php

namespace App\Exports;

use App\Models\SaleOrder;
use App\Models\Shipment;
use App\Models\Store;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

// TODO: should remove when the invoice function works well
class ShippingInvoiceTempExport implements FromCollection, WithHeadings
{
    protected $store;
    protected $startDate;
    protected $endDate;

    public function __construct(Store $store, $startDate, $endDate)
    {
        $this->store = $store;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function collection()
    {
        $data = collect();
        Shipment::query()
            ->join('sale_order', 'sale_order.id', '=', 'shipment.order_id')
            ->join('sale_order_address', function ($join) {
                $join->on('sale_order.id', 'sale_order_address.order_id')
                    ->where('sale_order_address.type_address', 'to_address');
            })
            ->where('sale_order.store_id', $this->store->id)
            ->where(function($q) {
                $q->where(function($queryShipped) {
                    $queryShipped->where('sale_order.order_status', SaleOrder::SHIPPED)
                        ->where('shipment.created_at', '>=', $this->startDate)
                        ->where('shipment.created_at', '<=', $this->endDate);
                })
                ->orWhere(function($queryLateCancelled) {
                    $queryLateCancelled->where('sale_order.order_status', SaleOrder::STATUS_LATE_CANCELLED)
                        ->where('sale_order.created_at', '>=', $this->startDate)
                        ->where('sale_order.created_at', '<=', $this->endDate);
                });
            })
            ->select([
                'shipment.id as id',
                'shipment.ship_date',
                'shipment.shipment_cost',
                'shipment.tracking_number',
                'shipment.carrier_code',
                'shipment.service_code',
                'shipment.weight_value',
                'shipment.weight_unit',
                'sale_order_address.city',
                'sale_order_address.state',
                'sale_order_address.zip',
                'sale_order_address.country',
                'sale_order.id as sale_order_id',
                'sale_order.store_id',
                'sale_order.order_number',
                'sale_order.external_number',
                'sale_order.order_status',
                'sale_order.order_date',
                'sale_order.shipping_calculate',
            ])
            ->chunkById(1000, function ($chunk) use ($data) {
                try {
                    foreach ($chunk as $item) {
                        $data->push($this->transformData($item));
                    }
                } catch (\Throwable $th) {
                    \Log::error($th->getMessage());
                }
            }, 'shipment.id', 'id');

        return $data;
    }

    public function headings(): array
    {
        return [
            $this->store->name . ' Order #',
            'Print Provider Order #',
            'Order Created',
            'Ship Date',
            'Ship Cost',
            'Tax',
            'Ship cost with tax',
            'Tracking Number',
            'City',
            'State',
            'Zip',
            'Country',
            'Weight (lbs)',
            'Carrier',
            'Service',
        ];
    }

    private function transformData($row)
    {
        // Calculate oz to lb and g to lb
        $weight = 0;
        switch ($row->weight_unit) {
            case 'ounces':
                $weight = ($row->weight_value ?? 0) * 0.0625;
                break;

            case 'grams':
                $weight = ($row->weight_value ?? 0) * 0.0022046;
                break;
                
            default:
                $weight = $row->weight_value ?? 0;
                break;
        }

        return [
            $row->external_number,
            $row->order_number,
            $row->order_date,
            $row->ship_date,
            '',
            '',
            '',
            $row->tracking_number,
            $row->city,
            $row->state,
            $row->zip,
            $row->country,
            $weight,
            $row->carrier_code,
            $row->service_code,
        ];
    }
}
