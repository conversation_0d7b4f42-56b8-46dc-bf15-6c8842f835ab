<?php
namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithMapping;

class SwiftpodExport implements FromArray, WithHeadings, WithTitle, ShouldAutoSize, WithMapping
{
    protected $rows;

    public function __construct(array $rows)
    {
        $this->rows = $rows;
    }

    public function map($row): array
    {
        return [
            $row['order_time'],
            $row['external_number'],
            $row['missing']
        ];
    }

    public function headings(): array
    {
        return [
            'Order Time',
            'External Number',
            'Missing'
        ];
    }

    public function array(): array
    {
        \Log::debug($this->rows);
        return $this->rows;
    }

    public function title(): string
    {
        return 'swiftpod';
    }
}
