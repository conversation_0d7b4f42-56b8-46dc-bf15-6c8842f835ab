<?php

namespace App\Exports;

use App\Models\Invoice;
use App\Models\InvoiceSaleOrder;
use App\Models\SaleOrder;
use App\Models\SurchargeFee;
use App\Models\SurchargeService;
use App\Repositories\SurchargeFeeRepository;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

// TODO: Change to queue for handle large data
class SurchargeInvoiceStandardExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder
{
    protected Invoice $invoice;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function bindValue(Cell $cell, $value)
    {
        if ($cell->getColumn() === 'A') {
            if (is_numeric($value)) {
                $cell->setValueExplicit($value, DataType::TYPE_STRING);

                return true;
            }
        }

        return parent::bindValue($cell, $value);
    }

    public function collection()
    {
        setTimezone();
        $data = collect();
        $surchargeFeeRepository = app()->make(SurchargeFeeRepository::class);
        $handlingService = SurchargeFee::query()
            ->join('surcharge_service', 'surcharge_service.id', '=', 'store_surcharge.service_id')
            ->select('surcharge_service.name', 'surcharge_service.per', 'store_surcharge.value')
            ->whereNotNull('store_surcharge.value')
            ->where('store_surcharge.value', '>', 0)
            ->where('store_id', $this->invoice->store_id)
            ->where('surcharge_service.api_value', SurchargeService::API_VALUE_HANDLING)
            ->first();
        $allSaleOrderIds = InvoiceSaleOrder::where('invoice_id', $this->invoice->id)->pluck('sale_order_id');
        $chunkSaleOrderIds = $allSaleOrderIds->chunk(500);
        foreach ($chunkSaleOrderIds as $saleOrderIds) {
            SaleOrder::query()
                ->select(
                    'sale_order.id',
                    'sale_order.order_number',
                    'sale_order.external_number',
                    'sale_order.order_quantity',
                )
                ->whereIn('sale_order.id', $saleOrderIds)
                ->chunk(200, function ($chunk) use (&$data, $surchargeFeeRepository, $handlingService) {
                    $combinedResults = collect();
                    if ($handlingService) {
                        foreach ($chunk as $item) {
                            $combinedResults->push((object) [
                                'id' => $item->id,
                                'order_number' => $item->order_number,
                                'external_number' => $item->external_number,
                                'quantity' => $item->order_quantity,
                                'total' => round($item->order_quantity * $handlingService->value, 2),
                                'name' => $handlingService->name,
                                'per' => $handlingService->per,
                                'fee' => $handlingService->value,
                            ]);
                        }
                    }
                    $saleOrderIds = $chunk->pluck('id')->toArray();
                    $saleOrdersSurcharge = $surchargeFeeRepository->fetchSaleOrdersSurcharge($saleOrderIds);
                    $combinedResults = $this->mergeMappedResults($saleOrdersSurcharge, $combinedResults);
                    $saleOrderItemsSurcharge = $surchargeFeeRepository->fetchSaleOrdersItemsSurcharge($saleOrderIds);
                    $combinedResults = $this->mergeMappedResults($saleOrderItemsSurcharge, $combinedResults);
                    if ($this->invoice->has_insert_invoice) {
                        $saleOrdersInsert = $surchargeFeeRepository->fetchSaleOrderInsertFeeByInvoice($this->invoice->id, $saleOrderIds);
                        $combinedResults = $this->mergeMappedResults($saleOrdersInsert, $combinedResults);
                    }
                    $sortedResults = $combinedResults->sortBy('id')->values()->all();
                    foreach ($sortedResults as $item) {
                        $data->push([
                            $item->external_number,
                            $item->order_number,
                            $item->name,
                            $item->per,
                            $item->quantity,
                            $item->fee,
                            'USD',
                            $item->total,
                        ]);
                    }
                });
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            $this->invoice->store->name . ' Order #',
            'Print Provider Order #',
            'Surcharge Type',
            'Per',
            'Quantity',
            'Fee',
            'Currency',
            'Total',
        ];
    }

    public function mergeMappedResults($sourceCollection, $combinedResults)
    {
        if (!$sourceCollection->isEmpty()) {
            $mappedResults = $sourceCollection->map(function ($item) {
                return (object) [
                    'id' => $item->id,
                    'order_number' => $item->order_number,
                    'external_number' => $item->external_number,
                    'quantity' => $item->quantity,
                    'total' => $item->total,
                    'name' => $item->name,
                    'per' => $item->per,
                    'fee' => ($item->quantity > 0) ? round($item->total / $item->quantity, 2) : 0,
                ];
            });

            return $combinedResults->concat($mappedResults);
        }

        return $combinedResults;
    }
}
