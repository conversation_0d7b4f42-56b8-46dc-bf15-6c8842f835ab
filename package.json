{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "prepare": "husky"}, "devDependencies": {"axios": "^0.21", "husky": "^9.0.11", "laravel-mix": "^6.0.6", "lint-staged": "^15.2.2", "lodash": "^4.17.19", "postcss": "^8.1.14"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "php artisan test --env=local-testing"}}, "lint-staged": {"./**/*.php": ["./vendor/bin/pint"]}}