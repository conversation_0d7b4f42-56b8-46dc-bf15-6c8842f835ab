{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "arcanedev/log-viewer": "~8", "barryvdh/laravel-dompdf": "^1.0", "barryvdh/laravel-snappy": "^1.0", "easypost/easypost-php": "^5.1", "fruitcake/laravel-cors": "^2.0", "google/apiclient": "^2.14", "guzzlehttp/guzzle": "^7.0.1", "hashids/hashids": "^4.1.0", "intervention/image": "^2.7", "koerel/pdfunite": "^0.1.3", "laminas/laminas-barcode": "^2.11", "laravel/cashier": "^13.17", "laravel/framework": "^8.65", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "league/flysystem-aws-s3-v3": "^1.0", "maatwebsite/excel": "^3.1", "mpdf/mpdf": "^8.0", "mpdf/qrcode": "^1.2", "nmiles/laminas-pdf": "^3.0", "picqer/php-barcode-generator": "^2.2", "quickbooks/v3-php-sdk": "^6.1", "rap2hpoutre/fast-excel": "^4.1", "simplesoftwareio/simple-qrcode": "~4", "spatie/laravel-permission": "^5.5", "tymon/jwt-auth": "^1.0", "wildbit/swiftmailer-postmark": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/pint": "1.5.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "pestphp/pest": "^1.21", "pestphp/pest-plugin-laravel": "^1.2", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}