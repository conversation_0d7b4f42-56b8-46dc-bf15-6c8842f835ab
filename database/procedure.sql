DELIMITER $$
CREATE DEFINER=`inventory`@`localhost` PROCEDURE `CALCPERFORMANCE`()
BEGIN
DECLARE LENGTH INT DEFAULT 0;
DECLARE counter INT DEFAULT 0;
SELECT COUNT(*)
FROM
    (SELECT product.sku,
            product.id,
            SUM(CASE
                    WHEN direction = 0
                        AND inventory.created_at < '2022-03-31' THEN inventory.quantity
                    ELSE 0
                END) - SUM(CASE
                               WHEN direction = 1
                                   AND inventory.created_at < '2022-03-31' THEN inventory.quantity
                               ELSE 0
                END) AS begining,
            SUM(CASE
                    WHEN direction=0
                        AND inventory.created_at >= '2022-03-31'
                        AND inventory.created_at <= '2022-06-30' THEN inventory.quantity
                    ELSE 0
                END) AS INPUT,
          SUM(CASE
                  WHEN direction=1
                       AND inventory.created_at >= '2022-03-31'
                       AND inventory.created_at<= '2022-06-30' THEN inventory.quantity
                  ELSE 0
              END) AS OUTPUT
     FROM inventory
         JOIN product ON inventory.product_id = product.id
     WHERE inventory.is_deleted = 0
       AND inventory.warehouse_id = '1'
     GROUP BY product.id)AS tb INTO LENGTH;

SET counter=0;


WHILE counter<4 DO
        SET @id = 0;
        SET @sku = 0;
        SET @ending =0;

SELECT
    id,
    sku,

    (begining + INPUT - OUTPUT) AS ending  INTO @id, @sku, @ending
FROM
    (SELECT product.sku,
            product.id,
            SUM(CASE
                    WHEN direction = 0
                        AND inventory.created_at < '2022-03-31' THEN inventory.quantity
                    ELSE 0
                END) - SUM(CASE
                               WHEN direction = 1
                                   AND inventory.created_at < '2022-03-31' THEN inventory.quantity
                               ELSE 0
                END) AS begining,
            SUM(CASE
                    WHEN direction=0
                        AND inventory.created_at >= '2022-03-31'
                        AND inventory.created_at <= '2022-06-30' THEN inventory.quantity
                    ELSE 0
                END) AS INPUT,
          SUM(CASE
                  WHEN direction=1
                       AND inventory.created_at >= '2022-03-31'
                       AND inventory.created_at<= '2022-06-30' THEN inventory.quantity
                  ELSE 0
              END) AS OUTPUT
     FROM inventory
         JOIN product ON inventory.product_id = product.id
     WHERE inventory.is_deleted = 0
       AND inventory.warehouse_id = '1'
     GROUP BY product.id)AS tb LIMIT counter,1 ;



SELECT *, @ending:=(@amount-quantity)
FROM
    (SELECT inventory_addition.product_id,
    inventory_addition.po_id,
    inventory_addition.quantity,
    inventory_addition.created_at
    FROM inventory_addition
    JOIN purchase_order_item ON purchase_order_item.po_id = inventory_addition.po_id
    AND purchase_order_item.product_id =inventory_addition.product_id AND inventory_addition.product_id = @id
    GROUP BY inventory_addition.product_id,
    inventory_addition.created_at
    ORDER BY created_at DESC) AS tmp
WHERE @ending >= 0;




SET counter = counter + 1;
END WHILE;
END$$
DELIMITER ;

DELIMITER $$
CREATE DEFINER=`admin`@`localhost` PROCEDURE `create_order_number`(IN `orderDate` VARCHAR(6), IN `warehouseID` VARCHAR(5), IN `orderType` VARCHAR(1))
BEGIN

INSERT INTO `sale_order_number` (serial, order_date, warehouse, order_type) VALUES (1, orderDate,warehouseID,orderType) ON DUPLICATE KEY UPDATE serial = serial + 1;

SELECT CONCAT(i.`order_date`, '-', i.warehouse, '-',i.`order_type`, '-', LPAD(i.serial, GREATEST(LENGTH(i.serial), 6), "0") ) AS number from `sale_order_number` i WHERE `order_type` = orderType AND  `order_date` = orderDate AND `warehouse` = warehouseID;



END$$
DELIMITER ;

DELIMITER $$
CREATE DEFINER=`admin`@`localhost` PROCEDURE `create_serial`(IN `pType` VARCHAR(3))
BEGIN
SELECT DATE_FORMAT(CURRENT_DATE, '%y') INTO @year;

INSERT INTO `serial` (serial, fiscal_year, type) VALUES (1, @year,pType) ON DUPLICATE KEY UPDATE serial = serial + 1;

SELECT CONCAT(i.type, '-',i.fiscal_year, LPAD(i.serial, GREATEST(LENGTH(i.serial), 4), "0") ) AS number from `serial` i WHERE `type` = pType AND  `fiscal_year` = @year;



END$$
DELIMITER ;

DELIMITER $$
CREATE DEFINER=`ducoffice`@`%` PROCEDURE `fetch_fifo_inventory`()
BEGIN


  DECLARE cursor_list_isdone BOOLEAN DEFAULT FALSE;
   DECLARE productId, end_unit INT;
   DECLARE productSku VARCHAR(250) DEFAULT '';
   DECLARE ip_start_date DATE DEFAULT '2022-01-01';
   DECLARE ip_end_date DATE DEFAULT  '2022-07-31';
   DECLARE warehoseId INT DEFAULT  1;

   DECLARE cursor_list CURSOR FOR
SELECT
    id,
    sku,
    (begining + INPUT - OUTPUT) AS end_unit
FROM
    (SELECT product.sku,
            product.id,
            SUM(CASE
                    WHEN direction = 0
                        AND inventory.created_at < ip_start_date THEN inventory.quantity
                    ELSE 0
                END) - SUM(CASE
                               WHEN direction = 1
                                   AND inventory.created_at < ip_start_date THEN inventory.quantity
                               ELSE 0
                END) AS begining,
            SUM(CASE
                    WHEN direction=0
                        AND inventory.created_at >= ip_start_date
                        AND inventory.created_at <= ip_end_date THEN inventory.quantity
                    ELSE 0
                END) AS INPUT,
          SUM(CASE
                  WHEN direction=1
                       AND inventory.created_at >= ip_start_date
                       AND inventory.created_at<= ip_end_date THEN inventory.quantity
                  ELSE 0
              END) AS OUTPUT
     FROM inventory
         JOIN product ON inventory.product_id = product.id
     WHERE inventory.is_deleted = 0   AND inventory.warehouse_id = warehoseId
     GROUP BY product.id  ORDER BY product.id DESC ) AS tb  ;



DECLARE CONTINUE HANDLER FOR NOT FOUND SET cursor_list_isdone = TRUE;



OPEN cursor_list;
loop_list: LOOP
     FETCH cursor_list INTO  productId, productSku, end_unit;
      IF cursor_list_isdone THEN
         LEAVE loop_list;
END IF;
       SET @end_unit = end_unit;
       SET @start_unit = 0;








TRUNCATE TABLE tmp_inv;

INSERT INTO tmp_inv
SELECT
    NULL,

    purchase_order_item.product_id,
    purchase_order_item.quantity,
    purchase_order_item.price



FROM purchase_order_item
         JOIN purchase_order ON purchase_order_item.po_id = purchase_order.id
         JOIN product ON product.id = purchase_order_item.product_id

    AND purchase_order.order_date >= @start_date AND purchase_order.order_date	 <= @end_date
WHERE purchase_order_item.product_id = productId AND
        purchase_order.order_status = 'completed' AND purchase_order.warehouse_id = warehoseId
ORDER BY purchase_order.order_date DESC;






INSERT INTO fifo_inventory (product_id, sku, value, start_date, end_date, start_unit, end_unit, last_unit, coun_transaction)
SELECT productId, productSku, IFNULL(SUM(item_quantity * price),0) AS sum_price, ip_start_date,ip_end_date,
       IF(COUNT(*) = 0 ,end_unit, @start_unit) AS s_unit , IFNULL(end_unit,0) AS en_unit, IFNULL(item_quantity,0), COUNT(*) AS count_tran
FROM



    (
        SELECT *, @end_unit:=(@end_unit-quantity),
        IF(@end_unit >= 0, quantity, @end_unit + quantity) AS item_quantity,
        IF(@end_unit<=0, @start_unit:= 0, @start_unit:= @end_unit ) AS update_count_down
        FROM

            tmp_inv
        WHERE @end_unit >= 0) AS t;



END LOOP loop_list;
CLOSE cursor_list;




#  SELECT SUM(end_unit),SUM(start_unit), ROUND(SUM(`value`),2), COUNT(*)  FROM fifo_inventory;


END$$
DELIMITER ;
