<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTimeCheckingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('time_checking', function (Blueprint $table) {
            $table->id();
            $table->integer('employee_id');
            $table->timestamp('start_time');
            $table->timestamp('end_time');
            $table->enum('job_type', ['quality_control','pretreat','folding','multiple_staging','addition','adjust_pullling_shelves','create_box_moving','create_box','pulling_shelve_to_rack','test_count','printing','work_order','filling_shelves','reprint_cs','reprint_qc','create_shipment_label','pulling','manifest','neck_printing','supply_addition','supply_deduction','supply_adjust_inventory','pretreat_printing']);
            $table->integer('quantity')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('time_checking');
    }
}
