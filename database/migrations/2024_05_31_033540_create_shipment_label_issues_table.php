<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShipmentLabelIssuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipment_label_issues', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('shipment_id');
            $table->string('issue_type');
            $table->json('issues')->nullable();
            $table->timestamps();
            $table->index(['shipment_id']);
            $table->index(['issue_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipment_label_issues');
    }
}
