<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderBulkTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_bulk', function (Blueprint $table) {
            $table->id();
            $table->integer('store_id')->nullable();
            $table->timestamps();
            $table->longText('data')->nullable();
            $table->integer('status')->default('0');
            $table->integer('manual')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_bulk');
    }
}
