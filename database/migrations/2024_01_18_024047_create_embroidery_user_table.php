<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmbroideryUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
     public function up()
     {
         Schema::create('embroidery_user', function (Blueprint $table) {
             $table->id();
             $table->string('username')->unique();
             $table->string('name');
             $table->string('password');
             $table->enum('role', ['admin', 'leader', 'editor'])->default('editor');
             $table->string('email')->unique();
             $table->unsignedBigInteger('team_id');
             $table->boolean('is_active')->default(1);
             $table->timestamps();
             $table->index('team_id');
         });
     }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('embroidery_users');
    }
}
