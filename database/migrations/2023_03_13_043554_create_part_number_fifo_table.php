<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartNumberFifoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('part_number_fifo', function (Blueprint $table) {
            $table->id();
            $table->integer('warehouse_id')->nullable();
            $table->integer('part_number_id')->nullable();
            $table->integer('quantity')->nullable();
            $table->date('date_input')->nullable();
            $table->integer('product_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('part_number_fifo');
    }
}
