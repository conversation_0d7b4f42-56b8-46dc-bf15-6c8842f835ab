<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddTypeAddressTypeForStoreAddressTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE store_address MODIFY COLUMN type_address ENUM('from_address', 'return_address', 'billing_address') COMMENT 'type + store id unique'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("ALTER TABLE store_address MODIFY COLUMN type_address ENUM('from_address', 'return_address', 'billing_address') COMMENT 'type + store id unique'");
    }
}
