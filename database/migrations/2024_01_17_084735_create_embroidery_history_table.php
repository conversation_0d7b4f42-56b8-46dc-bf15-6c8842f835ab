<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmbroideryHistoryTable extends Migration
{
    public function up()
    {
        Schema::create('embroidery_history', function (Blueprint $table) {
            $table->id(); // Primary key
            $table->foreignId('task_id');
            $table->string('action'); // Action types: create, assign, upload, review, reject, approve
            $table->string('note')->nullable();
            $table->string('embroidery_user_id')->nullable();
            $table->timestamps();
            $table->index('task_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('embroidery_history');
    }
}
