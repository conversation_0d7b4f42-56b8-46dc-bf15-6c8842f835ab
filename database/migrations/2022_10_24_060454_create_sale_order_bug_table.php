<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderBugTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_bug', function (Blueprint $table) {
            $table->id();
            $table->string('order_number',100)->nullable();
            $table->integer('external_id')->nullable();
            $table->string('external_key',50)->nullable();
            $table->string('external_number');
            $table->enum('order_status',['new_order','shipped','in_production','on_hold','cancelled','manual_progess','rejected'])->nullable();
            $table->integer('warehouse_id')->nullable();
            $table->integer('account_id')->nullable();
            $table->integer('store_id')->nullable();
            $table->integer('is_test')->default('0');
            $table->string('customer_email')->nullable();
            $table->decimal('order_total',13,2)->nullable();
            $table->decimal('amount_paid',13,2)->nullable();
            $table->decimal('tax_amount',13,2)->nullable();
            $table->decimal('shipping_amount',13,2)->nullable();
            $table->text('customer_note')->nullable();
            $table->text('internal_note')->nullable();
            $table->integer('is_gift')->default('0');
            $table->string('gift_message')->default('');
            $table->string('payment_method',100)->nullable();
            $table->enum('shipping_method',['standard','express'])->default('standard');
            $table->enum('source',['shipstation','api','manual',''])->nullable();
            $table->timestamps();
            $table->string('tag')->nullable();
            $table->tinyInteger('is_eps')->default('0');
            $table->date('order_date')->nullable();
            $table->timestamp('order_time')->nullable();
            $table->tinyInteger('is_xqc')->nullable();
            $table->tinyInteger('barcode_printed_status')->default('0');
            $table->timestamp('barcode_printed_at')->nullable();
            $table->timestamp('print_file_created_at')->nullable();
            $table->tinyInteger('print_file_status')->default('0');
            $table->tinyInteger('order_pulled_status')->default('0');
            $table->timestamp('order_pulled_at')->nullable();
            $table->tinyInteger('order_staged_status')->default('0');
            $table->timestamp('order_staged_at')->nullable();
            $table->tinyInteger('skip_alert_default')->default('0');
            $table->timestamp('order_pretreated_at')->nullable();
            $table->tinyInteger('order_pretreated_status')->default('0');
            $table->tinyInteger('order_qc_status')->default('0');
            $table->timestamp('order_qc_at')->nullable();
            $table->timestamp('order_folding_at')->nullable();
            $table->tinyInteger('order_folding_status')->default('0');
            $table->tinyInteger('order_shipping_status')->default('0');
            $table->timestamp('order_shipped_at')->nullable();
            $table->integer('order_quantity')->default('0');
            $table->integer('shipment_id')->nullable();
            $table->timestamp('calculated_at')->nullable();
            $table->integer('invoice_id')->nullable();
            $table->string('merchant_name')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->enum('rejected_reason',['ip_violation','invalid_artwork','invalid_address','out_of_stock'])->nullable();
            $table->tinyInteger('is_manual')->default('0');
            $table->tinyInteger('is_shipment_create_error')->default('0');
            $table->timestamp('manual_process_at')->nullable();
            $table->integer('employee_manual_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_bug');
    }
}
