<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBackorderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('backorder', function (Blueprint $table) {
            $table->id();
            $table->string('product_sku',20)->nullable();
            $table->integer('employee_id')->nullable();
            $table->integer('backorder')->nullable();
            $table->integer('allocated')->nullable();
            $table->timestamp('hide_at')->nullable();
            $table->timestamp('created_at')->nullable();
    		$table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('backorder');
    }
}
