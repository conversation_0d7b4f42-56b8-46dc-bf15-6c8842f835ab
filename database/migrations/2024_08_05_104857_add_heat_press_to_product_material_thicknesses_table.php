<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddHeatPressToProductMaterialThicknessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_material_thicknesses', function (Blueprint $table) {
            $table->integer('heat_press')->after('material_thickness')->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('product_material_thicknesses', function (Blueprint $table) {
            $table->dropColumn('heat_press');
        });
    }
}
