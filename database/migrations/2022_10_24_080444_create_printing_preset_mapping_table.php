<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePrintingPresetMappingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('printing_preset_mapping', function (Blueprint $table) {
            $table->id();
            $table->string('preset_name', 50)->nullable();
            $table->enum('shirt_type', ['fleece', 'tee', 'tiedye'])->nullable();
            $table->enum('ink_color', ['white', 'black', 'all'])->nullable();
            $table->enum('shirt_color', ['black', 'white', 'light', 'dark'])->nullable();
            $table->string('order_tag')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('printing_preset_mapping');
    }
}
