<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBackorderCsvLogItemTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('backorder_csv_log_item', function (Blueprint $table) {
            $table->id();
            $table->integer('backorder_csv_log_id')->nullable();
            $table->string('sku', 225)->nullable();
            $table->integer('backorder')->nullable();
            $table->integer('allocated')->nullable();
            $table->integer('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('backorder_csv_log_item');
    }
}
