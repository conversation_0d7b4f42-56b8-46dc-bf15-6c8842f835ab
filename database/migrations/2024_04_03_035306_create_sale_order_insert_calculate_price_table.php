<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderInsertCalculatePriceTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_insert_calculate_price', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('order_insert_id');
            $table->integer('qty')->nullable();
            $table->decimal('amount_paid', 13, 2)->nullable()->default(0.00);
            $table->decimal('unit_price', 13, 2)->nullable();
            $table->decimal('blank_price', 13, 2)->nullable();
            $table->decimal('handling_fee', 13, 2)->nullable();
            $table->timestamp('calculated_at')->nullable();
            $table->string('reason', 50)->nullable();
            $table->string('size', 50)->nullable();
            $table->string('type', 50)->nullable();
            $table->unsignedBigInteger('product_id')->nullable();
            $table->string('product_sku', 50)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_insert_calculate_price');
    }
}
