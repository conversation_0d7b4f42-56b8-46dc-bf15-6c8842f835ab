<?php

use App\Models\LicensedDesignPrintFile;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLicensedDesignPrintFilesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('licensed_design_print_files', function (Blueprint $table) {
            $table->id();
            $table->string('print_area')->nullable();
            $table->string('licensed_design_id')->nullable()->index();
            $table->enum('version', [LicensedDesignPrintFile::DEFAULT_VERSION, LicensedDesignPrintFile::WHITE_VERSION, LicensedDesignPrintFile::BLACK_VERSION])->nullable();
            $table->string('design_url', 500)->nullable();
            $table->integer('created_by')->nullable();
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('licensed_design_print_files');
    }
}
