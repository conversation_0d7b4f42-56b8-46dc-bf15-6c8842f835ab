<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBarcodePrintedTimeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('barcode_printed_time', function (Blueprint $table) {
            $table->id();
            $table->string('style_sku', 100)->nullable();
            $table->integer('store_id')->nullable();
            $table->integer('account_id')->nullable();
            $table->integer('is_xqc')->nullable();
            $table->integer('warehouse_id')->nullable();
            $table->integer('is_reprint')->nullable();
            $table->timestamp('printed_at')->nullable();
            $table->tinyInteger('is_manual')->nullable();
            $table->integer('is_reroute')->nullable();
            $table->integer('is_fba')->nullable();
            $table->integer('is_insert')->nullable();
            $table->string('color_sku', 100)->nullable();
            $table->tinyInteger('is_eps')->nullable();
            $table->tinyInteger('is_tiktok')->nullable();
            $table->string('print_method')->default(\App\Models\PrintMethod::DTG)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('barcode_printed_time');
    }
}
