<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number', 100)->nullable();
            $table->string('name');
            $table->string('type', 50)->nullable();
            $table->timestamp('start_at')->nullable();
            $table->timestamp('end_at')->nullable();
            $table->timestamp('processing_general_at')->nullable();
            $table->timestamp('processing_production_at')->nullable();
            $table->timestamp('processing_shipping_at')->nullable();
            $table->timestamp('processing_insert_at')->nullable();
            $table->integer('store_id')->nullable();
            $table->boolean('has_error')->default(false);
            $table->integer('has_insert_invoice')->default(0);
            $table->integer('has_promotion_order_invoice')->default(0);
            $table->integer('has_error_insert_invoice')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoices');
    }
}
