<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInventoryDeductionCsvTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('inventory_deduction_csv', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable();
            $table->timestamps();
            $table->string('file')->nullable();
            $table->integer('warehouse_id')->nullable();
            $table->integer('total_row')->nullable();
            $table->integer('import_status')->default(0);
            $table->string('original_file_name')->nullable();
            $table->integer('total_quantity')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('inventory_deduction_csv');
    }
}
