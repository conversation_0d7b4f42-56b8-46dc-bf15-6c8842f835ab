<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSupplyInventoryAdditionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('supply_inventory_addition', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('po_id')->nullable();
            $table->string('sku')->nullable();
            $table->integer('quantity')->nullable();
            $table->unsignedBigInteger('supply_id')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('warehouse_id');
            $table->integer('is_deleted')->default(0);
            $table->integer('location_id')->nullable();
            $table->integer('box_id')->nullable();
            $table->unsignedBigInteger('employee_id')->nullable();
            $table->index('po_id');
            $table->index('sku');
            $table->index('user_id');
            $table->index('warehouse_id');
            $table->index('is_deleted');
            $table->index('employee_id');
            $table->index('created_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('supply_inventory_addition');
    }
}
