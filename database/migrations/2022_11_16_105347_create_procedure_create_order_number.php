<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreateProcedureCreateOrderNumber extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $procedure = "DROP PROCEDURE IF EXISTS `create_order_number`;
        CREATE PROCEDURE create_order_number(IN orderDate VARCHAR(6), IN warehouseID VARCHAR(5), IN orderType VARCHAR(1))
        BEGIN
        INSERT INTO sale_order_number (serial, order_date, warehouse, order_type) VALUES (1, orderDate,warehouseID,orderType) ON DUPLICATE KEY UPDATE serial = serial + 1;
        SELECT CONCAT(i.order_date, '-', i.warehouse, '-',i.order_type, '-', LPAD(i.serial, GREATEST(LENGTH(i.serial), 6), '0') ) AS number from sale_order_number i WHERE order_type = orderType AND  order_date = orderDate AND warehouse = warehouseID;
        END;";
  
        DB::unprepared($procedure);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
