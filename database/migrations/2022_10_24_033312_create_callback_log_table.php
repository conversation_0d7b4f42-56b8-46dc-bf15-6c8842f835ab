<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCallbackLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('callback_log', function (Blueprint $table) {
            $table->id();
            $table->string('order_id',100)->nullable();
            $table->enum('event',['order_notify','shipment_notify', 'printed_notify'])->nullable()->comment('"order_notify","shipment_notify", ""printed_notify""');
            $table->string('value')->nullable();
            $table->integer('store_id')->unsigned()->nullable();
            $table->tinyInteger('status')->default(0);
            $table->longText('message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('callback_log');
    }
}
