<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLogMoveWarehouseTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('log_move_warehouse', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id')->nullable();
            $table->string('shiptation_id')->nullable();
            $table->tinyInteger('is_push_tag')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('log_move_warehouse');
    }
}
