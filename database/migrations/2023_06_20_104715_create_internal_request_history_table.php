<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInternalRequestHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('internal_request_history', function (Blueprint $table) {
            $table->id();
            $table->integer('internal_request_id');
            $table->integer('employee_id');
            $table->enum('action', ['create','receive','fulfill','confirm','reject','timeout','release', 'delete'])->default('create');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('internal_request_history');
    }
}
