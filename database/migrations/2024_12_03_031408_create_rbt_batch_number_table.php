<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRbtBatchNumberTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rbt_batch_number', function (Blueprint $table) {
            $table->id();
            $table->string('label_id', 255);
            $table->unsignedInteger('batch_id'); // Integer column for batch_id
            $table->integer('batch_number'); // Integer column for batch_number
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rbt_batch_number');
    }
}
