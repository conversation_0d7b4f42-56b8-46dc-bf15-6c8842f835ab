<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderAddressTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_address', function (Blueprint $table) {
            $table->id();
            $table->enum('type_address',['to_address','from_address','return_address']);
            $table->integer('order_id');
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('company')->nullable();
            $table->string('phone')->nullable();
            $table->string('street1')->nullable();
            $table->string('street2')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('zip')->nullable();
            $table->string('country',5)->nullable()->comment('country code');
            $table->integer('residential')->default('0')->comment('địa chỉ cư dân true/fase');
            $table->timestamps();
            $table->tinyInteger('verified_status')->default('0')->comment('0 chưa verify, 1 thành công, 2 có vấn đề, 3 lỗi');
            $table->text('verified_message')->nullable();
            $table->tinyInteger('force_verified_delivery')->default('0')->comment('1 là bỏ qua');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_address');
    }
}
