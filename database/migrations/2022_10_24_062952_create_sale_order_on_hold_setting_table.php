<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderOnHoldSettingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_on_hold_setting', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->enum('type_compare_name', ['include', 'equal'])->default('include');
            $table->string('phone')->nullable();
            $table->enum('type_compare_phone', ['include', 'equal'])->default('include');
            $table->string('email')->nullable();
            $table->enum('type_compare_email', ['include', 'equal'])->default('include');
            $table->string('street1')->nullable()->default('NULL');
            $table->enum('type_compare_street1', ['include', 'equal'])->default('include');
            $table->string('street2')->nullable();
            $table->enum('type_compare_street2', ['include', 'equal'])->default('include');
            $table->string('country')->nullable();
            $table->enum('type_compare_country', ['include', 'equal'])->default('include');
            $table->string('city')->nullable();
            $table->enum('type_compare_city', ['include', 'equal'])->default('include');
            $table->string('state')->nullable();
            $table->enum('type_compare_state', ['include', 'equal'])->default('include');
            $table->string('zip')->nullable();
            $table->enum('type_compare_zip', ['include', 'equal'])->default('include');
            $table->tinyInteger('is_active')->default('1');
            $table->string('type_address')->nullable()->default('to_address');
            $table->timestamp('created_at')->nullable()->useCurrent();
            $table->timestamp('updated_at')->nullable()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_on_hold_setting');
    }
}
