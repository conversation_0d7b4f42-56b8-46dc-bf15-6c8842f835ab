<?php

use App\Models\Store;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoreTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('store', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('code')->nullable();
            $table->integer('account_id')->nullable();
            $table->boolean('is_active')->default(1);
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->string('email')->nullable();
            $table->string('company')->nullable();
            $table->string('phone')->nullable();
            $table->string('street1')->nullable();
            $table->string('street2')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->string('zip')->nullable();
            $table->string('contact_name')->nullable();
            $table->string('contact_phone')->nullable();
            $table->string('contact_email')->nullable();
            $table->text('token')->nullable();
            $table->enum('non_delivery_option', ['return', 'abandon'])->default('return');
            $table->string('easypost_api_key')->nullable();
            $table->string('callback')->nullable();
            $table->boolean('is_auto_create_shipping')->default(0);
            $table->string('order_desk_api_key')->nullable();
            $table->integer('order_desk_store_id')->nullable();
            $table->tinyInteger('api_manual')->default(0)->comment('kiểm tra nếu true thì store mới được tạo manual order qua api');
            $table->tinyInteger('api_sample')->default(0)->comment('kiểm tra nếu true thì store mới được tạo XQC qua api');
            $table->tinyInteger('api_merchant_name')->default(1)->comment('kiểm tra nếu true thì store mới được tạo merchant_name qua api');
            $table->date('date_start_auto_label')->nullable();
            $table->tinyInteger('is_resize')->default(1);
            $table->timestamp('stop_sync_shipstation_orders_at')->nullable()->comment('bỏ qua không đồng bộ đơn của shipsatation');
            $table->boolean('is_calculate_shipping')->default(1);
            $table->boolean('is_calculate_price')->default(0);
            $table->string('billing_contact')->nullable();
            $table->integer('days_until_bill_due')->default(0);
            $table->tinyInteger('has_note')->default(0);
            $table->tinyInteger('template_neck')->default(0);
            $table->tinyInteger('is_on_hold')->default(0);
            $table->integer('limit_order')->default(0);
            $table->integer('client_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamp('auto_shipping_pending_at')->nullable();
            $table->string('billing_email')->nullable();
            $table->string('stripe_id')->nullable()->index();
            $table->string('pm_type')->nullable();
            $table->string('pm_last_four', 4)->nullable();
            $table->enum('payment_terms', [Store::STORE_POSTPAID, Store::STORE_PREPAID])->default(Store::STORE_POSTPAID);
            $table->integer('billing_address_id')->nullable();
            $table->boolean('is_refilling')->default(false);
            $table->integer('in_stock_date')->nullable(8);
            $table->integer('on_demand_date')->nullable(3);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('store');
    }
}
