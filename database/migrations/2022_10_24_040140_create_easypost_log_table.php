<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEasypostLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('easypost_log', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id');
            $table->integer('shipment_id');
            $table->longText('data')->comment('log data easypost tra ve');
            $table->tinyInteger('sync_zpl')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('easypost_log');
    }
}
