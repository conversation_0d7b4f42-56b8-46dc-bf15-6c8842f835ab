<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CostReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cost_report', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('warehouse_id');
            $table->integer('employee_id')->nullable();
            $table->integer('product_type_id')->nullable();
            $table->decimal('white_ink_cost', 13, 2)->nullable();
            $table->decimal('color_ink_cost', 13, 2)->nullable();
            $table->decimal('pretreat_cost', 13, 2)->nullable();
            $table->decimal('bag_cost', 13, 2)->nullable();
            $table->decimal('utility_cost', 13, 3)->nullable();
            $table->text('options')->nullable();
            $table->integer('affected_month_at')->nullable();
            $table->integer('affected_year_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cost_report');
    }
}
