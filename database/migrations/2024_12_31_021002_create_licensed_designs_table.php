<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLicensedDesignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('licensed_designs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('licensed_design_id', 50);
            $table->string('license_holder')->nullable();
            $table->bigInteger('licensed_holder_id')->nullable()->index();
            $table->timestamps();
            $table->unique(['licensed_design_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('licensed_designs');
    }
}
