<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('sale_order_account', function (Blueprint $table) {
            $table->unsignedInteger('default_store_id')->nullable()->after('is_editing');
        });
    }

    public function down(): void
    {
        Schema::table('sale_order_account', function (Blueprint $table) {
            $table->dropColumn('default_store_id');
        });
    }
};
