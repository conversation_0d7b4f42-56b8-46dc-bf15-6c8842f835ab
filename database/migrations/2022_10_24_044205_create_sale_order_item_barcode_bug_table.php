<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderItemBarcodeBugTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_item_barcode_bug', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id')->nullable();
            $table->integer('order_item_id')->nullable();
            $table->string('sku',50)->nullable();
            $table->string('label_id',32)->nullable();
            $table->string('label_root_id',32)->nullable();
            $table->integer('warehouse_id')->default('0');
            $table->integer('account_id')->default('0');
            $table->integer('store_id')->default('0');
            $table->integer('barcode_printed_id')->default('0');
            $table->integer('barcode_number')->nullable();
            $table->integer('order_quantity')->nullable();
            $table->tinyInteger('is_deleted')->default('0');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->integer('employee_pull_id')->nullable();
            $table->timestamp('pulled_at')->nullable();
            $table->integer('employee_pretreat_id')->nullable();
            $table->timestamp('pretreated_at')->nullable();
            $table->integer('employee_print_id')->nullable();
            $table->timestamp('printed_at')->nullable();
            $table->integer('employee_qc_id')->nullable();
            $table->timestamp('qc_at')->nullable();
            $table->integer('employee_ship_id')->nullable();
            $table->timestamp('shipped_at')->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamp('print_barcode_at')->nullable();
            $table->timestamp('staged_at')->nullable();
            $table->integer('employee_staging_id')->nullable();
            $table->integer('employee_folding_id')->nullable();
            $table->timestamp('folded_at')->nullable();
            $table->integer('employee_reprint_id')->nullable();
            $table->timestamp('reprinted_at')->nullable();
            $table->tinyInteger('reprint_status')->default('0');
            $table->integer('employee_reroute_id')->nullable();
            $table->integer('pdf_converted_id')->nullable();
            $table->tinyInteger('convert_pdf_status')->nullable();
            $table->integer('retry_convert')->default('0');
            $table->string('print_method')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_item_barcode_bug');
    }
}
