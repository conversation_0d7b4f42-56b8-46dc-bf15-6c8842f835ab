<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTestCountTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('test_count', function (Blueprint $table) {
            $table->id();
            $table->integer('box_available')->default('0');
            $table->integer('box_on_hand')->default('0');
            $table->integer('warehouse_id');
            $table->integer('location_id')->default('0');
            $table->integer('user_id');
            $table->string('note')->nullable();
            $table->integer('employee_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('test_count');
    }
}
