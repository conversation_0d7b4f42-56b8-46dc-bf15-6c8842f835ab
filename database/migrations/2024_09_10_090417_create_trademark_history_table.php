<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTrademarkHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('trademark_history', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['character', 'brand'])->default('character');
            $table->integer('trademark_id');
            $table->integer('updated_by');
            $table->integer('ownership_id');
            $table->tinyInteger('is_active')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('trademark_history');
    }
}
