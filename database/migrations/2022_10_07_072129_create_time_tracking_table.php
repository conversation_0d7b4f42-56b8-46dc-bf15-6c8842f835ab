<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTimeTrackingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('time_tracking', function (Blueprint $table) {
            $table->id();
            $table->integer('employee_id')->nullable();
            $table->timestamp('start_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->enum('job_type', ['quality_control', 'pretreat', 'folding', 'multiple_staging', 'addition', 'adjust_pullling_shelves', 'create_box_moving', 'create_box', 'pulling_shelve_to_rack', 'test_count', 'printing', 'work_order', 'filling_shelves', 'reprint_cs', 'reprint_qc', 'create_shipment_label', 'pulling', 'adjust_supply_inventory', 'press'])->nullable();
            $table->integer('quantity')->default('0');
            $table->integer('parent_id')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('time_tracking');
    }
}
