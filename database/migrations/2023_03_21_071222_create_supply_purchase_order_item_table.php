<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSupplyPurchaseOrderItemTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('supply_purchase_order_item', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('po_id');
            $table->unsignedBigInteger('supply_id');
            $table->integer('quantity')->default(0);
            $table->integer('quantity_onhand')->nullable();
            $table->decimal('price', 13, 2)->nullable()->comment('gia mua');
            $table->decimal('total', 13, 2)->nullable()->comment('= quanity * rate');
            $table->enum('received_status', ['received', 'not_received', 'partial_received'])->nullable();
            $table->tinyInteger('new_item')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('supply_purchase_order_item');
    }
}
