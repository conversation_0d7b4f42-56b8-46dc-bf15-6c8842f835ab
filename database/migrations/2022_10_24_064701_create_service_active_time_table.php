<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateServiceActiveTimeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('service_active_time', function (Blueprint $table) {
            $table->id();
            $table->timestamp('updated_at')->nullable();
            $table->tinyInteger('warehouse_id')->nullable();
            $table->tinyInteger('state')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('service_active_time');
    }
}
