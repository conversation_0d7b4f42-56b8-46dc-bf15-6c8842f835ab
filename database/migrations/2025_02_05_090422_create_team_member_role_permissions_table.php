<?php

use App\Models\TeamMemberRolePermission;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTeamMemberRolePermissionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('team_member_role_permissions', function (Blueprint $table) {
            $table->id();
            $table->integer('team_member_role_id');
            $table->enum('function_name', TeamMemberRolePermission::listFuction());
            $table->enum('permission', TeamMemberRolePermission::listPermission());
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('team_member_role_permissions');
    }
}
