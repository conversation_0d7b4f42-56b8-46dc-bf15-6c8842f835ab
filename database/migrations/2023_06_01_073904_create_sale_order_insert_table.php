<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderInsertTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_insert', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id')->nullable();
            $table->string('url', 1000)->nullable();
            $table->string('size', 50)->nullable();
            $table->string('type', 50)->nullable();
            $table->string('message', 250)->nullable();
            $table->timestamp('printed_at')->nullable()->default(now());
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_insert');
    }
}
