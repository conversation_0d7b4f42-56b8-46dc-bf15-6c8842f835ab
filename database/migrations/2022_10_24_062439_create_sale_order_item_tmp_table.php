<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSaleOrderItemTmpTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sale_order_item_tmp', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id')->nullable();
            $table->string('external_id')->nullable();
            $table->string('external_line_item_key')->nullable();
            $table->string('sku')->nullable();
            $table->string('name')->default('');
            $table->string('image_url',1000)->nullable();
            $table->decimal('weight',13,2)->nullable();
            $table->integer('quantity')->default('0');
            $table->longText('options')->nullable();
            $table->timestamp('created_at')->nullable()->useCurrent();
            $table->timestamp('updated_at')->nullable();
            $table->decimal('unit_price',13,2)->nullable();
            $table->decimal('tax_amount',13,2)->nullable();
            $table->decimal('shipping_amount',13,2)->nullable();
            $table->integer('store_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sale_order_item_tmp');
    }
}
