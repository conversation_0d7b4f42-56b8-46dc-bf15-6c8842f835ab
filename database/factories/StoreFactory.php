<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class StoreFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'username' => $this->faker->userName(),
            'password' => bcrypt('123456'),
            'email' => $this->faker->unique()->safeEmail(),
            'token' => $this->faker->windowsPlatformToken(),
        ];
    }
}
