<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;


class ImportOrderCsvFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'file_name' => 'swiftpod' . $this->faker->numberBetween(1, 5) . '.csv',
            'order_total' => $this->faker->numberBetween(10, 100),
            'order_imported' => $this->faker->numberBetween(1, 50),
            'order_failed' => $this->faker->numberBetween(1, 50),
            'file_imported' => $this->faker->word . '_imported.csv',
            'file_failed' => $this->faker->word . '_failed.csv',
        ];
    }
}
