<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class SaleOrderAddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'phone' => $this->faker->randomNumber(),
            'country' => $this->faker->country(),
            'street1' => $this->faker->streetAddress(),
            'street2' => $this->faker->streetAddress(),
            'city' => $this->faker->country(),
            'state' => $this->faker->country(),
            'zip' => $this->faker->postcode(),
            'email' => $this->faker->email(),
            'force_verified_delivery' => $this->faker->randomElement([0, 1]),
            'company' => $this->faker->name(),
        ];
    }
}
